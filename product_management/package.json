{"name": "kinn_backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"seeder": "NODE_ENV=development npx sequelize-cli db:seed:all", "start": "nodemon server", "cypress:open": "cypress open", "cypress:run": "cypress run --record --key ", "inittest": "MASTER_OTP_USAGE=true  NODE_ENV=test start-server-and-test start http://localhost:3020 cypress:open", "devtest": "MASTER_OTP_USAGE=true  NODE_ENV=test start-server-and-test start http://**************:3007 cypress:run", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@hapi/boom": "^10.0.0", "@hapi/hapi": "^21.2.0", "@hapi/inert": "^7.0.0", "@hapi/vision": "^7.0.0", "@joi/date": "^2.1.0", "axios": "^1.2.5", "axios-https-proxy-fix": "^0.17.1", "bcrypt": "^5.1.0", "child_process": "^1.0.2", "colors": "^1.4.0", "crypto": "^1.0.1", "crypto-js": "^4.1.1", "cypress": "^12.4.0", "dotenv": "^16.0.3", "fs": "0.0.1-security", "handlebars": "^4.7.7", "hapi-auth-jwt2": "^10.4.0", "hapi-auto-route": "^3.0.4", "hapi-cron": "^1.1.0", "hapi-i18n": "^3.0.1", "hapi-swagger": "^15.0.0", "http": "^0.0.1-security", "ip": "^1.1.8", "joi": "^17.7.0", "jsonwebtoken": "^9.0.0", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.29.4", "mysql2": "^3.0.1", "node-jose": "^2.1.1", "node-rsa": "^1.1.1", "nodemon": "^2.0.20", "nonce": "^1.0.4", "path": "^0.12.7", "prettier": "^2.8.3", "querystring": "^0.2.1", "sequelize": "^6.28.0", "sharp": "^0.31.3", "slug": "^8.2.2", "striptags": "^3.2.0", "uuid": "^9.0.0", "vimeo": "^2.3.1"}, "devDependencies": {"@hapi/code": "^9.0.2", "@hapi/lab": "^25.1.0", "chai": "^4.3.7", "sequelize-cli": "^6.6.0", "start-server-and-test": "^1.15.3"}}