'use strict';
const Constants= require('../constants');
const { v4: uuidv4 } = require('uuid');

module.exports = {
  async up (queryInterface, Sequelize) {
   await queryInterface.bulkInsert('prdmng_languages',[
    { name:"English", status:Constants.STATUS.ACTIVE, code:'en',    is_default:Constants.STATUS.ACTIVE,   created_at:new Date(),  updated_at:new Date()},
    { name:"Arabic",  status:Constants.STATUS.ACTIVE, code:'ar',    is_default:Constants.STATUS.INACTIVE, created_at:new Date(),  updated_at:new Date()},
    { name:"French",  status:Constants.STATUS.ACTIVE, code:'fr',    is_default:Constants.STATUS.INACTIVE, created_at:new Date(),  updated_at:new Date()},
    { name:"German",  status:Constants.STATUS.ACTIVE, code:'de',    is_default:Constants.STATUS.INACTIVE, created_at:new Date(),  updated_at:new Date()},
    { name:"Spanish", status:Constants.STATUS.ACTIVE, code:'es',    is_default:Constants.STATUS.INACTIVE, created_at:new Date(),  updated_at:new Date()},
  ])
  },
  
  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  }
};
