'use strict';

module.exports = {
	async up (queryInterface, Sequelize) {
		/**
		 * Add seed commands here.
		 *
		 * Example:
		 * await queryInterface.bulkInsert('People', [{
		 *	name:   '<PERSON>',
		 *	isBetaMember:   false
		 * }], {});
		*/

		await queryInterface.bulkInsert("prdmng_currencies", [
			{
				"currency_code" : "AED",
				"symbol"        : "\u062f.\u0625;",
				"name"          : "UAE dirham",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "AFN",
				"symbol"        : "Afs",
				"name"          : "Afghan afghani",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "ALL",
				"symbol"        : "L",
				"name"          : "Albanian lek",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "AMD",
				"symbol"        : "AMD",
				"name"          : "Armenian dram",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "ANG",
				"symbol"        : "NA\u0192",
				"name"          : "Netherlands Antillean gulden",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "AOA",
				"symbol"        : "Kz",
				"name"          : "Angolan kwanza",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "ARS",
				"symbol"        : "$",
				"name"          : "Argentine peso",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "AUD",
				"symbol"        : "$",
				"name"          : "Australian dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "AWG",
				"symbol"        : "\u0192",
				"name"          : "Aruban florin",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "AZN",
				"symbol"        : "AZN",
				"name"          : "Azerbaijani manat",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "BAM",
				"symbol"        : "KM",
				"name"          : "Bosnia and Herzegovina konvertibilna marka",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "BBD",
				"symbol"        : "Bds$",
				"name"          : "Barbadian dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "BDT",
				"symbol"        : "\u09f3",
				"name"          : "Bangladeshi taka",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "BGN",
				"symbol"        : "BGN",
				"name"          : "Bulgarian lev",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "BHD",
				"symbol"        : ".\u062f.\u0628",
				"name"          : "Bahraini dinar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "BIF",
				"symbol"        : "FBu",
				"name"          : "Burundi franc",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "BMD",
				"symbol"        : "BD$",
				"name"          : "Bermudian dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "BND",
				"symbol"        : "B$",
				"name"          : "Brunei dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "BOB",
				"symbol"        : "Bs.",
				"name"          : "Bolivian boliviano",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "BRL",
				"symbol"        : "R$",
				"name"          : "Brazilian real",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "BSD",
				"symbol"        : "B$",
				"name"          : "Bahamian dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "BTN",
				"symbol"        : "Nu.",
				"name"          : "Bhutanese ngultrum",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "BWP",
				"symbol"        : "P",
				"name"          : "Botswana pula",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "BYR",
				"symbol"        : "Br",
				"name"          : "Belarusian ruble",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "BZD",
				"symbol"        : "BZ$",
				"name"          : "Belize dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "CAD",
				"symbol"        : "$",
				"name"          : "Canadian dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "CDF",
				"symbol"        : "F",
				"name"          : "Congolese franc",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "CHF",
				"symbol"        : "Fr.",
				"name"          : "Swiss franc",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "CLP",
				"symbol"        : "$",
				"name"          : "Chilean peso",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "CNY",
				"symbol"        : "\u00a5",
				"name"          : "Chinese/Yuan renminbi",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "COP",
				"symbol"        : "Col$",
				"name"          : "Colombian peso",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "CRC",
				"symbol"        : "\u20a1",
				"name"          : "Costa Rican colon",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "CUC",
				"symbol"        : "$",
				"name"          : "Cuban peso",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "CVE",
				"symbol"        : "Esc",
				"name"          : "Cape Verdean escudo",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "CZK",
				"symbol"        : "K\u010d",
				"name"          : "Czech koruna",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "DJF",
				"symbol"        : "Fdj",
				"name"          : "Djiboutian franc",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "DKK",
				"symbol"        : "Kr",
				"name"          : "Danish krone",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "DOP",
				"symbol"        : "RD$",
				"name"          : "Dominican peso",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "DZD",
				"symbol"        : "\u062f.\u062c",
				"name"          : "Algerian dinar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "EEK",
				"symbol"        : "KR",
				"name"          : "Estonian kroon",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "EGP",
				"symbol"        : "\u00a3",
				"name"          : "Egyptian pound",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "ERN",
				"symbol"        : "Nfa",
				"name"          : "Eritrean nakfa",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "ETB",
				"symbol"        : "Br",
				"name"          : "Ethiopian birr",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "EUR",
				"symbol"        : "\u20ac",
				"name"          : "European Euro",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "FJD",
				"symbol"        : "FJ$",
				"name"          : "Fijian dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "FKP",
				"symbol"        : "\u00a3",
				"name"          : "Falkland Islands pound",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "GBP",
				"symbol"        : "\u00a3",
				"name"          : "British pound",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "GEL",
				"symbol"        : "GEL",
				"name"          : "Georgian lari",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "GHS",
				"symbol"        : "GH\u20b5",
				"name"          : "Ghanaian cedi",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "GIP",
				"symbol"        : "\u00a3",
				"name"          : "Gibraltar pound",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "GMD",
				"symbol"        : "D",
				"name"          : "Gambian dalasi",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "GNF",
				"symbol"        : "FG",
				"name"          : "Guinean franc",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "GQE",
				"symbol"        : "CFA",
				"name"          : "Central African CFA franc",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "GTQ",
				"symbol"        : "Q",
				"name"          : "Guatemalan quetzal",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "GYD",
				"symbol"        : "GY$",
				"name"          : "Guyanese dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "HKD",
				"symbol"        : "HK$",
				"name"          : "Hong Kong dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "HNL",
				"symbol"        : "L",
				"name"          : "Honduran lempira",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "HRK",
				"symbol"        : "kn",
				"name"          : "Croatian kuna",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "HTG",
				"symbol"        : "G",
				"name"          : "Haitian gourde",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "HUF",
				"symbol"        : "Ft",
				"name"          : "Hungarian forint",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "IDR",
				"symbol"        : "Rp",
				"name"          : "Indonesian rupiah",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "ILS",
				"symbol"        : "\u20aa",
				"name"          : "Israeli new sheqel",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "INR",
				"symbol"        : "\u20B9",
				"name"          : "Indian rupee",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "IQD",
				"symbol"        : "\u062f.\u0639",
				"name"          : "Iraqi dinar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "IRR",
				"symbol"        : "IRR",
				"name"          : "Iranian rial",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "ISK",
				"symbol"        : "kr",
				"name"          : "Icelandic kr\u00f3na",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "JMD",
				"symbol"        : "J$",
				"name"          : "Jamaican dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "JOD",
				"symbol"        : "JOD",
				"name"          : "Jordanian dinar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "JPY",
				"symbol"        : "\u00a5",
				"name"          : "Japanese yen",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "KES",
				"symbol"        : "KSh",
				"name"          : "Kenyan shilling",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "KGS",
				"symbol"        : "\u0441\u043e\u043c",
				"name"          : "Kyrgyzstani som",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "KHR",
				"symbol"        : "\u17db",
				"name"          : "Cambodian riel",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "KMF",
				"symbol"        : "KMF",
				"name"          : "Comorian franc",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "KPW",
				"symbol"        : "W",
				"name"          : "North Korean won",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "KRW",
				"symbol"        : "W",
				"name"          : "South Korean won",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "KWD",
				"symbol"        : "KWD",
				"name"          : "Kuwaiti dinar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "KYD",
				"symbol"        : "KY$",
				"name"          : "Cayman Islands dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "KZT",
				"symbol"        : "T",
				"name"          : "Kazakhstani tenge",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "LAK",
				"symbol"        : "KN",
				"name"          : "Lao kip",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "LBP",
				"symbol"        : "\u00a3",
				"name"          : "Lebanese lira",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "LKR",
				"symbol"        : "Rs",
				"name"          : "Sri Lankan rupee",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "LRD",
				"symbol"        : "L$",
				"name"          : "Liberian dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "LSL",
				"symbol"        : "M",
				"name"          : "Lesotho loti",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "LTL",
				"symbol"        : "Lt",
				"name"          : "Lithuanian litas",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "LVL",
				"symbol"        : "Ls",
				"name"          : "Latvian lats",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "LYD",
				"symbol"        : "LD",
				"name"          : "Libyan dinar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "MAD",
				"symbol"        : "MAD",
				"name"          : "Moroccan dirham",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "MDL",
				"symbol"        : "MDL",
				"name"          : "Moldovan leu",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "MGA",
				"symbol"        : "FMG",
				"name"          : "Malagasy ariary",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "MKD",
				"symbol"        : "MKD",
				"name"          : "Macedonian denar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "MMK",
				"symbol"        : "K",
				"name"          : "Myanma kyat",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "MNT",
				"symbol"        : "\u20ae",
				"name"          : "Mongolian tugrik",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "MOP",
				"symbol"        : "P",
				"name"          : "Macanese pataca",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "MRO",
				"symbol"        : "UM",
				"name"          : "Mauritanian ouguiya",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "MUR",
				"symbol"        : "Rs",
				"name"          : "Mauritian rupee",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "MVR",
				"symbol"        : "Rf",
				"name"          : "Maldivian rufiyaa",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "MWK",
				"symbol"        : "MK",
				"name"          : "Malawian kwacha",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "MXN",
				"symbol"        : "$",
				"name"          : "Mexican peso",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "MYR",
				"symbol"        : "RM",
				"name"          : "Malaysian ringgit",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "MZM",
				"symbol"        : "MTn",
				"name"          : "Mozambican metical",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "NAD",
				"symbol"        : "N$",
				"name"          : "Namibian dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "NGN",
				"symbol"        : "\u20a6",
				"name"          : "Nigerian naira",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "NIO",
				"symbol"        : "C$",
				"name"          : "Nicaraguan c\u00f3rdoba",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "NOK",
				"symbol"        : "kr",
				"name"          : "Norwegian krone",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "NPR",
				"symbol"        : "NRs",
				"name"          : "Nepalese rupee",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "NZD",
				"symbol"        : "NZ$",
				"name"          : "New Zealand dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "OMR",
				"symbol"        : "OMR",
				"name"          : "Omani rial",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "PAB",
				"symbol"        : "B./",
				"name"          : "Panamanian balboa",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "PEN",
				"symbol"        : "S/.",
				"name"          : "Peruvian nuevo sol",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "PGK",
				"symbol"        : "K",
				"name"          : "Papua New Guinean kina",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "PHP",
				"symbol"        : "\u20b1",
				"name"          : "Philippine peso",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "PKR",
				"symbol"        : "Rs.",
				"name"          : "Pakistani rupee",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "PLN",
				"symbol"        : "z\u0142",
				"name"          : "Polish zloty",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "PYG",
				"symbol"        : "\u20b2",
				"name"          : "Paraguayan guarani",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "QAR",
				"symbol"        : "QR",
				"name"          : "Qatari riyal",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "RON",
				"symbol"        : "L",
				"name"          : "Romanian leu",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "RSD",
				"symbol"        : "din.",
				"name"          : "Serbian dinar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "RUB",
				"symbol"        : "R",
				"name"          : "Russian ruble",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "SAR",
				"symbol"        : "SR",
				"name"          : "Saudi riyal",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "SBD",
				"symbol"        : "SI$",
				"name"          : "Solomon Islands dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "SCR",
				"symbol"        : "SR",
				"name"          : "Seychellois rupee",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "SDG",
				"symbol"        : "SDG",
				"name"          : "Sudanese pound",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "SEK",
				"symbol"        : "kr",
				"name"          : "Swedish krona",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "SGD",
				"symbol"        : "S$",
				"name"          : "Singapore dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "SHP",
				"symbol"        : "\u00a3",
				"name"          : "Saint Helena pound",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "SLL",
				"symbol"        : "Le",
				"name"          : "Sierra Leonean leone",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "SOS",
				"symbol"        : "Sh.",
				"name"          : "Somali shilling",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "SRD",
				"symbol"        : "$",
				"name"          : "Surinamese dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "SYP",
				"symbol"        : "LS",
				"name"          : "Syrian pound",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "SZL",
				"symbol"        : "E",
				"name"          : "Swazi lilangeni",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "THB",
				"symbol"        : "\u0e3f",
				"name"          : "Thai baht",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "TJS",
				"symbol"        : "TJS",
				"name"          : "Tajikistani somoni",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "TMT",
				"symbol"        : "m",
				"name"          : "Turkmen manat",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "TND",
				"symbol"        : "DT",
				"name"          : "Tunisian dinar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "TRY",
				"symbol"        : "TRY",
				"name"          : "Turkish new lira",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "TTD",
				"symbol"        : "TT$",
				"name"          : "Trinidad and Tobago dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "TWD",
				"symbol"        : "NT$",
				"name"          : "New Taiwan dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "TZS",
				"symbol"        : "TZS",
				"name"          : "Tanzanian shilling",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "UAH",
				"symbol"        : "UAH",
				"name"          : "Ukrainian hryvnia",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "UGX",
				"symbol"        : "USh",
				"name"          : "Ugandan shilling",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "USD",
				"symbol"        : "US$",
				"name"          : "United States dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "UYU",
				"symbol"        : "$U",
				"name"          : "Uruguayan peso",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "UZS",
				"symbol"        : "UZS",
				"name"          : "Uzbekistani som",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "VEB",
				"symbol"        : "Bs",
				"name"          : "Venezuelan bolivar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "VND",
				"symbol"        : "\u20ab",
				"name"          : "Vietnamese dong",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "VUV",
				"symbol"        : "VT",
				"name"          : "Vanuatu vatu",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "WST",
				"symbol"        : "WS$",
				"name"          : "Samoan tala",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "XAF",
				"symbol"        : "CFA",
				"name"          : "Central African CFA franc",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "XCD",
				"symbol"        : "EC$",
				"name"          : "East Caribbean dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "XDR",
				"symbol"        : "SDR",
				"name"          : "Special Drawing Rights",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "XOF",
				"symbol"        : "CFA",
				"name"          : "West African CFA franc",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "XPF",
				"symbol"        : "F",
				"name"          : "CFP franc",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "YER",
				"symbol"        : "YER",
				"name"          : "Yemeni rial",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "ZAR",
				"symbol"        : "R",
				"name"          : "South African rand",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "ZMK",
				"symbol"        : "ZK",
				"name"          : "Zambian kwacha",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			},
			{
				"currency_code" : "ZWR",
				"symbol"        : "Z$",
				"name"          : "Zimbabwean dollar",
				"created_at"    : new Date(),
				"updated_at"    : new Date(),
				"deleted_at"    : null
			}
		], {});
	},

	async down (queryInterface, Sequelize) {
		/**
		 * Add commands to revert seed here.
		 *
		 * Example:
		 * await queryInterface.bulkDelete('People', null, {});
		 */

		await queryInterface.bulkDelete("user_currencies", null, {});
	}
};
