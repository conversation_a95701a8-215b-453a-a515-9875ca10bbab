
const {updateAttachmentService}=require("../services/webhook")
module.exports = {
    /**
     * Handler for category CRUD
     * <AUTHOR> 
     * @param {*} req 
     * @param {*} h 
     * @returns 
     */

    //create products
    create  :   async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try{
            const {parentId,id,name}=req.payload
            let defaultLanguage=process.env.DEFAULT_LANGUANGE_CODE_ID
            
            let code=name.trim().toLowerCase().replace(new RegExp(" ", "g"),'-')
            if(id)
            {
                // update existing or create in differrnt lang.
                let record=await Models.Tag.findOne({where:{id}})
                if(!record){
                    await transaction.rollback()
                    return h.response({success: false,message: req.i18n.__("TAG_NOT_EXISTS"),responseData: {}}).code(400);
                }
               
                let alreadyExixt=await Models.TagContent.findOne({where:{tagId:id,languageId:lang}})
                if(alreadyExixt)
                {
                   
                    // Updating Content
                    await alreadyExixt.update({name:name},{transaction})
                    await transaction.commit()
                    return h.response({success: true,message: req.i18n.__("TAG_SUCCESSFULLY_UPDATED"),responseData:record}).code(200)
                }
                    // Creating Content
                    let res=await Models.TagContent.create({name,tagId:id,languageId:lang},{transaction})
                    await transaction.commit();
                    return h.response({success: true,message: req.i18n.__("TAG_SUCCESSFULLY_ADDED"),responseData:res}).code(200)
            }
            else{
                if(lang!=defaultLanguage)
                {
                    await transaction.rollback();
                    return h.response({success: false,message: req.i18n.__("PLEASE_CREATE_TAG_IN_DEFAULT_LANGUAGE"),responseData: {}}).code(400);
                }
                let res=await Models.Tag.create({
                    code,
                    parentId,
                    TagContents:[
                        {
                            name,
                            languageId:lang
                        }
                    ]
                },{
                transaction,include:[{model:Models.TagContent}]})
                // if (attachment != null) {
                //     let reqPayload = {
                //         data: [
                //             {
                //                 id: attachment.id,
                //                 status: Constants.STATUS.ACTIVE
                //             }
                //         ]
                //     }
                //     await updateAttachmentService(reqPayload, transaction);
                // }
                await transaction.commit()
                return h.response({success: true,message: req.i18n.__("TAG_SUCCESSFULLY_CREATED"),responseData: res}).code(200)
            }
        }
        catch(error)
        {
            console.error('Error in Tag Creation',error)
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    //get category
    get:async(req,h)=>{
        try{
            const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
            const offset = (req.query.pageNumber - 1) * limit
            const orderByValue = req.query.orderByValue
            const orderByParameter = req.query.orderByParameter
            let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
            let where = {},inner_where={} 
            inner_where={
                languageId:lang
            }
            replacements={}
        if(req.query.name!==null)
        {
            let name = '*'+req.query.name.replace('@','%40').replace('.','%2E')+'*';
            replacements={...replacements,"name":name}
           inner_where = { ...inner_where, [Op.or]: [
                Sequelize.literal('MATCH(`name`) AGAINST(:name IN BOOLEAN MODE)'),

              ] }
        }
        
        let _includesOption     = [{
                model: Models.TagContent,
                where:{
                    ...inner_where
                },
               attributes:attributeForTag()
               ,required:false
            }]
        let options = {
            where,
            order       : [
                    [orderByParameter, orderByValue]
                ],
            subQuery    : false,
            replacements,
            raw:true,
            nest:true,
            include     : _includesOption
        };
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                limit,
                offset

            };
        const category          = await Models.Tag.findAndCountAll(options);
        const totalPages        = await Common.getTotalPages(category.count, limit);
        const responseData      = {
            totalPages,
            perPage: limit,
            records: category.rows,
            totalRecords: category.count
        };
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
        }
        catch(error)
        {
            console.error('Error in getting category',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    //get by id
    getById:async(req,h)=>{
        try{
            let id=req.query.id
            let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
            let category=await Models.Tag.findOne({where:{id},include:[
                {model: Models.TagContent,where:{languageId:lang},
                attributes:attributeForTag(),
                required:false
            }
            ],
            raw:true,
            nest:true
        })
            if(!category)
            {
                return h.response({success: true, message: req.i18n.__("INVALID_TAG_ID"), responseData: {}}).code(400);
            }
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: category}).code(200);
        }
        catch(error)
        {
            console.error('Error in getting category by id')
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    //remove or destroy
    delete:async(req,h)=>{
        const transaction = await Models.sequelize.transaction()
        try{
            let id=req.query.id
            let category=await Models.Tag.findOne({where:{id},include:[
                {model: Models.TagContent}
            ]})
            if(!category)
            {
                return h.response({success: true, message: req.i18n.__("INVALID_TAG_ID"), responseData: {}}).code(400);
            }
            await Models.TagContent.destroy({where:{tagId:id},transaction})
            await category.destroy({transaction})
            await transaction.commit()
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: category}).code(200);
        }
        catch(error)
        {   
            await transaction.rollback()
            console.error('Error in destroying cat',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500)
        }
    },

    // // get all record without pagination and without authentication.
    getAllRecord:async(req,h)=>{
        try{
            const orderByValue = req.query.orderByValue
            const orderByParameter = req.query.orderByParameter
            let where = {},inner_where={} 
            inner_where={
                languageId:lang
            }
            replacements={}
        if(req.query.name!==null)
        {
            let name = '*'+req.query.name.replace('@','%40').replace('.','%2E')+'*';
            replacements={...replacements,"name":name}
           inner_where = { ...inner_where, [Op.or]: [
                Sequelize.literal('MATCH(`name`) AGAINST(:name IN BOOLEAN MODE)'),
            
              ] }
        }
        
        let _includesOption     = [{
                model: Models.TagContent,
                where:{
                    ...inner_where
                },
                required:false,
                attributes:attributeForTag()
            }]
        let options = {
            where,
            order       : [
                    [orderByParameter, orderByValue]
                ],
            subQuery    : false,
            replacements,
            raw:true,
            nest:true,
            include     : _includesOption
        };
       
        const category          = await Models.Tag.findAndCountAll(options);
        const responseData      = {
            records: category.rows,
            totalRecords: category.count
        };
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
        }
        catch(error)
        {
            console.error('Error in getting category',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    }
}


/**
     * Functions used  for category CRUD
     * <AUTHOR> Kushwaha 
     */
// Function for handling attributes for model at time of get query
const attributeForTag=()=>{
                let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
                return [
                [Sequelize.literal(`IF(TagContents.name is not null,TagContents.name, (select name from ${Models.TagContent.tableName} where language_id = ${defaultlanguageId}  and tag_id = Tag.id ))`),'name'],
                [Sequelize.literal(`IF(TagContents.language_id is not null,TagContents.language_id, (select language_id from ${Models.TagContent.tableName} where language_id = ${defaultlanguageId}  and tag_id = Tag.id))`),'languageId'],
                [Sequelize.literal(`IF(TagContents.tag_id is not null,TagContents.tag_id, (select tag_id from ${Models.TagContent.tableName} where language_id = ${defaultlanguageId}  and tag_id = Tag.id))`),'tagId'],
                [Sequelize.literal(`IF(TagContents.created_at is not null,TagContents.created_at, (select created_at from ${Models.TagContent.tableName} where language_id = ${defaultlanguageId}  and tag_id = Tag.id))`),'createdAt'],
                [Sequelize.literal(`IF(TagContents.updated_at is not null,TagContents.updated_at, (select updated_at from ${Models.TagContent.tableName} where language_id = ${defaultlanguageId}  and tag_id = Tag.id))`),'updatedAt']
                ]
}