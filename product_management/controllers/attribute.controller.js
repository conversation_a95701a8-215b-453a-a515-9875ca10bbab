const {attributeContent}=require('../attributes')
module.exports={
    /**
     * Handler for attribute CRUD
     * <AUTHOR> 
     * @param {*} req 
     * @param {*} h 
     * @returns 
     */
    
    create :async(req, h) =>{
        const transaction=await Models.sequelize.transaction()
        try{
            const {id,categoryId,type,isVariation,addOns,name,groupId,isPaid} = req.payload;
            let defaultLanguage=process.env.DEFAULT_LANGUANGE_CODE_ID
            let validCategory=await Models.Category.findOne({where:{id:categoryId}})
            if(!validCategory)
            {
                await transaction.rollback()
                return h.response({success: false,message: req.i18n.__("PLEASE_PROVIDE_A_VALID_CATEGORY_ID"),responseData: {}}).code(400); 
            }
            // let validGroup=await Models.Group.findOne({where:{id:groupId}})
            // if(!validGroup)
            // {
            //     await transaction.rollback()
            //     return h.response({success: false,message: req.i18n.__("PLEASE_PROVIDE_A_VALID_GROUP_ID"),responseData: {}}).code(400); 
            // }
            if(id)
            {
                // update existing or create in differrnt lang.
                let record=await Models.Attribute.findOne({where:{id}})
                if(!record){
                    await transaction.rollback()
                    return h.response({success: false,message: req.i18n.__("CATEGORY_NOT_EXISTS"),responseData: {}}).code(400);
                }
                await record.update({type,isVariation,addOns,isPaid},{transaction})
                
                let alreadyExixt=await Models.AttributeContent.findOne({where:{attributeId:id,languageId:lang}})
                if(alreadyExixt)
                {
                   
                    // Updating Content
                    await alreadyExixt.update({name:name},{transaction})
                    await transaction.commit()
                    return h.response({success: true,message: req.i18n.__("ATTRIBUTE_SUCCESSFULLY_UPDATED"),responseData:record}).code(200)
                }
                    // Creating Content
                    let res=await Models.AttributeContent.create({name,attributeId:id,languageId:lang,isPaid},{transaction})
                    await transaction.commit();
                    return h.response({success: true,message: req.i18n.__("ATTRIBUTE_SUCCESSFULLY_ADDED"),responseData:res}).code(200)
            }
            else{
                if(lang!=defaultLanguage)
                {
                    await transaction.rollback();
                    return h.response({success: false,message: req.i18n.__("PLEASE_CREATE_ATTRIBUTE_IN_DEFAULT_LANGUAGE"),responseData: {}}).code(400);
                }
                let res=await Models.Attribute.create({
                    categoryId,
                   // groupId,
                    type,
                    isVariation,
                    addOns,
                    AttributeContents:[
                        {
                            name,
                            languageId:lang
                        }
                    ]
                },{
                transaction,include:[{model:Models.AttributeContent}]})
                await transaction.commit()
                return h.response({success: true,message: req.i18n.__("ATTRIBUTE_SUCCESSFULLY_CREATED"),responseData: res}).code(200)
            }
            
        }catch(error){
            console.error('Error in Attribute Creation',error)
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    get :async(req,h) =>{
        try{
            let defaultLanguage=process.env.DEFAULT_LANGUANGE_CODE_ID
            const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
            const offset = (req.query.pageNumber - 1) * limit
            const orderByValue = req.query.orderByValue
            const orderByParameter = req.query.orderByParameter
            let where = {},inner_where={} 
            inner_where={
                languageId:lang
            }
            replacements={}
            if(req.query.name!==null)
        {
            let name = '*'+req.query.name.replace('@','%40').replace('.','%2E')+'*';
            replacements={...replacements,"name":name}
           inner_where = { ...inner_where, [Op.or]: [
                Sequelize.literal('MATCH(`name`) AGAINST(:name IN BOOLEAN MODE)')
              ] }
        }
        if(req.query.categoryId!==null)
        {
            where={...where,categoryId:req.query.categoryId}
        }
        let _includesOption     = [{
                model: Models.AttributeContent,
                where:{
                    ...inner_where
                },
            attributes:attributeContent(),
            required:false
            }]
        let options = {
            where,
            order       : [
                    [orderByParameter, orderByValue]
                ],
            subQuery    : false,
            replacements,
            raw:true,
            nest:true,
            include     : _includesOption
        };
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                limit,
                offset

            };
        const attribute         = await Models.Attribute.findAndCountAll(options);
        const totalPages        = await Common.getTotalPages(attribute.count, limit);
        const responseData      = {
            totalPages,
            perPage: limit,
            attribute: attribute.rows,
            totalRecords: attribute.count
        };
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);

        }catch(error){
            console.error('Error in getting Attributes',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    getById :async(req, h) =>{
        try{
            let id          =   req.query.id
            let attribute    =   await Models.Attribute.findOne({where:{id},include:[
                {
                    model: Models.AttributeContent,
                    where:{languageId:lang},
                    attribute:attributeForAttributes(),
                    required:false
                }
            ],
            raw:true,
            nest:true
        })
            if(!attribute)
            {
                return h.response({success: true, message: req.i18n.__("INVALID_CATEGORY_ID"), responseData: {}}).code(400);
            }
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: attribute}).code(200);

        }catch(error){
            console.log('Error in getting Attribute by the id.',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    delete :async(req, h) =>{
        const transaction=await Models.sequelize.transaction()
        try{
           const {id}=req.query
            // find attributes in the products
            let attribute=await Models.Attribute.findOne({where:{id}})
            

            if(!attribute)
            {
                await transaction.rollback()
                return h.response({success: false, message: req.i18n.__("ATTRIBUTE_NOT_FOUND"), responseData: {}}).code(400)
            }
            let attribueInUse=await Models.ProductAttribute.findOne({where:{attributeId:id}})
            if(!attribueInUse)
            {
                await transaction.rollback()
                return h.response({success: false, message: req.i18n.__("ATTRIBUTE_IN_USED_IN_A_PRODUCT"), responseData: {}}).code(400)
            }
            await attribute.destroy({transaction})
            
            await transaction.commit()
            return h.response({success: true, message: req.i18n.__("ATTRIBUTE_DELETED_SUCCESSFULLY"), responseData:attribute}).code(200);

        }catch(error){
            console.error('Error in deleting Attributes',error)
            await transaction.rollback()
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    }
}

/**
     * Constants Functions used for attribute CRUD
     * <AUTHOR> Kushwaha 
     * @returns 
     */


// Function for handling attributes for model at time of get query
const attributeForAttributes=()=>{
    let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
    return [
    [Sequelize.literal(`if(AttributeContents.name is not null,AttributeContents.name, (select name from ${Models.AttributeContent.tableName} where language_id = ${defaultlanguageId}  and attribute_id = Attribute.id))`),'name'],
    [Sequelize.literal(`if(AttributeContents.language_id is not null ,AttributeContents.language_id, (select language_id from ${Models.AttributeContent.tableName} where ${defaultlanguageId}  and attribute_id = Attribute.id))`),'languageId'],
    [Sequelize.literal(`if(AttributeContents.attribute_id is not null,AttributeContents.attribute_id, (select attribute_id from ${Models.AttributeContent.tableName} where ${defaultlanguageId}  and attribute_id = Attribute.id))`),'attributeId'],
    [Sequelize.literal(`if(AttributeContents.created_at is not null,AttributeContents.created_at, (select created_at from ${Models.AttributeContent.tableName} where ${defaultlanguageId}  and attribute_id = Attribute.id))`),'createdAt'],
    [Sequelize.literal(`if(AttributeContents.updated_at is not null,AttributeContents.updated_at, (select updated_at from ${Models.AttributeContent.tableName} where ${defaultlanguageId}  and attribute_id = Attribute.id))`),'updatedAt']
    ]
}