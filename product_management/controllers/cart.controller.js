// const {attributeContent}=require('../attributes')
const {products,cartProductCategoryContent,producePrice,currency}=require('../attributes')

module.exports={
    /**
     * Handler for attribute CRUD
     * <AUTHOR> 
     * @param {*} req 
     * @param {*} h 
     * @returns 
     */
    
    create :async(req, h) =>{
        const transaction=await Models.sequelize.transaction()
        try{
            const  {productId,count}=req.payload
            const userId= req.auth.credentials.userData.User.id
            let validProduct=await Models.Product.findOne({where:{id:productId}})
            if(!validProduct)
            {
                await transaction.rollback()
                return h.response({success: false,message: req.i18n.__("INVALID_PRODUCT_ID"),responseData: {}}).code(400)
            }
            let alreadyInCart=await Models.CartProduct.findOne({where:{productId:productId,userId:userId}})
            if(alreadyInCart)
                await alreadyInCart.update({count:alreadyInCart.count+count},{transaction})
            else
                alreadyInCart=await Models.CartProduct.create({productId,userId,count},{transaction})

                await transaction.commit()
                let _includesOption     = [
                    {
                        model:Models.Category,as:'category',
                       //attributes:cartProductCategoryContent(),
                        include:[{model:Models.CategoryContent,
                            attributes:cartProductCategoryContent(),
                            as:'content'}]
                    },
                    {
                    model: Models.ProductContent,
                    as:'content',
                    attributes:attributes.productContent(),
                    required:false
                },
                {
                    model: Models.ProductPrice,as:'price',
                    attributes:producePrice(),
                    include:[{model:Models.Currency,attribute:currency()}]
                }]
                let includesOption=[
                    {model:Models.Product,attributes:products(),
                    include:_includesOption
                    }
                ]
                let {data,currencySymbol,cartValue}        =   await getCart(req.auth.credentials.userData.User.id)
                //let cartValue   =   await cartTotal(req.auth.credentials.userData.User.id)
                return h.response({success: true,message: req.i18n.__("ATTRIBUTE_SUCCESSFULLY_CREATED"),responseData: {records:data,cartValue,currencySymbol}}).code(200)
        }catch(error){
            console.error('Error in Add Product Into cart',error)
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    get :async(req,h) =>{
        try{
            let defaultLanguage=process.env.DEFAULT_LANGUANGE_CODE_ID
            const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
            const offset = (req.query.pageNumber - 1) * limit
            const orderByValue = req.query.orderByValue
            const orderByParameter = req.query.orderByParameter
            let where = {userId:req.auth.credentials.userData.User.id}

            let _includesOption     = [
                {
                model: Models.ProductContent,
                as:'content',
                attributes:attributes.productContent(),
                required:false
            },
            {
                model:Models.Category,as:'category',
               //attributes:cartProductCategoryContent(),
                include:[{model:Models.CategoryContent,
                    attributes:cartProductCategoryContent(),
                    as:'content'}]
            },
            {
                model: Models.ProductPrice,as:'price',
                attributes:producePrice(),
                include:[{model:Models.Currency,attribute:currency()}]
            }
        ]

            let includesOption=[
                {model:Models.Product,attributes:products(),
                include:_includesOption
                }
            ]

        let options = {
            attributes:['productId','count'],
            where,
            order       : [
                    [orderByParameter, orderByValue]
                ],
            subQuery    : false,
            include     : includesOption
        };
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                limit,
                offset
            };
        let totalValue=await cartTotal(req.auth.credentials.userData.User.id)
        
        const cartProducts         = await Models.CartProduct.findAndCountAll(options);


        const totalPages        = await Common.getTotalPages(cartProducts.count, limit);
        let currencySymbol=null
        if(cartProducts.rows.length>0)
            {
                currencySymbol=cartProducts.rows[0].Product.price[0].dataValues.Currency.dataValues.symbol
            }
        const responseData      = {
            totalPages,
            perPage: limit,
            records: cartProducts.rows,
            totalRecords: cartProducts.count,
            cartValue: totalValue * 1
        };
       
            let data =JSON.parse(JSON.stringify(cartProducts.rows))
            console.log(data)
          for (const iterator of data) {
            iterator.Product.totalValue=(iterator.Product.price[0].price)*iterator.count
          }
        responseData.records=data
        responseData.currencySymbol=currencySymbol
        
        console.log(responseData.totalRecords)
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);

        }catch(error){
            console.error('Error in getting cart',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    delete :async(req, h) =>{
        const transaction=await Models.sequelize.transaction()
        try{
           const {productId,count}=req.query
            let userId=req.auth.credentials.userData.User.id
            let product=await Models.CartProduct.findOne({where:{userId,productId}})
            if(!product)
            {
                await transaction.rollback()
                return h.response({success: false, message: req.i18n.__("PRODUCT_DOES_NOT_EXIST_IN_CART"), responseData:{}}).code(400);
            }
            let available=product.dataValues.count
            if(available==1)
                await product.destroy({transaction})
            else
            {
                await product.update({count:(available-count)},{transaction})
                if(available==count)
                {
                    await product.destroy({transaction})
                }
            }
                
            await transaction.commit()
            let {data,currencySymbol,cartValue}        =   await getCart(req.auth.credentials.userData.User.id)
            //let cartValue   =   await cartTotal(req.auth.credentials.userData.User.id)
            return h.response({success: true, message: req.i18n.__("PRODUCT_REMOVED_FROM_CART_SUCCESSFULLY"), responseData:{records:data,cartValue,currencySymbol}}).code(200);

        }catch(error){
            console.error('Error in deleting Attributes',error)
            await transaction.rollback()
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },
    getCartCount:async(req,h)=>{
        try{
            const userId= req.auth.credentials.userData.User.id
            let productInCart= await Models.CartProduct.count({where:{userId:userId}})
            return h.response({success: true, message: req.i18n.__("SUCCESSFULLY_FOUND"), responseData: {productInCart}}).code(200);
            
        }
        catch{
            console.error('Error in getting cart',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    }
}





const attributes={
    productContent:()=>{
        let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
        return [
            [Sequelize.literal(`if(\`Product->content\`.name,\`Product->content\`.name, (select name from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id)) `),'name'],
        [Sequelize.literal(`if(\`Product->content\`.description_text,\`Product->content\`.description_text, (select description_text from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id)) `),'descriptionText'],
        [Sequelize.literal(`if(\`Product->content\`.description_html,\`Product->content\`.description_html, (select description_html from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id)) `),'descriptionHtml'],
        [Sequelize.literal(`if(\`Product->content\`.long_description_text,\`Product->content\`.long_description_text, (select long_description_text from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id)) `),'longDescriptionText'],
        [Sequelize.literal(`if(\`Product->content\`.long_description_html,\`Product->content\`.long_description_html, (select long_description_html from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id)) `),'longDescriptionHtml'],
    
        ]
    }
        
    
}


const cartTotal=async(userId)=>{
    try{
        let totalValue=0
        let _includesOption     = [
            {
            model: Models.ProductContent,
            as:'content',
            attributes:attributes.productContent(),
            required:false
        },
        {
            model: Models.ProductPrice,as:'price',
            attributes:producePrice(),
            include:[{model:Models.Currency,attribute:currency(),where:{currencyCode:process.env.DEFAULT_CURRENCY_CODE}}]
        }]
        let includesOption=[
            {model:Models.Product,attributes:products(),
            include:_includesOption
            }
        ]
        let cart=await Models.CartProduct.findAll({raw:true, nest:true,where:{userId:userId},include:includesOption})
        for (const iterator of cart) {
            totalValue+=(iterator.Product.price.price*iterator.count)
        }
        return totalValue.toFixed(2)
    }
    catch(error)
    {
        console.log('Error in getting cart totel',error)
        return 0
    }
}


const getCart=async(userId)=>{
    let _includesOption     = [
        {
            model:Models.Category,as:'category',
            include:[{model:Models.CategoryContent,
                attributes:cartProductCategoryContent(),
                as:'content'}]
        },
        {
        model: Models.ProductContent,
        as:'content',
        attributes:attributes.productContent(),
        required:false
    },
    {
        model: Models.ProductPrice,as:'price',
        attributes:producePrice(),
        include:[{model:Models.Currency,attribute:currency()}]
    }]
    let includesOption=[
        {model:Models.Product,attributes:products(),
        include:_includesOption
        }
    ]
    let cart    =   await Models.CartProduct.findAll({where:{userId:userId},include:includesOption,order: [
        ['id', 'DESC']
    ]})
    let data =JSON.parse(JSON.stringify(cart))
    let cartValue=0,currencySymbol=null
          for (const iterator of data) {
            iterator.Product.totalValue=(iterator.Product.price[0].price)*iterator.count
            cartValue+=iterator.Product.totalValue
          }
          if(data.length>0)
            {
                console.log(data[0].Product.price[0].Currency.symbol)
                currencySymbol=data[0].Product.price[0].Currency.symbol
                //currencySymbol=data[0].Product.price[0].Currency.symbol
            }
    // data.currencySymbol=currencySymbol
    // data.cartValue=cartValue
    return {data,currencySymbol,cartValue}
}