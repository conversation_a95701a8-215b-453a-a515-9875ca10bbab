// const {attributeContent}=require('../attributes')
const {attributeForAttributes,productTagContent,productTag,productAttributeValue,productCategory,products,productsContent,attributeForCategory,productAttributeContents,producePrice,currency,attribute,productAttribute}=require('../attributes')

module.exports={
    /**
     * Handler for attribute CRUD
     * <AUTHOR> 
     * @param {*} req 
     * @param {*} h 
     * @returns 
     */
    
    create :async(req, h) =>{
        const transaction=await Models.sequelize.transaction()
        try{
            const  {productId}=req.payload
            const userId= req.auth.credentials.userData.User.id
            let validProduct=await Models.Product.findOne({where:{id:productId}})
            if(!validProduct)
            {
                await transaction.rollback()
                return h.response({success: false,message: req.i18n.__("INVALID_PRODUCT_ID"),responseData: {}}).code(400)
            }
            let alreadyAdded=await Models.WishList.findOne({where:{productId,userId}})
            if(alreadyAdded)
            {
                await alreadyAdded.destroy({transaction})
                await transaction.commit()
                return h.response({success: true,message: req.i18n.__("ITEM_REMOVED_IN_WISH_LIST"),responseData: {}}).code(200)
            }
            let record=await Models.WishList.create({productId,userId},{transaction})
                await transaction.commit()
                return h.response({success: true,message: req.i18n.__("ITEM_ADDED_IN_WISH_LIST"),responseData: record}).code(200)
        }catch(error){
            console.error('Error in Add Product Into cart',error)
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    get :async(req,h) =>{
        try{
            const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
            const offset = (req.query.pageNumber - 1) * limit
            const orderByValue = req.query.orderByValue
            let orderByParameter = req.query.orderByParameter
            
            let where = {},inner_where={} 
            inner_where={
                languageId:lang
            }
            replacements={}
        // if(req.query.name!==null)
        // {
        //     let name = '*'+req.query.name.replace('@','%40').replace('.','%2E')+'*';
        //     replacements={...replacements,"name":name}
        //    inner_where = { ...inner_where, [Op.or]: [
        //         Sequelize.literal('MATCH(`name`) AGAINST(:name IN BOOLEAN MODE)'),

        //       ] }
        // }
        
        let _includesOption     = [
            {
                model: Models.WishList,
                required:true,
                where:{userId:req.auth.credentials.userData.User.id}
             },
            {
                model: Models.ProductContent,
                as:'content',
                where:{
                    ...inner_where
                },
                attributes:productsContent(),
                required:false
            },
            {
                model: Models.ProductPrice,as:'price',
                attributes:producePrice(),
                include:[{model:Models.Currency,attribute:currency()}]
            },
            {
                model:Models.ProductAttribute,as:'attribute',
                attributes:productAttribute(),
                include:[
                    {
                        model:Models.Attribute,
                        attributes:attribute(),
                        include:[{
                        model:Models.AttributeContent,
                      attributes:productAttributeContents()
                    }]},
                    {
                        model:Models.ProductAttributeContent,
                         as:'content',
                        attributes:productAttributeValue()
                }]
            },
           
            {
                model:Models.Category,as:'category',
                attributes:productCategory(),
                include:[{model:Models.CategoryContent,attributes:attributeForCategory(),as:'content'}]
            },
            {
                model:Models.Tag,
                through:
                {
                   // 'prdmng_product_tags',
                    attributes:[],
                },
                attributes:productTag(),
                include:[{model:Models.TagContent,as:'content',
                attributes:productTagContent()
            }]
            }
        ]
        let options = {
            where,
            attributes:products(),
            order       : [
                    [orderByParameter, orderByValue]
                ],
            replacements,
            include     : _includesOption,
            distinct    : true
        };
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                limit,
                offset

            };
       
        const product          = await Models.Product.findAndCountAll(options);
        const totalPages        = await Common.getTotalPages(product.count, limit);
        const responseData      = {
            totalPages,
            perPage: limit,
            products: product.rows,
            totalRecords: product.count
        }
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);

        }catch(error){
            console.error('Error in getting wishlist',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    }
}





const attributes={
    productContent:()=>{
        let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
        return [
            [Sequelize.literal(`if(\`Product->content\`.name ,\`Product->content\`.name, (select name from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id)) `),'name'],
        [Sequelize.literal(`if(\`Product->content\`.description_text,\`Product->content\`.description_text, (select description_text from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id)) `),'descriptionText'],
        [Sequelize.literal(`if(\`Product->content\`.description_html,\`Product->content\`.description_html, (select description_html from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id)) `),'descriptionHtml'],
        [Sequelize.literal(`if(\`Product->content\`.long_description_text,\`Product->content\`.long_description_text, (select long_description_text from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id)) `),'longDescriptionText'],
        [Sequelize.literal(`if(\`Product->content\`.long_description_html,\`Product->content\`.long_description_html, (select long_description_html from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id)) `),'longDescriptionHtml'],
    
        ]
    }
        
    
}

