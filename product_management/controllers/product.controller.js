
const { v4: uuidv4 }                =   require('uuid');
const {updateAttachmentService}     =   require("../services/webhook")
const Striptags                     =   require('striptags')
const {DgStoreRequest}=require('../common')
const _=require('lodash')
const {attributeForAttributes,productTagContent,productTag,productAttributeValue,productCategory,products,productsContent,productsSearchContent,attributeForCategory,productAttributeContents,producePrice,currency,attribute,productAttribute}=require('../attributes');
const { Op } = require('sequelize');
const Vimeo   = require('vimeo').Vimeo;

module.exports = {
    /**
     * Handler for products CRUD
     * <AUTHOR> <PERSON>waha 
     * @param {*} req 
     * @param {*} h 
     * @returns 
     */

    createSimilarProducts: async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try {
            const productId = req.payload.id;
            const similarProducts = req.payload.similarProducts;

            const productInfo = await Models.Product.findOne({ where: { id: productId } });
            if(!productInfo) {
                await transaction.rollback()
                return h.response({success: false,message: req.i18n.__("INVALID_PRODUCT_ID_PROVIDED"),responseData: {}}).code(400)
            }

            const updatedProduct = await productInfo.update({ similarProducts }, { transaction });

            await transaction.commit();
            return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULL"),responseData: updatedProduct}).code(200)

        } catch (error) {
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    //create Product
    create:async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try{
            
           let {name,descriptionHtml,dgStoreId,longDescriptionHtml,categoryId,price,attachment,tags,dimensions,additionalInformation, productLanguages, ordering,highlights, courseId, videoLink}=req.payload
           if(productLanguages === null) productLanguages = []
            // if(lang!=process.env.DEFAULT_LANGUANGE_CODE_ID) {
            //     await transaction.rollback()
            //     return h.response({success: false,message: req.i18n.__("PLEASE_CREATE_PRODUCT_IN_DIFFERENT_LANGUAGE_FIRST"),responseData: {}}).code(400)
            // }
        
            let productName = name; 
            let uuid=uuidv4();
            let product=await saveProduct(transaction,Striptags(descriptionHtml),Striptags(longDescriptionHtml),productName,descriptionHtml,longDescriptionHtml,attachment,tags,dimensions,additionalInformation,price,categoryId,uuid,dgStoreId,ordering,highlights,courseId,videoLink)
            for(let language of productLanguages) {
                let languageInfo = await Models.Language.findOne({ where: { code: language } });
                if(!languageInfo) continue;
                await Models.ProductLanguage.create({ productId: product.dataValues.id , languageId: languageInfo.id, languageCode: language }, { transaction });
            }

            if (attachment != null) {
                let reqPayload = {
                    data: [ { id: attachment.id, status: Constants.STATUS.ACTIVE } ]
                }
                await updateAttachmentService(reqPayload, transaction);
            }
                await transaction.commit()
                return h.response({success: true,message: req.i18n.__("PRODUCTS_SUCCESSFULLY_CREATED"),responseData: products}).code(200)
            }
        catch(error)
        {
            console.error('Error in Products Creation',error)
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },
    // create:async(req,h)=>{
    //     const transaction = await Models.sequelize.transaction();
    //     try{
            
    //        let {name,descriptionHtml,dgStoreId,longDescriptionHtml,categoryId,price,attributeSet,attachment,tags,dimensions,additionalInformation, productLanguages}=req.payload
    //        if(productLanguages === null) productLanguages = []
    //         if(lang!=process.env.DEFAULT_LANGUANGE_CODE_ID)
    //         {
    //             await transaction.rollback()
    //             return h.response({success: false,message: req.i18n.__("PLEASE_CREATE_PRODUCT_IN_DIFFERENT_LANGUAGE_FIRST"),responseData: {}}).code(400)
    //         }
    //         // let attributeData=await productCreationLogic(attributeSet)
    //         let variantId=null
    //         let products=[]
    //         let res={}
    //         let defaultlanguageId=lang;
    //         let combinations= createDataFromAttributes(_.cloneDeep(attributeSet))
    //         if(!combinations.success)
    //         {
    //             await transaction.rollback()
    //             return h.response({success: false,message: req.i18n.__("ERROR_IN_CREATE_COMBINATIONS"),responseData: {}}).code(400)

    //         }

    //         // await transaction.rollback()
    //         let combinationsData=_.cloneDeep(combinations.data.finalCopies)
    //         let loopcounter=0;
    //         //console.log('combinationsData',combinationsData)
    //         for (const combine of combinationsData) {
    //             let attributeArray=[]
    //             let appendToTitle='';
    //             for (const attribute of combine) {
    //                 attributeArray.push({attributeId:attribute.id,
    //                     'contents':[
    //                         {
    //                             languageId:defaultlanguageId,
    //                             value:attribute.value
    //                         }
    
    //                     ]})
    //             }
    //             for(atvar of combinations.variationData[loopcounter]){
    //                 if(appendToTitle!='')
    //                     appendToTitle+=', '
    //                 appendToTitle+=atvar.value;
    //             }
    //             loopcounter++;
    //             let productName = name + (appendToTitle!=''?' ('+appendToTitle+')':'');
    //             let variationCode=((appendToTitle+'').trim().toLowerCase().replace(new RegExp(",", "g"),'-')).replace(new RegExp(" ", "g"),'')
    //             let combinationAttribute=_.cloneDeep(combine)
    //             let uuid=uuidv4() 
    //             let product=await saveProduct(transaction,attributeArray,Striptags(descriptionHtml),Striptags(longDescriptionHtml),productName,descriptionHtml,longDescriptionHtml,attachment,tags,dimensions,additionalInformation,price,categoryId,variantId,combinationsData,combinationAttribute,attributeSet,uuid,variationCode,dgStoreId)               
    //             if(variantId==null)
    //                 variantId=product.dataValues.id    
    //                 products.push(product)

    //             for(let language of productLanguages) {
    //                 let languageInfo = await Models.Language.findOne({ where: { code: language } });
    //                 if(!languageInfo) continue;
    //                 await Models.ProductLanguage.create({ productId: product.dataValues.id , languageId: languageInfo.id, languageCode: language }, { transaction });
    //             }

    //         }
    //        if (attachment != null) {
    //         let reqPayload = {
    //             data: [
    //                 {
    //                     id: attachment.id,
    //                     status: Constants.STATUS.ACTIVE
    //                 }
    //             ]
    //         }
    //         await updateAttachmentService(reqPayload, transaction);
    //     }
    //             await transaction.commit()
    //             return h.response({success: true,message: req.i18n.__("PRODUCTS_SUCCESSFULLY_CREATED"),responseData: products}).code(200)
    //         }
    //     catch(error)
    //     {
    //         console.error('Error in Products Creation',error)
    //         await transaction.rollback();
    //         return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
    //     }
    // },
    // Update Product 
    update:async(req,h)=>{
        const transaction=await Models.sequelize.transaction()
        try{
            let {id,dgStoreId,name,descriptionHtml,longDescriptionHtml,isPublished,attributeSet,categoryId,price,attachment,tags,dimensions,additionalInformation,productLanguages,ordering,highlights, courseId, videoLink}=req.payload
                let product=await Models.Product.findOne({where:{id}})
                if(!product)
                {
                    await transaction.rollback()
                    return h.response({success: false, message: req.i18n.__("INVALID_PRODUCT_ID"), responseData:{}}).code(400) 
                }
                await product.update({dgStoreId,categoryId,attachment,isPublished,tags,dimensions,additionalInformation, ordering,highlights, courseId, videoLink},{transaction})
                // Productcontent update if Not Available create in defferent Language---
                let productsContent=await Models.ProductContent.findOne({where:{productId:id}})
                if(productsContent)
                {
                    await productsContent.update({
                        name,
                        descriptionHtml,
                        longDescriptionHtml,
                        descriptionText:Striptags(descriptionHtml),
                        longDescriptionText:Striptags(longDescriptionHtml)
                    },{transaction})
                }else
                {
                    await Models.ProductContent.create({
                        name,
                        descriptionHtml,
                        longDescriptionHtml,
                        descriptionText         :   Striptags(descriptionHtml),
                        longDescriptionText     :   Striptags(longDescriptionHtml),
                        languageId              :   lang,
                        productId               :   id
                    },{transaction})
                }
                // //  Setting Product Attributes----
                // for (const attribute of attributeSet) {
                //         let attributeId     =   attribute.id
                //         let value           =   attribute.value
                //         let alreadyExist    =   await Models.ProductAttribute.findOne({where:{productId:id,attributeId}})
                //         if(alreadyExist)
                //         {

                //             let attributeContent    =   await Models.ProductAttributeContent.findOne({where:{productAttributeId:alreadyExist.id,languageId:lang}})
                //             if(attributeContent){
                //                 // Updating Existing Content---
                //                 await attributeContent.update({value},{transaction})
                //             }
                //             else{
                //                 // Creating New  Content
                //                 await Models.ProductAttributeContent.create({productAttributeId:alreadyExist.id,languageId:lang,value},{transaction})
                //             }
                //         }
                //         else{
                //             // Creating New Attribute With New Attribute Value----
                //             let record  =   await Models.ProductAttribute.create({productId:id,attributeId},{transaction})
                //             let content =   await Models.ProductAttributeContent.create({productAttributeId:record.dataValues.id,languageId:lang,value},{transaction})
                //         }

                // }
            // Seeting and Updating Price
            for (const p of price) {
                let currencyId      =   p.currencyId
                let price           =   p.price
                let alreadyExist=await Models.ProductPrice.findOne({where:{currencyId,productId:id}})
                if(alreadyExist){
                    await alreadyExist.update({price},{transaction})
                }
                else{
                    await Models.ProductPrice.create({productId:id,currencyId,price},{transaction})
                }
            }
            if (attachment != null) {
                let reqPayload = {
                    data: [
                        {
                            id: attachment.id,
                            status: Constants.STATUS.ACTIVE
                        }
                    ]
                }
                await updateAttachmentService(reqPayload, transaction);
            }

            await Models.ProductLanguage.destroy({ where: { productId: id }, transaction, paranoid: false });

            for(let language of productLanguages) {
                let languageInfo = await Models.Language.findOne({ where: { code: language } });
                if(!languageInfo) continue;
                let data = await Models.ProductLanguage.create({ productId: id , languageId: languageInfo.id, languageCode: language }, { transaction });
            }
            // setting tags
            // await product.setTags(tags,{transaction});
            await transaction.commit()
            return h.response({success: true, message: req.i18n.__("SUCCESSFULLY_UPDATED"), responseData:product}).code(200);
        }
        catch(error)
        {
        await transaction.rollback()
        console.log('Error While Updating A Product',error)
        return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: error.toString()}).code(500);
        }
    },
    //get Product List
    get:async(req,h)=>{
        try{
            const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
            const offset = (req.query.pageNumber - 1) * limit
            const orderByValue = req.query.orderByValue
            let orderByParameter = req.query.orderByParameter
            let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
            let where = {},inner_where={} 
            let productLangWhere = {};

            // inner_where={
            //     languageId:lang
            // }
            replacements={}

            if(req.query.categoryId) {
                where = {...where, categoryId: req.query.categoryId}
            }

            if(req.query.isPublished !== null) {
                where = {...where, isPublished: req.query.isPublished}
            }
            
            if(req.query.isFeatured !== null) {
                where = {...where, isFeatured: req.query.isFeatured}
            }

            if(req.query.language !== null) {
                productLangWhere = {...productLangWhere, languageCode: req.query.language}
            }

            let contentRequired = false
            let productContentAttributes = productsContent()
        if(req.query.name!==null)
        {
            let name = '*'+req.query.name.replace('@','%40').replace('.','%2E')+'*';
            replacements={...replacements,"name":req.query.name}
            // replacements={...replacements,"name":name}
            inner_where = { ...inner_where, name: {[Op.like]: `%${req.query.name}%`}
                // [
                //     // Sequelize.literal('MATCH(`name`) AGAINST(:name IN BOOLEAN MODE)'),
                //     Sequelize.literal("`content`.`name` like '%" + req.query.name + "%'"),
                // ]
            }
            contentRequired = true;
            productContentAttributes = productsSearchContent()
        }
        
        let _includesOption     = [{
                model: Models.ProductContent,
                as:'content',
                where:{
                    ...inner_where
                },
                // attributes:productContentAttributes,
                required:contentRequired
            },
            {
                model: Models.ProductLanguage,as:'productLanguages'
            },
            {
                model: Models.ProductLanguage,
                where: productLangWhere, attributes:[]
            },
            {
                model: Models.ProductPrice,as:'price',
                attributes:producePrice(),
                include:[{model:Models.Currency,attribute:currency()}]
            },
            // {
            //     model:Models.ProductAttribute,as:'attribute',
            //     attributes:productAttribute(),
            //     include:[
            //         {
            //             model:Models.Attribute,
            //             attributes:attribute(),
            //             include:[{
            //             model:Models.AttributeContent,
            //           attributes:productAttributeContents()
            //         }]},
            //         {
            //             model:Models.ProductAttributeContent,
            //              as:'content',
            //             attributes:productAttributeValue()
            //     }]
            // },
           
            {
                model:Models.Category,as:'category',
                attributes:productCategory(),
                include:[{model:Models.CategoryContent,attributes:attributeForCategory(),as:'content'}]
            },
        //     {
        //         model:Models.Tag,
        //         through:
        //         {
        //            // 'prdmng_product_tags',
        //             attributes:[],
        //         },
        //         attributes:productTag(),
        //         include:[{model:Models.TagContent,as:'content',
        //         attributes:productTagContent()
        //     }
        // ]
        //     }
        ]
        let options = {
            where,
            attributes:products(),
            order       : [
                    [orderByParameter, orderByValue]
                ],
            replacements,
            include     : _includesOption,
            distinct    : true
        };
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                limit,
                offset

            };
       
        const product          = await Models.Product.findAndCountAll(options);
        const totalPages        = await Common.getTotalPages(product.count, limit);
   
        const responseData      = {
            totalPages,
            perPage: limit,
            products: product.rows,
            totalRecords: product.count
        };
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
        }
        catch(error)
        {
            console.error('Error in getting products',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },
    //get product By Id
    getById:async(req,h)=>{
        try{
            let id=req.query.id
            let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
            let  where={
                //languageId:lang
            }
            let _includesOption     = [{
                model: Models.ProductContent,
                as:'content',
                where,
                attributes:productsContent(),
                required:false
            },
            {
                model: Models.ProductLanguage,as:'productLanguages'
            },
            {
                model: Models.ProductPrice,as:'price',
                attributes:producePrice(),
                include:[{model:Models.Currency,attribute:currency()}]
            },
            {
                model:Models.ProductAttribute,as:'attribute',
                attributes:productAttribute(),
              
                include:[
                    {
                        model:Models.Attribute,
                        attributes:attribute(),
                        required:false,
                        include:[{
                        model:Models.AttributeContent,
                        // where,
                        required:false,
                      attributes:productAttributeContents()
                    }]},
                    {
                        model:Models.ProductAttributeContent,
                        required:false,
                        as:'content',
                        where,
                        attributes:productAttributeValue()

                }]
            },
           
            {
                model:Models.Category,as:'category',
                attributes:productCategory(),
                required:false,
                include:[{model:Models.CategoryContent,required:false,  where,as:'content',attributes:attributeForCategory()}]
            },
            {
                model:Models.Tag,
                through:
                {
                    attributes:[],
                },
                attributes:productTag(),
                include:[{model:Models.TagContent,as:'content',  where,
                attributes:productTagContent()
            }]
            }
        ]
            let product= await Models.Product.findOne(
                {where:{id},include:_includesOption}
            )
            if(!product)
            {
                //await transaction.rollback()
                return  h.response({success: false, message: req.i18n.__("INVALID_PRODUCT_ID"), responseData: {}}).code(400)
            }
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: product}).code(200);
        }
        catch(error)
        {
            console.error('Error in getting Product by id',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    //remove a single Product
    delete:async(req,h)=>{
        const transaction = await Models.sequelize.transaction()
        try{
            let id=req.query.id
            let product=await Models.Product.findOne({where:{id:id}})
            if(!product)
            {
                await transaction.rollback()
                return h.response({success: true, message: req.i18n.__("INVALID_PRODUCT_ID"), responseData: {}}).code(400);
            }
            await product.destroy({transaction})
            await Models.CartProduct.destroy({ where: { productId: id }, transaction })
            await transaction.commit()
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: product}).code(200);
        }
        catch(error)
        {   
            await transaction.rollback()
            console.error('Error in destroying product',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500)
        }
    },

    // product publish & unpublish
    publish:async(req,h)=>{
        const transaction=await Models.sequelize.transaction()
        try{
            let {id,isPublished}=req.payload
            let product=await Models.Product.findOne({where:{id:id}})
            if(!product)
            {
                await transaction.rollback()
                return h.response({success: false, message: req.i18n.__("PRODUCT_NOT_FOUND"), responseData: {}}).code(400)
            }
            let dgStoreId=product.dataValues.dgStoreId
            if(dgStoreId==null && isPublished==1)
            {
                await transaction.rollback()
                return h.response({success: false, message: req.i18n.__("DG_STORE_ID_IS_REQUIRED"), responseData: {}}).code(400)
            }
            await product.update({isPublished},{transaction})
            await transaction.commit()
            return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: product}).code(200);
        }
        catch(error)
        {
            await transaction.rollback()
            console.error('Error in publishing a product',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500)
        }
    },
    // product publish & unpublish
    featured:async(req,h)=>{
        const transaction=await Models.sequelize.transaction()
        try{
            let {id,isFeatured}=req.payload
            let product=await Models.Product.findOne({where:{id:id}})
            if(!product) {
                await transaction.rollback()
                return h.response({success: false, message: req.i18n.__("PRODUCT_NOT_FOUND"), responseData: {}}).code(400)
            }

            await product.update({isFeatured},{transaction})
            await transaction.commit()
            return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: product}).code(200);
        }
        catch(error)
        {
            await transaction.rollback()
            console.error('Error in publishing a product',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500)
        }
    },

    // get List 
    getDetails:async(req,h)=>{
        try{
            let {id}    =   req.query;
            let product =   await Models.Product.findOne({where:{id}})
            if(!product)
            {
                return  h.response({success: false, message: req.i18n.__("INVALID_PRODUCT_ID"), responseData: {}}).code(400)
            }
            let variationAttributes=[]
            let normalAttributes=[]
            let possibleDataSet=[]
            let variantId=product.dataValues.variantId
        // Setting up possibe dataSet:-----

        if(product.dataValues.inputAttribute && product.dataValues.inputAttribute.length > 0) {
            for (const iterator of product.dataValues.inputAttribute) {
           
                let temp ={
                    id:iterator.id,
                    value:[]
                }
                for (const i of iterator.data) {
                   temp.value.push(i.value)
                }
                possibleDataSet.push(temp)
              }
        }

          // Include Options In Products:------
          let _includesOption     = [{
            model: Models.ProductContent,
            as:'content',
            attributes:productsContent(),
            required:false
        },
        {
            model: Models.ProductPrice,as:'price',
            attributes:producePrice(),
            include:[{model:Models.Currency,attribute:currency()}]
        },
        {
            model:Models.ProductAttribute,as:'attribute',
            attributes:productAttribute(),
            include:[
                {
                    model:Models.Attribute,
                    attributes:attribute(),
                   
                    include:[{
                    model:Models.AttributeContent,
                   
                  attributes:productAttributeContents()
                }]},
                {
                    model:Models.ProductAttributeContent,
                    as:'content',
                    attributes:productAttributeValue()
            }]
        },
       
        {
            model:Models.Category,as:'category',
            attributes:productCategory(),
            include:[{model:Models.CategoryContent,as:'content',attributes:attributeForCategory()}]
        },
        {
            model:Models.Tag,
            through:
            {
                attributes:[],
            },
            attributes:productTag(),
            include:[{model:Models.TagContent,as:'content',
            attributes:productTagContent()
        }]
        }
    ]
            let inputCombinations       =   product.dataValues.inputAttribute
            let allProducts             =   []
            let dataSendToFrountEnd=[]
           
        // Setting Up Combinations for send to frount-end:------

        if(inputCombinations && inputCombinations > 0) {
            for (const combination of inputCombinations) {
                let temp=combination.data
                if(combination.data.length==1)
                {
                    let attribute    =   await Models.Attribute.findOne({where:{id:combination.id},include:[
                        {
                            model: Models.AttributeContent,
                            where:{languageId:lang},
                            attribute:attributeForAttributes(),
                            required:false
                        }
                    ],
                    raw:true,
                    nest:true
                })
                // console.log('attribute',attribute)
                    normalAttributes.push({
                        "id":combination.id,
                        "name":attribute.AttributeContents.name,
                        "value":[
                            combination.data[0].value
                        ]
                    })
                }
                else
                {
                    let attribute    =   await Models.Attribute.findOne({where:{id:combination.id},include:[
                        {
                            model: Models.AttributeContent,
                            where:{languageId:lang},
                            attribute:attributeForAttributes(),
                            required:false
                        }
                    ],
                    raw:true,
                    nest:true
                })
                
                    let obj={
                        "id":combination.id,
                        name:attribute.AttributeContents.name,
                        value:[]
                    }
                for(let i=0;i<temp.length;i++)
                {
                    obj.value.push(temp[i].value)
                }
                variationAttributes.push(obj)
            }
             } 
        }


         let settelData=[]
         for (const i of variationAttributes) {
            // console.log('i',i)
                settelData.push(i.value)
        }
        let possibleCombinations=combine(settelData)
 
        let matrix=[]
        for (let i=0;i<settelData.length;i++) {
            let d=settelData[i];matrix[i]=[]
            for(let j=0;j<d.length;j++)
            {
                matrix[i].push(j)
            }
        }
        let possibeCombinationIndex=combine(matrix)
        for(let i=0;i<possibleCombinations.length;i++)
        {
            
            let comb=(possibleCombinations[i].toString().trim().toLowerCase().replace(new RegExp(",", "g"),'-')).replace(new RegExp(" ", "g"),'')
            let product =await Models.Product.findOne({where:{variantId:variantId,variationCode:comb,isPublished:1}})
            if(product)
                {
                    dataSendToFrountEnd.push(
                        {
                            possibleCombination:possibleCombinations[i],
                            index:possibeCombinationIndex[i],
                            comb:comb,
                            productId:product.dataValues.id
                        }
                    )
                }
                else{
                    console.log('Product Not Found For',comb)
                } 
        }
        //      allProducts=await Models.Product.findAll(
        //     {
        //         where:{variantId:variantId,isPublished:1},
        //         attributes:['id','uuid','slug','variantId','variationCode','dimensions','additionalInformation','attachment','isPublished'],
        //         include:_includesOption
        // }
        //    )
         
let mainProduct=await Models.Product.findOne({where:{id:id},include:_includesOption, 
   // attributes:['dgStoreId','id','uuid','slug','variantId','variationCode','dimensions','additionalInformation','attachment','isPublished','highlights']
})
        let alreadyInwishList=null
        if(req.query.userId!==null)
        {
            let inWishList= await Models.WishList.findOne({where:{productId:id,userId:req.query.userId}})
            if(inWishList)
            {
                alreadyInwishList=1
            }
            else{
                alreadyInwishList=0
            }
        }
         // Return Data to Frount End:-
            responseData={
                normalAttributes,
                variationAttributes,
                allProducts:mainProduct,
                dataSendToFrontEnd:dataSendToFrountEnd,
                alreadyInwishList
            }

            return h.response({success: true, message: req.i18n.__("PRODUCT_FOUND_SUCCESSFULLY"), responseData}).code(200)

        }
        catch(error)
        {
            console.log('Error in Getting Product',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500)
        }
    },

    getList:async(req,h)=>{
        try{
            const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
            const offset = (req.query.pageNumber - 1) * limit
            const orderByValue = req.query.orderByValue
            let orderByParameter = req.query.orderByParameter
            let where = {isPublished:1},inner_where={} 
            console.log(req.query)
            let wishListWhere={}
            wishListWhere={userId:req.query.userId}
            // inner_where={ languageId:lang }
            inner_where={}
            replacements={}

            // const languageInfo = await Models.Language.findOne({ where: { id: lang } });
            // let languageCode = languageInfo?.code
            
            // if(req.query.name!==null) {
            //     let name = '*'+req.query.name.replace('@','%40').replace('.','%2E')+'*';
            //     replacements={...replacements,"name":name}
            //     inner_where = { ...inner_where, [Op.or]: [ Sequelize.literal('MATCH(`name`) AGAINST(:name IN BOOLEAN MODE)')] }
            // }

            let productContentAttributes = productsContent();
            let productRequired = false
            if(req.query.name!==null)
            {
                let name = '*'+req.query.name.replace('@','%40').replace('.','%2E')+'*';
                replacements={...replacements,
                //    "name":req.query.name
                }
                // replacements={...replacements,"name":name}
                inner_where = { ...inner_where, [Op.or]: [
                    // Sequelize.literal('MATCH(`name`) AGAINST(:name IN BOOLEAN MODE)'),
                    Sequelize.literal("`content`.`name` like '%" + req.query.name + "%'")
                    
                ] }
                productRequired = true;
                productContentAttributes = productsSearchContent()
                // where = {...where, [Op.or]:[Sequelize.literal("`content`.`name` like '%" + req.query.name + "%'")] }
            }



            if(req.query.categoryId!==null) {
                where={...where,categoryId:req.query.categoryId}
            }
            let _includesOption = [
                {
                    model: Models.WishList,where:wishListWhere,
                    required:false
                },
                {
                    model: Models.ProductLanguage,
                    where: {languageId: lang},
                    as: "productLanguages",
                    required:true
                },
                {
                    model: Models.ProductContent,
                    as:'content',
                    where:{ ...inner_where },
                    attributes:productContentAttributes,
                    required:productRequired
                },
                {
                    model: Models.ProductPrice,as:'price',
                    attributes:producePrice(),
                    include:[{model:Models.Currency,attribute:currency()}]
                },
                // {
                //     model:Models.ProductAttribute,as:'attribute',
                //     attributes:productAttribute(),
                //     include:[
                //         {
                //             model:Models.Attribute, attributes:attribute(),
                //             include:[{ model:Models.AttributeContent, attributes:productAttributeContents()}]
                //         },
                //         {
                //             model:Models.ProductAttributeContent, as:'content',
                //             attributes:productAttributeValue()
                //         }
                //     ]
                // },
                {
                    model:Models.Category,as:'category',
                    attributes:productCategory(),
                    include:[
                        {model:Models.CategoryContent,attributes:attributeForCategory(),as:'content'}
                    ]
                },
                // {
                //     model:Models.Tag,
                //     // where: { code: languageCode },
                //     // required: true,
                //     through:{ attributes:[] },
                //     attributes:productTag(),
                //     include:[{model:Models.TagContent,as:'content', attributes:productTagContent()}]
                // }
            ]
      
            let order=[]
            if (orderByParameter=='price') { order=[["price","price",orderByValue]] }
            else if (orderByParameter=='id') { order=[["id",orderByValue]] }
            else if(orderByParameter=='createdAt') { order=[["createdAt",orderByValue]] }

            if(req.query.isFeatured === 1) {
                where = {...where, isFeatured: 1},
                order = Sequelize.literal("rand()");
            }

            let options = {
                where, attributes: products(), distinct: true,
                // subQuery: false,
                order, replacements, include: _includesOption,
            };
        
            if (req.query.pageNumber !== null)  options = { ... options, limit, offset}
    
            const product = await Models.Product.findAndCountAll(options);
            const totalPages = await Common.getTotalPages(product.count, limit);
            let loadMore=false
            if(totalPages>req.query.pageNumber) {
                loadMore=true
            }
            const responseData      = {
                totalPages, perPage: limit, products: product.rows,
                totalRecords: product.count, loadMore
            };
            return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
        }
        catch(error) {
            console.error('Error in getting products',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },
    similarProducts:async(req,h)=>{
        try{
            
            const productId = req.query.id;

            const productInfo = await Models.Product.findOne({ where: { id: productId } });
            if(!productInfo) {
                console.log(" =================== 11111")
                console.log(" =================== 11111")
                console.log(" =================== 11111")
                return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: []}).code(200);
            }

            const similarProductsList = productInfo.similarProducts;
            if(!similarProductsList) {
                console.log(" =================== 22222")
                console.log(" =================== 22222")
                console.log(" =================== 22222")
                return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: []}).code(200);
            }

            if(similarProductsList.length < 1) {
                console.log(" =================== 33333")
                console.log(" =================== 33333")
                console.log(" =================== 33333")
                return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: []}).code(200);
            }

            console.log(" =================== 44444")
            console.log(" =================== 44444")
            console.log(" =================== 44444")
            
            let where = {id: similarProductsList},inner_where={} 

            if(req.query.isPublished === 1) {
                where = { ...where, isPublished:1 }
            }
          
            let wishListWhere={}
            wishListWhere={userId:req.query.userId}
            inner_where={ languageId:lang }
            replacements={}
           
            let _includesOption = [
                {
                    model: Models.WishList,where:wishListWhere,
                    required:false
                },
                {
                    model: Models.ProductLanguage,
                    where: {languageId: lang},
                    as: "productLanguages",
                    required:false
                },
                {
                    model: Models.ProductContent,
                    as:'content',
                    where:{ ...inner_where },
                    attributes:productsContent(),
                    required:false
                },
                {
                    model: Models.ProductPrice,as:'price',
                    attributes:producePrice(),
                    include:[{model:Models.Currency,attribute:currency()}]
                },
                // {
                //     model:Models.ProductAttribute,as:'attribute',
                //     attributes:productAttribute(),
                //     include:[
                //         {
                //             model:Models.Attribute, attributes:attribute(),
                //             include:[{ model:Models.AttributeContent, attributes:productAttributeContents()}]
                //         },
                //         {
                //             model:Models.ProductAttributeContent, as:'content',
                //             attributes:productAttributeValue()
                //         }
                //     ]
                // },
                {
                    model:Models.Category,as:'category',
                    attributes:productCategory(),
                    include:[{model:Models.CategoryContent,attributes:attributeForCategory(),as:'content'}]
                },
                // {
                //     model:Models.Tag,
                //     through:{ attributes:[] },
                //     attributes:productTag(),
                //     include:[{model:Models.TagContent,as:'content', attributes:productTagContent()}]
                // }
            ];

            let options = {
                where, attributes: products(), distinct: true,
                order: Sequelize.literal("rand()"), replacements, include: _includesOption,
            };
        
            const product = await Models.Product.findAll(options);
            
            return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: product}).code(200);
        }
        catch(error) {
            console.error('Error in getting products',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },
    // similarProducts:async(req,h)=>{
    //     try{
    //         const id= req.query.id
    //         const userId=req.query.userId
    //         let tagProduct =await Models.Product.findOne({where:{id:id}})
    //         if(!tagProduct){
    //         return h.response({success: false, message: req.i18n.__("PRODUCT_NOT_FOUND"), responseData: {}}).code(400);
    //         }
    //         let tags= await tagProduct.getTags({raw:true,nest:true,attributes:['id'],joinTableAttributes:[]})
    //         tags=tags.map((e,i)=>{
    //             return e.id;
    //         })
    //         const limit = 10
    //         const order = Sequelize.literal('rand()')
    //         let where = {isPublished:1,id:{[Op.notIn]:[id]}},inner_where={} 
    //         let wishListWhere={}
    //             wishListWhere={userId:userId}
    //         inner_where={
    //             languageId:lang
    //         }
    //     let _includesOption     = [
    //         {
    //            model: Models.WishList,where:wishListWhere,
    //            required:false
    //         },
    //         {
    //             model: Models.ProductContent,
    //             as:'content',
    //             where:{
    //                 ...inner_where
    //             },
    //             attributes:productsContent(),
    //             required:false
    //         },
    //         {
    //             model: Models.ProductPrice,as:'price',
    //             attributes:producePrice(),
    //             required:false,
    //             include:[{model:Models.Currency,attribute:currency()}]
    //         },
    //         {
    //             model:Models.ProductAttribute,as:'attribute',
    //             attributes:productAttribute(),
    //             required:false,
    //             include:[
    //                 {
    //                     model:Models.Attribute,
    //                     attributes:attribute(),
    //                     required:false,
    //                     include:[{
    //                     model:Models.AttributeContent,
    //                     required:false,
    //                   attributes:productAttributeContents()
    //                 }]},
    //                 {
    //                     model:Models.ProductAttributeContent,
    //                     required:false,
    //                      as:'content',
    //                     attributes:productAttributeValue()
    //             }]
    //         },
           
    //         {
    //             model:Models.Category,as:'category',
    //             attributes:productCategory(),
    //             required:false,
    //             include:[{model:Models.CategoryContent,required:false,attributes:attributeForCategory(),as:'content'}]
    //         },
    //         {
    //             model:Models.Tag,
    //             where:{id:{[Op.in]:[...tags]}},
    //             through:
    //             {
               
    //                 attributes:[],
    //             },
    //             attributes:productTag(),
    //             include:[{model:Models.TagContent,as:'content',
    //             attributes:productTagContent()
    //         }]
    //         }
    //     ]
      
       
    //     let options = {
    //         where,
    //         attributes:products(),
    //         distinct    : true,
    //         order,
    //         include     : _includesOption,

    //     };
        
    //     if (req.query.pageNumber !== null) 
    //         options = {
    //             ... options,
    //             limit,
                

    //         }
    
    //  const product          = await Models.Product.findAndCountAll(options);

    //     const responseData      = {
    //         //totalPages,
    //         perPage: limit,
    //         products: product.rows,
    //         totalRecords: product.count
    //     };
    //     return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
    //     }
    //     catch(error){
    //         console.error('Error in getting products',error)
    //         return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
    //     }
    // },

    createProductLink: async(req, h) => {
        const transaction = await Models.sequelize.transaction();
        try {
            const productId = req.payload.productId;
            let productLinks = req.payload.productInfo;
    
            if(productLinks === null) productLinks = [];
    
            const productInfo = await Models.Product.findOne({ where: { id: productId } });
            if(!productInfo) {
                await transaction.rollback()
                return h.response({success: false,message: req.i18n.__("INVALID_PRODUCT_ID_PROVIDED"),responseData: {}}).code(400)
            }
    
            await Models.ProductLink.destroy({ where: { productId }, transaction })
    
            let bulkCreateArray = []
            for(let item of productLinks) {
                let generatedLink = item?.url;
                if(item?.url && item.type === "video") {
                    generatedLink = await getAuthorizedVideo(item?.url);
                    console.log(generatedLink, "  == generatedLink returned by function")
                }
                bulkCreateArray.push({ productId, type: item.type, url: generatedLink });
            }
    
            let createdLinks = []
            if(bulkCreateArray.length > 0) {
                createdLinks = await Models.ProductLink.bulkCreate(bulkCreateArray, { transaction });
            }
    
            await transaction.commit()
            return h.response({success: true, message: req.i18n.__("SUCCESSFULLY_UPDATED"), responseData:createdLinks}).code(200);
        } catch (error) {
            console.error(error)
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },
    
    getProductLinks: async(req, h) => {
        try {
            const authId = req.auth.credentials.userData.User.id;
            const productId = req.query.productId;

            if(!req.auth.credentials.userData.Role.includes('admin')) {
                const hasAccess = await Models.UserProduct.findOne({ where: { productId, userId: authId } });
                if(!hasAccess) {
                    await transaction.rollback()
                    return h.response({success: false,message: req.i18n.__("PLEASE_SUSCRIBE_TO_THE_SERVICE"),responseData: {}}).code(400)
                }
            }
    
            const productLinks = await Models.ProductLink.findAll({ where: { productId: productId } });
    
            return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFULL"), responseData:productLinks}).code(200);
        } catch (error) {
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    }
,

    // DG Store Apis :_
    getDgstoreList:async(req,h)=>{
        try{
            // let list =await 
            // let res=await axios({
            //     method: 'get',
            //     baseURL:'https://www.digistore24.com/api/call/',
            //     url:"listProducts/?sort_by=name",
            //     headers:{
            //         'X-DS-API-KEY': "818169-M5ChmCeRQFfIz6UfcK7SHAhrUjzVVFPVdcMgGICN"
            //     },
            //   });
            
            // // console.log(res?.data)
            // return h.response({success: true, message: req.i18n.__("SUCCESSFULLY"), responseData:res.data}).code(200)
            let list =await DgStoreRequest('listProducts/?sort_by=name')
            if(list.success)
                return h.response({success: true, message: req.i18n.__("SUCCESSFULLY"), responseData:list.data}).code(200)
            else
            throw list.error
        }
        catch(error)
        {
            console.error('Error in getting products',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData:error}).code(500);
        }
    },

    getDgstoreById:async(req,h)=>{
        try{
            let res =await DgStoreRequest(`getProduct/?product_id=${req.query.id}`)
            if(res.success)
                return h.response({success: true, message: req.i18n.__("SUCCESSFULLY"), responseData:res.data}).code(200)
            else
            throw res.error
            //let res=await axios({
            //     method: 'get',
            //     baseURL:'https://www.digistore24.com/api/call/',
            //     url:"listProducts/?sort_by=name",
            //     headers:{
            //         'X-DS-API-KEY': "818169-M5ChmCeRQFfIz6UfcK7SHAhrUjzVVFPVdcMgGICN"
            //     },
            //   });
            
            // console.log(res?.data)
          
        }
        catch(error)
        {
            console.error('Error in getting products',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData:error.toString}).code(500);
        }
    },


    getPrductById:async(id)=>{
        try{
            let _includesOption     = [{
                model: Models.ProductContent,
                as:'content',
                attributes:productsContent(),
                required:false
            },
            {
                model: Models.ProductPrice,as:'price',
                attributes:producePrice(),
                required:false,
                include:[{model:Models.Currency,attribute:currency()}]
            },
            {
                model:Models.ProductAttribute,as:'attribute',
                attributes:productAttribute(),
                required:false,
                include:[
                    {
                        model:Models.Attribute,
                        attributes:attribute(),
                        required:false,
                        include:[{
                        model:Models.AttributeContent,
                       
                      attributes:productAttributeContents()
                    }]},
                    {
                        model:Models.ProductAttributeContent,
                        as:'content',
                        required:false,
                        attributes:productAttributeValue()
                }]
            },
           
            {
                model:Models.Category,as:'category',
                attributes:productCategory(),
                required:false,
                include:[{model:Models.CategoryContent,required:false,as:'content',attributes:attributeForCategory()}]
            },
            {
                model:Models.Tag,
                through:
                {
                    attributes:[],
                },
                attributes:productTag(),
                include:[{model:Models.TagContent,as:'content',
                attributes:productTagContent()
            }]
            }
        ]
            let product=await Models.Product.findOne({where:{dgStoreId:id},include:_includesOption, attributes:['dgStoreId','id','uuid','slug','variantId','variationCode','dimensions','additionalInformation','attachment','isPublished','courseId']})
            if(!product)
            {
                return {}
            }
            let returnData  = JSON.parse(JSON.stringify(product))
           return returnData
        }
        catch(error)
        {
            console.log('error',error)
            throw error.toString()
        }  
    },

}


/**
     * Functions used  for product CRUD
     * <AUTHOR> Kushwaha 
     */


// function to save Products in database----
const saveProduct   =   async (transaction,
    descriptionText,
    longDescriptionText,
    name,
    descriptionHtml,
    longDescriptionHtml,
    attachment,
    tags,
    dimensions,
    additionalInformation,
    price,
    categoryId,
    uuid,
    dgStoreId, ordering, highlights, courseId, videoLink)=>{
    try{
        const languageId    =   lang
        // const 
        const product       =   await Models.Product.create(
            {
                dimensions,
                additionalInformation,
                isPublished:0,
                slug:uuid,
                uuid:uuid,
                categoryId,
                attachment,
                dgStoreId,
                ordering,
                highlights,
                courseId,
                videoLink,
                contents:[
                    {
                        languageId,
                        name,
                        descriptionText,
                        descriptionHtml,
                        longDescriptionText,
                        longDescriptionHtml,
                    }
                ],
                price:[
                    ...price
                ]
            },
            {
                include:[
                    {model:Models.ProductContent,as:'contents'},
                    {model:Models.ProductPrice,as:'price'}
                ],
                transaction
            }
        )
      
        // await product.setTags(tags,{transaction:transaction})
        return product
    }
    catch(error){
        console.log('Error in saving Product',error)
        return {success:false,error:error}
    }
}
// const saveProduct   =   async (transaction,attribute,descriptionText,longDescriptionText,name,descriptionHtml,longDescriptionHtml,attachment,tags,dimensions,additionalInformation,price,categoryId,variantId,combinationsData,combinationAttribute,inputAttribute,uuid,variationCode,dgStoreId)=>{
//     try{
//         const languageId    =   lang
//         // const 
//         const product       =   await Models.Product.create(
//             {
//                 dimensions,
//                 additionalInformation,
//                 isPublished:0,
//                 variantId,
//                 variationCode:variationCode,
//                 slug:uuid,
//                 uuid:uuid,
//                 categoryId,
//                 attachment,
//                 dgStoreId,
//                 combinationAttribute,
//                 inputAttribute,
//                 contents:[
//                     {
//                         languageId,
//                         name,
//                         descriptionText,
//                         descriptionHtml,
//                         longDescriptionText,
//                         longDescriptionHtml,
//                     }
//                 ],
//                 price:[
//                     ...price
//                 ],
//                 attribute:[
//                     ...attribute
//                 ]
//             },
//             {
//                 include:[
//                     {model:Models.ProductContent,as:'contents'},
//                     {model:Models.ProductPrice,as:'price'},
//                     {model:Models.ProductAttribute, as:'attribute'
//                         ,include:[{model:Models.ProductAttributeContent,as:'contents'}]}
//                 ],
//                 transaction
//             }
//         )
       
//         if(variantId==null)
//         {
//             await product.update({variantId:product.dataValues.id},{transaction})
//         }
      
//         await product.setTags(tags,{transaction:transaction})
//         return product
//     }
//     catch(error){
//         console.log('Error in saving Product',error)
//         return {success:false,error:error}
//     }
// }

const createCopies=(array,count)=>{
    let copies=[]
    for(i=0;i<count;i++)copies.push(array)
    return copies
    }
function combine(arrays) { let result = []; let temp = []; function recurse(arr, i) { if (i === arrays.length) { result.push(temp.slice()); } else { for (let j = 0; j < arrays[i].length; j++) { temp[i] = arrays[i][j]; recurse(arrays, i + 1); } } } recurse(arrays, 0); return result; }
    
const createCombinations=(input)=>{
      let response=combine(input)
      return response
    }
    
const combineArrays=(arr1,arr2)=>{return [...arr1,...arr2]}
    
const createDataFromAttributes=(attributeSet)=>{
        try{
            let normalAttributes=[],variationAttributes=[]
            let meximumProducts=1
            let maxAttributeLength=0
            for(i=0;i<attributeSet.length;i++)
            {
              let attribute=attributeSet[i]
              if(attribute.data.length>=1 && maxAttributeLength<attribute.data.length)
              {
                maxAttributeLength=attribute.data.length
              }
              if(attribute.data.length>=2)
              {
                variationAttributes.push(attribute)
                meximumProducts*=attribute.data.length
              }
              else{
                normalAttributes.push(attribute)
              }
            }
            let possibleAttribute=[]
            for (const iterator of normalAttributes) {
              possibleAttribute.push(iterator.data[0])
            }
            let finalCopies=createCopies(possibleAttribute,meximumProducts)
            let combinationInput=[]
            for (let i=0;i<variationAttributes.length;i++) {
              combinationInput.push(variationAttributes[i].data)
            }
            let combinations=createCombinations(combinationInput,meximumProducts)
            for (let i=0;i<combinations.length;i++) {    
                finalCopies[i]=_.cloneDeep(combineArrays(combinations[i],finalCopies[i]))
            }
            return {success:true,data:{finalCopies},variationData:combinations}
        }
        catch(error)
        {
            console.log('Error in Formate Data For Products',error)
            return {success:false,error:error}
        }
    }
    
const uniqueArray=(array,key)=>{
    return  [...new Map(array.map(item =>
        [item[key], item])).values()];
}
   

const getAuthorizedVideo = async(payload) => {

    try {
            let videoUrl = payload

            const clientId     = process.env.CLIENT_IDENTIFIER;
            const clientSecret = process.env.CLIENT_SECRET;
            const accessToken  = process.env.TOKEN;
    
            let client = new Vimeo(clientId, clientSecret, accessToken);
            let link = null
                const url = await new Promise((resolve, reject) => {
                    client.request({
                        method : 'GET', path   : '/videos',
                        query: { links: videoUrl },
                    }, async function (error, body, status_code, headers) {
                        if (error) {
                            console.log("IN ERROR BLOCK", error);
                            reject(null)
                        }
                        else {
                            const vimeoUrl = (body.data[0].player_embed_url).split("?")[0];
                            const vimeoId  = vimeoUrl.split("/").at(-1);

                            client.request({
                                method : 'put',
                                path   : `/videos/${vimeoId}/privacy/domains/${process.env.VIMEO_WHITELIST_DOMAIN}`,
                            }, async function (error, body, status_code, headers) {
                                if (error) {
                                    console.log("White List domain Error", error);
                                }
                                else {
                                    console.log("White listed successfully");
                                }
                            });
                            link = body.data[0].player_embed_url;
                            resolve(body.data[0].player_embed_url)
                        }
                    })
                })
            
        return link;
    } catch (error) {
        return false;
    }
}


      