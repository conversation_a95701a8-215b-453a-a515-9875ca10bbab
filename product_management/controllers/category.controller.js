
const {updateAttachmentService}=require("../services/webhook")
module.exports = {
    /**
     * Handler for category CRUD
     * <AUTHOR> 
     * @param {*} req 
     * @param {*} h 
     * @returns 
     */

    //create products
    create  :   async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try{
            const {parentId,id,name,attachment, ordering}=req.payload
            let defaultLanguage=process.env.DEFAULT_LANGUANGE_CODE_ID
            let code=name.trim().toLowerCase().replace(new RegExp(" ", "g"),'-')
            if(id)
            {
                // update existing or create in differrnt lang.
                let record=await Models.Category.findOne({where:{id}})
                if(!record){
                    await transaction.rollback()
                    return h.response({success: false,message: req.i18n.__("CATEGORY_NOT_EXISTS"),responseData: {}}).code(400);
                }
                await record.update({attachment,parentId, ordering},{transaction})
                if (attachment != null) {
                    let reqPayload = {
                        data: [
                            {
                                id: attachment.id,
                                status: Constants.STATUS.ACTIVE
                            }
                        ]
                    }
                    await updateAttachmentService(reqPayload, transaction);
                }
                let alreadyExixt=await Models.CategoryContent.findOne({where:{categoryId:id,languageId:lang}})
                if(alreadyExixt)
                {
                   
                    // Updating Content
                    await alreadyExixt.update({name:name},{transaction})
                    await transaction.commit()
                    return h.response({success: true,message: req.i18n.__("CATEGORY_SUCCESSFULLY_UPDATED"),responseData:record}).code(200)
                }
                    // Creating Content
                    let res=await Models.CategoryContent.create({name,categoryId:id,languageId:lang},{transaction})
                    await transaction.commit();
                    return h.response({success: true,message: req.i18n.__("CATEGORY_SUCCESSFULLY_ADDED"),responseData:res}).code(200)
            }
            else{
                if(lang!=defaultLanguage)
                {
                    await transaction.rollback();
                    return h.response({success: false,message: req.i18n.__("PLEASE_CREATE_CATEGORY_IN_DEFAULT_LANGUAGE"),responseData: {}}).code(400);
                }
                let res=await Models.Category.create({
                    code,
                    attachment,
                    parentId,
                    ordering,
                    CategoryContents:[
                        {
                            name,
                            languageId:lang
                        }
                    ]
                },{
                transaction,include:[{model:Models.CategoryContent}]})
                if (attachment != null) {
                    let reqPayload = {
                        data: [
                            {
                                id: attachment.id,
                                status: Constants.STATUS.ACTIVE
                            }
                        ]
                    }
                    await updateAttachmentService(reqPayload, transaction);
                }
                await transaction.commit()
                return h.response({success: true,message: req.i18n.__("CATEGORY_SUCCESSFULLY_CREATED"),responseData: res}).code(200)
            }
        }
        catch(error)
        {
            console.error('Error in Category Creation',error)
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    updateStatus: async(req, h) => {
        const transaction = await Models.sequelize.transaction();
        try {
            const categoryId = req.payload.id;
            const status = req.payload.status;

            const categoryInfo = await Models.Category.findOne({where:{ id: categoryId }});
            if(!categoryInfo) {
                await transaction.rollback();
                return h.response({success: false,message: req.i18n.__("INVALID_CATEGORY_ID"),responseData: {}}).code(400);
            }

            await categoryInfo.update({ status }, { transaction });
            await transaction.commit();
            return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULL"),responseData: {}}).code(200)
        } catch (error) {
            console.log(error)
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    //get category
    get:async(req,h)=>{
        try{
            const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
            const offset = (req.query.pageNumber - 1) * limit
            const orderByValue = req.query.orderByValue
            const orderByParameter = req.query.orderByParameter
            let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
            let where = {},inner_where={} 
            inner_where={
                languageId:lang
            }
            replacements={}
        if(req.query.name!==null)
        {
            let name = '*'+req.query.name.replace('@','%40').replace('.','%2E')+'*';
            replacements={...replacements,"name":name}
           inner_where = { ...inner_where, [Op.or]: [
                Sequelize.literal('MATCH(`name`) AGAINST(:name IN BOOLEAN MODE)'),

              ] }
        }
        
        let _includesOption     = [{
                model: Models.CategoryContent,
                where:{
                    ...inner_where
                },
               attributes:attributeForCategory()
               ,required:false
            }]
        let options = {
            where,
            order       : [
                    [orderByParameter, orderByValue]
                ],
            subQuery    : false,
            replacements,
            raw:true,
            nest:true,
            include     : _includesOption
        };
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                limit,
                offset

            };
        const category          = await Models.Category.findAndCountAll(options);
        const totalPages        = await Common.getTotalPages(category.count, limit);
        const responseData      = {
            totalPages,
            perPage: limit,
            category: category.rows,
            totalRecords: category.count
        };
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
        }
        catch(error)
        {
            console.error('Error in getting category',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    //get by id
    getById:async(req,h)=>{
        try{
            let id=req.query.id
            let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
            let category=await Models.Category.findOne({where:{id},include:[
                {model: Models.CategoryContent,where:{languageId:lang},
                attributes:attributeForCategory(),
                required:false
            }
            ],
            raw:true,
            nest:true
        })
            if(!category)
            {
                return h.response({success: true, message: req.i18n.__("INVALID_CATEGORY_ID"), responseData: {}}).code(400);
            }
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: category}).code(200);
        }
        catch(error)
        {
            console.error('Error in getting category by id')
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    //remove or destroy
    delete:async(req,h)=>{
        const transaction = await Models.sequelize.transaction()
        try{
            let id=req.query.id
            let category=await Models.Category.findOne({where:{id},include:[
                {model: Models.CategoryContent}
            ]})
            if(!category)
            {
                return h.response({success: true, message: req.i18n.__("INVALID_CATEGORY_ID"), responseData: {}}).code(400);
            }
            await Models.CategoryContent.destroy({where:{categoryId:id},transaction})
            await category.destroy({transaction})
            await transaction.commit()
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: category}).code(200);
        }
        catch(error)
        {   
            await transaction.rollback()
            console.error('Error in destroying cat',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500)
        }
    },

    // // get all record without pagination and without authentication.
    getAllRecord:async(req,h)=>{
        try{
            const orderByValue = req.query.orderByValue
            const orderByParameter = req.query.orderByParameter
            let where = {status: 1},inner_where={} 
            inner_where={
                languageId:lang
            }
            replacements={}
        if(req.query.name!==null)
        {
            let name = '*'+req.query.name.replace('@','%40').replace('.','%2E')+'*';
            replacements={...replacements,"name":name}
           inner_where = { ...inner_where, [Op.or]: [
                Sequelize.literal('MATCH(`name`) AGAINST(:name IN BOOLEAN MODE)'),
            
              ] }
        }
        let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
        let _includesOption     = [{
                model: Models.CategoryContent,
                where:{
                    ...inner_where
                },
                required:false,
                attributes:attributeForCategory()
            }]
        let options = {
            where,
            order       : [
                    [orderByParameter, orderByValue]
                ],
            subQuery    : false,
            replacements,
            raw:true,
            nest:true,
            include     : _includesOption
        };

        const category          = await Models.Category.findAndCountAll(options);


        let categoryData = [];
        for(let item of category.rows) {
            const categoryId = item.id;
            const productInfo = await Models.Product.findOne({ where: { categoryId: categoryId, isPublished: 1 }, include: [ 
                { model: Models.ProductLanguage, as: "productLanguages", where: { languageId: lang } } 
            ] });
            if(productInfo) categoryData.push(item);
        }

        const responseData      = {
            category: categoryData,
        };
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
        }
        catch(error)
        {
            console.error('Error in getting category',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    }



    // getAllRecord:async(req,h)=>{
    //     try{
    //         const orderByValue = req.query.orderByValue
    //         const orderByParameter = req.query.orderByParameter
    //         let where = {status: 1},inner_where={} 
    //         inner_where={ languageId:lang }

    //         let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
    //         let _includesOption     = [
    //         {
    //             model: Models.CategoryContent,
    //             where:{
    //                 ...inner_where
    //             },
    //             required:false,
    //             // as: "content",
    //             attributes:attributeForCategory()
    //         },
    //         {
    //             model: Models.Product, attributes: [], as: "productsInCategory", required: true,
    //             include: [
    //                 {
    //                     attributes: [],
    //                     model: Models.ProductLanguage, as: "productLanguages",
    //                     where: {languageId: lang},
    //                     required: true
    //                 }
    //             ]
    //         }
    //     ]
    //     // let options = {
    //     //     where,
    //     //     order       : [
    //     //             [orderByParameter, orderByValue]
    //     //         ],
    //     //     subQuery    : false,
    //     //     distinct: true,
    //     //     include     : _includesOption
    //     // };

    //     const category = await Models.Category.findAll({
    //         where: { status: 1 },
    //         order: [[orderByParameter, orderByValue]],
    //         distinct: true,
    //         include: [
    //             {
    //                 model: Models.CategoryContent,
    //                 as: "content",
    //                 where: { languageId:lang },
    //                 attributes:attributeForCategory()
    //             }
    //         ]
    //     });
    //     const responseData      = {
    //         category: JSON.parse(JSON.stringify(category)),
    //     };
    //     return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
    //     }
    //     catch(error)
    //     {
    //         console.error('Error in getting category',error)
    //         return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
    //     }
    // }
}


/**
     * Functions used  for category CRUD
     * <AUTHOR> Kushwaha 
     */
// Function for handling attributes for model at time of get query

const attributeForCategory=()=>{
                let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
                return [
                [Sequelize.literal(`IF(CategoryContents.name is not null,CategoryContents.name, (select name from ${Models.CategoryContent.tableName} where language_id = ${defaultlanguageId}  and category_id = Category.id))`),'name'],
                [Sequelize.literal(`IF(CategoryContents.language_id is not null,CategoryContents.language_id, (select language_id from ${Models.CategoryContent.tableName} where language_id = ${defaultlanguageId}  and category_id = Category.id))`),'languageId'],
                [Sequelize.literal(`IF(CategoryContents.category_id is not null,CategoryContents.category_id, (select category_id from ${Models.CategoryContent.tableName} where language_id = ${defaultlanguageId}  and category_id = Category.id))`),'categoryId'],
                [Sequelize.literal(`IF(CategoryContents.created_at is not null,CategoryContents.created_at, (select created_at from ${Models.CategoryContent.tableName} where language_id = ${defaultlanguageId}  and category_id = Category.id))`),'createdAt'],
                [Sequelize.literal(`IF(CategoryContents.updated_at is not null,CategoryContents.updated_at, (select updated_at from ${Models.CategoryContent.tableName} where language_id = ${defaultlanguageId}  and category_id = Category.id))`),'updatedAt']
                ]
}