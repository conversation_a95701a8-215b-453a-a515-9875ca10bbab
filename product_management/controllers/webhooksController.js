const Models = require("../models/index.js");
require('dotenv').config();
module.exports =
{
    upsertUser : async(req, h) => {
        const transaction = await Models.sequelize.transaction();
        try {
           
            await Models.User.upsert({
                ...req.payload
            }, {transaction})
            await transaction.commit();
            return h.response({success:true,message:req.i18n.__("USER_CREATED_SUCCESSFULLY"),responseData:{}}).code(201);
        }
        catch (error) {
            console.error(error);
            await transaction.rollback();
            return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
        }
    },
    createSettings:async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try{
            let {data}=req.payload
            let setting=await Models.Setting.bulkCreate(data, {updateOnDuplicate: ["key","value","type"] ,transaction:transaction});
            await transaction.commit();
            return h.response({success: true, message: req.i18n.__('SUCESSFULLY_DELETED'), responseData:setting}).code(200);
        }
        catch(error){
            console.log(error);
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__('SOMETHING_WENT_WRONG'), responseData: {}}).code(500);
        }
    },
}