module.exports = {
    //create category
    create:async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try{
            const {id,name,isDefault}=req.payload
            let defaultLanguage=process.env.DEFAULT_LANGUANGE_CODE_ID
            let code=name.trim().toLowerCase().replace(new RegExp(" ", "g"),'-')
            if(id)
            {
                // update existing or create in differrnt lang.
                let record=await Models.Group.findOne({where:{id}})
                if(!record){
                    await transaction.rollback()
                    return h.response({success: false,message: req.i18n.__("GROUP_NOT_EXISTS"),responseData: {}}).code(400);
                }
                await record.update({isDefault},{transaction})
                
                let alreadyExixt=await Models.GroupContent.findOne({where:{groupId:id,languageId:lang}})
                if(alreadyExixt)
                {
                   
                    // Updating Content
                    await alreadyExixt.update({name:name},{transaction})
                    await transaction.commit()
                    return h.response({success: true,message: req.i18n.__("GROUP_SUCCESSFULLY_UPDATED"),responseData:record}).code(200)
                }
                    // Creating Content
                    let res=await Models.GroupContent.create({name,groupId:id,languageId:lang},{transaction})
                    await transaction.commit();
                    return h.response({success: true,message: req.i18n.__("GROUP_SUCCESSFULLY_ADDED"),responseData:res}).code(200)
            }
            else{
                if(lang!=defaultLanguage)
                {
                    await transaction.rollback();
                    return h.response({success: false,message: req.i18n.__("PLEASE_CREATE_GROUP_IN_DEFAULT_LANGUAGE"),responseData: {}}).code(400);
                }
                let res=await Models.Group.create({
                    code,
                    isDefault,
                    GroupContents:[
                        {
                            name,
                            languageId:lang
                        }
                    ]
                },{
                transaction,include:[{model:Models.GroupContent}]})
                await transaction.commit()
                return h.response({success: true,message: req.i18n.__("GROUP_SUCCESSFULLY_CREATED"),responseData: res}).code(200)
            }
        }
        catch(error)
        {
            console.error('Error in Group Creation for the attributes',error)
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    //get category
    get:async(req,h)=>{
        try{
            const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
            const offset = (req.query.pageNumber - 1) * limit
            const orderByValue = req.query.orderByValue
            const orderByParameter = req.query.orderByParameter
            let where = {},inner_where={} 
            inner_where={
                languageId:lang
            }
            replacements={}
        if(req.query.name!==null)
        {
            let name = '*'+req.query.name.replace('@','%40').replace('.','%2E')+'*';
            replacements={...replacements,"name":name}
           inner_where = { ...inner_where, [Op.or]: [
                Sequelize.literal('MATCH(`name`) AGAINST(:name IN BOOLEAN MODE)'),
            
              ] }
        }
        let _includesOption     = [{
                model: Models.GroupContent,
                where:{
                    ...inner_where
                }
            }]
        let options = {
            where,
            order       : [
                    [orderByParameter, orderByValue]
                ],
            subQuery    : false,
            replacements,
            raw:true,
            nest:true,
            include     : _includesOption
        };
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                limit,
                offset

            };
        const groups          = await Models.Group.findAndCountAll(options);
        const totalPages        = await Common.getTotalPages(groups.count, limit);
        const responseData      = {
            totalPages,
            perPage: limit,
            groups: groups.rows,
            totalRecords: groups.count
        };
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
        }
        catch(error)
        {
            console.error('Error in getting groups',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    //get by id
    getById:async(req,h)=>{
        try{
            let id=req.query.id
            let group=await Models.Group.findOne({where:{id},include:[
                {model: Models.GroupContent,where:{languageId:lang}}
            ],
            raw:true,
            nest:true
        })
            if(!group)
            {
                return h.response({success: true, message: req.i18n.__("INVALID_GROUP_ID"), responseData: {}}).code(400);
            }
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: group}).code(200);
        }
        catch(error)
        {
            console.error('Error in getting category by id')
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    //remove or destroy
    delete:async(req,h)=>{
        const transaction = await Models.sequelize.transaction()
        try{
            let id=req.query.id
            let group=await Models.Group.findOne({where:{id}})
            if(!group)
            {
                await transaction.rollback()
                return h.response({success: true, message: req.i18n.__("INVALID_GROUP_ID"), responseData: {}}).code(400);
            }
            await Models.GroupContent.destroy({where:{groupId:id},transaction})
            await group.destroy({transaction})
            await transaction.commit()
            return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: group}).code(200);
        }
        catch(error)
        {   
            await transaction.rollback()
            console.error('Error in destroying cat',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500)
        }
    }
}

