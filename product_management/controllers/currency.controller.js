module.exports={
    get:async(req,h)=>{
        try{
            const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT;
            const offset = (req.query.pageNumber!==null?req.query.pageNumber:1 - 1) * limit;
            const orderByValue = req.query.orderByValue;
            const orderByParameter = req.query.orderByParameter;
            let where = {}
            
            if(req.query.currencyCode!==null)
            {
                where = {
                    ... where,
                    currencyCode:{
                        [Op.like]:
                            `%${
                                req.query.currencyCode.trim()
                            }%`
                    },
                
            }
        }
        if(req.query.name!==null)
        {
            where = {
                ... where,
                name:{
                    [Op.like]:
                        `%${
                            req.query.currencyCode.trim()
                        }%`
                },
            
        }
        }
            let options = {
                where,
                order: [
                    [orderByParameter, orderByValue]
                ],
                subQuery: false,
                attributes: ['id','name','currencyCode','symbol']
            };
            if (req.query.pageNumber !== null) 
                options = {
                    ... options,
                    limit,
                    offset
                };
            const currency = await Models.Currency.findAndCountAll(options);
            const totalPages = await Common.getTotalPages(currency.count, limit);
            const responseData = {
                totalPages,
                perPage: limit,
                currency: currency.rows,
                totalRecords: currency.count
            };
            return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
        }
        catch(error){
            return h.response({success: false, message: req.i18n.__("SOMTHING_WENT_WRONG"), responseData: error.toString()}).code(200);
        }
    }
}