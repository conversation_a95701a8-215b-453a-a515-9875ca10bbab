// const {attributeContent}=require('../attributes')
const {products,cartProductCategoryContent,producePrice,currency}=require('../attributes')
const Decrypt=require('../digistore24')
const{createUserInAllService}=require('../services/webhook')
// const {attribute,productTagContent,productTag,productAttributeValue,productCategory,productsContent,attributeForCategory,productAttributeContents,productAttribute}=require('../attributes')

const{getPrductById}=require('./product.controller')
const {DgStoreRequest}=require('../common')
const { Op, QueryTypes } = require("sequelize");
const Moment = require("moment");
// console.log(Moment(new Date()).format("YYYY-MM-DD h:m:s"))
// const { sequelize } = require('../../video_management/models')
module.exports={
    /**
     * Handler for attribute CRUD
     * <AUTHOR> 
     * @param {*} req 
     * @param {*} h 
     * @returns 
     */
    
    create :async(req, h) =>{
        const transaction=await Models.sequelize.transaction()
        try{
            let {data,userId}=req.payload
            //let userId=req.auth.credentials.userData.User.id
            let decryprData =   await Decrypt.decodeData(data)
            if(userId==null)
            {
                let payload={
                    email       :   decryprData.buyer_email,
                    firstName   :   decryprData.buyer_first_name,
                    lastName    :   decryprData.buyer_last_name
                }
                let res= await createUserInAllService(payload,transaction)
                userId=res.userId
            }
            let cart            =   await getCart(userId)
            let orderDetails= await DgStoreRequest(`getPurchase/?purchase_id=${decryprData.order_id}`)
            let custom=[]
            console.log('orderDetails',orderDetails)
            let productId = null;
            let userProductArray = [];
            for (const product of orderDetails.data.data.items) {
                let productData= await getPrductById(product.product_id)

                console.log(" productData ", productData)
                let data={
                    count   :   product.count,
                    product :   productData,
                }
                custom.push(data)

                const existsUserProduct = await Models.UserProduct.findOne({ where: { productId: productData.id, userId: userId } })
                if(!existsUserProduct) {
                    userProductArray.push({ productId: productData.id, userId });
                   // await Models.UserProduct.create({ productId: product.product_id, userId: userId }, { transaction });
                }

                if(productData.courseId !== null) {
                    let videoCheck = await sequelize.query(`SELECT * FROM vidMgt_videos where id = ${productData.courseId}`,{type: QueryTypes.SELECT});
                    console.log(videoCheck, " ============================ videoCheck")
                    if(videoCheck && videoCheck.length > 0) {
                        let PurchasedTopicInsert = await sequelize.query(
                            // `SELECT * FROM vidMgt_videos where id = ${productData.courseId}`
                            `INSERT INTO vidMgt_purchased_topics (user_id, video_id, has_passed,starting_date,purchase_date,created_at,updated_at,is_shop_product, status)
                            VALUES (${userId}, ${productData.courseId}, 0, '${Moment(new Date()).format("YYYY-MM-DD h:m:s")}','${Moment(new Date()).format("YYYY-MM-DD h:m:s")}','${Moment(new Date()).format("YYYY-MM-DD h:m:s")}','${Moment(new Date()).format("YYYY-MM-DD h:m:s")}', 1, 1);`
                            ,{type: QueryTypes.INSERT, transaction});

                            console.log(PurchasedTopicInsert, " ====================== PurchasedTopicInsert")
                    }
                }

            // if(videoCheck && videoCheck.length > 0) {
            //     await Models.PurchasedTopic.create({
            //         userId: userId,
            //         videoId: videoId,
            //         hasPassed: 0,
            //         purchaseDate: new Date(),
            //         startingDate: new Date(),
            //         endDate: null,
            //         activeSubscription: null,
            //         status: 1
            //     },{transaction});
            // }




            }
            let record                  =   await Models.Order.create({userId,data:decryprData,orderDetails:orderDetails.data.data,custom,cart},{transaction})

            let updatedUserProductArray = [];
            for(let item of userProductArray) {
                updatedUserProductArray.push({...item, orderId: record.id});
            }

            await Models.UserProduct.bulkCreate(updatedUserProductArray, { transaction });
        
            await Models.CartProduct.destroy({where:{userId},transaction})


            // let videoCheck = await sequelize.query(`SELECT * FROM vidMgt_videos where id = ${}`,{type: QueryTypes.SELECT})
            // if(videoCheck && videoCheck.length > 0) {
            //     await Models.PurchasedTopic.create({
            //         userId: userId,
            //         videoId: videoId,
            //         hasPassed: 0,
            //         purchaseDate: new Date(),
            //         startingDate: new Date(),
            //         endDate: null,
            //         activeSubscription: null,
            //         status: 1
            //     },{transaction});
            // }



                await transaction.commit()
                return h.response({success: true,message: req.i18n.__("ATTRIBUTE_SUCCESSFULLY_CREATED"),responseData:record}).code(200)
        }catch(error){
            console.error('Error in Add orders',error)
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    get :async(req,h) =>{
        try{
            const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
            const offset = (req.query.pageNumber - 1) * limit
            const orderByValue = req.query.orderByValue
            const orderByParameter = req.query.orderByParameter
            let authId = req.auth.credentials.userData.User.id;
            let categoryIds = req.query.categoryId; 
            let where ={}
            // if((req.auth.credentials.userData.Role.includes('admin')))
            // {
               
            //     if(req.query.userId!==null)
            //     {
            //         where= {...where,userId:req.query.userId}
            //     }
            // }
            // else{
            //     where= {userId:req.auth.credentials.userData.User.id}
            // }

            if(req.auth.credentials.userData.Role.includes('companion')) {
                where = {companionId: authId}
            } else if(req.auth.credentials.userData.Role.includes('costumer')) {
                where = {userId: authId}
            } else {
                if(req.query.userId!==null)
                {
                    where= {...where,userId:req.query.userId}
                }
            }
           
            if(categoryIds !== null) {
                where = {...where, [Op.and] : [Models.sequelize.literal("JSON_EXTRACT(`Order`.`custom`, '$[0].product.category.id') in ("+ categoryIds + ")")]}
            }



            if(req.query.startDate && req.query.endDate) {
                if(Moment(req.query.endDate) < Moment(req.query.startDate)) {
                  return h.response({success:false,message:req.i18n.__('END_DATE_CANNOT_BE_LESS_THAN_START_DATE'),responseData:{}}).code(400)
                }
                let startDate =  Moment(req.query.startDate).utc().format('YYYY-MM-DD HH:mm:ss');
                let endDate =  Moment(req.query.endDate).utc().format('YYYY-MM-DD HH:mm:ss');
                where = {...where, createdAt: {[Op.between]: [startDate, endDate]}}
            } else if(req.query.startDate) {
                let startDate =  Moment(req.query.startDate).utc().format('YYYY-MM-DD HH:mm:ss');
                where = {...where, createdAt: {[Op.gte]: startDate}}
            } else if(req.query.endDate) {
                let endDate =  Moment(req.query.endDate).utc().format('YYYY-MM-DD HH:mm:ss');
                where = {...where, createdAt: {[Op.lte]: endDate}}
            }




        let options = {
            //attributes:['productId','count'],
            where,
            order       : [
                    [orderByParameter, orderByValue]
                ],
            subQuery    : false,
            include     : [
                {model:Models.User}, 
                { 
                    model: Models.UserProduct, required: false,
                    include: [{ model: Models.ProductLink, required: false }]
            
                }
            ]
        };
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                limit,
                offset
            };
          
          

        const cartProducts         = await Models.Order.findAndCountAll(options);
        const totalPages        = await Common.getTotalPages(cartProducts.count, limit);
        const responseData      = {
            totalPages,
            perPage: limit,
            records: cartProducts.rows,
            totalRecords: cartProducts.count,
            //cartValue:totalValue
        };
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData}).code(200);

        }catch(error){
            console.error('Error in getting Attributes',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },
    
    getById:async(req,h) =>{
        try{
            const id=req.query.id;
            let order=await Models.Order.findOne({where:{id},include: [{model:Models.User}]})
            if(!order)
            {
                return h.response({success: true, message: req.i18n.__("ORDER_NOT_FOUND"), responseData:{}}).code(400);
            }
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData:order}).code(200);

        }catch(error){
            console.error('Error in getting Attributes',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

   
}




const Dataattribute={
    productContent:()=>{
        let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
        return [
            [Sequelize.literal(`if(\`Product->content\`.name,\`Product->content\`.name, (select name from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id)) `),'name'],
        [Sequelize.literal(`if(\`Product->content\`.description_text,\`Product->content\`.description_text, (select description_text from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id)) `),'descriptionText'],
        [Sequelize.literal(`if(\`Product->content\`.description_html,\`Product->content\`.description_html, (select description_html from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id)) `),'descriptionHtml'],
        [Sequelize.literal(`if(\`Product->content\`.long_description_text,\`Product->content\`.long_description_text, (select long_description_text from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id)) `),'longDescriptionText'],
        [Sequelize.literal(`if(\`Product->content\`.long_description_html,\`Product->content\`.long_description_html, (select long_description_html from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id)) `),'longDescriptionHtml'],
    
        ]
    }
        
    
}

const getCart=async(userId)=>{
    let _includesOption     = [
        {
            model:Models.Category,as:'category',
            include:[{model:Models.CategoryContent,
                attributes:cartProductCategoryContent(),
                as:'content'}]
        },
        {
        model: Models.ProductContent,
        as:'content',
        attributes:Dataattribute.productContent(),
        required:false
    },
    {
        model: Models.ProductPrice,as:'price',
        attributes:producePrice(),
        include:[{model:Models.Currency,attribute:currency()}]
    }]
    let includesOption=[
        {model:Models.Product,attributes:products(),
        include:_includesOption
        }
    ]
    let cart    =   await Models.CartProduct.findAll({where:{userId:userId},include:includesOption,order: [
        ['id', 'DESC']
    ]})
    let data =JSON.parse(JSON.stringify(cart))
    let cartValue=0,currencySymbol=null
          for (const iterator of data) {
            iterator.Product.totalValue=(iterator.Product.price[0].price)*iterator.count
            cartValue+=iterator.Product.totalValue
          }
          if(data.length>0)
            {
                console.log(data[0].Product.price[0].Currency.symbol)
                currencySymbol=data[0].Product.price[0].Currency.symbol
                //currencySymbol=data[0].Product.price[0].Currency.symbol
            }
    return {data,currencySymbol,cartValue}
}



// const getPrductById=async(id)=>
// {
//     try{
//         let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
//             let  where={
//                 languageId:lang
//             }
//             //console.log('where',where)
//             let _includesOption     = [{
//                 model: Models.ProductContent,
//                 as:'content',
//                 where,
//                 attributes:productsContent(),
//                 required:false
//             },
//             {
//                 model: Models.ProductPrice,as:'price',
//                 attributes:producePrice(),
//                 include:[{model:Models.Currency,attribute:currency()}]
//             },
//             // {
//             //     model:Models.ProductAttribute,as:'attribute',
//             //     attributes:productAttribute(),
              
//             //     include:[
//             //         {
//             //             model:Models.Attribute,
//             //             attributes:attribute(),
//             //             include:[{
//             //             model:Models.AttributeContent,
//             //             where,
//             //           attributes:productAttributeContents()
//             //         }]},
//             //         {
//             //             model:Models.ProductAttributeContent,
//             //             as:'content',
//             //             where,
//             //             attributes:productAttributeValue()

//             //     }]
//             // },
           
//             // {
//             //     model:Models.Category,as:'category',
//             //     attributes:productCategory(),
//             //     include:[{model:Models.CategoryContent,  where,as:'content',attributes:attributeForCategory()}]
//             // },
//             // {
//             //     model:Models.Tag,
//             //     through:
//             //     {
//             //         attributes:[],
//             //     },
//             //     attributes:productTag(),
//             //     include:[{model:Models.TagContent,as:'content',  where,
//             //     attributes:productTagContent()
//             // }]
//             // }
//         ]
//             let product= await Models.Product.findOne(
//                 {where:{dgStoreId:id},include:_includesOption}
//             )
//             let returnData  = JSON.parse(JSON.stringify(product))
//             return returnData
//     }
//     catch(error)
//     {   
//         console.log('error',error)
//         throw error.toString()
//     }s
// }