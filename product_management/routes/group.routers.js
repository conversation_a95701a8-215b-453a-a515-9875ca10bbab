"use strict";
const controller = require("../controllers/group.controller");
module.exports = [ 
    {
		method : "POST",
		path : "/group",
		handler : controller.create,
		options: {
			tags: ["api", "Attribute-Group"],
			notes: "Endpoint to define a new group for attribute",
			description:"Create group for Attribute",
			auth: {strategy: 'jwt', scope: ["admin","create_group","manage_group",'manage_products']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
				   	id:Joi.number().optional().default(null),
				   	isDefault: Joi.number().optional().default(0),
                    name : Joi.string().required().error(errors=>{return Common.routeError(errors,'GROUP_NAME_TYPE_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: <PERSON><PERSON>
			},
			pre : [{method: Common.prefunction}]
		}
	},
	
	{
		method : "GET",
		path : "/group",
		handler : controller.get,
		options: {
			tags: ["api", "Attribute-Group"],
			notes: "Endpoint to get Category",
			description:"Fetch group",
			auth: {strategy: 'jwt', scope: ["admin","manage_category","costumer"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					name:Joi.string().max(250).optional().default(null),
					limit: Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/group-id",
		handler : controller.getById,
		options: {
			tags: ["api", "Attribute-Group"],
			notes: "Endpoint to get Category",
			description:"Fetch group",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "DELETE",
		path : "/group",
		handler : controller.delete,
		options: {
			tags: ["api", "Attribute-Group"],
			notes: "Endpoint to Delete Category",
			description:"Delete Ctegory",
			auth: {strategy: 'jwt', scope: ["admin","manage_category","costumer"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},


]