"use strict";
const Joi = require("joi");
const controller = require("../controllers/order.controller");

module.exports = [ 
    
    {
		method : "POST",
		path : "/order",
		handler : controller.create,
		options : {
			tags: ["api", "Cart-Order"],
			notes: "Add Cart-Order",
			description: "Add Cart-Order",
			auth : false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options : {
					abortEarly: false
				},
				payload : {
                  data:Joi.string().required().error(errors=>{return Common.routeError(errors,'DATA_IS_REQUIRED')}),
				  userId:Joi.number().optional().default(null)
				},
				failAction: async(req,h, err) => {
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},

	
	{
		method : "GET",
		path : "/order",
		handler : controller.get,
		options: {
			tags: ["api", "Cart-Order"],
			notes : "Get Cart-Order List",
			description: "Get Cart-Order List",
			auth : {strategy: 'jwt',scope : ["admin","manage_cart","costumer",'companion','order_management']},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					limit: Joi.number().integer().optional().default(null),
					userId:Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id'),
                    categoryId: Joi.string().allow(null).optional().default(null),
					startDate:Joi.date().example('2022-07-05T04:00:00Z').optional().default(null),
					endDate:Joi.date().example('2022-07-05T04:00:00Z').optional().default(null)
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method :Common.prefunction}]
		}
    },
	{
		method : "GET",
		path : "/order-id",
		handler : controller.getById,
		options: {
			tags: ["api", "Cart-Order"],
			notes : "Get Cart-Order List",
			description: "Get Cart-Order List",
			auth : {strategy: 'jwt',scope : ["admin","manage_cart","costumer",'companion','order_management']},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),	
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method :Common.prefunction}]
		}
    }
]