"use strict";
const Joi = require("joi");
const controller = require("../controllers/currency.controller");
module.exports = [ 
	{
		method : "GET",
		path : "/currency",
		handler : controller.get,
		options: {
			tags: ["api", "Product-Currency"],
			notes: "Endpoint to get Currency",
			description:"Fetch Currency",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					name:Joi.string().max(250).optional().default(null),
					limit: Joi.number().integer().optional().default(null),
                    currencyCode:Joi.string().optional().uppercase().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	}
]