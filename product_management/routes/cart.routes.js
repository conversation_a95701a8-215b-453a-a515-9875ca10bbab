"use strict";
const controller = require("../controllers/cart.controller");

module.exports = [ 
    
    {
		method : "POST",
		path : "/cart-product",
		handler : controller.create,
		options : {
			tags: ["api", "Cart-Product"],
			notes: "Add Cart-Product",
			description: "Add Cart-Product",
			auth : {strategy:'jwt', scope: ["admin","manage_cart","costumer",'companion']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly: false
				},
				payload : {
                    productId 	: Joi.number().example(1).required().error(errors=>{return Common.routeError(errors,'PRODUCT_ID_IS_REQUIRED')}),
                    count 		: Joi.number().example(1).min(1).optional().default(1)
				},
				failAction: async(req,h, err) => {
					return  Common.FailureError(err, req);
				},
				validator: <PERSON><PERSON>
			},
			pre : [{method: Common.prefunction}]
		}
	},

	
	{
		method : "GET",
		path : "/cart-product",
		handler : controller.get,
		options: {
			tags: ["api", "Cart-Product"],
			notes : "Get Cart-Product List",
			description: "Get Cart-Product List",
			auth : {strategy: 'jwt',scope : ["admin","manage_cart","costumer",'companion']},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					limit: Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method :Common.prefunction}]
		}
    },

	{
		method : "DELETE",
		path : "/cart-product",
		handler : controller.delete,
		options: {
			tags : ["api", "Cart-Product"],
			notes : "Delete Attribute",
			description : "Delete Attribute",
			auth : {strategy: 'jwt', scope : ["admin","manage_cart","costumer",'companion']},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					productId 	: Joi.number().example(1).required().error(errors=>{return Common.routeError(errors,'PRODUCT_ID_IS_REQUIRED')}),
                    count 		: Joi.number().example(1).min(1).optional().default(1)
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre: [{method: Common.prefunction}]
		}
    },
	
	{
		method : "GET",
		path : "/cart-value",
		handler : controller.getCartCount,
		options: {
			tags: ["api", "Cart-Product"],
			notes : "Get Cart-Product List",
			description: "Get Cart-Product List",
			auth : {strategy: 'jwt',scope : ["admin","manage_cart","costumer",'companion']},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method :Common.prefunction}]
		}
    },
    
]