const Joi               = require("joi").extend(require("@joi/date"))
const webhookController = require("../controllers/webhooksController")
const webhookUserModel = Joi.object({
    userId          : Joi.number().integer(),
    firstName       : Joi.string().trim(),
    lastName        : Joi.string().trim(),
    title           : Joi.string().trim(),
    profilePhotoUrl : Joi.string().trim().allow(null),
    profilePhotoId  : Joi.number().integer().allow(null),
    languages       :   Joi.array().allow(null),
    meetingPrice:Joi.string().allow(null),
    reason:Joi.string().allow(null),
    rating:Joi.string().allow(null),
    scheduleTime:Joi.number().allow(null),
    reScheduleTime:Joi.number().allow(null),
    cancelTime:Joi.number().allow(null),
    role:Joi.array().allow(null),
    gender:Joi.number().allow(null),
    userObject: Joi.object().optional().allow(null)
}).label("User_Webhook");
module.exports = [
    {
        method  : "POST",
        path    : "/webhook/user",
        options : {
            handler     : webhookController.upsertUser,
            description : "Add and update User",
            notes       : "Add and update User",
            tags        : ["api", "Webhook"],
            auth        : false,
            validate    : {
                headers : Joi.object
                ({
                    "language"      : Joi.string().default("en"),
                    "Authorization" : Joi.string()
                }).options
                ({
                    "allowUnknown"  : true
                }),
                payload   : webhookUserModel.keys().options({ presence: 'required' }),
                validator : Joi
            }
        }
    },
    {
        method : "POST",
        path : "/settings",
        handler :webhookController.createSettings,
        options: {
            tags: ["api", "Admin"],
            notes: "Endpoint to add User By Admin",
            description: "Add User By Admin",
            auth:false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload:{
                        data:Joi.array().example([{'key':'key','value':'value'}]).items(
                            Joi.object().example({'key':'key','value':'value'})
                        ).min(1).optional().default(null),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
]