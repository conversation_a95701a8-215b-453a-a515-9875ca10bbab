"use strict";
const controller = require("../controllers/attribute.controller");

module.exports = [ 
    
    {
		method : "POST",
		path : "/attribute",
		handler : controller.create,
		options : {
			tags: ["api", "Attributes"],
			notes: "Add Attributes",
			description: "Add Attributes",
			auth : {strategy:'jwt', scope: ["admin", "manage-products"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly: false
				},
				payload : {
                    id			: Joi.number().example(1).optional().default(null),
                    categoryId 	: Joi.number().example(1).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')}),
					type 		: Joi.number().example(1).optional().default(null),
					isVariation : Joi.number().required().error(errors=>{return Common.routeError(errors,'ISVARIENT_IS_REQUIRED')}),
					addOns 		: Joi.number().required().error(errors=>{return Common.routeError(errors,'ADDDONS_IS_REQUIRED')}),
					isPaid		: Joi.number().example(1).optional().default(0),
					name 		: Joi.string().trim().required().error(errors=>{return Common.routeError(errors,'NAME_IS_REQUIRED')}),
				    //groupId 	: Joi.number().required().error(errors=>{return Common.routeError(errors,'GROUP_ID_IS_REQUIRED')}),
				},
				failAction: async(req,h, err) => {
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},

	
	{
		method : "GET",
		path : "/attribute",
		handler : controller.get,
		options: {
			tags: ["api", "Attributes"],
			notes : "Get Attributes List",
			description: "Get Attributes List",
			auth : {strategy: 'jwt',scope : ["admin","manage-products", "product_management"]},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					name:Joi.string().max(250).optional().default(null),
					limit: Joi.number().integer().optional().default(null),
					categoryId:Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method :Common.prefunction}]
		}
    },
    
    {
		method : "GET",
		path : "/attribute-id",
		handler : controller.getById,
		options: {
			tags: ["api", "Attributes"],
			notes : "Get Attributes",
			description: "Get Attributes",
			auth : {strategy: 'jwt',scope : ["admin", "manage-products", "product_management"]},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					id 		: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method : Common.prefunction}]
		}
	},

	{
		method : "DELETE",
		path : "/attribute",
		handler : controller.delete,
		options: {
			tags : ["api", "Attributes"],
			notes : "Delete Attribute",
			description : "Delete Attribute",
			auth : {strategy: 'jwt', scope : ["admin","manage-products", "product_management"]},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					id 		: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre: [{method: Common.prefunction}]
		}
    }
    
]