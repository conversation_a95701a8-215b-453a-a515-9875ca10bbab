"use strict";
const Joi= require('joi')
const controller = require("../controllers/product.controller");
module.exports = [ 
    {
		method : "POST",
		path : "/product",
		handler : controller.create,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to define a new product",
			description:"Create Product",
			auth: {strategy: 'jwt', scope: ["admin",'manage-product',"product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					name 				: Joi.string().example('name').required().error(errors=>{return Common.routeError(errors,'PRODUCT_NAME_IS_REQUIRED')}),
					descriptionHtml 	: Joi.string().trim().example('descriptionHtml').optional().allow('',null).default(null),
					
                    longDescriptionHtml : Joi.string().trim().example('longDescriptionHtml').optional().allow('',null).default(null),
					highlights 	: Joi.string().trim().example('longDescriptionHtml').optional().allow('',null),
					categoryId			: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')}),
					courseId: Joi.number().integer().example(1).optional().allow(null).default(null),
					price				: Joi.array().items(
						{  
							currencyId	:	Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PRICE_ID_IS_REQUIRED')}),
							price		:	Joi.number().required().example(1.1).error(errors=>{return Common.routeError(errors,'PRICE_VALUE_IS_REQUIRED')})
						}
					).min(1).required().error(errors=>{return Common.routeError(errors,'PRICE_IS_REQUIRED')}),
					// attributeSet		: Joi.array().items(
					// 			Joi.object().keys(
					// 				{
					// 				id		:	Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ATTRIBUTE_ID_IS_REQUIRED')}),
					// 				data	:	Joi.array().items(
					// 					{
					// 						id		:	Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ATTRIBUTE_ID_IS_REQUIRED')}),
					// 						value	:	Joi.string().trim().required().error(errors=>{return Common.routeError(errors,'ATTRIBUTE_VALUE_IS_REQUIRED')}),
					// 					}
					// 				)
					// 				}
					// 			)
					// ).min(1).required().error(errors=>{return Common.routeError(errors,'ATTRIBUTE_SET_IS_REQUIRED')}),
					attachment				:	Joi.object().example({'id':1,'path':'demo'}).optional().allow(null).default(null),
					tags					:	Joi.array().items(Joi.number().integer()).example([1,2,3,4]).error(errors=>{return Common.routeError(errors,'TAGS_IS_REQUIRED')}),
					dimensions				:	Joi.object().example({'height':'123',"width":"123","length":'123',"weight":"123"}).error(errors=>{return Common.routeError(errors,'DIMANTIONS_IS_REQUIRED')}),
					additionalInformation	:	Joi.object().example({}).optional().allow({},null).default(null),
					dgStoreId				:	Joi.string().trim().example('longDescriptionHtml').optional().allow(null).default(null),
					productLanguages: Joi.array().items(Joi.string()).optional().default(null),
					ordering: Joi.number().optional().default(1),
					videoLink: Joi.string().trim().optional().default(null).allow(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "PATCH",
		path : "/product",
		handler : controller.update,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to update an existing Product",
			description:"Update Product",
			auth: {strategy: 'jwt', scope: ["admin",'manage-product',"product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					id					: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
					isPublished			: Joi.number().integer().valid(1,0).required().error(errors=>{return Common.routeError(errors,'IS_PUBLISH_IS_REQUIRED')}),
					name 				: Joi.string().example('name').required().error(errors=>{return Common.routeError(errors,'PRODUCT_NAME_IS_REQUIRED')}),
					highlights 	: Joi.string().trim().example('longDescriptionHtml').optional().allow('',null),
					courseId: Joi.number().integer().example(1).optional().allow(null).default(null),
					descriptionHtml 	: Joi.string().trim().example('descriptionHtml').optional().allow('',null).default(null),
                    longDescriptionHtml : Joi.string().trim().example('longDescriptionHtml').optional().allow('',null).default(null),
					categoryId			: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')}),
					price				: Joi.array().items(
						{
							currencyId	:	Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PRICE_ID_IS_REQUIRED')}),
							price		:	Joi.number().required().example(1.1).error(errors=>{return Common.routeError(errors,'PRICE_VALUE_IS_REQUIRED')})
						}
					).min(1).required().error(errors=>{return Common.routeError(errors,'PRICE_IS_REQUIRED')}),
					// attributeSet		: Joi.array().items(
					// 	{
					// 				id		:	Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'ATTRIBUTE_ID_IS_REQUIRED')}),
					// 				value	:	Joi.string().trim().example('attribute-value').error(errors=>{return Common.routeError(errors,'VALUE_IS_REQUIRED')})
					// 	}
					// ).min(1).required().error(errors=>{return Common.routeError(errors,'ATTRIBUTE_SET_IS_REQUIRED')}),
					attachment				:	Joi.object().example({'id':1,'path':'demo'}).optional().default({}),
					tags					:	Joi.array().items(Joi.number().integer()).example([1,2,3,4]).error(errors=>{return Common.routeError(errors,'TAGS_IS_REQUIRED')}),
					dimensions				:	Joi.object().keys().example({'height':'123',"width":"123","length":'123',"weight":"123"}).error(errors=>{return Common.routeError(errors,'DIMANTIONS_IS_REQUIRED')}),
					additionalInformation	:	Joi.object().example({}).optional().allow({}).default({}),
					dgStoreId				:	Joi.string().trim().example('longDescriptionHtml').optional().allow(null).default(null),
					productLanguages: Joi.array().items(Joi.string()).optional().default(null),
					ordering: Joi.number().optional().default(1),
					videoLink: Joi.string().trim().optional().allow(null).default(null)
					// similarProducts: Joi.array().items(Joi.number().optional()).optional().default(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/similar-products",
		handler : controller.createSimilarProducts,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to update an existing Product",
			description:"Update Product",
			auth: {strategy: 'jwt', scope: ["admin",'manage-product',"product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					id: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
					similarProducts: Joi.array().items(Joi.number().optional()).optional().default(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/product",
		handler : controller.get,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product",
			description:"Fetch group",
			auth: {strategy: 'jwt', scope: ["admin","manage_category","costumer","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					categoryId: Joi.number().integer().optional().default(null),
					isPublished: Joi.number().integer().optional().default(null),
					isFeatured: Joi.number().integer().optional().default(null),
					language: Joi.string().optional().default(null),
					name:Joi.string().max(250).optional().default(null),
					limit: Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/product-list",
		handler : controller.getList,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product",
			description:"Fetch Products",
			auth:false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					userId	:	Joi.number().integer().optional().default(null),
					isFeatured: Joi.number().integer().optional().default(null),
					name	:	Joi.string().max(250).optional().default(null),
					limit	: 	Joi.number().integer().optional().default(null),
					categoryId:Joi.number().integer().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id','price','ordering').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/product-id",
		handler : controller.getById,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product by id",
			description:"Get Products",
			auth: {strategy: 'jwt', scope: ["admin","manage_category","costumer","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "DELETE",
		path : "/product",
		handler : controller.delete,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product by id",
			description:"Delete Products",
			auth: {strategy: 'jwt', scope: ["admin","manage_products","costumer","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/product-publish",
		handler : controller.publish,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to publish a product",
			description:"Publish Product",
			auth: {strategy: 'jwt', scope: ["admin",'manage-product',"product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					id					: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PRODUCT_ID_IS_REQUIRED')}),
					isPublished			: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'IS_PUBLISHED_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/product-featured",
		handler : controller.featured,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to publish a product",
			description:"Publish Product",
			auth: {strategy: 'jwt', scope: ["admin",'manage-product',"product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					id					: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PRODUCT_ID_IS_REQUIRED')}),
					isFeatured			: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'IS_isFeatured_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/product-details",
		handler : controller.getDetails,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product Details",
			description:"Get Product Details",
			auth:false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
					userId:Joi.number().optional().default(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	// {
	// 	method : "GET",
	// 	path : "/category-list",
	// 	handler : controller.getAllRecord,
	// 	options: {
	// 		tags: ["api", "Product-Category"],
	// 		notes: "Endpoint to get Category",
	// 		description:"Get all",
	// 		auth: false,
	// 		validate: {
	// 			headers: Joi.object(Common.headers()).options({
	// 				allowUnknown: true
	// 			}),
	// 			options: {
	// 				abortEarly: false
	// 			},
	// 			query: {
	// 				name:Joi.string().optional().default(null),
	// 				orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
    //                 orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
	// 			},
	// 			failAction: async (req, h, err) => {
	// 				return Common.FailureError(err, req);
	// 			},
	// 			validator: Joi
	// 		},
	// 		pre : [{method: Common.prefunction}]
	// 	}
	// },
	{
		method : "GET",
		path : "/dg-product-list",
		handler : controller.getDgstoreList,
		options: {
			tags: ["api", "Dg-Store-Product"],
			notes: "Endpoint to Get Dg Store Product List",
			description:"Dg Store Product List",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					//id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/dg-product-id",
		handler : controller.getDgstoreById,
		options: {
			tags: ["api", "Dg-Store-Product"],
			notes: "Endpoint to Get Dg Store Product",
			description:"Dg Store Product ",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
				id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/similar-products",
		handler : controller.similarProducts,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product Details",
			description:"Get Product Details",
			auth:false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
					isPublished: Joi.number().optional().default(1),
					userId:Joi.number().optional().default(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/admin/similar-products",
		handler : controller.similarProducts,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product Details",
			description:"Get Product Details",
			auth:false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
					isPublished: Joi.number().optional().default(0),
					userId:Joi.number().optional().default(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/product-links",
		handler : controller.getProductLinks,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product Details",
			description:"Get Product Details",
			auth: {strategy: 'jwt'},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					productId:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/product-link",
		handler : controller.createProductLink,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product Details",
			description:"Get Product Details",
			auth: {strategy: 'jwt'},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					productId:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
					productInfo: Joi.array().items({
						type: Joi.string().required().error(errors=>{return Common.routeError(errors,'TYPE_REQUIRED')}),
						url: Joi.string().required().error(errors=>{return Common.routeError(errors,'URL_REQUIRED')})
					}).optional().default(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},

]