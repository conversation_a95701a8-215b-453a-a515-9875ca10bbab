"use strict";
const controller = require("../controllers/category.controller");
module.exports = [ 
    {
		method : "POST",
		path : "/category",
		handler : controller.create,
		options: {
			tags: ["api", "Product-Category"],
			notes: "Endpoint to define a new group for portal",
			description:"Create group",
			auth: {strategy: 'jwt', scope: ["admin","create_group","manage_group",'owner',"product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
                   // groupTypeId : Joi.number().required().error(errors=>{return Common.routeError(errors,'GROUP_TYPE_IS_REQUIRED')}),
				   	id:Joi.number().optional().default(null),
				   	parentId: Joi.number().optional().default(null),
					attachment:Joi.object().optional().default(null),
					ordering: Joi.number().optional().default(1),
                    name : Joi.string().required().error(errors=>{return Common.routeError(errors,'GROUP_NAME_TYPE_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "PATCH",
		path : "/category/status",
		handler : controller.updateStatus,
		options: {
			tags: ["api", "Product-Category"],
			notes: "Endpoint to update category status",
			description:"Create group",
			auth: {strategy: 'jwt', scope: ["admin","create_group","manage_group",'owner',"product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
				   	id:Joi.number().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
				   	status: Joi.number().required().error(errors=>{return Common.routeError(errors,'STATUS_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	
	{
		method : "GET",
		path : "/category",
		handler : controller.get,
		options: {
			tags: ["api", "Product-Category"],
			notes: "Endpoint to get Category",
			description:"Fetch group",
			auth: {strategy: 'jwt', scope: ["admin","manage_category","costumer","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					name:Joi.string().max(250).optional().default(null),
					limit: Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/category-id",
		handler : controller.getById,
		options: {
			tags: ["api", "Product-Category"],
			notes: "Endpoint to get Category",
			description:"Fetch group",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/category-list",
		handler : controller.getAllRecord,
		options: {
			tags: ["api", "Product-Category"],
			notes: "Endpoint to get Category",
			description:"Get all",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					name:Joi.string().optional().default(null),
					orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id', 'ordering').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "DELETE",
		path : "/category",
		handler : controller.delete,
		options: {
			tags: ["api", "Product-Category"],
			notes: "Endpoint to Delete Category",
			description:"Delete Ctegory",
			auth: {strategy: 'jwt', scope: ["admin","manage_category","costumer","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},


]