"use strict";
const controller = require("../controllers/tag.controller");
module.exports = [ 
    {
		method : "POST",
		path : "/tag",
		handler : controller.create,
		options: {
			tags: ["api", "Product-Tag"],
			notes: "Endpoint to define a new tag ",
			description:"Create Tag",
			auth: {strategy: 'jwt', scope: ["admin","manage_tags","manange-products","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
                   // groupTypeId : Joi.number().required().error(errors=>{return Common.routeError(errors,'GROUP_TYPE_IS_REQUIRED')}),
				   	id:Joi.number().optional().default(null),
				   	//parentId: Joi.number().optional().default(null),
					//attachment:Joi.object().optional().default(null),
                    name : Joi.string().required().error(errors=>{return Common.routeError(errors,'GROUP_NAME_TYPE_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	
	{
		method : "GET",
		path : "/tag",
		handler : controller.get,
		options: {
			tags: ["api", "Product-Tag"],
			notes: "Endpoint to get Tag",
			description:"Fetch Tag",
			auth: {strategy: 'jwt', scope: ["admin","manage_category","costumer","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					name:Joi.string().max(250).optional().default(null),
					limit: Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/tag-id",
		handler : controller.getById,
		options: {
			tags: ["api", "Product-Tag"],
			notes: "Endpoint to get Tag",
			description:"Fetch Tag",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/tag-list",
		handler : controller.getAllRecord,
		options: {
			tags: ["api", "Product-Tag"],
			notes: "Endpoint to get Tag",
			description:"Get all",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					name:Joi.string().optional().default(null),
					orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "DELETE",
		path : "/tag",
		handler : controller.delete,
		options: {
			tags: ["api", "Product-Tag"],
			notes: "Endpoint to Delete Tag",
			description:"Delete Tag",
			auth: {strategy: 'jwt', scope: ["admin","manage_category","costumer","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},


]