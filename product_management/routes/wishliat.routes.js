"use strict";
const controller = require("../controllers/wishList.controller");

module.exports = [ 
    
    {
		method : "POST",
		path : "/wish-list",
		handler : controller.create,
		options : {
			tags: ["api", "Wish-List"],
			notes: "Add Wish-List",
			description: "Add Wish-List",
			auth : {strategy:'jwt', scope: ["admin","manage_cart","costumer",'companion']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly: false
				},
				payload : {
                    productId 	: Joi.number().example(1).required().error(errors=>{return Common.routeError(errors,'PRODUCT_ID_IS_REQUIRED')})
				},
				failAction: async(req,h, err) => {
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},

	
	{
		method : "GET",
		path : "/wish-list",
		handler : controller.get,
		options: {
			tags: ["api", "Wish-List"],
			notes : "Get wish-list List",
			description: "Get Wish-List List",
			auth : {strategy: 'jwt',scope : ["admin","manage_cart","costumer",'companion']},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					limit: Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method :Common.prefunction}]
		}
    }
    
]