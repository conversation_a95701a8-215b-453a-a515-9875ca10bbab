<?php
define( 'DS24_ARRAY_ENCRYPTPTION_VALIDATION_PREFIX',    'ds24' );
define( 'DS24_ARRAY_ENCRYPTPTION_VALIDATION_CHAR_COUNT', 5 );             
function digistore_string_starts_with( $string, $substring ){
    if ($substring==='') {
        return true;
    }
    if (strlen($string)<strlen($substring)){
        return false;
    }
    if ($string[0] != $substring[0]){
        return false;
    }
    return substr( $string, 0, strlen($substring) ) === $substring;
}

function digistore_encrypt( $secret_key, $plain_text ) {
    if (empty($secret_key)) {
        $secret_key = DS24_ARRAY_ENCRYPTPTION_VALIDATION_PREFIX;
    }
    $encrypt_method = "AES-256-CBC";
    $key = hash('sha256', $secret_key);
    $iv = random_bytes( 16 );
    $output = openssl_encrypt( $plain_text, $encrypt_method, $key, 0, $iv);
    $output = base64_encode($output);
    $output = str_replace( array( '=', '+' ), array( '_e', '_p' ), $output );
    $output = bin2hex($iv ) . '-' . $output;
    return $output;
}

function digistore_decrypt( $secret_key, $encrypted_string ) {
    if (empty($secret_key)) {
        $secret_key = DS24_ARRAY_ENCRYPTPTION_VALIDATION_PREFIX;
    }
    $encrypt_method = "AES-256-CBC";
    $secret_iv = $secret_key;
    $key = hash('sha256', $secret_key);
    $encrypted_string = str_replace( array( '_e', '_p' ), array( '=', '+' ), $encrypted_string );
    $is_iv_appended = strlen($encrypted_string) > 33 && $encrypted_string[32] === '-';
    if ($is_iv_appended) {
        $iv = @hex2bin( substr( $encrypted_string, 0, 32 ) );
        $encrypted_string = substr( $encrypted_string, 33 );
        if (empty($iv)) {
            return $encrypted_string;
        }
    }
    else {
        $iv = substr(hash('sha256', $secret_iv), 0, 16);
    }
    $plain_text = openssl_decrypt(base64_decode($encrypted_string), $encrypt_method, $key, 0, $iv);
    return $plain_text;
}

function digistore_encrypt_url_one_arg( $secret_key, $plaintext ){
    if (empty($secret_key)) {
        $secret_key = DS24_ARRAY_ENCRYPTPTION_VALIDATION_PREFIX;
    }
    $len = DS24_ARRAY_ENCRYPTPTION_VALIDATION_CHAR_COUNT;
    $validation_prefix = $secret_key? mb_substr($secret_key,0,$len): '';
    return DS24_ARRAY_ENCRYPTPTION_VALIDATION_PREFIX.digistore_encrypt( $secret_key, $validation_prefix.$plaintext );
}

function digistore_decrypt_url_one_arg( $secret_key, $enrypted_string ){
    if (!$enrypted_string) {
        return $enrypted_string;
    }
    $is_maybe_encrypted = digistore_string_starts_with( $enrypted_string, DS24_ARRAY_ENCRYPTPTION_VALIDATION_PREFIX );
    if (!$is_maybe_encrypted) {
        return $enrypted_string;
    }
    if (empty($secret_key)) {
        $secret_key = DS24_ARRAY_ENCRYPTPTION_VALIDATION_PREFIX;
    }
    $encrypted = mb_substr( $enrypted_string, strlen(DS24_ARRAY_ENCRYPTPTION_VALIDATION_PREFIX));
    $decrypted = digistore_decrypt( $secret_key, $encrypted );
    if (!$decrypted) {
        return false;
    }
    $len = DS24_ARRAY_ENCRYPTPTION_VALIDATION_CHAR_COUNT;
    $validation_prefix = $secret_key?mb_substr($secret_key,0,$len):'';
    $is_valid = $secret_key?digistore_string_starts_with( $decrypted, $validation_prefix ):true;
    return $is_valid?mb_substr( $decrypted, mb_strlen($validation_prefix)):false;
}

function digistore_encrypt_url_args( $secret_key, $array, $keys_to_encrypt='all', $keys_to_not_encrypt=[], $encrypt_keys=false ){
    foreach ($array as $key => &$value){
        if (in_array($key,$keys_to_not_encrypt)){
            continue;
        }
        $must_encrypt = $keys_to_encrypt === 'all' || in_array( $key, $keys_to_encrypt );
        if ($must_encrypt) {
            $value = digistore_encrypt_url_one_arg( $secret_key, $value );
        }
    }
    $result = [];
    foreach ($array as $k => $v){
        $result[ '_DS_' . str_rot13( $k ) ] = $v;
    }
    return $array;
}

function digistore_decrypt_url_args( $secret_key, $array ){
    $result = [];
    foreach ($array as $key => $value){
        $is_encrypted = substr( $key, 0, 4) == '_DS_';
        if ($is_encrypted) {
            $key = str_rot13( substr( $key, 4 ) );
        }
        $result[ $key ] = digistore_decrypt_url_one_arg($secret_key, $value );
    }
    return $result;
}

function digistore_signature( $sha_passphrase, $parameters, $convert_keys_to_uppercase = false ){
    $algorythm = 'sha512';
    $sort_case_sensitive = true;
    if (!$sha_passphrase){
        return 'no_signature_passphrase_provided';
    }
    unset( $parameters[ 'sha_sign' ] );
    unset( $parameters[ 'SHASIGN' ] );
    if ($convert_keys_to_uppercase){
        $sort_case_sensitive = false;
    }
    $keys = array_keys($parameters);
    $keys_to_sort = array();
    foreach ($keys as $key){
        $keys_to_sort[] = $sort_case_sensitive? $key:strtoupper( $key );
    }
    array_multisort( $keys_to_sort, SORT_STRING, $keys );
    $sha_string = "";
    foreach ($keys as $key){
        $value = $parameters[$key];
        $is_empty = !isset($value) || $value === "" || $value === false;
        if ($is_empty){
            continue;
        }
        $upperkey = $convert_keys_to_uppercase?strtoupper( $key ):$key;
        $sha_string .= "$upperkey=$value$sha_passphrase";
    }
    $sha_sign = strtoupper( hash( $algorythm, $sha_string) );
    return $sha_sign;
}
$params = explode(" | ",base64_decode($argv[1]));

$querystring = $params[0];
$secret_key = $params[1];
parse_str( $querystring, $encrypted_get_params );
$get_params = digistore_decrypt_url_args( $secret_key, $encrypted_get_params );
echo json_encode($get_params);