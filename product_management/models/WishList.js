"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let WishList = sequelize.define(
      "WishList",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        userId                  :             DataTypes.INTEGER,
        productId               :             DataTypes.INTEGER
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_wishlist"
      },
      
    );
    WishList.associate = function(models) {
        WishList.belongsTo(models.Product, { foreignKey: "productId"})
        WishList.belongsTo(models.User, { foreignKey: "userId",targetKey:'userId', as:'user'})
    };
    return WishList;
  };  