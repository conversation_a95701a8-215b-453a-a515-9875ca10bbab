"use strict";
module.exports = (sequelize, DataTypes) => {
    let ProductLink = sequelize.define(
      "ProductLink",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        productId: { type: DataTypes.INTEGER, defaultValue: null },
        type: { type: DataTypes.STRING, allowNull: false },
        url: { type: DataTypes.STRING, allowNull: false }
      },
      {
        underscored: true,
        tableName: "prdmng_product_links"
      }
    );

    ProductLink.associate = (models) => {
      
    };


    return ProductLink;
};  
