"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let UserProduct = sequelize.define("UserProduct", {
        id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false },
        productId: { type: DataTypes.INTEGER, allowNull: false },
        orderId: { type: DataTypes.INTEGER, allowNull: false },
        userId: { type: DataTypes.INTEGER, allowNull: false }
      },
      {
        paranoid: true, underscored: true, tableName: "prdmng_user_products"
      },
      
    );
    UserProduct.associate = function(models) {
      UserProduct.hasMany(models.ProductLink, { foreignKey: "productId", sourceKey: "productId"})
    };
    return UserProduct;
  };  