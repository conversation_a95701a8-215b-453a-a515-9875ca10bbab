"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let Product = sequelize.define(
      "Product",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        dgStoreId             :     {     type    :   DataTypes.STRING    , allowNull: true},
        stock                 :     {     type    :   DataTypes.INTEGER    , allowNull: true,defaultValue:0},
        ///
        categoryId            :     {     type    :   DataTypes.INTEGER   , allowNull: false },
        uuid                  :     {     type    :   DataTypes.UUID      , allowNull: false },
        slug                  :     {     type    :   DataTypes.STRING    , allowNull: true },
        variantId             :     {     type    :   DataTypes.INTEGER   , allowNull: true },
        variationCode         :     {     type    :   DataTypes.STRING    , allowNull: true },
        dimensions            :     {     type    :   DataTypes.JSON      , allowNull:true},
        additionalInformation :     {     type    :   DataTypes.JSON      , allowNull:true},
        tags                  :     {     type    :   DataTypes.JSON      , allowNull:true},
        attachment            :     {     type    :   DataTypes.JSON      , allowNull:true},
        isPublished           :     {     type    :   DataTypes.INTEGER   , allowNull: false ,defaultValue:0},
        isFeatured            :     {     type    :   DataTypes.INTEGER   , allowNull: false ,defaultValue:0},
        combinationsData      :     {     type    :   DataTypes.JSON      , allowNull:true},
        combinationAttribute  :     {     type    :   DataTypes.JSON      , allowNull:true},
        inputAttribute        :     {     type    :   DataTypes.JSON      , allowNull:true},
        similarProducts       :     {     type    :   DataTypes.JSON      , allowNull: true},
        highlights 	: {     type    :   DataTypes.TEXT    , allowNull: true },
        ordering: {type: DataTypes.INTEGER, defaultValue: 1},
        courseId: { type: DataTypes.INTEGER, allowNull: true },
        videoLink: { type: DataTypes.STRING, allowNull: true }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_products"
      }
    );

    Product.associate = (models) => {
        Product.belongsTo(models.Category,          {   foreignKey  :   'categoryId',   as:"category" }),
        Product.hasMany(models.ProductContent,      {   foreignKey  :   'productId',    as:'contents'})
        Product.hasMany(models.ProductLanguage,      {   foreignKey  :   'productId',    as:'productLanguages'})
        Product.hasMany(models.ProductLanguage,      {   foreignKey  :   'productId'})
        Product.hasOne(models.ProductContent,       {   foreignKey  :   'productId',    as:'content'   })
        Product.hasOne(models.ProductContent,       {   foreignKey  :   'productId',    as:'contentttt'   })
        Product.hasMany(models.ProductPrice,        {   foreignKey  :   'productId',    as:'price'}),
        Product.hasMany(models.ProductAttribute,    {   foreignKey  :   'productId',    as:'attribute'})
        Product.hasMany(models.CartProduct,         {   foreignKey  :   'productId',    as:'cart'})
        Product.hasMany(models.WishList,            {   foreignKey  :   'productId'})
        Product.belongsToMany(models.Tag,           {   through: 'prdmng_product_tags', foreignKey: 'productId', otherKey: 'tagId' });
    };

    return Product;
  };  