"use strict";
module.exports = (sequelize, DataTypes) => {
    let Attribute = sequelize.define(
      "Attribute",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        categoryId  :   {type:DataTypes.INTEGER,allowNull:false},
        groupId     :   {type:DataTypes.INTEGER,allowNull:true},
        type        :   {type:DataTypes.INTEGER,allowNull:true},
        isVariation :   {type:DataTypes.INTEGER,defaultValue:0},
        isPaid      :   {type:DataTypes.INTEGER,defaultValue:0},
        addOns      :   {type:DataTypes.INTEGER,allowNull:true}
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_attributes"
      },
      
    );
    Attribute.associate = function(models) {
        Attribute.hasMany(models.AttributeContent, { foreignKey: "attributeId"})
        Attribute.belongsTo(models.Category, { foreignKey: "categoryId"})
        Attribute.belongsTo(models.Group, { foreignKey: "groupId"})
    };
    return Attribute;
  };  