"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let Order = sequelize.define(
      "Order",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
              data: { type: DataTypes.JSON, allowNull: false },
              userId: { type: DataTypes.INTEGER, defaultValue:null},
              orderDetails:{ type: DataTypes.JSON, allowNull: false },
              custom:{ type: DataTypes.JSON, defaultValue:null},
              cart:{ type: DataTypes.JSON, defaultValue:null},
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_orders"
      }
    );

    Order.associate = (models) => {
      //Order.belongsTo(models.Permission, { through: 'rolePermissions', foreignKey: 'roleId', otherKey: 'permissionId' });
      Order.belongsTo(models.User, {  targetKey:"userId",foreignKey: 'userId'});
      Order.hasMany(models.UserProduct, { foreignKey: "orderId" })
    };

    return Order;
  };  