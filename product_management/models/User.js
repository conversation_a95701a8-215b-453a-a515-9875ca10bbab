"use strict";
module.exports = (sequelize, DataTypes) => {
    let User = sequelize.define(
      "User",
      {
        userId            : {
          type          : DataTypes.INTEGER,
          allowNull     : false,
          unique        : true
      },
      firstName         : { 
          type          : DataTypes.STRING,
          defaultValue  : null
      },
      lastName          : {
          type          : DataTypes.STRING,
          defaultValue  : null
      },
      title             : {
          type          : DataTypes.STRING,
          defaultValue  : null
      },
      profilePhotoUrl   : {
          type          : DataTypes.STRING,
          allowNull     : true,
      },
      profilePhotoId    : {
          type          : DataTypes.STRING,
          allowNull     : true,
      },
      gender            :{
        type          : DataTypes.INTEGER,
        allowNull     : true
      },
      userObject: { type: DataTypes.JSON, allowNull: true, defaultValue: null }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_users"
      }
    );
    User.associate = function(models) {
  };
    return User;
};  
