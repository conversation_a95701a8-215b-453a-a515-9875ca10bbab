const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
  let Currency = sequelize.define(
    "Currency",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      currencyCode:
      {
          type      : DataTypes.STRING,
          allowNull : false
      },
      symbol:
      {
          type      : DataTypes.STRING,
          allowNull : false
      },
      name:
      {
          type      : DataTypes.STRING,
          allowNull : false
      }
    },
    {
      paranoid: true,
      underscored: true,
      tableName: "prdmng_currencies"
    }
  );

  Currency.associate = (models) => {

  };

  return Currency;
};