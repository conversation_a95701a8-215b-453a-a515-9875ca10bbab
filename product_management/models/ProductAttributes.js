"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let ProductAttribute = sequelize.define(
      "ProductAttribute",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        productId               :    {   type    :   DataTypes.INTEGER              , allowNull: false },
        attributeId             :    {   type    :   DataTypes.INTEGER              , allowNull: false },
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_product_attributes"
      }
    );

    ProductAttribute.associate = (models) => {
        ProductAttribute.belongsTo(models.Attribute,            {   foreignKey  :   'attributeId'}),
        ProductAttribute.hasMany(models.ProductAttributeContent,{   foreignKey:'productAttributeId', as:'contents'})
        ProductAttribute.hasOne(models.ProductAttributeContent,{   foreignKey:'productAttributeId', as:'content'})
        ProductAttribute.belongsTo(models.Product,              {   foreignKey  :   'productId'})
    };

    return ProductAttribute;
  };  