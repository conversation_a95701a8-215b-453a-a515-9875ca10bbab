"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let Request = sequelize.define(
      "Request",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        axiosObj : { type: DataTypes.JSON, allowNull: false },
        sucess: { type: DataTypes.INTEGER, defaultValue: Constants.STATUS.INACTIVE }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_requests"
      }
    );

    // Role.associate = (models) => {
    //   Role.belongsToMany(models.Permission, { through: 'rolePermissions', foreignKey: 'roleId', otherKey: 'permissionId' });
    //   Role.belongsToMany(models.User, { through: 'user_userRoles', foreignKey: 'roleId', otherKey: 'userId' });
    // };

    return Request;
  };  