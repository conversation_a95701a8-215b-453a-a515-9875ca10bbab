"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let Setting = sequelize.define(
      "Setting",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        key             :   { type: DataTypes.STRING, allowNull: false ,unique:true},
        value           :   {type: DataTypes.STRING, allowNull: true},
        label           :   {type: DataTypes.STRING, allowNull: true},
        type            :   {type:DataTypes.INTEGER,allowNull:true}
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_settings"
      }
    );
    return Setting;
  };  