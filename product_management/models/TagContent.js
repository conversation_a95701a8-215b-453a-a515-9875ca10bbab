"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let TagContent = sequelize.define(
      "TagContent",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        languageId  :   DataTypes.INTEGER,
        tagId       :   DataTypes.INTEGER,
        name        :   DataTypes.STRING
      },
      {
        paranoid: true,
        underscored: true,
        // indexes: [
        //   {type: 'FULLTEXT', name: 'Name', fields: ['name']},
        // ],
        tableName: "prdmng_tag_contents"
      },
      
    );
    TagContent.associate = function(models) {
      TagContent.belongsTo(models.Language, { foreignKey: "languageId"})
    };
    return TagContent;
  };  