"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let AttributeContent = sequelize.define(
      "AttributeContent",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        attributeId               :             {type:DataTypes.INTEGER,allowNull:false},
        name                      :             {type:DataTypes.STRING,allowNull:false},
        languageId                :             {type:DataTypes.INTEGER,allowNull:false}   
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_attribute_contents",
        indexes: [
          //{type: 'FULLTEXT', name: 'UserName', fields: ['first_name']},
          {type: 'FULLTEXT', name: 'AttributeName', fields: ['name']},
        ],
      },
      
    );
    AttributeContent.associate = function(models) {
        //Attribute.hasMany(models.AttributeContent, { foreignKey: "attributeId"})
        //Attribute.belongsTo(models.Category, { foreignKey: "categoryId"})
        //Attribute.belongsTo(models.Group, { foreignKey: "groupId"})
    };
    return AttributeContent;
  };  