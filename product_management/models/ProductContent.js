"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let ProductContent = sequelize.define(
      "ProductContent",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        productId           :    {   type    :   DataTypes.INTEGER      , allowNull: false },
        languageId          :    {   type    :   DataTypes.INTEGER       , allowNull: false },
        name                :    {   type    :   DataTypes.STRING       , allowNull: false },
        descriptionText     :    {   type    :   DataTypes.TEXT         , allowNull: false },
        descriptionHtml     :    {   type    :   DataTypes.TEXT         , allowNull: false },
        longDescriptionText :    {   type    :   DataTypes.TEXT('long')         , allowNull: false },
        longDescriptionHtml :    {   type    :   DataTypes.TEXT('long')         , allowNull: false }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_product_contents"
      }
    );

    ProductContent.associate = (models) => {
        ProductContent.belongsTo(models.Language,       {   foreignKey  :   'languageId'})
        // ProductContent.belongsTo(models.Product,        {   foreignKey  :   'productId'})
    };

    return ProductContent;
  };  