"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let CategoryContent = sequelize.define(
      "CategoryContent",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        languageId  :   DataTypes.INTEGER,
        categoryId  :   DataTypes.INTEGER,
        name        :   DataTypes.STRING
      },
      {
        paranoid: true,
        underscored: true,
        // indexes: [
        //   {type: 'FULLTEXT', name: 'Name', fields: ['name']},
        // ],
        tableName: "prdmng_category_contents"
      },
      
    );
    CategoryContent.associate = function(models) {
        CategoryContent.belongsTo(models.Language, { foreignKey: "languageId"})
    };
    return CategoryContent;
  };  