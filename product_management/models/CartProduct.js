"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let CartProduct = sequelize.define(
      "CartProduct",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        userId                  :             DataTypes.INTEGER,
        productId               :             DataTypes.INTEGER,
        count                   :             DataTypes.INTEGER
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_cart_products"
      },
      
    );
    CartProduct.associate = function(models) {
        CartProduct.belongsTo(models.Product, { foreignKey: "productId"})
        CartProduct.belongsTo(models.User, { foreignKey: "userId",targetKey:'userId', as:'user'})
    };
    return CartProduct;
  };  