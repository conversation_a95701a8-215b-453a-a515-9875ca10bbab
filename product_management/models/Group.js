"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let Group = sequelize.define(
      "Group",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        code                  :             DataTypes.STRING,
        status                :             {type:DataTypes.INTEGER,defaultValue:1},
        isDefault             :              {type:DataTypes.INTEGER,defaultValue:null}       
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_groups"
      },
      
    );
    Group.associate = function(models) {
        Group.hasMany(models.GroupContent, { foreignKey: "groupId"})
    };
    return Group;
  };  