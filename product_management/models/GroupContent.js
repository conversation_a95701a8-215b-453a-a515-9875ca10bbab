"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let GroupContent = sequelize.define(
      "GroupContent",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        groupId               :             {type:DataTypes.INTEGER,allowNull:false},
        name                  :             {type:DataTypes.STRING,allowNull:false},
        languageId            :             {type:DataTypes.INTEGER,allowNull:false}    
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_group_contents"
      },
      
    );
    GroupContent.associate = function(models) {
        GroupContent.belongsTo(models.Group, { foreignKey: "groupId"})
        GroupContent.belongsTo(models.Language,{foreignKey:"languageId"})
    };
    return GroupContent;
  };  