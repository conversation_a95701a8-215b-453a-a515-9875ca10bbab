"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let Category = sequelize.define(
      "Category",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        ordering: {type: DataTypes.INTEGER, defaultValue: 1},
        parentId              :             DataTypes.INTEGER,
        attachment            :             DataTypes.JSON,
        code                  :             DataTypes.STRING,
        status                :             {type:DataTypes.INTEGER,defaultValue:1}
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_categories"
      },
      
    );
    Category.associate = function(models) {
        Category.hasMany(models.CategoryContent, { foreignKey: "categoryId"})
        Category.hasOne(models.Product, { foreignKey: "categoryId", as: "productsInCategory"})
        Category.hasOne(models.CategoryContent, { foreignKey: "categoryId", as:'content'})
    };
    return Category;
  };  