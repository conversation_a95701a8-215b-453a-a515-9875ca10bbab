"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let ProductAttributeContent = sequelize.define(
      "ProductAttributeContent",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        productAttributeId      :    {   type    :   DataTypes.INTEGER              , allowNull: false },
        languageId              :    {   type    :   DataTypes.INTEGER              , allowNull: false },
        value                   :    {   type    :   DataTypes.STRING               , allowNull: true }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_product_attribute_contents"
      }
    );

    ProductAttributeContent.associate = (models) => {
      //  ProductAttributeContent.belongsTo(models.ProductAttribute,           {   foreignKey  :   'productAttributeId'}),
        ProductAttributeContent.belongsTo(models.Language,                   {   foreignKey  :   'languageId'})
    };

    return ProductAttributeContent;
  };  