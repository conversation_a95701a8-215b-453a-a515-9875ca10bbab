"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let Tag = sequelize.define(
      "Tag",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        //parentId              :             DataTypes.INTEGER,
        //attachment            :             DataTypes.JSON,
        code                  :               DataTypes.STRING,
        status                :             {type:DataTypes.INTEGER,defaultValue:1}
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_tags"
      },
      
    );
    Tag.associate = function(models) {
        Tag.hasMany(models.TagContent, { foreignKey: "tagId"})
        Tag.hasOne(models.TagContent, { foreignKey: "tagId", as:'content'})
        Tag.belongsToMany(models.Product, { through: 'prdmng_product_tags', foreignKey: 'tagId', otherKey: 'productId' });

    };
    return Tag;
  };  