"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let ProductPrice = sequelize.define(
      "ProductPrice",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        productId           :    {   type    :   DataTypes.INTEGER          , allowNull: false },
        currencyId          :    {   type    :   DataTypes.INTEGER          , allowNull: false },
        price               :    {   type    :   DataTypes.FLOAT            , allowNull: false }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_product_prices"
      }
    );

    ProductPrice.associate = (models) => {
        ProductPrice.belongsTo(models.Currency,       {   foreignKey  :   'currencyId'}),
        ProductPrice.belongsTo(models.Product,        {   foreignKey  :   'productId'})
    };

    return ProductPrice;
  };  