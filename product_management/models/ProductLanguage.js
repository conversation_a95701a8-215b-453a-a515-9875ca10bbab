"use strict";

const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let ProductLanguage = sequelize.define(
      "ProductLanguage",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        status: { type: DataTypes.INTEGER, defaultValue: 1 },
        productId: { type: DataTypes.INTEGER, defaultValue: null },
        languageId: { type: DataTypes.STRING, allowNull: false },
        languageCode: { type: DataTypes.STRING, allowNull: false }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "prdmng_product_languages"
      }
    );

    ProductLanguage.associate = (models) => {
      // Language.belongsToMany(models.User, { through: 'prdmng_languages'})
    };


    return ProductLanguage;
};  
