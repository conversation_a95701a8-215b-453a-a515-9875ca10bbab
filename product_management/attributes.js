module.exports={
    // Products Attributes
    products:()=>{
        return ['id','categoryId','uuid','slug','variantId','dimensions','additionalInformation','tags','attachment','isPublished','isFeatured','dgStoreId','similarProducts','createdAt']
    },
    //Product Price Attribute
    producePrice:()=>{
        return ['id','productId','currencyId','price']
    },
    // Currency Attributes
    currency:()=>{
        return['id','currencyCode','symbol','name']
    },
    // Products Attributes attributes
    productAttribute:()=>{
        return['productId','attributeId']
    },
    // Attributes attribute model
    attribute:()=>{
        return ['id','categoryId','type','isVariation','addOns']
    },
    // Attribute Content models
     attributeContent:()=>{
        let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
        return [
        [Sequelize.literal(`IF(AttributeContents.name is not null,AttributeContents.name, (select name from ${Models.AttributeContent.tableName} where language_id = ${defaultlanguageId}  and attribute_id = Attribute.id))`),'name'],
        [Sequelize.literal(`IF(AttributeContents.language_id is not null,AttributeContents.language_id, (select language_id from ${Models.AttributeContent.tableName} where ${defaultlanguageId}  and attribute_id = Attribute.id))`),'languageId'],
        [Sequelize.literal(`IF(AttributeContents.attribute_id is not null,AttributeContents.attribute_id, (select attribute_id from ${Models.AttributeContent.tableName} where ${defaultlanguageId}  and attribute_id = Attribute.id))`),'attributeId'],
        //[Sequelize.literal(`IF(AttributeContents.created_at,AttributeContents.created_at, (select created_at from ${Models.AttributeContent.tableName} where ${defaultlanguageId}  and attribute_id = Attribute.id))`),'createdAt'],
        //[Sequelize.literal(`IF(AttributeContents.updated_at,AttributeContents.updated_at, (select updated_at from ${Models.AttributeContent.tableName} where ${defaultlanguageId}  and attribute_id = Attribute.id))`),'updatedAt']
        ]
    },
    // Product Content Models

    productsContent:()=>{
        let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
        // if(flag === true) {
        //     return ["id", "name", "descriptionText", "descriptionHtml", "longDescriptionText", "longDescriptionHtml"]
        // }
        return [
        [Sequelize.literal(`IF(content.id is not null,content.id , (select id from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id limit 1)) `),'id'],
        [Sequelize.literal(`IF(content.name is not null,content.name , (select name from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id limit 1)) `),'name'],
        [Sequelize.literal(`IF(content.description_text is not null,content.description_text, (select description_text from ${Models.ProductContent.tableName} where ${defaultlanguageId}  and product_id = Product.id limit 1))`),'descriptionText'],
        [Sequelize.literal(`IF(content.description_html is not null,content.description_html, (select description_html from ${Models.ProductContent.tableName} where ${defaultlanguageId}  and product_id = Product.id limit 1))`),'descriptionHtml'],
        [Sequelize.literal(`IF(content.long_description_text is not null,content.long_description_text, (select long_description_text from ${Models.ProductContent.tableName} where ${defaultlanguageId}  and product_id = Product.id limit 1))`),'longDescriptionText'],
        [Sequelize.literal(`IF(content.long_description_html is not null,content.long_description_html, (select long_description_html from ${Models.ProductContent.tableName} where ${defaultlanguageId}  and product_id = Product.id limit 1))`),'longDescriptionHtml']
        ]
    },   
    productsSearchContent:()=>{
        let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
        // if(flag === true) {
        //     return ["id", "name", "descriptionText", "descriptionHtml", "longDescriptionText", "longDescriptionHtml"]
        // }
        return [
        // [Sequelize.literal(`IF(content.id is not null,content.id , (select id from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id limit 1)) `),'id'],
        [Sequelize.literal(`IF(content.name is not null,content.name , (select name from ${Models.ProductContent.tableName} where language_id = ${defaultlanguageId}  and product_id = Product.id limit 1)) `),'name'],
        [Sequelize.literal(`IF(content.description_text is not null,content.description_text, (select description_text from ${Models.ProductContent.tableName} where ${defaultlanguageId}  and product_id = Product.id limit 1))`),'descriptionText'],
        [Sequelize.literal(`IF(content.description_html is not null,content.description_html, (select description_html from ${Models.ProductContent.tableName} where ${defaultlanguageId}  and product_id = Product.id limit 1))`),'descriptionHtml'],
        [Sequelize.literal(`IF(content.long_description_text is not null,content.long_description_text, (select long_description_text from ${Models.ProductContent.tableName} where ${defaultlanguageId}  and product_id = Product.id limit 1))`),'longDescriptionText'],
        [Sequelize.literal(`IF(content.long_description_html is not null,content.long_description_html, (select long_description_html from ${Models.ProductContent.tableName} where ${defaultlanguageId}  and product_id = Product.id limit 1))`),'longDescriptionHtml']
        ]
    },   
    // Products Attribute Contents 
    productAttributeContents:()=>{
        let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
        return [
            [Sequelize.literal(`IF( \`attribute->Attribute->AttributeContents\`.name is not null,\`attribute->Attribute->AttributeContents\`.name, (select name from ${Models.AttributeContent.tableName} where language_id = ${defaultlanguageId}  and attribute_id = attribute.attribute_id limit 1))`),'name'],
            [Sequelize.literal(`IF( \`attribute->Attribute->AttributeContents\`.language_id is not null,\`attribute->Attribute->AttributeContents\`.language_id, (select language_id from ${Models.AttributeContent.tableName} where language_id = ${defaultlanguageId}  and attribute_id = attribute.attribute_id limit 1))`),'language_id'],
            [Sequelize.literal(`IF( \`attribute->Attribute->AttributeContents\`.attribute_id is not null,\`attribute->Attribute->AttributeContents\`.attribute_id, (select attribute_id from ${Models.AttributeContent.tableName} where language_id = ${defaultlanguageId}  and attribute_id = attribute.attribute_id limit 1))`),'attribute_id'],
        ]
    },

    productAttributeValue:()=>{
        let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
        return [
        [Sequelize.literal(`IF( \`attribute->content\`.id is not null,\`attribute->content\`.id, (select id from ${Models.ProductAttributeContent.tableName} where language_id = ${defaultlanguageId}  and product_attribute_id = attribute.id limit 1))`),'id'],
        [Sequelize.literal(`IF( \`attribute->content\`.value is not null,\`attribute->content\`.value, (select value from ${Models.ProductAttributeContent.tableName} where language_id = ${defaultlanguageId}  and product_attribute_id = attribute.id limit 1))`),'value'],
        [Sequelize.literal(`IF( \`attribute->content\`.language_id is not null,\`attribute->content\`.language_id, (select language_id from ${Models.ProductAttributeContent.tableName} where language_id = ${defaultlanguageId}  and product_attribute_id = attribute.id limit 1))`),'language_id'],
        [Sequelize.literal(`IF( \`attribute->content\`.product_attribute_id is not null,\`attribute->content\`.product_attribute_id, (select product_attribute_id from ${Models.ProductAttributeContent.tableName} where language_id = ${defaultlanguageId}  and product_attribute_id = attribute.id limit 1))`),'product_attribute_id'],
        //[Sequelize.literal(`IF( \`attribute->content\`.product_attribute_id,\`attribute->content\`.value, (select value from ${Models.AttributeContent.tableName} where language_id = ${defaultlanguageId}  and product_attribute_id = attribute.attribute_id))`),'value']
        ]
    },

     attributeForCategory:()=>{
        let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
        return [
        [Sequelize.literal(`IF(\`category->content\`.name is not null,\`category->content\`.name, (select name from ${Models.CategoryContent.tableName} where language_id = ${defaultlanguageId}  and category_id = category.id limit 1))`),'name'],
        [Sequelize.literal(`IF(\`category->content\`.language_id is not null,\`category->content\`.language_id, (select language_id from ${Models.CategoryContent.tableName} where ${defaultlanguageId}  and category_id = category.id limit 1))`),'languageId'],
        [Sequelize.literal(`IF(\`category->content\`.category_id is not null,\`category->content\`.category_id, (select category_id from ${Models.CategoryContent.tableName} where ${defaultlanguageId}  and category_id = category.id limit 1))`),'categoryId'],
       // [Sequelize.literal(`IF(\`category->CategoryContents\`.created_at,\`category->CategoryContents\`.created_at, (select created_at from ${Models.CategoryContent.tableName} where ${defaultlanguageId}  and category_id = category.id))`),'createdAt'],
        //[Sequelize.literal(`IF(\`category->CategoryContents\`.updated_at,\`category->CategoryContents\`.updated_at, (select updated_at from ${Models.CategoryContent.tableName} where ${defaultlanguageId}  and category_id = category.id))`),'updatedAt']
        ]
       
    },
    
    productCategory:()=>{
            return ['attachment','code','id']
    },
    productTag:()=>{
        return['id']
    },
    productTagContent:()=>{
        let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
        return [
        [Sequelize.literal(`IF(\`Tags->content\`.name is not null,\`Tags->content\`.name, (select name from ${Models.TagContent.tableName} where language_id = ${defaultlanguageId}  and tag_id = Tags.id limit 1))`),'name'],
        [Sequelize.literal(`IF(\`Tags->content\`.language_id is not null,\`Tags->content\`.language_id, (select language_id from ${Models.TagContent.tableName} where ${defaultlanguageId}  and tag_id = Tags.id limit 1))`),'languageId'],
        [Sequelize.literal(`IF(\`Tags->content\`.tag_id is not null,\`Tags->content\`.tag_id, (select tag_id from ${Models.TagContent.tableName} where ${defaultlanguageId}  and tag_id = Tags.id limit 1))`),'tagId'],
        ]
    },
     attributeForAttributes:()=>{
        let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
        return [
        [Sequelize.literal(`IF(AttributeContents.name is not null,AttributeContents.name, (select name from ${Models.AttributeContent.tableName} where language_id = ${defaultlanguageId}  and attribute_id = Attribute.id limit 1))`),'name'],
        [Sequelize.literal(`IF(AttributeContents.language_id is not null,AttributeContents.language_id, (select language_id from ${Models.AttributeContent.tableName} where ${defaultlanguageId}  and attribute_id = Attribute.id limit 1))`),'languageId'],
        [Sequelize.literal(`IF(AttributeContents.attribute_id is not null,AttributeContents.attribute_id, (select attribute_id from ${Models.AttributeContent.tableName} where ${defaultlanguageId}  and attribute_id = Attribute.id limit 1))`),'attributeId'],
        [Sequelize.literal(`IF(AttributeContents.created_at is not null,AttributeContents.created_at, (select created_at from ${Models.AttributeContent.tableName} where ${defaultlanguageId}  and attribute_id = Attribute.id limit 1))`),'createdAt'],
        [Sequelize.literal(`IF(AttributeContents.updated_at is not null,AttributeContents.updated_at, (select updated_at from ${Models.AttributeContent.tableName} where ${defaultlanguageId}  and attribute_id = Attribute.id limit 1))`),'updatedAt']
        ]
    },
    cartProductCategoryContent:()=>{
        let defaultlanguageId=process.env.DEFAULT_LANGUANGE_CODE_ID
        return [
        [Sequelize.literal(`IF(\`Product->category->content\`.name is not null,\`Product->category->content\`.name, (select name from ${Models.CategoryContent.tableName} where language_id = ${defaultlanguageId}  and category_id = \`Product->category\`.id limit 1))`),'name'],
        [Sequelize.literal(`IF(\`Product->category->content\`.language_id is not null,\`Product->category->content\`.language_id, (select language_id from ${Models.CategoryContent.tableName} where ${defaultlanguageId}  and category_id = \`Product->category\`.id limit 1))`),'languageId'],
        [Sequelize.literal(`IF(\`Product->category->content\`.category_id is not null,\`Product->category->content\`.category_id, (select category_id from ${Models.CategoryContent.tableName} where ${defaultlanguageId}  and category_id = \`Product->category\`.id limit 1))`),'categoryId'],
       // [Sequelize.literal(`IF(\`category->CategoryContents\`.created_at,\`category->CategoryContents\`.created_at, (select created_at from ${Models.CategoryContent.tableName} where ${defaultlanguageId}  and category_id = category.id))`),'createdAt'],
        //[Sequelize.literal(`IF(\`category->CategoryContents\`.updated_at,\`category->CategoryContents\`.updated_at, (select updated_at from ${Models.CategoryContent.tableName} where ${defaultlanguageId}  and category_id = category.id))`),'updatedAt']
        ]
    }
}





