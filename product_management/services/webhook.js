module.exports = {
    
    updateAttachmentService : async(reqPayload, transaction) => {
        
        const url = Constants.URL.ATTACHMENT_UPDATE;

        const axiosObj = {
            url    : url,
            method : "PATCH",
            data   : reqPayload
        };

        await 
            Axios(axiosObj)
            .then()
            .catch(async () => {
                await Models.Request.create({
                    axiosObj : axiosObj,
                    sucess   : 0
                }, {transaction});
            });
    },
    createUserInAllService:async(reqPayload)=>{
        const url = Constants.URL.CREATE_USER;
        const axiosObj = {
            url    : url,
            method : "POST",
            data   : reqPayload
        };
    //   console.log('axiosObj',axiosObj)
        let res=await 
            Axios(axiosObj)
            .then((res)=>{
                console.log(res)
                return res
            })
            .catch(async (error) => {
                console.log(error)
                throw 'ERROR_IN_USER_CREATION'
            });
            // console.log('res',res)
            return res?.data
    }
}