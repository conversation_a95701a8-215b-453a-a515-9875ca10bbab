describe('User Onboarding', () => { // Server Init Test case ------------------------------------------
    it('Initial Test', () => {
        cy.request('/').then((res) => { // cy.log(res);
            expect(res.body.Message).to.equal("Welcome User");
        })
    })


    // Login Test Case -------------------------------------------------
    let token,id
    it('User Sign up By Phone Test', () => {
        let payload = {
            "loginType": 5,
            "countryCode": "+65",
            "phoneNumber": "90909",
            "countryCodeValue":"IN"
        }
        let injectionObject = {
            method: 'POST',
            url: '/user/signup',
            body: payload
        };
        cy.request(injectionObject).then((res) => {
            expect(res.body.message).to.equal("OTP sent sucessfully");
            token = res.body.responseData.token
            
            //verify OTP 
           let  body={
                token,
                code:'99999',
                type:'mobile'
            }
            injectionObject = {
                method: 'POST',
                url: '/user/verifyCode',
                body, 
                failOnStatusCode: false
            };
            cy.request(injectionObject).then(res=>{
                expect(res.body.message).to.equal("Incorrect OTP provided");
                let  body={
                    token,
                    code:'9999',
                    type:'mobile',
                    //roles:[2,3]
                }
                injectionObject = {
                    method: 'POST',
                    url: '/user/verifyCode',
                    body
                };
                cy.request(injectionObject).then(res=>{
                    expect(res.body.message).to.equal("Sucessfull login");
                    //cy.log('token',res.body.responseData.token)
                              let token = res.body.responseData.token
                              let  id= res.body.responseData.User.id
                                let body = {
                                        "userId": id,
                                        "dob": "2015-03-25",
                                        "name": "John",
                                        "nric": "test1234",
                                        "buildingName": "1456",
                                        "street": "Mart Streer",
                                        "city": "Washintan DC",
                                        "zipCode": " 123455",
                                        "gender": "Male"
                                        }
                                    
                                    let injectionObject = {
                                    method: 'POST',
                                    url: '/user/profile',
                                    body,
                                     failOnStatusCode: false,
                                    headers: {
                                    authorization:token,
                                   
                                    },
                                    };
                                cy.request(injectionObject).then(res=>{
                                    expect(res.body.message).to.equal("Profile created successfully");
                                    body={
                                        "name": "John",
                                        "userId": id
                                    }
                                    let injectionObject = {
                                    method: 'PATCH',
                                    url: '/user/profile',
                                    body,
                                    headers: {
                                    authorization:token
                                    },
                                    };
                                    cy.request(injectionObject).then(res=>{
                                        expect(res.body.message).to.equal("Sucessfully updated");
                                    })
                                })
                })
            })
        })
    })



})
