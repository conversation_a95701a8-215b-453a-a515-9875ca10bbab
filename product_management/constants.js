
module.exports = {
    URL:{
            ATTACHMENT_UPDATE       :   `${process.env.ATTACHMENT_DOMAIN}/attachment/update`,
            SEND_EMAIL              :   `${process.env.EMAIL_SERVICE}/email/send`,
            CREATE_USER             :   `${process.env.USER_SERVICE}/create-dg-user`
    },
    STATUS:{
        INACTIVE    :   0,
        ACTIVE      :   1
    },
    PAGINATION_LIMIT        :   20,
    MAX_PAGINATION_LIMIT    :   20,
    CRON:[
        {
            name: 'everyHourCron',
            time: '0 * * * *',
            timezone: 'America/Danmarkshavn',
            request: {
            method: 'GET',
                url: '/cron/everyHourCron'
            },
            onComplete: async (res) => {
                await Common.retryRequests();
                console.log('------------Cron Job Executed ( Every Hour ) -----------');
            }
        },
        {
            name: 'everyDayCron',
            time: '0 0 * * *',
            timezone: 'America/Danmarkshavn',
            request: {
            method: 'GET',
                url: '/cron/everyDayCron'
            },
            onComplete: (res) => {
                console.log('------------Cron Job Executed ( Every Day ) -----------');
            }
        },
        {
            name: 'everyMonthCron',
            time: '0 0 1 * *',
            timezone: 'America/Danmarkshavn',
            request: {
            method: 'GET',
                url: '/cron/everyMonthCron'
            },
            onComplete: (res) => {

                console.log('------------Cron Job Executed ( Every Month ) -----------');
            }
        },
        {
            name: 'everyYearCron',
            time: '0 0 1 1 *',
            timezone: 'America/Danmarkshavn',
            request: {
            method: 'GET',
                url: '/cron/everyYearCron'
            },
            onComplete: (res) => {

                console.log('------------Cron Job Executed ( Every Year ) -----------');
            }
        },

    ]
}