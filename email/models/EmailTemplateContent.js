"use strict";

const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let EmailTemplateContent = sequelize.define(
      "EmailTemplateContent",
      {
        id: {
          primaryKey: true,
          allowNull: false,
          autoIncrement: true,
          type: DataTypes.INTEGER,
        },
        languageId: { type: DataTypes.INTEGER, allowNull: false, unique: 'uniqueLanguage' },
        emailTemplateId: { type: DataTypes.INTEGER, allowNull: false, unique: 'uniqueLanguage' },
        content: { type: DataTypes.TEXT, allowNull: false},
        subject: { type: DataTypes.TEXT, allowNull: false},
        replacements: { type: DataTypes.TEXT, defaultValue: null},
        uuid: { type: DataTypes.UUID, allowNull: false, defaultValue: DataTypes.UUIDV4 },
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "email_email_template_contents"
      }
    );

    EmailTemplateContent.associate = (models) => {
        EmailTemplateContent.belongsTo(models.Language, { foreignKey: "languageId" });
        EmailTemplateContent.belongsTo(models.EmailTemplate, { foreignKey: "emailTemplateId" });
    };

    return EmailTemplateContent;
}; 