"use strict";

const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let UserNotifyEmail = sequelize.define(
      "UserNotifyEmail",
      {
        id: {
          primaryKey: true,
          allowNull: false,
          autoIncrement: true,
          type: DataTypes.INTEGER,
        },
        email:            { type: DataTypes.STRING, allowNull: false },
        type:             { type: DataTypes.STRING, defaultValue: 'COMING_SOON' },
        emailTemplateId:  { type: DataTypes.INTEGER, allowNull: false },
        status:           { type: DataTypes.INTEGER, defaultValue: Constants.STATUS.ACTIVE },
        uuid:             { type: DataTypes.UUID, allowNull: false, defaultValue: DataTypes.UUIDV4 },
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "email_user_notify_emails"
      }
    );

    UserNotifyEmail.associate = (models) => {
        UserNotifyEmail.belongsTo(models.EmailTemplate, { foreignKey: "emailTemplateId" });
    };

    return UserNotifyEmail;
}; 