"use strict";

const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let Email = sequelize.define(
      "Email",
      {
        id: {
          primaryKey: true,
          allowNull: false,
          autoIncrement: true,
          type: DataTypes.INTEGER,
        },
        userId: { type: DataTypes.INTEGER, allowNull: false },
        emailTemplateId: { type: DataTypes.INTEGER, allowNull: false },
        content: { type: DataTypes.TEXT, allowNull: false },
        userObj: { type: DataTypes.JSON, defaultValue: null },
        attachments: { type: DataTypes.TEXT, defaultValue: null},
        status: { type: DataTypes.INTEGER, defaultValue: Constants.STATUS.INACTIVE },
        uuid: { type: DataTypes.UUID, allowNull: false, defaultValue: DataTypes.UUIDV4 },
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "email_emails"
      }
    );

    Email.associate = (models) => {
        Email.belongsTo(models.EmailTemplate, { foreignKey: "emailTemplateId" });
    };

    return Email;
}; 