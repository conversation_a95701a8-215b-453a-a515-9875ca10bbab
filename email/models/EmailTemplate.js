"use strict";

const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let EmailTemplate = sequelize.define(
      "EmailTemplate",
      {
        id: {
          primaryKey: true,
          allowNull: false,
          autoIncrement: true,
          type: DataTypes.INTEGER,
        },
        status: { type: DataTypes.INTEGER, defaultValue: null },
        code: { type: DataTypes.STRING, allowNull: false, unique: 'emailTemplate' },
        uuid: { type: DataTypes.UUID, allowNull: false, defaultValue: DataTypes.UUIDV4 },
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "email_email_templates"
      }
    );

    EmailTemplate.associate = (models) => {
        EmailTemplate.hasMany(models.Email, { foreignKey: "emailTemplateId", onDelete: 'cascade', hooks:true });
        EmailTemplate.hasMany(models.EmailTemplateContent, { foreignKey: "emailTemplateId", onDelete: 'cascade' });
        EmailTemplate.hasMany(models.EmailTemplateContent, { foreignKey: "emailTemplateId", onDelete: 'cascade', hooks:true, as: "mainContent"  });
        EmailTemplate.hasMany(models.EmailTemplateContent, { foreignKey: "emailTemplateId", onDelete: 'cascade', hooks:true, as: "defaultContent" });
    };
    
    return EmailTemplate;
}; 