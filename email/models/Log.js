"use strict";

const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let Log = sequelize.define(
      "Log",
      {
        id: {
          primaryKey: true,
          allowNull: false,
          autoIncrement: true,
          type: DataTypes.INTEGER,
        },
        recipients      : { type: DataTypes.JSON,   defaultValue: null},
        from            : { type: DataTypes.JSON,   defaultValue: null},
        cc              : { type: DataTypes.JSON,   defaultValue: null},
        bcc             : { type: DataTypes.JSON,   defaultValue: null},
        subject         : { type: DataTypes.STRING, defaultValue: null},
        content         : { type: DataTypes.TEXT,   defaultValue:null },
        replacements    : { type: DataTypes.JSON,   defaultValue: null},
        attachments     : { type: DataTypes.JSON,   defaultValue: null},
        language        : { type: DataTypes.STRING, defaultValue: null},
        template        : { type: DataTypes.STRING, defaultValue: null},
        priority        : { type: DataTypes.STRING, defaultValue: null},
        status          : { type: DataTypes.INTEGER, defaultValue: null},
        responseId      : { type: DataTypes.STRING, defaultValue: null }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "email_logs"
      }
    );

    Log.associate = (models) => {
    };
    return Log;
}; 