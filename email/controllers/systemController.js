exports.initializeSystem = async (req,h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const emailExists = await Models.EmailTemplate.findOne({});
        if(emailExists) {
            await transaction.rollback();
            return h.response({success:false,message:req.i18n.__('EMAIL_SYSTEM_ALREADY_INITIALIZED'),responseData:{}}).code(400);
        }

        // --------------------------------------------- ADDING LANGUAGE ------------------------------------------
        await Models.Language.bulkCreate([
            {name:"English",status:Constants.STATUS.ACTIVE,code:'en',isDefault:Constants.STATUS.ACTIVE},
        ]);


        // --------------------------------------------- CREATING EMAIL TEMPLATES ------------------------------------------
        await Models.EmailTemplate.bulkCreate([
            {code:'SIGNUP_PROCESS',status:Constants.STATUS.ACTIVE},
            {code:'SIGNUP_SUCCESS',status:Constants.STATUS.ACTIVE},
            {code:'SIGNUP_INVITATION',status:Constants.STATUS.ACTIVE},
            {code:'RESEND_EMAIL_TOKEN',status:Constants.STATUS.ACTIVE},
            {code:'RESET_PASSWORD',status:Constants.STATUS.ACTIVE},
            {code:'COMING_SOON',status:Constants.STATUS.ACTIVE},
        ],{transaction:transaction});


        // --------------------------------------------- CREATING EMAIL TEMPLATE CONTENT ------------------------------------------
        await Models.EmailTemplateContent.bulkCreate([
            {emailTemplateId:1,languageId:1,content:'<div><h3>Hi! Your Verification Code is {{code}} </h3><br> Or <br> Click the below URL to verify email <br> {{domain}}{{token}}</div>',subject:'Verification Code',replacements:'code,domain,token'},
            {emailTemplateId:2,languageId:1,content:'<div><h3>Hi User! Miranda Welcomes You! Please visit the below URL to know more about us! <br> {{domain}}</h3></div>',subject:'Welcome To KINN',replacements:'domain'},
            {emailTemplateId:3,languageId:1,content:'<div><h3>You have been Invited for care of a Pet. Please visit {{domain}} and get started with KINN </h3></div>',subject:'Signup Invitation',replacements:'domain'},
            {emailTemplateId:4,languageId:1,content:'<div><h3>Hi! Your Verification Code is {{code}} </h3><br> Or <br> Click the below URL to verify email <br> {{domain}}{{token}}</div>',subject:'Verification Code',replacements:'code,domain,token'},
            {emailTemplateId:5,languageId:1,content:'<div><h3>Your Password Reset Code is {{code}} </h3></div>',subject:'Password Reset Code',replacements:'code'},
            {emailTemplateId:6,languageId:1,content:'<div><h3>We thank you from the core of our heart for choosing us!<br> We will be in action soon. </h3></div>',subject:'Coming Soon',replacements:''},
        ],{transaction:transaction});

        await transaction.commit();
        return h.response({success:false,message:req.i18n.__('EMAIL_MANAGEMENT_INITIALIZED_SUCCESSFULLY'),responseData:{}}).code(200);
    } catch (error) {
        console.log(error);
        await transaction.rollback();
        return h.response(Common.generateError(req,500,'SOMETHING_WENT_WRONG'),error).code(500);
    }
}