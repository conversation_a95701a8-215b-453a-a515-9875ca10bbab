const client = require("@mailchimp/mailchimp_marketing");
const constants = require("../constants");
const mailchimp = require("@mailchimp/mailchimp_transactional")(constants.MAILCHIMP.MANDRIL_KEY);

client.setConfig({
  apiKey: constants.MAILCHIMP.API_KEY,
  server: constants.MAILCHIMP.SERVER_PREFIX
});

exports.createEmailTemplate = async (req, h) => {
  const transaction = await Models.sequelize.transaction();
  try {
    let languageId = LanguageIds[LanguageCodes.indexOf(req.headers.language)];
    let languageCode = LanguageCodes.indexOf(req.headers.language);

    const { code, content, subject, replacements } = req.payload;
    let defaultDefined = await Models.EmailTemplate.findOne({ where: { code } });
    let defaultLanguage = process.env.DEFAULT_LANGUANGE_CODE_ID;

    let doExists = await Models.EmailTemplate.findOne({
      where: { code },
      include: [{ model: Models.EmailTemplateContent, where: { language_id: languageId } }]
    });
    if (!doExists) {
      let emailTemplate = {};
      if (defaultDefined) {
        let emailTemplateContentData = await Models.EmailTemplateContent.create(
          {
            emailTemplateId: defaultDefined.id,
            languageId,
            content,
            subject,
            replacements
          },
          { transaction: transaction }
        );
        emailTemplate = defaultDefined;
        emailTemplate.dataValues.EmailTemplateContents = [emailTemplateContentData];
      } else {
        let emailTemplateContent = [];
        if (!defaultDefined && defaultLanguage != languageId)
          emailTemplateContent.push({
            languageId: defaultLanguage,
            content,
            subject,
            replacements
          });
        emailTemplateContent.push({ languageId, content, subject, replacements });
        emailTemplate = await Models.EmailTemplate.create(
          {
            code,
            status: Constants.STATUS.ACTIVE,
            EmailTemplateContents: emailTemplateContent
          },
          { transaction: transaction, include: [{ model: Models.EmailTemplateContent }] }
        );
      }
      if (emailTemplate) {
        await transaction.commit();
        return h
          .response({
            success: true,
            message: req.i18n.__("EMAIL_TEMPLATE_CREATED_SUCCESSFULLY"),
            responseData: { createdEmailTemplate: emailTemplate }
          })
          .code(201);
      } else {
        await transaction.rollback();
        return h
          .response(Common.generateError(req, 400, "ERROR_WHILE_CREATING_EMAIL_TEMPLATE", null))
          .code(400);
      }
    } else {
      await transaction.rollback();
      return h
        .response(Common.generateError(req, 400, "EMAIL_TEMPLATE_ALREADY_EXISTS", null))
        .code(400);
    }
  } catch (error) {
    console.log(error);
    await transaction.rollback();
    return h.response(Common.generateError(req, 500, "SOMETHING_WENT_WRONG", error)).code(500);
  }
};

exports.updateEmailTemplate = async (req, h) => {
  const transaction = await Models.sequelize.transaction();
  try {
    let languageId = LanguageIds[LanguageCodes.indexOf(req.headers.language)];
    let languageCode = LanguageCodes.indexOf(req.headers.language);

    const { subject, content, replacements } = req.payload;

    const emailTemplateId = req.payload.emailTemplateId;
    let doExists = await Models.EmailTemplate.findOne({
      where: { uuid: emailTemplateId },
      include: [{ model: Models.EmailTemplateContent, where: { languageId } }]
    });
    if (!doExists) {
      await transaction.rollback();
      return h.response(Common.generateError(req, 400, "EMAIL_TEMPLATE_NOT_FOUND", null)).code(400);
    }

    const updatedEmailTemplate = await doExists.EmailTemplateContents[0].update(
      { content, subject, replacements },
      { transaction: transaction }
    );
    await transaction.commit();
    return h
      .response({
        success: true,
        message: req.i18n.__("EMAIL_TEMPLATE_UPDATED_SUCCESSFULLY"),
        responseData: { updatedEmailTemplate }
      })
      .code(200);
  } catch (error) {
    console.log(error);
    await transaction.rollback();
    return h.response(Common.generateError(req, 500, "SOMETHING_WENT_WRONG", error)).code(500);
  }
};

exports.deleteEmailTemplate = async (req, h) => {
  const transaction = await Models.sequelize.transaction();
  try {
    const emailTemplateId = req.payload.emailTemplateId;
    let emailTemplateExists = await Models.EmailTemplate.findOne({
      where: { uuid: emailTemplateId }
    });
    if (!emailTemplateExists) {
      await transaction.rollback();
      return h.response(Common.generateError(req, 400, "EMAIL_TEMPLATE_NOT_FOUND", null)).code(400);
    }

    let deletedEmailTemplate = await emailTemplateExists.destroy({
      where: { uuid: emailTemplateId },
      transaction: transaction
    });
    await Models.EmailTemplateContent.destroy({
      where: { emailTemplateId: deletedEmailTemplate.id },
      transaction: transaction
    });
    await transaction.commit();
    return h
      .response({
        success: true,
        message: req.i18n.__("EMAIL_TEMPLATE_DELETED_SUCCESSFULLY"),
        responseData: { deletedEmailTemplate }
      })
      .code(200);
  } catch (error) {
    console.log(error);
    await transaction.rollback();
    return h.response(Common.generateError(req, 500, "SOMETHING_WENT_WRONG", error)).code(500);
  }
};

exports.listEmailTemplates = async (req, h) => {
  try {
    const limit =
      req.query.limit !== null
        ? req.query.limit > Constants.MAX_PAGINATION_LIMIT
          ? Constants.MAX_PAGINATION_LIMIT
          : req.query.limit
        : Constants.PAGINATION_LIMIT;
    const offset = (req.query.pageNumber - 1) * limit;
    const orderByValue = req.query.orderByValue;
    const orderByParameter = req.query.orderByParameter;
    let requestedLanguage = await Models.Language.findOne({
      where: { code: req.headers.language }
    });
    let defaultLanguage = await Models.Language.findOne({
      where: { code: process.env.DEFAULT_LANGUANGE_CODE }
    });
    if (!requestedLanguage || !defaultLanguage) {
      return h
        .response(
          Common.generateError(req, 400, "REQUESTED_LANGUAGE_OR_DEFAULT_LANGUAGE_NOT_FOUND", null)
        )
        .code(400);
    }
    let where = {};
    if (req.query.status !== null) where = { ...where, status: req.query.status };
    let options = {
      where,
      order: [[orderByParameter, orderByValue]],
      distinct: true,
      include: [
        {
          model: Models.EmailTemplateContent,
          as: "mainContent",
          where: { languageId: requestedLanguage.id },
          required: false
        },
        {
          model: Models.EmailTemplateContent,
          as: "defaultContent",
          where: { languageId: defaultLanguage.id },
          required: false
        }
      ]
    };
    if (req.query.pageNumber !== null) options = { ...options, limit, offset };
    const emailTemplates = await Models.EmailTemplate.findAndCountAll(options);
    const totalPages = await Common.getTotalPages(emailTemplates.count, limit);
    const responseData = {
      totalPages,
      perPage: limit,
      totalRecords: emailTemplates.count,
      emailTemplates: emailTemplates.rows,
      baseUrl: process.env.NODE_SERVER_PUBLIC_API
    };
    return h
      .response({
        success: true,
        message: req.i18n.__("REQUEST_SUCCESSFUL"),
        responseData: responseData
      })
      .code(200);
  } catch (error) {
    console.log(error);
    return h.response(Common.generateError(req, 500, "SOMETHING_WENT_WRONG", error)).code(500);
  }
};

exports.getEmailTemplateDetails = async (req, h) => {
  try {
    let requestedLanguage = await Models.Language.findOne({
      where: { code: req.headers.language }
    });
    let defaultLanguage = await Models.Language.findOne({
      where: { code: process.env.DEFAULT_LANGUANGE_CODE }
    });
    if (!requestedLanguage || !defaultLanguage) {
      return h
        .response(
          Common.generateError(req, 400, "REQUESTED_LANGUAGE_OR_DEFAULT_LANGUAGE_NOT_FOUND", null)
        )
        .code(400);
    }
    const emailTemplateId = req.params.emailTemplateId;
    let responseData = await Models.EmailTemplate.findOne({
      where: { uuid: emailTemplateId },
      include: [
        {
          model: Models.EmailTemplateContent,
          as: "mainContent",
          where: { languageId: requestedLanguage.id },
          required: false
        },
        {
          model: Models.EmailTemplateContent,
          as: "defaultContent",
          where: { languageId: defaultLanguage.id },
          required: false
        }
      ]
    });
    return h
      .response({
        success: true,
        message: req.i18n.__("REQUEST_SUCCESSFUL"),
        responseData: responseData
      })
      .code(200);
  } catch (error) {
    console.log(error);
    return h.response(Common.generateError(req, 500, "SOMETHING_WENT_WRONG", error)).code(500);
  }
};

exports.sendEmail = async (req, h) => {
  const transaction = await Models.sequelize.transaction();
  try {
    let languageId = LanguageIds[LanguageCodes.indexOf(req.headers.language)];
    let languageCode = req.headers.language;

    let languageInfo = await Models.Language.findOne({ where: { code: languageCode } });
    if (!languageInfo) {
      languageInfo = await Models.Language.findOne({ where: { isDefault: 1 } });
    }

    languageId = languageInfo.id;

    const fromEmail = process.env.FROM_EMAIL;
    const { code, replacements, recipients, priority } = req.payload;
    let emailTemplateExists = await Models.EmailTemplate.findOne({
      where: { code },
      include: [
        { where: { languageId }, model: Models.EmailTemplateContent, required: false },
        {
          where: { languageId: process.env.DEFAULT_LANGUANGE_CODE_ID },
          model: Models.EmailTemplateContent,
          as: "defaultContent",
          required: false
        }
      ]
    });
    if (!emailTemplateExists) {
      await transaction.rollback();
      return h
        .response(
          Common.generateError(req, 400, "EMAIL_TEMPLATE_DOESNT_EXIST_WITH_PROVIDED_CODE", null)
        )
        .code(400);
    }

    let emailContent =
      emailTemplateExists.EmailTemplateContents.length > 0
        ? emailTemplateExists.EmailTemplateContents[0].content
        : emailTemplateExists.defaultContent[0].content;
    let subject =
      emailTemplateExists.EmailTemplateContents.length > 0
        ? emailTemplateExists.EmailTemplateContents[0].subject
        : emailTemplateExists.defaultContent[0].subject;
    let emailTemplateReplacements =
      emailTemplateExists.EmailTemplateContents.length > 0
        ? emailTemplateExists.EmailTemplateContents[0].replacements.split(",")
        : emailTemplateExists.defaultContent[0].replacements.split(",");

    let providedReplacements = {};
    for (let replacementParameter of emailTemplateReplacements)
      providedReplacements[replacementParameter] = replacements[replacementParameter];

    const status = await Common.sendEmail(
      recipients,
      [fromEmail],
      [],
      [],
      subject,
      emailContent,
      providedReplacements,
      [],
      languageCode,
      "default",
      priority
    );

    console.log(status, " ============== status")

    // const log = await Models.Log.create(
    //   {
    //     recipients: recipients,
    //     fromEmail: [fromEmail],
    //     cc: [],
    //     bcc: [],
    //     subject: subject,
    //     content: emailContent,
    //     replacements: providedReplacements,
    //     attachments: [],
    //     language: languageCode,
    //     template: "default",
    //     priority: priority,
    //     responseId: status
    //   },
    //   { transaction }
    // );

    await transaction.commit();
    return h
      .response({
        success: true,
        message: req.i18n.__("EMAIL_SENT_SUCCESSFULLY"),
        responseData: {}
      })
      .code(200);
  } catch (error) {
    console.log(error);
    await transaction.rollback();
    return h.response(Common.generateError(req, 500, "SOMETHING_WENT_WRONG", error)).code(400);
  }
};

exports.notifyUser = async (req, h) => {
  const transaction = await Models.sequelize.transaction();
  try {
    let languageId = LanguageIds[LanguageCodes.indexOf(req.headers.language)];
    let languageCode = req.headers.language;

    const fromEmail = process.env.FROM_EMAIL;
    const email = req.payload.email;
    let emailTemplateExists = await Models.EmailTemplate.findOne({
      where: { code: "COMING_SOON" },
      include: [
        { where: { languageId }, model: Models.EmailTemplateContent, required: false },
        {
          where: { languageId: process.env.DEFAULT_LANGUANGE_CODE_ID },
          model: Models.EmailTemplateContent,
          as: "defaultContent",
          required: false
        }
      ]
    });

    if (!emailTemplateExists) {
      await transaction.rollback();
      return h
        .response(
          Common.generateError(req, 400, "Email template does not exist with provided code.", null)
        )
        .code(400);
    }

    let emailContent =
      emailTemplateExists.EmailTemplateContents.length > 0
        ? emailTemplateExists.EmailTemplateContents[0].content
        : emailTemplateExists.defaultContent[0].content;

    let subject =
      emailTemplateExists.EmailTemplateContents.length > 0
        ? emailTemplateExists.EmailTemplateContents[0].subject
        : emailTemplateExists.defaultContent[0].subject;

    let chkEmail = await Models.UserNotifyEmail.findOne({ where: { email: email } });

    if (!chkEmail) {
      await Models.UserNotifyEmail.create(
        { email, emailTemplateId: 6 },
        { transaction: transaction }
      );
      // add subscriber to mailchimp account
      const response = await client.lists.addListMember(constants.MAILCHIMP.LIST_ID, {
        email_address: email,
        status: "subscribed"
      });

      let providedReplacements = {};
      // const responseData = await Common.sendEmail([email],[fromEmail],[],[],subject,emailContent,providedReplacements,[],languageCode,'default','low');

      // const responseData = await mailchimp.messages.send({ message: {text: emailContent, subject: subject, from_email: '<EMAIL>', from_name: 'Sqauds', to: [{'email': email}]} });

      await transaction.commit();
      return h
        .response({
          success: false,
          message: req.i18n.__("Email sent successfully."),
          responseData: email
        })
        .code(200);
    } else {
      await transaction.rollback();
      return h.response(Common.generateError(req, 400, "Email already subscribed", null)).code(400);
    }
  } catch (error) {
    console.log(error);
    await transaction.rollback();
    return h.response(Common.generateError(req, 500, "Something went wrong", error)).code(400);
  }
};

// sendEmailTest = async () => {
//   try {
//     const fromEmail = process.env.FROM_EMAIL;
//     const array = [
//           {
//     id: 21649,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Einzelgespräch beendet!",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hallo {{companion}}!</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Dein Einzel-Gespräch mit {{meetingWith}} ist beendet und dauerte {{date}} Minuten.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Beste Grüße<br>Das KUBYteam</p>',
//     replacements: '{"date": 71, "companion": "Annette", "meetingWith": "Brigitta"}',
//     attachments: "[]",
//     language: "de",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 11:12:06",
//     updated_at: "2025-06-28 11:12:06",
//     deleted_at: null
//   },
//   {
//     id: 21650,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Bezahle jetzt dein Einzelgespräch",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hallo {{sendTo}}!</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Dein Einzel-Gespräch mit {{meetingWith}} ist beendet und dauerte {{date}} Minuten.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Bitte klick auf den folgenden Link, um das Gespräch zu bezahlen:</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0"><a href="{{paymentLink}}"style="display:inline-block;padding:5px 10px;font-family:Inter,sans-serif;font-weight:700;font-size:16px;color:#ffffff;background-color:#028337;text-decoration:none;border-radius:5px;">Jetzt bezahlen</a></p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Nachdem du bezahlt hast, kannst du deine Aufzeichnung <a href="{{link}}">hier</a> herunterladen. Die Audioaufzeichnung kannst du jederzeit abrufen, die Videoaufzeichnung ist für 7 Tage verfügbar.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Beste Grüße<br>Das KUBYteam</p>',
//     replacements:
//       '{"date": 70, "link": "https://portal.kuby.info/user/meeting-history?meetingId=53577", "sendTo": "Brigitta", "password": "TaRV@9e5", "meetingWith": "Annette", "paymentLink": "https://www.digistore24.com/offer/344921183/WkPMdgDYb0F8/555958"}',
//     attachments: "[]",
//     language: "de",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 11:21:14",
//     updated_at: "2025-06-28 11:21:14",
//     deleted_at: null
//   },
//   {
//     id: 21651,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Bezahle jetzt dein Einzelgespräch",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hallo {{sendTo}}!</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Dein Einzel-Gespräch mit {{meetingWith}} ist beendet und dauerte {{date}} Minuten.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Bitte klick auf den folgenden Link, um das Gespräch zu bezahlen:</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0"><a href="{{paymentLink}}"style="display:inline-block;padding:5px 10px;font-family:Inter,sans-serif;font-weight:700;font-size:16px;color:#ffffff;background-color:#028337;text-decoration:none;border-radius:5px;">Jetzt bezahlen</a></p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Nachdem du bezahlt hast, kannst du deine Aufzeichnung <a href="{{link}}">hier</a> herunterladen. Die Audioaufzeichnung kannst du jederzeit abrufen, die Videoaufzeichnung ist für 7 Tage verfügbar.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Beste Grüße<br>Das KUBYteam</p>',
//     replacements:
//       '{"date": 70, "link": "https://portal.kuby.info/user/meeting-history?meetingId=53577", "sendTo": "Brigitta", "password": "TaRV@9e5", "meetingWith": "Annette", "paymentLink": "https://www.digistore24.com/offer/344921183/WkPMdgDYb0F8/555958"}',
//     attachments: "[]",
//     language: "de",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 11:26:19",
//     updated_at: "2025-06-28 11:26:19",
//     deleted_at: null
//   },
//   {
//     id: 21652,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Your access data for the KUBYportal",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hello {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">You can now log in to our portal at <a href="{{link}}">{{link}}</a> using your registered email and password.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Your e-mail: {{email}}<br>Your password: {{password}}</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>KUBYteam</p>',
//     replacements:
//       '{"link": "https://portal.kuby.info/signin", "name": "Octavia", "email": "<EMAIL>", "password": "pDVEGsdDy7NM"}',
//     attachments: "[]",
//     language: "en",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 11:59:02",
//     updated_at: "2025-06-28 11:59:02",
//     deleted_at: null
//   },
//   {
//     id: 21653,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Reset Password",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hello {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Click here to reset your password for your KUBY account:</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#141444;margin-top:0"><a href="{{token}}"style="display:inline-block;padding:5px 10px;font-family:Inter,sans-serif;font-weight:700;font-size:16px;color:#ffffff;background-color:#028337;text-decoration:none;border-radius:5px;">Reset password</a></p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">If you did not request this reset, you can safely ignore this email. Your password will remain unchanged.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>KUBYteam</p>',
//     replacements:
//       '{"name": "Technik", "token": "https://portal.kuby.info/reset-password?email=<EMAIL>&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiM2Q0M2M3ZjA5NjAwNzFjNTU5MDJmNTNhZjBiYjEzZmVkMmU3YWU1MTEzNDNjZDEwNTYwZjg0ZmMzOWZiODk3NTcxMzhjYThhYWE0ZmM3YzM2ZGIyZTBlZGJmY2Y5MDg1IiwiaWF0IjoxNzUxMTE2OTMxfQ.BeOrgc1ufpu1NIh-8yazWOQ6SX3MrennapZ4Qv6HKHQ"}',
//     attachments: "[]",
//     language: "en",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 13:22:11",
//     updated_at: "2025-06-28 13:22:11",
//     deleted_at: null
//   },
//   {
//     id: 21654,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Reset Password",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hello {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Click here to reset your password for your KUBY account:</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#141444;margin-top:0"><a href="{{token}}"style="display:inline-block;padding:5px 10px;font-family:Inter,sans-serif;font-weight:700;font-size:16px;color:#ffffff;background-color:#028337;text-decoration:none;border-radius:5px;">Reset password</a></p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">If you did not request this reset, you can safely ignore this email. Your password will remain unchanged.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>KUBYteam</p>',
//     replacements:
//       '{"name": "Neeraj", "token": "https://portal.kuby.info/reset-password?email=<EMAIL>&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiM2NkZWFlY2RiMmUzMTU0ODE5ZmVmMDRlYTdhMDI1ZDlkZjIzMGI2NjRiMTgyODU2ZmYwMDFhMDE1MjRjNzBlODQzM2Y1YmUxNzI4MjE1MDQwOTdkMjVkYTkxYmIzZWMyIiwiaWF0IjoxNzUxMTE4NjM4fQ.sSMS4xVmgPaOVQ9kLrCIu-aQk2vDhBbEDqvh9bpF_2c"}',
//     attachments: "[]",
//     language: "en",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 13:50:39",
//     updated_at: "2025-06-28 13:50:39",
//     deleted_at: null
//   },
//   {
//     id: 21655,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Reset Password",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hello {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Click here to reset your password for your KUBY account:</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#141444;margin-top:0"><a href="{{token}}"style="display:inline-block;padding:5px 10px;font-family:Inter,sans-serif;font-weight:700;font-size:16px;color:#ffffff;background-color:#028337;text-decoration:none;border-radius:5px;">Reset password</a></p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">If you did not request this reset, you can safely ignore this email. Your password will remain unchanged.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>KUBYteam</p>',
//     replacements:
//       '{"name": "Neeraj", "token": "https://portal.kuby.info/reset-password?email=<EMAIL>&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiM2NkZWFlY2RiMmUzMTU0ODE5ZmVmMDRlYTdhMDI1ZDlkZjIzMGI2NjRiMTgyODU2ZmYwMDFhMDE1MjRjNzBlODQzM2Y1YmUxNzI4MjE1MDQwOTdkMjVkYTkxYmIzZWMyIiwiaWF0IjoxNzUxMTIwNDgyfQ.WBxlAz3zLbOty2kw_DZixeq9diFVt6uHe1srmm14pTk"}',
//     attachments: "[]",
//     language: "en",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 14:21:22",
//     updated_at: "2025-06-28 14:21:22",
//     deleted_at: null
//   },
//   {
//     id: 21656,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Reset Password",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hello {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Click here to reset your password for your KUBY account:</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#141444;margin-top:0"><a href="{{token}}"style="display:inline-block;padding:5px 10px;font-family:Inter,sans-serif;font-weight:700;font-size:16px;color:#ffffff;background-color:#028337;text-decoration:none;border-radius:5px;">Reset password</a></p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">If you did not request this reset, you can safely ignore this email. Your password will remain unchanged.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>KUBYteam</p>',
//     replacements:
//       '{"name": "Neeraj", "token": "https://portal.kuby.info/reset-password?email=<EMAIL>&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiM2NkZWFlY2RiMmUzMTU0ODE5ZmVmMDRlYTdhMDI1ZDlkZjIzMGI2NjRiMTgyODU2ZmYwMDFhMDE1MjRjNzBlODQzM2Y1YmUxNzI4MjE1MDQwOTdkMjVkYTkxYmIzZWMyIiwiaWF0IjoxNzUxMTIwNDgyfQ.WBxlAz3zLbOty2kw_DZixeq9diFVt6uHe1srmm14pTk"}',
//     attachments: "[]",
//     language: "en",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 14:25:51",
//     updated_at: "2025-06-28 14:25:51",
//     deleted_at: null
//   },
//   {
//     id: 21657,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Reset Password",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hello {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Click here to reset your password for your KUBY account:</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#141444;margin-top:0"><a href="{{token}}"style="display:inline-block;padding:5px 10px;font-family:Inter,sans-serif;font-weight:700;font-size:16px;color:#ffffff;background-color:#028337;text-decoration:none;border-radius:5px;">Reset password</a></p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">If you did not request this reset, you can safely ignore this email. Your password will remain unchanged.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>KUBYteam</p>',
//     replacements:
//       '{"name": "Neeraj", "token": "https://portal.kuby.info/reset-password?email=<EMAIL>&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiM2NkZWFlY2RiMmUzMTU0ODE5ZmVmMDRlYTdhMDI1ZDlkZjIzMGI2NjRiMTgyODU2ZmYwMDFhMDE1MjRjNzBlODQzM2Y1YmUxNzI4MjE1MDQwOTdkMjVkYTkxYmIzZWMyIiwiaWF0IjoxNzUxMTIwNDgyfQ.WBxlAz3zLbOty2kw_DZixeq9diFVt6uHe1srmm14pTk"}',
//     attachments: "[]",
//     language: "en",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 14:26:18",
//     updated_at: "2025-06-28 14:26:18",
//     deleted_at: null
//   },
//   {
//     id: 21658,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Reset Password",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hello {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Click here to reset your password for your KUBY account:</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#141444;margin-top:0"><a href="{{token}}"style="display:inline-block;padding:5px 10px;font-family:Inter,sans-serif;font-weight:700;font-size:16px;color:#ffffff;background-color:#028337;text-decoration:none;border-radius:5px;">Reset password</a></p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">If you did not request this reset, you can safely ignore this email. Your password will remain unchanged.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>KUBYteam</p>',
//     replacements:
//       '{"name": "Neeraj", "token": "https://portal.kuby.info/reset-password?email=<EMAIL>&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiM2NkZWFlY2RiMmUzMTU0ODE5ZmVmMDRlYTdhMDI1ZDlkZjIzMGI2NjRiMTgyODU2ZmYwMDFhMDE1MjRjNzBlODQzM2Y1YmUxNzI4MjE1MDQwOTdkMjVkYTkxYmIzZWMyIiwiaWF0IjoxNzUxMTIxMzAzfQ.G1g191qBBmb4EtQVN_zU2rHWEYw84OMtDLLTuMQbSIM"}',
//     attachments: "[]",
//     language: "en",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 14:35:03",
//     updated_at: "2025-06-28 14:35:03",
//     deleted_at: null
//   },
//   {
//     id: 21659,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Your access data for the KUBYportal",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hello {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">You can now log in to our portal at <a href="{{link}}">{{link}}</a> using your registered email and password.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Your e-mail: {{email}}<br>Your password: {{password}}</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>KUBYteam</p>',
//     replacements:
//       '{"link": "https://portal.kuby.info/signin", "name": "ingrid", "email": "<EMAIL>", "password": "mG9UO22RHovF"}',
//     attachments: "[]",
//     language: "en",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 14:55:56",
//     updated_at: "2025-06-28 14:55:56",
//     deleted_at: null
//   },
//   {
//     id: 21660,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Dein Seelenschreiben wurde kommentiert!",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hallo {{customer}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Wir möchten dich informieren, dass {{companion}} dein Seelenschreiben kommentiert hat.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0"><a href="https://portal.kuby.info/dashboard/soulwriting" style="display:inline-block;padding:5px 10px;font-family:Inter,sans-serif;font-weight:700;font-size:16px;color:#fff;background-color:#028337;text-decoration:none;border-radius:5px">Zum Seelenschreiben</a></p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Beste Grüße,<br>Das KUBYteam</p>',
//     replacements: '{"customer": "Wolfgang Himberger", "companion": "Ilka Butzke"}',
//     attachments: "[]",
//     language: "de",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 15:15:57",
//     updated_at: "2025-06-28 15:15:57",
//     deleted_at: null
//   },
//   {
//     id: 21661,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Dein Seelenschreiben wurde kommentiert!",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hallo {{customer}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Wir möchten dich informieren, dass {{companion}} dein Seelenschreiben kommentiert hat.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0"><a href="https://portal.kuby.info/dashboard/soulwriting" style="display:inline-block;padding:5px 10px;font-family:Inter,sans-serif;font-weight:700;font-size:16px;color:#fff;background-color:#028337;text-decoration:none;border-radius:5px">Zum Seelenschreiben</a></p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Beste Grüße,<br>Das KUBYteam</p>',
//     replacements: '{"customer": "Wolfgang Himberger", "companion": "Ilka Butzke"}',
//     attachments: "[]",
//     language: "de",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 15:17:48",
//     updated_at: "2025-06-28 15:17:48",
//     deleted_at: null
//   },
//   {
//     id: 21662,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Reset Password",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hello {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Click here to reset your password for your KUBY account:</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#141444;margin-top:0"><a href="{{token}}"style="display:inline-block;padding:5px 10px;font-family:Inter,sans-serif;font-weight:700;font-size:16px;color:#ffffff;background-color:#028337;text-decoration:none;border-radius:5px;">Reset password</a></p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">If you did not request this reset, you can safely ignore this email. Your password will remain unchanged.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>KUBYteam</p>',
//     replacements:
//       '{"name": "Neeraj", "token": "https://portal.kuby.info/reset-password?email=<EMAIL>&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiM2NkZWFlY2RiMmUzMTU0ODE5ZmVmMDRlYTdhMDI1ZDlkZjIzMGI2NjRiMTgyODU2ZmYwMDFhMDE1MjRjNzBlODQzM2Y1YmUxNzI4MjE1MDQwOTdkMjVkYTkxYmIzZWMyIiwiaWF0IjoxNzUxMTIxMzAzfQ.G1g191qBBmb4EtQVN_zU2rHWEYw84OMtDLLTuMQbSIM"}',
//     attachments: "[]",
//     language: "en",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 15:44:50",
//     updated_at: "2025-06-28 15:44:50",
//     deleted_at: null
//   },
//   {
//     id: 21663,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Reset Password",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hello {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Click here to reset your password for your KUBY account:</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#141444;margin-top:0"><a href="{{token}}"style="display:inline-block;padding:5px 10px;font-family:Inter,sans-serif;font-weight:700;font-size:16px;color:#ffffff;background-color:#028337;text-decoration:none;border-radius:5px;">Reset password</a></p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">If you did not request this reset, you can safely ignore this email. Your password will remain unchanged.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>KUBYteam</p>',
//     replacements:
//       '{"name": "Neeraj", "token": "https://portal.kuby.info/reset-password?email=<EMAIL>&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiM2NkZWFlY2RiMmUzMTU0ODE5ZmVmMDRlYTdhMDI1ZDlkZjIzMGI2NjRiMTgyODU2ZmYwMDFhMDE1MjRjNzBlODQzM2Y1YmUxNzI4MjE1MDQwOTdkMjVkYTkxYmIzZWMyIiwiaWF0IjoxNzUxMTI1NTExfQ.A9aqpthb6HVJ5ou4yV4RXFcmRoq_Bg0nBHLT2Cp0DwU"}',
//     attachments: "[]",
//     language: "en",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 15:45:11",
//     updated_at: "2025-06-28 15:45:11",
//     deleted_at: null
//   },
//   {
//     id: 21664,
//     recipients: '["<EMAIL>"]',
//     from: null,
//     cc: "[]",
//     bcc: "[]",
//     subject: "Reset Password",
//     content:
//       '<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Hello {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Click here to reset your password for your KUBY account:</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#141444;margin-top:0"><a href="{{token}}"style="display:inline-block;padding:5px 10px;font-family:Inter,sans-serif;font-weight:700;font-size:16px;color:#ffffff;background-color:#028337;text-decoration:none;border-radius:5px;">Reset password</a></p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">If you did not request this reset, you can safely ignore this email. Your password will remain unchanged.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>KUBYteam</p>',
//     replacements:
//       '{"name": "Neeraj", "token": "https://portal.kuby.info/reset-password?email=<EMAIL>&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiM2NkZWFlY2RiMmUzMTU0ODE5ZmVmMDRlYTdhMDI1ZDlkZjIzMGI2NjRiMTgyODU2ZmYwMDFhMDE1MjRjNzBlODQzM2Y1YmUxNzI4MjE1MDQwOTdkMjVkYTkxYmIzZWMyIiwiaWF0IjoxNzUxMTI2NjM0fQ.4S9ysEVIyRfyD3dFTz2-OdGGgOgGWrS8tkp-bymsAqs"}',
//     attachments: "[]",
//     language: "en",
//     template: "default",
//     priority: "high",
//     status: null,
//     created_at: "2025-06-28 16:03:54",
//     updated_at: "2025-06-28 16:03:54",
//     deleted_at: null
//   }
//     ]

//     const responseArray = [];

//     for(let data of array) {
//         const status = await Common.sendEmail(
//             JSON.parse(data.recipients),
//             [fromEmail],
//             [],
//             [],
//             data.subject,
//             data.content,
//             JSON.parse(data.replacements),
//             [],
//             data.language,
//             "default",
//             data.priority
//         );

//         responseArray.push({ id: data.id, success: true })
//     }


//     return responseArray
//   } catch (error) {
//     return false;
//   }
// };

// sendEmailTest();