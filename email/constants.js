module.exports = {
    PAGINATION_LIMIT: 20,
    MAX_PAGINATION_LIMIT: 50,
    SMTP:{
        host:process.env.EMAIL_HOST,
        username:process.env.EMAIL_USERNAME,
        password:process.env.EMAIL_PASSWORD,
        port:'465',
        ssl:'ssl'
    },
    // SMTP:{
    //     host:'email-smtp.eu-central-1.amazonaws.com',
    //     username:'AKIA4Y3MHBRZVQOMNZOF',
    //     password:'BMUw7Vu4eEC1AGnER6mmrh5rU9VncctkoxvqY/cm7XJr',
    //     port:'465',
    //     ssl:'ssl'
    // },
    // SMTP:{
    //     host:'smtp.gmail.com',
    //     username:'<EMAIL>',
    //     password:'ZaZh=8VL1',
    //     port:'465',
    //     ssl:'ssl'
    // },
    MAILCHIMP:{
        API_KEY:"*************************************",
        SERVER_PREFIX:'us14',
        LIST_ID:'847be9218f',
        MANDRIL_KEY:'FpZi_hZvdPn5oOnOEGdufQ'
    },
    STATUS:{
        INACTIVE:0,
        ACTIVE:1
    },
    AMOUNT_TYPE: {
        FIXED: 1,
        PERCENTAGE: 2
    },
    DEVICE_TYPE: {
        WEB: 1,
        ANDROID: 2,
        IOS: 3,
    },
    CRON:[
    ],
    KYC_STATUS: {
    },
    NOTIFICATION: {
    }
}