{"name": "email", "version": "1.0.0", "description": "Email Management", "main": "server.js", "scripts": {"test": "lab --leaks --timeout 30000", "start": "node server", "seeder": "NODE_ENV=development npx sequelize-cli db:seed:all"}, "repository": {"type": "git", "url": "git+https://github.com/sqauds/backend-api.git"}, "keywords": ["<PERSON><PERSON>"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@hapi/boom": "^10.0.0", "@hapi/hapi": "^20.2.2", "@hapi/inert": "^7.0.0", "@hapi/vision": "^7.0.0", "@mailchimp/mailchimp_marketing": "^3.0.80", "@mailchimp/mailchimp_transactional": "^1.0.49", "axios": "^1.1.3", "bcrypt": "^5.1.0", "dotenv": "^16.0.3", "fs": "0.0.1-security", "handlebars": "^4.7.7", "hapi-auth-jwt2": "^10.2.0", "hapi-auto-route": "^3.0.4", "hapi-cron": "^1.1.0", "hapi-i18n": "^3.0.1", "hapi-swagger": "^14.5.5", "http": "^0.0.1-security", "joi": "^17.7.0", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.29.4", "mysql2": "^2.3.3", "ngrok": "^4.3.3", "nodemailer": "^6.9.1", "path": "^0.12.7", "pg": "^8.8.0", "request": "^2.88.2", "sequelize": "^6.25.3", "sequelize-cli": "^6.5.2", "sharp": "^0.31.1", "striptags": "^3.2.0", "uuid": "^9.0.0"}, "devDependencies": {"@hapi/code": "^9.0.1", "@hapi/lab": "^25.0.1", "chai": "^4.3.6"}}