exports.privateKey='ABCDEFGHIJKLMNOPQRSTUVWXYZ123456';
exports.algorithm="aes-256-cbc";
exports.iv='QWERTY1234567890';

decrypt = (text) => {
  let decipher = crypto.createDecipheriv(this.algorithm, this.privateKey,this.iv);
  let decrypted = decipher.update(text, "hex", "utf8");
  decrypted = decrypted + decipher.final("utf8");
  return decrypted;
}

encrypt = (text) => {
  let cipher = crypto.createCipheriv(this.algorithm,this.privateKey,this.iv);
  let encrypted = cipher.update(text, "utf8", "hex");
  encrypted = encrypted + cipher.final("hex");
  return encrypted;
}

readHTMLFile = (path,callback) => {
  Fs.readFile(path, { encoding: "utf-8" }, (err, html) => {
    if (err) {
      throw err;
    } else {
      callback(null, html);
    }
  });
};

exports.prefunction = (req,h) => {
  global.LanguageCodes = process.env.ALL_LANGUAGE_CODE.split(',');
  global.LanguageIds = process.env.ALL_LANGUAGE_ID.split(',').map((item) => parseInt(item,10));
  global.utcOffset = req.headers.utcoffset;
  return true;
}

exports.routeError = (errors,message) => {
  errors.forEach(err=>{ 
    switch(err.code) {
      case "any.required":
        err.message=message;
        break
    }
  });
  return errors;
}

exports.validateToken = async (token) => {
  fetchtoken = JSON.parse(decrypt(token.data));
  var diff = Moment().diff(Moment(token.iat * 1000));
  if (diff > 0) {
    return {
      isValid: true,
      credentials: { 
        userData: fetchtoken, 
        // scope: fetchtoken.Permissions
        scope: [...fetchtoken.Role,...fetchtoken.Permissions] 
      }
    }; 
  }
  return {
    isValid: false
  };
};

exports.convertToUTC = (date,offset) => {
  let utcDate = Moment(date).utcOffset(offset,true);
  return utcDate;
}

exports.signToken = (tokenData) => {
    return Jwt.sign(
      { data: encrypt(JSON.stringify(tokenData))},
      this.privateKey
    );
};

exports.headers = (authorized) => {
	let Globalheaders = {
    language    :   Joi.string().optional().default(process.env.DEFAULT_LANGUANGE_CODE),
    utcoffset   :   Joi.string().optional().default(0),
    timezone    :   Joi.string().optional().default(null),
  };
	if (authorized){
    _.assign(Globalheaders, {authorization: Joi.string().required().description("Token to identify user who is performing the action")});
	}
	return Globalheaders;
};

exports.sendOTP = async (phoneNumber) => {
  return {phoneNumber:phoneNumber,pinId:process.env.MASTER_OTP}
}



exports.sendEmail = async (recipients,from,cc,bcc,subject,content,replacements,attachments,language,template,priority) => {
  try{
  let protocol = process.env.EMAIL_PROTOCOL;
  switch(protocol) {
    case 'smtp':
        let transporter = nodemailer.createTransport({
          host: Constants.SMTP.host,
          port: Constants.SMTP.port,
          secure: Constants.SMTP.ssl,
          // secureConnection: false,
          auth: {
            user: Constants.SMTP.username,
            pass: Constants.SMTP.password,
          },
          debug: true,
          logger: true
        });
        readHTMLFile(__dirname + `/emails/${language}/${template}.html`, async (err,html) => {
          let sendto = recipients.join(',');
          var template = handlebars.compile(html);
          var mergeContent = template({content});
          var templateToSend = handlebars.compile(mergeContent);
          var htmlToSend = templateToSend(replacements);

          let rawSubject = handlebars.compile(subject);
          var subjectToSend = rawSubject(replacements);


          let mailOptions = {
            from: {
              name: 'KUBYteam',
              address: from
            },                                           // sender address
            to: sendto,                                             // list of receivers
            cc: cc.join(','),                                       // cc
            bcc: bcc.join(','),                                     // bcc
            subject: subjectToSend,                                       // Subject line
            text: striptags(htmlToSend),                            // plain text body
            html: htmlToSend,                                       // html body
            attachments: attachments,                               // Attachments if any
            priority: priority,
            replyTo: language == "cz" ? "<EMAIL>" : "<EMAIL>"                                     // One of "high", "normal" and "low"
          };
          let info = await transporter.sendMail(mailOptions);
          if(info) {
                const log = await Models.Log.create(
                  {
                    recipients: recipients,
                    fromEmail: [from],
                    cc: [],
                    bcc: [],
                    subject: subject,
                    content: content,
                    replacements: replacements,
                    attachments: [],
                    language: language,
                    template: "default",
                    priority: priority,
                    responseId: info.messageId
                  }
                );
            return info.messageId;
          }
          return null;
        });
  }
}
catch(error){
  console.error('error',error);
  return null;
  }
}

exports.generateCode = (requestedlength) => {
  const char = '1234567890'; //Random Generate Every Time From This Given Char
  const length = typeof requestedlength !='undefined' ? requestedlength : 4;
  let randomvalue = '';
  for ( let i = 0; i < length; i++) {
    const value = Math.floor(Math.random() * char.length);
    randomvalue += char.substring(value, value + 1).toUpperCase();
  }
  return randomvalue;
}

exports.FailureError = (err,req) => {
  const updatedError = err;
	updatedError.output.payload.message = [];
	let customMessages = {};
	if (err.isJoi && Array.isArray(err.details) && err.details.length > 0){
		err.details.forEach((error) => {
			customMessages[error.context.label] = req.i18n.__(error.message);
		});
	}
	delete updatedError.output.payload.validation;
	updatedError.output.payload.error =  req.i18n.__('BAD_REQUEST');
  // console.log('err.details.type', err.details)
  if(err.details[0].type === 'string.email') {
    updatedError.output.payload.message = req.i18n.__(
      "PLEASE_ENTER_A_VALID_EMAIL"
    );
  } else {
    updatedError.output.payload.message = req.i18n.__(
      "ERROR_WHILE_VALIDATING_REQUEST"
    );
  }
	updatedError.output.payload.errors = customMessages;
	return updatedError;
}

exports.generateError = (req,statusCode,message,error)=>{
  let customError = {status:false,responseData:{}};
  switch(statusCode) {
    case 400:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__(message);
      break;
    case 401:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__('UNAUTHORIZED_REQUEST');
      break;
    case 403:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__('PERMISSION_DENIED');
      break;
    case 500:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__('INTERNAL_SERVER_ERROR');
      break;
    default:
      customError['error'] = error.message;
      customError['message'] = req.i18n.__('UNKNOWN_ERROR_OCCURED');
      break;
  }
  return customError;
}

exports.getTotalPages = async (records, perpage) => {
  let totalPages = Math.ceil(records / perpage);
  return totalPages;
};