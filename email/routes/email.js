const emailController = require("../controllers/emailController");
module.exports = [
	{
		method : "GET",
		path : "/email/templates/{id}",
		handler : emailController.getEmailTemplateDetails,
		options: {
			tags: ["api", "Email"],
			notes: "Endpoint to get defined email template by code",
			description:"Get email template",
			// auth: {strategy: 'jwt', scope: ["admin","view_emailTemplates","list_emailTemplates","manage_emailTemplates"]},
			auth: false,
			validate: {
				// headers: Joi.object(Common.headers(true)).options({
				// 	allowUnknown: true
				// }),
				options: {
					abortEarly: false
				},
				params: {
					id: Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_TEMPLATE_ID_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: <PERSON><PERSON>
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/email/templates",
		handler : emailController.listEmailTemplates,
		options: {
			tags: ["api", "Email"],
			notes: "Endpoint to list defined email template for portal",
			description:"List email templates",
			// auth: {strategy: 'jwt', scope: ["admin","view_emailTemplates","list_emailTemplates","manage_emailTemplates"]},
			auth: false,
			validate: {
				// headers: Joi.object(Common.headers(true)).options({
				// 	allowUnknown: true
				// }),
				options: {
					abortEarly: false
				},
				query: {
					limit: Joi.number().integer().optional().default(null),
                    status: Joi.number().valid(0,1).optional().default(null),
					pageNumber : Joi.number().integer().min(1).optional().default(null),
					orderByValue: Joi.string().allow('ASC', 'DESC').optional().default('DESC'),
					orderByParameter: Joi.string().allow('createdAt','id').optional().default('id'),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "POST",
		path : "/email/template",
		handler : emailController.createEmailTemplate,
		options: {
			tags: ["api", "Email"],
			notes: "Endpoint to define a new email template for portal",
			description: "Create Email Template",
			// auth: {strategy: 'jwt', scope: ["admin","view_emailTemplates","list_emailTemplates","manage_emailTemplates"]},
			auth: false,
			validate: {
				// headers: Joi.object(Common.headers(true)).options({
				// 	allowUnknown: true
				// }),
				options: {
					abortEarly: false
				},
				payload: {
					replacements : Joi.string().optional().default(null),
                    code : Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_TEMPLATE_CODE_IS_REQUIRED')}),
					subject : Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_TEMPLATE_SUBJECT_IS_REQUIRED')}),
					content : Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_TEMPLATE_CONTENT_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "PATCH",
		path : "/email/template",
		handler : emailController.updateEmailTemplate,
		options: {
			tags: ["api", "Email"],
			notes: "Endpoint to update defined email template for portal by id",
			description:"Update Email Template",
			// auth: {strategy: 'jwt', scope: ["admin","view_emailTemplates","list_emailTemplates","manage_emailTemplates"]},
			auth: false,
			validate: {
				// headers: Joi.object(Common.headers(true)).options({
				// 	allowUnknown: true
				// }),
				options: {
					abortEarly: false
				},
				payload: {
					replacements : Joi.string().optional().default(null),
					subject : Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_TEMPLATE_SUBJECT_IS_REQUIRED')}),
					content : Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_TEMPLATE_CONTENT_IS_REQUIRED')}),
					emailTemplateId: Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_TEMPLATE_ID_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "DELETE",
		path : "/email/template",
		handler : emailController.deleteEmailTemplate,
		options: {
			tags: ["api", "Email"],
			notes: "Endpoint to remove defined email template from the portal by id",
			description: "Remove Email Template",
			// auth: {strategy: 'jwt', scope: ["admin","view_emailTemplates","list_emailTemplates","manage_emailTemplates"]},
			auth: false,
			validate: {
				// headers: Joi.object(Common.headers(true)).options({
				// 	allowUnknown: true
				// }),
				options: {
					abortEarly: false
				},
				payload: {
					emailTemplateId: Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_TEMPLATE_ID_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "POST",
		path : "/email/send",
		handler : emailController.sendEmail,
		options: {
			tags: ["api", "Email"],
			notes: "Endpoint to send email to a created template",
			description: "Send Email To Recipients",
			// auth: {strategy: 'jwt', scope: ["admin","view_emailTemplates","list_emailTemplates","manage_emailTemplates"]},
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					replacements : Joi.object().optional().default({}),
					priority : Joi.string().valid("high","normal","low").default("low"),
                    code : Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_TEMPLATE_CODE_IS_REQUIRED')}),
					recipients : Joi.array().min(1).items(Joi.string().email()).required().error(errors=>{return Common.routeError(errors,'RECIPIENTS_IS/ARE_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "POST",
		path : "/email/notify-user",
		handler : emailController.notifyUser,
		options: {
			tags: ["api", "Email"],
			notes: "Endpoint to notify users for coming soon",
			description: "Send Email To users whom we want to notify",
			// auth: {strategy: 'jwt', scope: ["admin","view_emailTemplates","list_emailTemplates","manage_emailTemplates"]},
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					email : Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
]