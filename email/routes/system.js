const systemController = require('../controllers/systemController');

module.exports = [
  //  {
    //     method: "GET",
    //     path: "/email/system/initialize",
    //     handler: systemController.initializeSystem,
	// 	options: {
	// 		tags: ["api", "System"],
	// 		notes: "Endpoint to initlaize email-management micro-service",
	// 		description: "Initialize Email Management Micro-Service",
	// 		auth: false,
	// 		validate: {
	// 			options: {
	// 				abortEarly: false
	// 			},
	// 			failAction: async (req, h, err) => {
	// 				return Common.FailureError(err, req);
	// 			},
    //             query: {
    //             },
	// 			validator: Joi
	// 		},
    //         plugins: {
    //             'hapi-swagger': {
    //                 payloadType: 'form'
    //             }
    //         },
	// 		pre : [{method: Common.prefunction}]
	// 	}
    // }
]