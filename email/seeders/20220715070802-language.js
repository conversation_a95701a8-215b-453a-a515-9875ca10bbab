'use strict';
const Constants= require('../constants');
const { v4: uuidv4 } = require('uuid');

module.exports = {
  async up (queryInterface, Sequelize) {
   await queryInterface.bulkInsert('email_languages',[
    { name:"English", status:Constants.STATUS.ACTIVE, code:'en',  is_default:Constants.STATUS.ACTIVE,   created_at:new Date(),  updated_at:new Date(),uuid:uuidv4()},
    { name:"Arabic",  status:Constants.STATUS.ACTIVE, code:'ar',  is_default:Constants.STATUS.INACTIVE, created_at:new Date(),  updated_at:new Date(),uuid:uuidv4()},
    { name:"French",  status:Constants.STATUS.ACTIVE, code:'fr',  is_default:Constants.STATUS.INACTIVE, created_at:new Date(),  updated_at:new Date(),uuid:uuidv4()},
    { name:"German",  status:Constants.STATUS.ACTIVE, code:'de',  is_default:Constants.STATUS.INACTIVE, created_at:new Date(),  updated_at:new Date(),uuid:uuidv4()},
    { name:"Spanish", status:Constants.STATUS.ACTIVE, code:'es',  is_default:Constants.STATUS.INACTIVE, created_at:new Date(),  updated_at:new Date(),uuid:uuidv4()},
  ])
  },
  
  async down (queryInterface, Sequelize) {
    /**
     * Add commands to rever t seed here.
     *
     * Example:
     * 
     * await queryInterface.bulkDelete('People', null, {});
     */
  }
};
