'use strict';
const Constants= require('../constants');
const { v4: uuidv4 } = require('uuid');

module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.bulkInsert('email_email_templates',[
      // { name:"English",status:Constants.STATUS.ACTIVE,code:'en',is_default:Constants.STATUS.ACTIVE,created_at:new Date(),updated_at:new Date(),uuid:uuidv4()}
      {code:'WELCOME_EMAIL',status:Constants.STATUS.ACTIVE,created_at:new Date(),updated_at:new Date(),uuid:uuidv4()},  //1
      {code:'SEND_VERIFICATION_CODE',status:Constants.STATUS.ACTIVE,created_at:new Date(),updated_at:new Date(),uuid:uuidv4()},     //2
      {code:'SUCESSFULLY_VERIFY',status:Constants.STATUS.ACTIVE,created_at:new Date(),updated_at:new Date(),uuid:uuidv4()},   //3
      {code:'RESET_PASSWORD',status:Constants.STATUS.ACTIVE,created_at:new Date(),updated_at:new Date(),uuid:uuidv4()},   //4
      {code:'ACCOUNT_CREATED',status:Constants.STATUS.ACTIVE,created_at:new Date(),updated_at:new Date(),uuid:uuidv4()},    //5


//  Seeders for email send of 
{code:'MEETING_SCHEDULED',status:Constants.STATUS.ACTIVE,created_at:new Date(),updated_at:new Date(),uuid:uuidv4()},  //6
{code:'MEETING_RESCHEDULED',status:Constants.STATUS.ACTIVE,created_at:new Date(),updated_at:new Date(),uuid:uuidv4()},  //7
{code:'MEETING_CANCELLED',status:Constants.STATUS.ACTIVE,created_at:new Date(),updated_at:new Date(),uuid:uuidv4()},  //8
{code:'MEETING_FEEDBACK',status:Constants.STATUS.ACTIVE,created_at:new Date(),updated_at:new Date(),uuid:uuidv4()},  //9
{code:'SOUL_WRITING_SUBMISSION',status:Constants.STATUS.ACTIVE,created_at:new Date(),updated_at:new Date(),uuid:uuidv4()},  //10
{code:'SOUL_WRITING_FEEDBACK',status:Constants.STATUS.ACTIVE,created_at:new Date(),updated_at:new Date(),uuid:uuidv4()},  //10
{code:'USER_BY_ADMIN',status:Constants.STATUS.ACTIVE,created_at:new Date(),updated_at:new Date(),uuid:uuidv4()},  //10



    ])},

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  }
};
