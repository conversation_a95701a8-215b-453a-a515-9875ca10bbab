"use strict";
const Constants = require("../constants");
const { v4: uuidv4 } = require("uuid");

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkInsert("email_email_template_contents", [
      {
        email_template_id: 1,
        language_id: 1,
        content:
          "<div><h3>Hi {{name}},</h3><br><br> Welcome to Kuby Services.<br><br>Sincerely<br>Team KUBY</div>",
        subject: "Welcome to Kuby",
        replacements: "name",
        created_at: new Date(),
        updated_at: new Date(),
        uuid: uuidv4()
      },
      {
        email_template_id: 2,
        language_id: 1,
        content:
          `<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Dear {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Thank you for signing up with our platform! To complete the registration process and start using our services, we need to verify your email address. This is to ensure that we have a valid and secure way of communicating with you.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">To verify your email address, please click on the link below or copy and paste it into your web browser:</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0"><a href="{{token}}">{{token}}</a></p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">If you did not register for our platform, please disregard this email.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Thank you for your cooperation.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>Team KUBY</p>`,
        subject: "Please Verify Your Email Address",
        replacements: "code,token,name",
        created_at: new Date(),
        updated_at: new Date(),
        uuid: uuidv4()
      },
      {
        email_template_id: 3,
        language_id: 1,
        content:
          "<div><h3>Hi User! Kuby Welcomes You! Please visit the below URL to know more about us! <br> {{domain}}</h3></div>",
        subject: "Welcome To KUBY",
        replacements: "domain",
        created_at: new Date(),
        updated_at: new Date(),
        uuid: uuidv4()
      },
      {
        email_template_id: 4,
        language_id: 1,
        content: "<div>Hi!<br> Click the below URL to verify email <br> {{token}}</div>",
        subject: "Reset Password",
        replacements: "token",
        created_at: new Date(),
        updated_at: new Date(),
        uuid: uuidv4()
      },
      {
        email_template_id: 5,
        language_id: 1,
        content:
          "<div>Hi {{name}}!<br> Click the below URL <br></br>{{url}}to get started with KUBY <br></br>account details are as followd.<br></br>username : {email} <br></br>password : {email} </div>",
        subject: "Welcome to KUBY",
        replacements: "url,email,name",
        created_at: new Date(),
        updated_at: new Date(),
        uuid: uuidv4()
      },

      // Meetings Contnet
      {
        email_template_id: 6,
        language_id: 1,
        content:
          `<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Dear {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">The meeting has been successfully booked for {{time}} at<a href="{{zoomLink}}">{{zoomLink}}</a>. We are looking forward to your meeting with {{companion}}.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Thank you for taking the time to meet with us.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>Team KUBY</p>`,
        subject: "Meeting Successfully Booked",
        replacements: "name,time,companion,zoomLink",
        created_at: new Date(),
        updated_at: new Date(),
        uuid: uuidv4()
      },
      {
        email_template_id: 7,
        language_id: 1,
        content:
         `<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Dear {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">The meeting has been rescheduled successfully for {{time}} on<a href="{{zoomLink}}">{{zoomLink}}</a>. We are looking forward to your meeting with {{companion}}.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Thank you for taking the time to meet with us.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>Team KUBY</p>`,
        subject: "Meeting Rescheduled Successfully",
        replacements: "name,time,companion,zoomLink",
        created_at: new Date(),
        updated_at: new Date(),
        uuid: uuidv4()
      },
      {
        email_template_id: 8,
        language_id: 1,
        content:
          `<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Dear {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">We regret to inform you that our scheduled meeting on {{time}} with {{companion}} has been canceled.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">We apologize for any inconvenience this may cause and thank you for your understanding and cooperation.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>Team KUBY</p>`,
        subject: "Cancellation of Meeting",
        replacements: "name,time,companion",
        created_at: new Date(),
        updated_at: new Date(),
        uuid: uuidv4()
      },
      {
        email_template_id: 9,
        language_id: 1,
        content:
          `<div>Hi {{name}}!<br>Your feedback link for One-to-One Meeting is here {{link}}. <br></br></div>`,
        subject: "Meeting Feedback",
        replacements: "name,link",
        created_at: new Date(),
        updated_at: new Date(),
        uuid: uuidv4()
      },
      {
        email_template_id: 10,
        language_id: 1,
        content:
          `<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Dear {{companion}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">We hope this email finds you well. We are reaching out to share a Soul Writing piece submitted by {{customer}}, one of our valued members.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">As you may know, Soul Writing is a deeply personal process, and it takes courage to share our innermost thoughts and feelings with others. We are honored that {{customer}} has trusted us with their submission and we wanted to extend that trust to you as well.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">We would greatly appreciate it if you could take the time to read their submission and provide any feedback or thoughts you may have. As a supportive community, your input is invaluable, and it will help {{customer}} to continue on their personal journey.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Thank you for taking the time to review this Soul Writing submission. We value your contribution and look forward to continuing our journey together.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>Team KUBY</p>`,
        subject: "Request for Review - Soul Writing Submission",
        replacements: "companion,customer",
        created_at: new Date(),
        updated_at: new Date(),
        uuid: uuidv4()
      },
      {
        email_template_id: 11,
        language_id: 1,
        content:
          `<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Dear {{customer}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Thank you for sharing your Soul Writing with us. Your work is heartfelt and deeply introspective, and we appreciate your willingness to be vulnerable and share your innermost thoughts and feelings.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">After reviewing your submission, we have some constructive feedback. While your writing is strong, we noticed a few areas where you could explore more deeply or expand upon certain ideas to strengthen the piece further.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Overall, we are grateful for the opportunity to read your Soul Writing and hope that our feedback is helpful. We encourage you to continue on your journey of growth and self-expression.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Thank you again for your submission, and we look forward to reading more from you in the future.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>Team KUBY</p>`,
        subject: "Soul Writing Submission Feedback",
        replacements: "customer",
        created_at: new Date(),
        updated_at: new Date(),
        uuid: uuidv4()
      },
      {
        email_template_id: 12,
        language_id: 1,
        content:
          `<h3 style="margin-top:0;font-family:Inter,sans-serif;font-style:normal;font-weight:700;font-size:20px;line-height:1.4;letter-spacing:.1px;color:#141444;margin-bottom:12px">Dear {{name}},</h3><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">We am pleased to inform you that your registration has been successfully processed by our administration team. You are now officially a registered member of KUBY. We would like to extend a warm welcome to you and are delighted that you have chosen to join us.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">You can now log in to your account using your registered email and password. You will also receive regular updates and notifications about our latest products, services, and upcoming events.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">We encourage you to explore our website and make the most of our resources. If you have any questions or need any assistance, please do not hesitate to contact us. Our support team is always here to help you and address your concerns.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Thank you for choosing KUBY. We look forward to having you as a valued member and serving you in the future.</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">password: {{password}}</p><p style="font-family:Inter,sans-serif;font-style:normal;font-weight:400;font-size:16px;line-height:1.75;letter-spacing:.1px;color:#495377;margin-top:0">Best regards,<br>Team KUBY</p>`,
        subject: "Congratulations! You have been successfully registered",
        replacements: "name,password",
        created_at: new Date(),
        updated_at: new Date(),
        uuid: uuidv4()
      }
    ]);
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  }
};
// module.exports = {
//   async up(queryInterface, Sequelize) {
//     await queryInterface.bulkInsert("email_email_template_contents", [
//       {
//         email_template_id: 1,
//         language_id: 1,
//         content:
//           "<div><h3>Hi {{name}},</h3><br><br> Welcome to Kuby Services.<br><br>Sincerely<br>Team KUBY</div>",
//         subject: "Welcome to Kuby",
//         replacements: "name",
//         created_at: new Date(),
//         updated_at: new Date(),
//         uuid: uuidv4()
//       },
//       {
//         email_template_id: 2,
//         language_id: 1,
//         content:
//           "<div>Hi {{name}}<br><br> Click the below URL to verify email <br>{{token}}<br><br>Sincerely<br>Team KUBY</div>",
//         subject: "Verification Code",
//         replacements: "code,token,name",
//         created_at: new Date(),
//         updated_at: new Date(),
//         uuid: uuidv4()
//       },
//       {
//         email_template_id: 3,
//         language_id: 1,
//         content:
//           "<div><h3>Hi User! Kuby Welcomes You! Please visit the below URL to know more about us! <br> {{domain}}</h3></div>",
//         subject: "Welcome To KUBY",
//         replacements: "domain",
//         created_at: new Date(),
//         updated_at: new Date(),
//         uuid: uuidv4()
//       },
//       {
//         email_template_id: 4,
//         language_id: 1,
//         content: "<div>Hi!<br> Click the below URL to verify email <br> {{token}}</div>",
//         subject: "Reset Password",
//         replacements: "token",
//         created_at: new Date(),
//         updated_at: new Date(),
//         uuid: uuidv4()
//       },
//       {
//         email_template_id: 5,
//         language_id: 1,
//         content:
//           "<div>Hi {{name}}!<br> Click the below URL <br></br>{{url}}to get started with KUBY <br></br>account details are as followd.<br></br>username : {email} <br></br>password : {email} </div>",
//         subject: "Welcome to KUBY",
//         replacements: "url,email,name",
//         created_at: new Date(),
//         updated_at: new Date(),
//         uuid: uuidv4()
//       },

//       // Meetings Contnet
//       {
//         email_template_id: 6,
//         language_id: 1,
//         content:
//           "<div>Hi {{name}}!<br>Your One-to-One Meeting has been scheduled for {{time}} with {{companion}}. <br></br> </div>",
//         subject: "One-to-One Meeting Schedule",
//         replacements: "name,time,companion",
//         created_at: new Date(),
//         updated_at: new Date(),
//         uuid: uuidv4()
//       },
//       {
//         email_template_id: 7,
//         language_id: 1,
//         content:
//           "<div>Hi {{name}}!<br> Your One-to-One Meeting has been rescheduled for {{time}} with {{companion}}. <br></br></div>",
//         subject: "Meeting Rescheduled",
//         replacements: "name,time,companion",
//         created_at: new Date(),
//         updated_at: new Date(),
//         uuid: uuidv4()
//       },
//       {
//         email_template_id: 8,
//         language_id: 1,
//         content:
//           "<div>Hi {{name}}!<br>Your One-to-One Meeting has been cancelled for {{time}} with {{companion}}. <br></br></div>",
//         subject: "Metting Cancelled",
//         replacements: "name,time,companion",
//         created_at: new Date(),
//         updated_at: new Date(),
//         uuid: uuidv4()
//       }
//     ]);
//   },

//   async down(queryInterface, Sequelize) {
//     /**
//      * Add commands to revert seed here.
//      *
//      * Example:
//      * await queryInterface.bulkDelete('People', null, {});
//      */
//   }
// };
