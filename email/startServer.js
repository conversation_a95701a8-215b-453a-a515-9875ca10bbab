global.server = new Hapi.server({
  host: process.env.NODE_HOST,
  port: process.env.NODE_PORT,
  routes: {
    cors: {
      origin: ['*'],
      headers: ['accept', 'authorization', 'Content-Type', 'If-None-Match',"language","utcoffset","timezone"],
      additionalHeaders: ["Access-Control-Allow-Origin","Access-Control-Allow-Headers","Origin, X-Requested-With, Content-Type"]
    }
  }
});
init=async()=>{
  const swaggerOptions = {
      info: {
        title: process.env.SITE_NAME,
        version:process.env.API_VERSION
      },
      securityDefinitions: {
        Bearer: {
          type: "apiKey",
          name: "Authorization",
          in: "header"
        }
      },
      grouping: "tags",
      sortEndpoints: "ordered",
      consumes: ["application/json"],
      produces: ["application/json"],
      documentationPath: '/docs'
    };
    
    await server.register([auth_jwt],{
      once: true  //critical so that you don't re-init your plugins
    });

    await server.auth.strategy("jwt", "jwt", {
      complete: true,
      key: Common.privateKey, // secret key
      validate: Common.validateToken, // validate function defined in common function for timestamp check
      verifyOptions: { algorithms: ["HS256"] } // algorithm
    });
    server.auth.default("jwt");

    await server.register(
        [
            Inert,
            Vision,
            {plugin: HapiSwagger,options: swaggerOptions},
            {plugin: i18n,options: {locales: process.env.VALID_LANGUANGE_CODES.split(','),directory: __dirname + "/locales",languageHeaderField: "language",defaultLocale:process.env.DEFAULT_LANGUANGE_CODE}},
            {plugin: Routes,options:{routes_dir: Path.join(__dirname, 'routes')}},
            {plugin: Cron,options: { jobs: Constants.CRON }}
          ], {
            once: true  //critical so that you don't re-init your plugins
          }
    );
    await Models.sequelize.authenticate();
    
    await Models.sequelize.sync().then(async () => {
      await server.start();
      console.log("SERVER STARTED AT :", server.info.uri)
    });
   
    server.route({
      method: 'GET',
      path: '/',
      options:{
        auth:false
      },
      handler: function (request, h) {
          return h.response({'Message':'Welcome User'}).code(200);
      }
  });
  
}



// console.log('err.name', err.name);
// console.log('err.message', err.message);
// console.log('err.errors', err.errors);
// err.errors.map(e => console.log(e.message))