"use strict";
module.exports = (sequelize, DataTypes) => {
    const Attachment = sequelize.define(
        "Attachment",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
                allowNull: false
            },
            userId: { type: DataTypes.INTEGER, defaultValue: null },
            inUse: { type: DataTypes.TINYINT, defaultValue: 0 },
            path: { type: DataTypes.STRING, defaultValue: null },
            originalName:{type: DataTypes.STRING, defaultValue: null },
            thumbnailDestination:{type: DataTypes.STRING, defaultValue: null },
            mimeType:{type: DataTypes.STRING, defaultValue: null },
            uniqueName: { type: DataTypes.STRING, allowNull: false },
            extension: { type: DataTypes.STRING, defaultValue: null },
            size: { type: DataTypes.INTEGER, defaultValue: null, comment: "Size is stored in KB" },
        },
        {
            underscored: true,
            tableName: "attachment_attachments"
        }
    );

    Attachment.associate = (models) => {
        // Attachment.belongsTo(models.User, {foreignKey: "userId"});
    };
    
    return Attachment;
}