module.exports = {
    STATUS:{
        INACTIVE:0,
        ACTIVE:1
    },
    CRON:[
        // {
        //     name: 'everyDayCrone',
        //     time: '0 0 * * *',
        //     timezone: 'America/Danmarkshavn',
        //     request: {
        //     method: 'GET',
        //         url: '/cron/everyDayCrone'
        //     },
        //     onComplete: async (res) => {
        //         let data=await Common.DeleteAttachments();
        //         if(true)
        //         {
        //             console.log('CRONE_SUCESSFULL')
        //         }
        //         console.log('------------<PERSON><PERSON> Job Executed ( Every Day ) -----------');
        //     }
        // },
    ],
}