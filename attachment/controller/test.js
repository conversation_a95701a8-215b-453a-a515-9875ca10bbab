// const data = {
//   "success": true,
//   "message": "Request successful",
//   "responseData": {
//     "categoryList": [
//       {
//         "id": 1,
//         "type": "Reason",
//         "name": "Reason"
//       },
//       {
//         "id": 2,
//         "type": "Project",
//         "name": "Project"
//       },
//       {
//         "id": 3,
//         "type": "Protagonist",
//         "name": "Protagonist"
//       },
//       {
//         "id": 4,
//         "type": "Occasion",
//         "name": "Occasion"
//       },
//       {
//         "id": 5,
//         "type": "Trigger",
//         "name": "Trigger"
//       },
//       {
//         "id": 6,
//         "type": "Pain Picture",
//         "name": "Pain Picture"
//       },
//       {
//         "id": 7,
//         "type": "Bridge",
//         "name": "Bridge"
//       },
//       {
//         "id": 8,
//         "type": "Redline",
//         "name": "Red Line"
//       },
//       {
//         "id": 9,
//         "type": "Rewrite",
//         "name": "Rewrite"
//       },
//       {
//         "id": 10,
//         "type": "Affirmation",
//         "name": "Affirmation"
//       },
//       {
//         "id": 11,
//         "type": "Projection",
//         "name": "Projection"
//       },
//       {
//         "id": 12,
//         "type": "Implementation",
//         "name": "Realization"
//       }
//     ],
//     "projectDetails": {
//       "id": 1992,
//       "title": "New soulwriting",
//       "reason": "This is new soulwriting",
//       "projectMeta": {
//         "bridgeCharacter": [
//           {
//             "data": {
//               "selectedText": [
//                 {
//                   "step": 4,
//                   "type": "past",
//                   "category": 4,
//                   "lineNumber": 5,
//                   "selectionText": "Picture"
//                 },
//                 {
//                   "step": 2,
//                   "type": "present",
//                   "category": 2,
//                   "lineNumber": 2,
//                   "selectionText": "fsdfsdf"
//                 },
//                 {
//                   "step": 4,
//                   "type": "past",
//                   "category": 4,
//                   "lineNumber": 6,
//                   "selectionText": "icture 4"
//                 },
//                 {
//                   "step": 2,
//                   "type": "present",
//                   "category": 2,
//                   "lineNumber": 1,
//                   "selectionText": "sdfsdf"
//                 }
//               ],
//               "characterflag": true
//             }
//           },
//           {
//             "category_reason_title": {
//               "title": null,
//               "reason": null,
//               "bridgeId": 7,
//               "redlineId": 8,
//               "protogonistId": null
//             }
//           },
//           {
//             "trigger_date": null
//           },
//           {
//             "projection_date": null
//           }
//         ]
//       },
//       "comments": {
//         "bridge": "<p>Bridge comment</p>",
//         "reason": "<p>sfjls jfsldf jsldjf lsjflksjfd</p><p>sdf</p><p>sdf</p><p>s</p><p>dfs</p><p>df</p><p>sd</p><p>fs</p><p>dfsd</p>",
//         "project": "<p>Project comment</p>"
//       },
//       "companion": {
//         "id": 176,
//         "userId": 106,
//         "firstName": "Spigen",
//         "lastName": "Companion",
//         "title": "1",
//         "profilePhotoUrl": "resources/attachments/2024/10/19/dbf983a0-a67d-11ef-bde0-f7dc97387fbb.jpeg",
//         "profilePhotoId": "5572",
//         "gender": 1,
//         "email": "<EMAIL>"
//       },
//       "author": {
//         "id": 6,
//         "userId": 6,
//         "firstName": "Neeraj",
//         "lastName": "Thakur",
//         "title": "1",
//         "profilePhotoUrl": null,
//         "profilePhotoId": null,
//         "gender": 1,
//         "email": "<EMAIL>"
//       }
//     },
//     "contents": [
//       {
//         "id": 88656044,
//         "uuid": "dd284b1a-1e01-4a2f-9fdf-3fc7b6c0699d",
//         "categoryId": 4,
//         "lineNumber": 1,
//         "content": "<p>sdfsdf</p>",
//         "character": {
//           "id": 4312,
//           "title": "1",
//           "name": "I",
//           "age": "32",
//           "protogonistObject": {
//             "nativeLanguage": "English",
//             "defaultCharacter": true
//           }
//         }
//       },
//       {
//         "id": 88656045,
//         "uuid": "de429e53-54d2-4ed3-9b14-bff55ed25939",
//         "categoryId": 4,
//         "lineNumber": 2,
//         "content": "<p>sd fsdfsdf</p>",
//         "character": {
//           "id": 4312,
//           "title": "1",
//           "name": "I",
//           "age": "32",
//           "protogonistObject": {
//             "nativeLanguage": "English",
//             "defaultCharacter": true
//           }
//         }
//       },
//       {
//         "id": 88656046,
//         "uuid": "100efaa5-5ed3-493a-b561-40c5c41d4276",
//         "categoryId": 5,
//         "lineNumber": 3,
//         "content": "<p>Trigger 1</p>",
//         "character": {
//           "id": 4312,
//           "title": "1",
//           "name": "I",
//           "age": "32",
//           "protogonistObject": {
//             "nativeLanguage": "English",
//             "defaultCharacter": true
//           }
//         }
//       },
//       {
//         "id": 88656047,
//         "uuid": "bf814103-dae0-418c-8195-38ec31b24f17",
//         "categoryId": 5,
//         "lineNumber": 4,
//         "content": "<p>Trigger 2</p>",
//         "character": {
//           "id": 4312,
//           "title": "1",
//           "name": "I",
//           "age": "32",
//           "protogonistObject": {
//             "nativeLanguage": "English",
//             "defaultCharacter": true
//           }
//         }
//       },
//       {
//         "id": 88656048,
//         "uuid": "f37ba226-e800-4ccd-adcf-22237ba0567b",
//         "categoryId": 6,
//         "lineNumber": 5,
//         "content": "<p>Pain Picture 3</p>",
//         "character": {
//           "id": 4312,
//           "title": "1",
//           "name": "I",
//           "age": "32",
//           "protogonistObject": {
//             "nativeLanguage": "English",
//             "defaultCharacter": true
//           }
//         }
//       },
//       {
//         "id": 88656049,
//         "uuid": "a99795d8-d050-4792-bd95-a3fb37990ab7",
//         "categoryId": 6,
//         "lineNumber": 6,
//         "content": "<p>Pain Picture 4</p>",
//         "character": {
//           "id": 4312,
//           "title": "1",
//           "name": "I",
//           "age": "32",
//           "protogonistObject": {
//             "nativeLanguage": "English",
//             "defaultCharacter": true
//           }
//         }
//       },
//       {
//         "id": 88656050,
//         "uuid": "3caa0881-22e4-4dff-9103-9ef9dd1f1b44",
//         "categoryId": 9,
//         "lineNumber": 7,
//         "content": "<p>Rewrite 1</p>",
//         "character": {
//           "id": 4312,
//           "title": "1",
//           "name": "I",
//           "age": "32",
//           "protogonistObject": {
//             "nativeLanguage": "English",
//             "defaultCharacter": true
//           }
//         }
//       },
//       {
//         "id": 88656051,
//         "uuid": "8c1302be-a9c9-46d0-8609-96eea399deec",
//         "categoryId": 9,
//         "lineNumber": 8,
//         "content": "<p>Rewrite 2</p>",
//         "character": {
//           "id": 4312,
//           "title": "1",
//           "name": "I",
//           "age": "32",
//           "protogonistObject": {
//             "nativeLanguage": "English",
//             "defaultCharacter": true
//           }
//         }
//       },
//       {
//         "id": 88656052,
//         "uuid": "7b5c9df6-2c57-4a13-92d9-25bdd85e86b6",
//         "categoryId": 10,
//         "lineNumber": 9,
//         "content": "<p>Rewrite 3</p>",
//         "character": {
//           "id": 4312,
//           "title": "1",
//           "name": "I",
//           "age": "32",
//           "protogonistObject": {
//             "nativeLanguage": "English",
//             "defaultCharacter": true
//           }
//         }
//       },
//       {
//         "id": 88656053,
//         "uuid": "0d6a3e01-9ec6-4358-a5df-e514797a5d8e",
//         "categoryId": 11,
//         "lineNumber": 10,
//         "content": "<p>Rewrite 4</p>",
//         "character": {
//           "id": 4312,
//           "title": "1",
//           "name": "I",
//           "age": "32",
//           "protogonistObject": {
//             "nativeLanguage": "English",
//             "defaultCharacter": true
//           }
//         }
//       },
//       {
//         "id": 88656054,
//         "uuid": "180aa8b5-18a4-48a7-99b5-8157454dadac",
//         "categoryId": 11,
//         "lineNumber": 11,
//         "content": "<p>Projection 5</p>",
//         "character": {
//           "id": 4312,
//           "title": "1",
//           "name": "I",
//           "age": "32",
//           "protogonistObject": {
//             "nativeLanguage": "English",
//             "defaultCharacter": true
//           }
//         }
//       },
//       {
//         "id": 88656055,
//         "uuid": "e2d9cd4d-ea8a-45f2-8baa-499f7509b9d7",
//         "categoryId": 11,
//         "lineNumber": 12,
//         "content": "",
//         "character": {
//           "id": 4312,
//           "title": "1",
//           "name": "I",
//           "age": "32",
//           "protogonistObject": {
//             "nativeLanguage": "English",
//             "defaultCharacter": true
//           }
//         }
//       },
//       {
//         "id": 88656056,
//         "uuid": "e8378340-bbfb-492d-978a-92eecfbca356",
//         "categoryId": 12,
//         "lineNumber": 13,
//         "content": "<p>Rewrite 5</p>",
//         "character": {
//           "id": 4325,
//           "title": "1",
//           "name": "John",
//           "age": "22",
//           "protogonistObject": {
//             "moreInfo": {
//               "ageInTheScene": "22",
//               "sympatheticValue": "very_trustworthy"
//             },
//             "surveyData": [
//               {
//                 "q": "What do you call the person?",
//                 "id": 2,
//                 "text": "John",
//                 "gender": "",
//                 "isText": false,
//                 "parentId": 1,
//                 "forKinnship": false
//               },
//               {
//                 "q": "Family",
//                 "id": 3,
//                 "text": "",
//                 "gender": "",
//                 "isText": false,
//                 "parentId": 2,
//                 "forKinnship": false
//               },
//               {
//                 "q": "Siblings",
//                 "id": 12,
//                 "text": "",
//                 "gender": "",
//                 "isText": false,
//                 "parentId": 3,
//                 "forKinnship": false
//               },
//               {
//                 "q": "Brother",
//                 "id": 13,
//                 "text": "",
//                 "gender": "male",
//                 "isText": false,
//                 "parentId": 12,
//                 "forKinnship": true
//               }
//             ],
//             "defaultCharacter": true
//           }
//         }
//       },
//       {
//         "id": 88656057,
//         "uuid": "03b5805a-f114-45f5-a836-acb0f7dd7bb1",
//         "categoryId": 12,
//         "lineNumber": 14,
//         "content": "<p>Self character</p>",
//         "character": {
//           "id": 4312,
//           "title": "1",
//           "name": "I",
//           "age": "32",
//           "protogonistObject": {
//             "nativeLanguage": "English",
//             "defaultCharacter": true
//           }
//         }
//       },
//       {
//         "id": 88656058,
//         "uuid": "8c058610-2a25-462f-89e0-c13a8e491875",
//         "categoryId": 12,
//         "lineNumber": 15,
//         "content": "<p>Added by companion</p>",
//         "character": {
//           "id": 4312,
//           "title": "1",
//           "name": "I",
//           "age": "32",
//           "protogonistObject": {
//             "nativeLanguage": "English",
//             "defaultCharacter": true
//           }
//         }
//       },
//       {
//         "id": 88656059,
//         "uuid": "cf9f099e-d99f-4dbf-b111-0450a4fa8ab9",
//         "categoryId": 12,
//         "lineNumber": 16,
//         "content": "<p>New row</p>",
//         "character": {
//           "id": 4312,
//           "title": "1",
//           "name": "I",
//           "age": "32",
//           "protogonistObject": {
//             "nativeLanguage": "English",
//             "defaultCharacter": true
//           }
//         }
//       }
//     ],
//     "protogonistList": [
//       {
//         "id": 4312,
//         "title": "1",
//         "name": "I",
//         "age": "32",
//         "protogonistObject": {
//           "nativeLanguage": "English",
//           "defaultCharacter": true
//         }
//       },
//       {
//         "id": 4325,
//         "title": "1",
//         "name": "John",
//         "age": "22",
//         "protogonistObject": {
//           "moreInfo": {
//             "ageInTheScene": "22",
//             "sympatheticValue": "very_trustworthy"
//           },
//           "surveyData": [
//             {
//               "q": "What do you call the person?",
//               "id": 2,
//               "text": "John",
//               "gender": "",
//               "isText": false,
//               "parentId": 1,
//               "forKinnship": false
//             },
//             {
//               "q": "Family",
//               "id": 3,
//               "text": "",
//               "gender": "",
//               "isText": false,
//               "parentId": 2,
//               "forKinnship": false
//             },
//             {
//               "q": "Siblings",
//               "id": 12,
//               "text": "",
//               "gender": "",
//               "isText": false,
//               "parentId": 3,
//               "forKinnship": false
//             },
//             {
//               "q": "Brother",
//               "id": 13,
//               "text": "",
//               "gender": "male",
//               "isText": false,
//               "parentId": 12,
//               "forKinnship": true
//             }
//           ],
//           "defaultCharacter": true
//         }
//       }
//     ]
//   }
// }
// const fs = require("fs");
// const {  Document, Packer, Paragraph, Table, TableRow, TableCell, WidthType, ShadingType, TextRun } = require("docx");

// const children = [];
// const headerProperties = { bold: true, color: "#4472c4", size: 26 };
// const tableHeaderProperties = { bold: true, color: "#4472c4" };

// const categoryList = data.responseData.categoryList;
// const projectDetails = data.responseData.projectDetails;
// const contents = data.responseData.contents;
// const protogonistList = data.responseData.protogonistList;

// let serialNumber = 1;

// for(let category of categoryList) {
//   if(category.type === "Redline") continue;

//   children.push(new Paragraph({
//     children: [new TextRun({ text: category.name, ...headerProperties })]
//   }))
//   // children.push(new Paragraph({}))

//   if(category.type === "Reason") {
//     children.push(new Paragraph({
//       children: [new TextRun({ text: projectDetails.reason })],
//     }))
//     children.push(new Paragraph({}))
//   }

//   if(category.type === "Project") {
//     children.push(new Paragraph({
//       children: [new TextRun({ text: projectDetails.title })],
//     }))
//     children.push(new Paragraph({}))
//   }

//   if(category.type === "Protagonist") {
//     let rows = [];
//     for(let item of protogonistList) {
//       rows.push(
//         new TableRow({
//           children: [
//             new TableCell({
//               children: [new Paragraph(
//                 {
//                   children: [new TextRun({ text: item.name })],
//                 }
//               )]
//             }),
//             new TableCell({
//               children: [
//                 new Paragraph(
//                   {
//                     children: [new TextRun({ text: `Gender: ${item.title == "1" ? "Male" : "Female"}` })],
//                   }
//                 ),
//                 new Paragraph(
//                   {
//                     children: [new TextRun({ text: `Age: ${item.age}` })],
//                   }
//                 )
//               ]
//             })
//           ]
//         })
//       )
//     }

//     const tableData = new Table({
//       width: { size: 100, type: WidthType.PERCENTAGE },
//       rows: rows
//     });

//     children.push(new Paragraph({spacing: { before: 0 }}))
//     children.push(tableData)
//     children.push(new Paragraph({}))

//   }



//   if(category.type === "Bridge") {
//     const bridgeObj = projectDetails.projectMeta.bridgeCharacter.find(item => item.hasOwnProperty("data"));
//     const bridge = bridgeObj.data.selectedText;
//     const past = [];
//     const present = [];

//     for(let item of bridge) {
//       if(item.type === "past") past.push(item.selectionText)
//       if(item.type === "present") present.push(item.selectionText)
//     }

//     let rows = [new TableRow({
//       children: [
//         new TableCell({
//           shading: { fill: "#efefef", type: ShadingType.CLEAR },
//           children: [new Paragraph({
//             children: [new TextRun({ text: "Past", ...tableHeaderProperties })],
//           })]
//         }),
//         new TableCell({
//           shading: { fill: "#efefef", type: ShadingType.CLEAR },
//           children: [new Paragraph({
//             children: [new TextRun({ text: "Present", ...tableHeaderProperties })],
//           })]
//         })
//       ]
//     })]


//     for(let i = 0; i < Math.max(past.length,present.length); i++) {
//       rows.push(
//         new TableRow({
//           children: [
//             new TableCell({
//               children: [new Paragraph(
//                 {
//                   children: [new TextRun({ text: past[i] ? past[i] : "" })],
//                 }
//               )]
//             }),
//             new TableCell({
//               children: [new Paragraph(
//                 {
//                   children: [new TextRun({ text: present[i] ? present[i] : "" })],
//                 }
//               )]
//             })
//           ]
//         })
//       )
//     }

//     const tableData = new Table({
//       width: { size: 100, type: WidthType.PERCENTAGE },
//       rows: rows
//     });

//     children.push(new Paragraph({spacing: { before: 0 }}))
//     children.push(tableData)
//     children.push(new Paragraph({}))

//   }

//   if(
//     category.type === "Occasion" || category.type === "Trigger" 
//     || category.type === "Pain Picture" || category.type === "Implementation"
//     || category.type === "Rewrite" || category.type === "Affirmation" || category.type === "Projection"
//   ) {

//     if(category.type === "Trigger") {
//       const triggerDateObj = projectDetails.projectMeta.bridgeCharacter.find(item => item.hasOwnProperty("trigger_date"));
//       triggerDateObj ? triggerDateObj.trigger_date : "-";

//       children.push(new Paragraph({
//         children: [new TextRun({ text: "When did the project enter your life?" })],
//       })) 
//       children.push(new Paragraph({
//         children: [new TextRun({ text: triggerDateObj })],
//       })) 
//     }
   
//     if(category.type === "Implementation") {
//       const triggerDateObj = projectDetails.projectMeta.bridgeCharacter.find(item => item.hasOwnProperty("projection_date"));
//       triggerDateObj ? triggerDateObj.trigger_date : "-";

//       children.push(new Paragraph({
//         children: [new TextRun({ text: "Target Date" })],
//       })) 
//       children.push(new Paragraph({
//         children: [new TextRun({ text: triggerDateObj })],
//       })) 
//     }

//     let rows = [new TableRow({
//       children: [
//         new TableCell({
//           shading: { fill: "#efefef", type: ShadingType.CLEAR },
//           children: [new Paragraph({children: [new TextRun({ text: "No.", ...tableHeaderProperties })]})]
//         }),
//         new TableCell({
//           shading: { fill: "#efefef", type: ShadingType.CLEAR },
//           children: [new Paragraph(
//             {
//               children: [new TextRun({ text: "Protagonist", ...tableHeaderProperties })],
//             }
//           )]
//         }),
//         new TableCell({
//           shading: { fill: "#efefef", type: ShadingType.CLEAR },
//           children: [new Paragraph(
//             {
//               children: [new TextRun({ text: "Literal speech/direction", ...tableHeaderProperties })],
//             }
//           )]
//         }),
//         new TableCell({
//           shading: { fill: "#efefef", type: ShadingType.CLEAR },
//           children: [new Paragraph(
//             {
//               children: [new TextRun({ text: "Comment", ...tableHeaderProperties })],
//             }
//           )]
//         })
//       ]
//     })]


//     for(let item of contents) {
//       if(item.categoryId == category.id) {
//         rows.push(
//           new TableRow({
//             children: [
//               new TableCell({
//                 children: [new Paragraph(
//                   {
//                     children: [new TextRun({ text: serialNumber.toString() })],
//                   }
//                 )]
//               }),
//               new TableCell({
//                 children: [new Paragraph(
//                   {
//                     children: [new TextRun({ text: item.character.name })],
//                   }
//                 )]
//               }),
//               new TableCell({
//                 children: [new Paragraph(
//                   {
//                     children: [new TextRun({ text: item.content })],
//                   }
//                 )]
//               }),
//               new TableCell({
//                 children: [new Paragraph(
//                   {
//                     children: [new TextRun({ text: "Dummy Comment" })],
//                   }
//                 )]
//               })
//             ]
//           })
//         )

//         serialNumber += 1;
//       }
//     }

//     const tableData = new Table({
//       width: { size: 100, type: WidthType.PERCENTAGE },
//       rows: rows
//     });

//     children.push(new Paragraph({spacing: { before: 0 }}))
//     children.push(tableData)
//     children.push(new Paragraph({}))
//   }

// }

// // Create a document
// const doc = new Document({
//   styles: {
//     default: {
//       document: {
//         paragraph: {
//           spacing: { before: 100 }, // Add spacing before & after non-table paragraphs
//         },
//         // table: {
//         //   spacing: { before: 100 }, // Add spacing before & after non-table paragraphs
//         // },
//         run: {
//           font: "Inter", // Global font
//           size: 22, // 14pt (half-points)
//           color: "#07080a", // Black text
//         },
//       },
//     },
//   },
//   sections: [
//     {
//       properties: {},
//       children: children
//     }
//   ]
// });

// // Generate and save the Word file
// Packer.toBuffer(doc).then((buffer) => {
//     fs.writeFileSync("simple.docx", buffer);
//     console.log("Word document created successfully!");
// });
