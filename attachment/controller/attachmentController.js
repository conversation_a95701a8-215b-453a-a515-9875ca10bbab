"use strict";
const { constant } = require('lodash');
const { Readable } = require('stream');
const extensions = require('../extensions');
const imageThumbnail = require('image-thumbnail');
const Downloader = require("nodejs-file-downloader");
const xlsx = require('xlsx');
/** Create directory structure */
const createFolderIfNotExists = () => {
    const dt = new Date();
    const folder = dt.getUTCFullYear() + "/" + dt.getUTCMonth() + "/" +  dt.getUTCDate() + '/';
    const targetDir = 'resources/attachments/' + folder;
    Fs.mkdirSync(targetDir, { recursive: true }, 0o777);

    return targetDir
}

const createThumbnailFolderIfNotExists=async()=>{
    const dt = new Date();
    const folder = dt.getUTCFullYear() + "/" + dt.getUTCMonth() + "/" +  dt.getUTCDate() + '/';
    const targetDir = 'resources/attachments/thumbnail/' + folder;
    await Fs.mkdirSync(targetDir, { recursive: true }, 0o777);
    
    return targetDir
}
 /** Check file is array or object and call respective functions */
const uploader = (file, options) => {
    return Array.isArray(file) ? filesHandler(file, options) : fileHandler(file, options);
}

/** Function to upload multiple files */
const filesHandler = (files, options) => {
    const promises = files.map(x => fileHandler(x, options));
    return Promise.all(promises);
}

/** unlink file from path */
const unlinkFile = (path) => {
    Fs.unlink(path, (err) => {
        if (err) {
            console.error(err)
            return
        }      
    })
}

const writeFile = (destination, filename, data) => {
	return new Promise(async (resolve, reject) => {
	  await Fs.writeFile(destination  + filename, data, (err) => {
		if (err) {
		  return reject(false);
		}
		return resolve(true);
	  });
	});
};


/** Function to upload single file */
const fileHandler = async (file, options) => {
    if (!file.hapi.filename) throw new Error(422);
    const extension         =   Path.extname(file.hapi.filename);
    const name              =   uuid.v1() + extension;
    const destinationPath   =   `${options.dest}${name}`;
    const fileStream        =   await Fs.createWriteStream(destinationPath);
    const thumbnailPath     =   options.thumbnailPath;
    return new Promise((resolve, reject) => {
        file.on('error', (err) => {
            reject(err);
        });
        file.pipe(fileStream);
        file.on('end', async (err) => {
            setTimeout(async() => {
                const { size } = Fs.statSync(destinationPath);
                const kb = Math.ceil(size / 1000);
                //save thumbnail
                //find destination path for thumbnail
                let thumbnailDestination = thumbnailPath;
                let imageExtensions=[".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg", ".webp", ".tiff", ".ico"]
                if(imageExtensions.includes(extension)){
                    let thumbName = await imageThumbnail(destinationPath);
                      let thumbData = await writeFile(thumbnailDestination,name,thumbName);
                }

                const fileDetails = {
                    uniqueName: name,
                    extension: extension,           
                    path: destinationPath,
                    size: kb,
                    userId: options.user_id,
                    thumbnailDestination:thumbnailDestination+name,
                    in_use: 0,
                    originalName:file.hapi.filename,
                    mimeType:file.hapi.headers['content-type']
                }
                resolve(fileDetails);
            }, 100); 
        });
    });
}

/** Upload attachment */
exports.uploadFile = async (request, header) => {
    try { 
        if (request.payload && request.payload['files']) {
            const path = createFolderIfNotExists();
            const thumbnailPath=await createThumbnailFolderIfNotExists()
            const user_id = request.payload.hasOwnProperty('user_id') ? request.payload.user_id : null
            const upload_info = {
                dest: path,
                user_id: user_id,
                thumbnailPath
            }
            let fileDetails = await uploader(request.payload['files'], upload_info);
            if ((fileDetails && fileDetails.hasOwnProperty('uniqueName')) || (Array.isArray(fileDetails) && fileDetails && fileDetails.length)) {
                fileDetails = Array.isArray(fileDetails) ? fileDetails : [ fileDetails ]; 
         
                const respData = await Models.Attachment.bulkCreate(fileDetails, { returning: true });
                return header.response({
                    responseData: respData,
                    message: request.i18n.__("FILE_UPLOADED_SUCCESSFULLY")
                }).code(200);
            }
        }
    } catch(err) {
        console.error('err',err)
        if (err.message == 422) {
            return Common.generateError(request, 422, 'SELECT_FILE_TO_UPLOAD', err);
        }
        return Common.generateError(request, 500, 'SOMETHING_WENT_WRONG_WITH_EXCEPTION', err);
    }
}

/**  Delete attachment */
exports.deleteAttachments = async (request, header) => {
    try {
        const deleteIds = request.query.ids;
        const idsArray = deleteIds.split`,`.map(x => +x);
        const attachmentArray = await Models.Attachment.findAll({ where: {id: idsArray}, attributes: ['id', 'path'] });
        attachmentArray.forEach( async (data) => {
            unlinkFile(data.path);
            await Models.Attachment.destroy({ where: { id: data.id } });
        });
        if (attachmentArray.length) {
            return header.response({
                message: request.i18n.__("FILE_DELETED_SUCCESSFULLY")
            }).code(200);
        } else {
            return Common.generateError(request, 400, 'FILE_NOT_FOUND');
        }
    } catch (err)  {

        return Common.generateError(request, 500, 'SOMETHING_WENT_WRONG_WITH_EXCEPTION', err);
    }
}

/** Download attachment */
exports.downloadFile = async (request, h) => {
    try {
        const attachment = await Models.Attachment.findOne({where:{id: request.query.id}, attributes: ['id', 'path', 'uniqueName', 'extension','originalName']});
        if (attachment) {
            if (attachment.path && attachment.uniqueName) {
                const stream = Fs.createReadStream(attachment.path);
                const streamData = new Readable().wrap(stream);
                const contentType = extensions.getContentType(attachment.extension);
                if(req.query.returnData){
                    return h.response({data:streamData,contentType:contentType,name:attachment.uniqueName}).code(200)  
                }else{
                    return h.response(streamData)
                    .header('Content-Type', contentType)
                    .header('Content-Disposition', 'attachment; filename= ' + attachment.uniqueName);
                }
            }
        } else {
            return Common.generateError(request, 404, 'FILE_NOT_FOUND');
        }
    } catch (err)  {
        console.log('error',err);
        return Common.generateError(request, 500, 'SOMETHING_WENT_WRONG_WITH_EXCEPTION', err);
    }
}

/** Download attachment */
// exports.downloadRecordingFile = async (request, h) => {
//     try {
//         const meetingId = request.query.id;
//         const filePath = `recordings/${meetingId}.m4a`;
//         const fs = require('fs').promises;

//         console.log("********************************************")

//         await fs.access(filePath)
//         .then(() => {
//             console.log("222222222222222222222222222222222222222222")
//             const stream = Fs.createReadStream(filePath);
//             const streamData = new Readable().wrap(stream);
//             const contentType = extensions.getContentType("m4a");
//             if(request.query.returnData){
//                 console.log(" ======================= 555555555555555555")
//                 return h.response({data:streamData,contentType:contentType,name:meetingId}).code(200)  
//             }else{
//                 console.log(" ======================= 66666666666666666")
//                 return h.response(streamData)
//                 .header('Content-Type', contentType)
//                 .header('Content-Disposition', 'attachment; filename= ' + meetingId);
//             }
//         })
//         .catch((err) => {
            
//             console.log("******************************************")
//             console.log(err)
//             return Common.generateError(request, 404, 'FILE_NOT_FOUND');
//         });

//         console.log("4444444444444444444444444444444444444444444444")
//     } catch (err)  {
//         console.log('error',err);
//         return Common.generateError(request, 500, 'SOMETHING_WENT_WRONG_WITH_EXCEPTION', err);
//     }
// }



exports.downloadRecordingFile = async (request, h) => {
    try {
        const meetingId = request.query.id;
        const filePath = `recordings/${meetingId}.m4a`;
        const fs = require('fs').promises;
        // Check if the file exists
        try {
            await fs.access(filePath);
        } catch (err) {
            console.log(err)
            return Common.generateError(request, 404, 'FILE_NOT_FOUND');
        }

        // const stream = Fs.createReadStream(filePath);
        // const streamData = new Readable().wrap(stream);
        // const contentType = extensions.getContentType("m4a");

        // if (request.query.returnData) {
        //     return h.response({ data: streamData, contentType: contentType, name: `${meetingId}.m4a` }).code(200);
        // } else {
        //     return h.response(streamData)
        //         .header('Content-Type', contentType)
        //         .header('Content-Disposition', 'attachment; filename=' + meetingId + '.m4a');
        // }

        const contentType = extensions.getContentType("m4a");
        let buffer = await Fs.readFileSync(filePath);
        // if(request.query.returnData)
            // {
                return h.response(buffer.toString('base64')).header('Content-Type',contentType).header('Content-Disposition', 'attachment; filename=' + meetingId + '.m4a').code(200)
            // }
            // return h.response(buffer).type(contentType).code(200)


        console.log("4444444444444444444444444444444444444444444444");
    } catch (err) {
        console.log('error', err);
        return Common.generateError(request, 500, 'SOMETHING_WENT_WRONG_WITH_EXCEPTION', err);
    }
};

exports.downloadPDFFile = async (request, h) => {
    try {
        const filePath = request.query.path;
        const fs = require('fs').promises;
        // Check if the file exists
        try {
            await fs.access(filePath);
        } catch (err) {
            return Common.generateError(request, 404, 'FILE_NOT_FOUND');
        }

        // const stream = Fs.createReadStream(filePath);
        // const streamData = new Readable().wrap(stream);
        // const contentType = extensions.getContentType("m4a");

        // if (request.query.returnData) {
        //     return h.response({ data: streamData, contentType: contentType, name: `${meetingId}.m4a` }).code(200);
        // } else {
        //     return h.response(streamData)
        //         .header('Content-Type', contentType)
        //         .header('Content-Disposition', 'attachment; filename=' + meetingId + '.m4a');
        // }

        const contentType = extensions.getContentType("pdf");
        let buffer = await Fs.readFileSync(filePath);
        // if(request.query.returnData)
            // {
                return h.response(buffer.toString('base64')).header('Content-Type',contentType).header('Content-Disposition', 'attachment; filename=' + soulwriting + '.pdf').code(200)
            // }
            // return h.response(buffer).type(contentType).code(200)


        console.log("4444444444444444444444444444444444444444444444");
    } catch (err) {
        console.log('error', err);
        return Common.generateError(request, 500, 'SOMETHING_WENT_WRONG_WITH_EXCEPTION', err);
    }
};






/** View attachment */
exports.viewAttachments = async (request, h) => {
    try {
        const attachment = await Models.Attachment.findOne({where:{id: request.query.id}, attributes: ['id', 'path', 'uniqueName', 'extension','originalName']});
        if (attachment) {
            if (attachment.path && attachment.uniqueName) {
                const contentType = extensions.getContentType(attachment.extension);
            let buffer = await Fs.readFileSync(attachment.path);
            if(request.query.returnData)
            {
                return h.response(buffer.toString('base64')).header('Content-Type',contentType).code(200)
            }
            return h.response(buffer).type(contentType).code(200)
            }

        } else {
            return Common.generateError(request, 404, 'FILE_NOT_FOUND');
        }
    } catch (err)  {
       console.log('error',err);
        return Common.generateError(request, 500, 'SOMETHING_WENT_WRONG_WITH_EXCEPTION', err);
    }
}

exports.updateAttachment=async(req,h)=>{ 
    try{
let data=req.payload.data;
if(data!==null)
for (const iterator of data) {

    let findAttachment= await Models.Attachment.findOne({where:{id:iterator.id}});
    if(findAttachment) await findAttachment.update({inUse:iterator.status})
   // console.log('find Attachment',findAttachment)
}
return h.response({sucess:true,message:req.i18n.__('SUCESS'),responseData:{}}).code(200)
    }
    catch(err){
        console.error('error',err)
        return h.response({sucess:false,message:req.i18n.__('SOMETHING_WENT_WRONG_WITH_EXCEPTION'),responseData:err}).code(500)
        //return Common.generateError(request, 500, 'SOMETHING_WENT_WRONG_WITH_EXCEPTION', err);
    }
}



exports.checkValid= async(req,h)=>{
    let id =req.query.id
try{
    let found=await Models.Attachment.findOne({where:{id}});
if(!found)return h.response({sucess:false,message:req.i18n.__('REQUESTED_FILE_NOT_FOUND'),responseData:{}}).code(404)

return h.response({sucess:true,message:req.i18n.__('SUCESS_FOUND'),responseData:{}}).code(200)

}catch(err){
    return h.response({sucess:false,message:req.i18n.__('SOMETHING_WENT_WRONG_WITH_EXCEPTION'),responseData:err}).code(500)
}
}


const downloadFile = async (url, token, meetingId) => {
    const targetDir = `resources/attachments/meetings/` + meetingId ;
    await Fs.mkdirSync(targetDir, { recursive: true }, 0o777)
    let filePath = targetDir+ "/" +meetingId+".m4a";


    console.log(" ========================== ")
    console.log(" ========================== ")
    console.log(" ========================== ")
    console.log(" ========================== ")
    console.log(" ========================== ")
    console.log(" ========================== ")
    console.log(" ========================== ")
    console.log(" ========================== ")

console.log({
    url, directory: targetDir, fileName:meetingId+".m4a", headers: {authorization: `Bearer ${token}`, 'content-type': 'application/json'},
})

    const downloader = new Downloader({
        url, directory: targetDir, fileName:meetingId+".m4a", headers: {authorization: `Bearer ${token}`, 'content-type': 'application/json'},
        onProgress: function (percentage, chunk, remainingSize) {
          console.log("% ", percentage);
          console.log("Current chunk of data: ", chunk);
          console.log("Remaining bytes: ", remainingSize);
        },
      });
      console.log("======== passed ========")
      try { 
        await downloader.download();
        let attachment = {uniqueName: meetingId+".m4a", name: meetingId+".m4a", path: filePath,extension: "m4a", inUse: 1};
        return {success: true, attachment}
      } catch (error) { 
        console.log(error) 
        return {success: false, attachment: null}
    }

}

exports.zoomAttachmentWebhook = async(req,h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const meetingId = req.payload.meetingId;
        const downloadUrl = req.payload.downloadUrl;
        const videoUrl = req.payload.videoUrl;
        const passcode = req.payload.passcode;
        const token = req.payload.token;
        let attachmentContent = await downloadFile(downloadUrl, token, meetingId.toString());
        if(!attachmentContent.success) {
            await transaction.rollback();
            return h.response({sucess:false,message:req.i18n.__('TRY_AGAIN'),responseData:{}}).code(400)
        }
        let attachment = await Models.Attachment.create(attachmentContent.attachment, {transaction});
        
        let eventUrl = `${process.env.EVENT_MANAGEMENT_GATEWAY}/zoom-details`;
        let responseData = await Common.axiosRequest(eventUrl,'POST', {}, { zoomId: meetingId,audioRecording: attachment, audioLink: downloadUrl, videoLink: videoUrl, videoPassword: passcode });
        console.log(responseData, " ==================== response data")

        await transaction.commit();
        return h.response({sucess:true,message:req.i18n.__('SUCESS_FOUND'),responseData:{attachment}}).code(200)
    } catch (error) {
        await transaction.rollback();
        console.log("================ error block ======================", error);
        return h.response({sucess:false,message:req.i18n.__('SOMETHING_WENT_WRONG_WITH_EXCEPTION'),responseData:error}).code(500)
    }
}

exports.generatePdf = async(req,h) => {
    try {
        // const projectId = 1019;
        // const version = 3;
        // const language= "en";
        const projectId = req.payload.projectId;
        const version = req.payload.version;
        const language= req.payload.language;
        const url = `https://portal.kuby.info/soulwriting-pdf?projectId=${projectId}&version=${version}&language=${language}`;
        // const url = `https://google.com`;
        const Fs = require("fs");
        const targetDir = `resources/soulwriting/`;
        Fs.mkdirSync(targetDir, { recursive: true }, 0o777)
        
        const filePath = targetDir + projectId+"_"+version+"_"+language+".pdf";

        console.log(filePath, " =============== filepath")


        const puppeteer = require("puppeteer");
        const browser = await puppeteer.launch({executablePath: "/usr/bin/chromium-browser"});
        console.log("=========================== browser")
        console.log(browser)
        const page = await browser.newPage();
        console.log("================= page")
        console.log(page)
        await page.goto(url, { waitUntil: 'networkidle2' });
        console.log("================= goto")
        console.log(url)
        await page.emulateMediaType('screen');
        await page.pdf({ path: filePath, margin: { bottom: 30, top: 30 } });
        console.log("================= pdf")
        await browser.close();

        return h.response({sucess:true,message:req.i18n.__('SUCESS_FOUND'),responseData:{filePath: filePath}}).code(200)
    } catch (error) {
        console.log("================ error block ======================", error);
        return h.response({sucess:false,message:req.i18n.__('SOMETHING_WENT_WRONG_WITH_EXCEPTION'),responseData:error}).code(500)
    }
}

exports.downloadFileFromUrl = async(req, h) => {
    const transaction = await Models.sequelize.transaction();

    try
    {
        const url = req.payload.url;
        const fsPromises = Fs.promises;

        const fileResponse = await Axios({
            url: url,
            method: "GET",
            responseType: "stream",
        });

        const path = createFolderIfNotExists();
        const size = fileResponse.headers["Content-Length"] / 1000;

        const extension         =   url.split(".").at(-1);
        const name              =   uuid.v1() + "." + extension;
        const destinationPath   =   `${path}${name}`;
        await fsPromises.writeFile(destinationPath, fileResponse.data);

        const fileDetails = {
            uniqueName           : name,
            extension            : extension,           
            path                 : destinationPath,
            size                 : size,
            userId               : null,
            thumbnailDestination : null,
            in_use               : 1,
            originalName         :url.split("/").at(-1),
            mimeType             :fileResponse.headers['content-type']
        }

        const respData = await Models.Attachment.create(fileDetails, { transaction });

        await transaction.commit();
        return h.response({sucess:false,message:req.i18n.__('ATTACHMENT_UPLOADED_SUCCESSFULLY'),responseData:respData}).code(201)
    }
    catch (error)
    {
        console.log(error);
        await transaction.rollback();
        return h.response({sucess:false,message:req.i18n.__('SOMETHING_WENT_WRONG_WITH_EXCEPTION'),responseData:error}).code(500)
    }
}

exports.convertExcelToJson = async(req, h) => {
    try {
        let language = req.headers.language;
        const fileExists = await Models.Attachment.findOne({ where: { path: req.payload.url } });
        if(!fileExists) {
            return h.response({sucess:false,message:req.i18n.__('FILE_DOESNOT_EXISTS'),responseData:{}}).code(400)
        }
        let readableFile = xlsx.readFile(req.payload.url);
        let sheetsData = readableFile.Sheets["Sheet1"]
        let sheetJson = xlsx.utils.sheet_to_json(sheetsData, { raw: false })
        let obj = {};
        for(let item of sheetJson) {
            obj[item.keys] = item[language] ? item[language] : "";
        }
        console.log(obj)
        
        if(req.payload.deleteFile === 1) {
            Fs.unlink(req.payload.url, async() => {
                await Models.Attachment.destroy({ where: { path: req.payload.url } })
            });
        }

        return h.response({sucess:false,message:req.i18n.__('ATTACHMENT_UPLOADED_SUCCESSFULLY'),responseData:obj}).code(201)
    } catch (error) {
        console.log(error);
        return h.response({sucess:false,message:req.i18n.__('SOMETHING_WENT_WRONG_WITH_EXCEPTION'),responseData:error}).code(500)
    }
}