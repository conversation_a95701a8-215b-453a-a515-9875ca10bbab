"use strict";
const attachmentController = require('../controller/attachmentController');
const extensions = require('../extensions');
module.exports = [
// Upload an attachment 
    {
        method: "POST",
        path: "/attachment/upload",
        handler: attachmentController.uploadFile,
        options: {
            tags: [
                "api", "Attachment"
            ],
            notes: "Endpoint to upload single/multiple attachments",
            description: "Upload attachment",
            auth: false,
            validate: {
                options: {
                    abortEarly: false
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                payload: Joi.object(
                    {
                        files: Joi.any().meta(
                            {swaggerType: 'file'}
                        ).required().description('Array of files or object'),
                        user_id: Joi.number().optional().default(null)
                    }
                ),
                validator: Joi
            },
            payload: {
                maxBytes: 10000000,
                output: "stream",
                parse: true,
                multipart: true,
                timeout: 60000
            },
            plugins: {
                'hapi-swagger': {
                    payloadType: 'form'
                }
            },
            pre: [
                {
                    method: Common.prefunction
                }
            ]
        }
    },
// Delete an attachment
    {
        method: "DELETE",
        path: "/attachment/delete",
        handler: attachmentController.deleteAttachments,
        options: {
            tags: [
                "api", "Attachment"
            ],
            notes: "Endpoint to delete single/multiple attachments by id",
            description: "Delete attachment",
            auth: false,
            validate: {
                options: {
                    abortEarly: false
                },
                query: {
                    ids: Joi.string().required().description('Comma seperated ids to delete multiple attachments')
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre: [
                {
                    method: Common.prefunction
                }
            ]
        }
    },
// Download an attachment
    {
        method: "GET",
        path: "/attachment/download",
        handler: attachmentController.downloadFile,
        options: {
            tags: [
                "api", "Attachment"
            ],
            notes: "Endpoint to download attachment",
            description: "Download Attachment",
            auth: false,
            validate: {
                options: {
                    abortEarly: false
                },
                query: {
                    id: Joi.number().required(),
                    returnData: Joi.number().default(0)
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre: [
                {
                    method: Common.prefunction
                }
            ]
        }
    },
    {
        method: "GET",
        path: "/attachment/meeting/download",
        handler: attachmentController.downloadRecordingFile,
        options: {
            tags: [
                "api", "Attachment"
            ],
            notes: "Endpoint to download attachment",
            description: "Download Attachment",
            // auth: false,
            auth: {strategy: "jwt"},
            validate: {
                headers: Joi.object({
                    authorization: Joi.string().required().description("Token to identify user who is performing the action")
                }).unknown(true),
                options: {
                    abortEarly: false
                },
                query: {
                    id: Joi.string().required(),
                    returnData: Joi.number().default(0)
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre: [
                {
                    method: Common.prefunction
                }
            ]
        }
    },
    {
        method: "GET",
        path: "/attachment/pdf/download",
        handler: attachmentController.downloadPDFFile,
        options: {
            tags: [
                "api", "Attachment"
            ],
            notes: "Endpoint to download attachment",
            description: "Download Attachment",
            // auth: false,
            auth: false,
            validate: {
                // headers: Joi.object({
                //     authorization: Joi.string().required().description("Token to identify user who is performing the action")
                // }).unknown(true),
                options: {
                    abortEarly: false
                },
                query: {
                    path: Joi.string().required(),
                    returnData: Joi.number().default(0)
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre: [
                {
                    method: Common.prefunction
                }
            ]
        }
    },
// View an attachment
    {
        method: "GET",
        path: "/attachment/view",
        handler: attachmentController.viewAttachments,
        options: {
            tags: [
                "api", "Attachment"
            ],
            notes: "Endpoint to view attachment",
            description: "View Attachment",
            auth: false,
            validate: {
                options: {
                    abortEarly: false
                },
                query: {
                    id: Joi.number().required(),
                    returnData: Joi.number().default(0)
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre: [
                {
                    method: Common.prefunction
                }
            ]
        }
    },
// View attachment  
	{
        method: "GET",
        path: "/resources/attachments/{file*}",
        options: {
            tags: [
				"api", "Attachment"
            ],
            plugins: {
                "hapi-swagger": {}
            },
            notes: "Access static content",
            description: "Access Static content",
            auth: false,
             validate: {
                options: {
                    abortEarly: false
                },
                query: {
                   // data: Joi.number().default(0),
                    returnData:Joi.number().default(0)
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
        },
        handler: async (req, h) => {
            console.log('req',req.query.data)
            if(req.query.returnData==1)
            {
                 let attachment = await Models.Attachment.findOne({where:
                    {
                        [Op.or]:[
                            {path:'resources/attachments/'+ req.params.file},
                            {path:'resources/meetings/'+ req.params.file},
                            {thumbnailDestination:'resources/attachments/'+ req.params.file}
                        ]
                    }
                   
                    , attributes: ['thumbnailDestination','id', 'path', 'uniqueName', 'extension','originalName']});
                if(!attachment)
                {
                    return Common.generateError(req, 404, 'FILE_NOT_FOUND');
                }

                const contentType = extensions.getContentType(attachment.dataValues.extension);
                let reqPath=req.params.file
                let servingUrl=attachment.path
                console.log('servingUrl',servingUrl)
                if(reqPath.includes("thumbnail"))
                {
                    console.log('Requesting Thumbnail by gateway')
                    servingUrl=attachment.thumbnailDestination
                }
                else if(reqPath.includes("meetings")) {
                    console.log("meeting request ===================")
                    servingUrl=attachment.path
                    console.log(servingUrl, "video path")
                    let buffer = await Fs.readFileSync(servingUrl);
                    return h.response(buffer.toString('base64')).header('Content-Type',contentType).code(200)
                }
                else
                {
                    console.log('Requesting Attachment by gateway')
                }
                console.log('servingUrl',servingUrl)
                let buffer = await Fs.readFileSync(servingUrl);
                return h.response(buffer.toString('base64')).header('Content-Type',contentType).code(200)
            }
            let fileSource = "resources/attachments/" + req.params.file;
            console.log('fileSource',fileSource)
            return h.file(fileSource);
        }
    },
	{
        method: "GET",
        path: "/resources/soulwriting/{file*}",
        options: {
            tags: [
				"api", "Attachment"
            ],
            plugins: {
                "hapi-swagger": {}
            },
            notes: "Access static content",
            description: "Access Static content",
            auth: false,
             validate: {
                options: {
                    abortEarly: false
                },
                query: {
                   // data: Joi.number().default(0),
                    returnData:Joi.number().default(0)
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
        },
        handler: async (req, h) => {
            console.log('req',req.query.data)
            if(req.query.returnData==1){
                const contentType = extensions.getContentType(".pdf");
                let servingUrl=`resources/soulwriting/${req.params.file}`;
                let buffer = await Fs.readFileSync(servingUrl);
                return h.response(buffer.toString('base64')).header('Content-Type',contentType).code(200)
            }
            let fileSource = "resources/soulwriting/" + req.params.file;
            console.log('fileSource',fileSource)
            return h.file(fileSource);
        }
    },
// Update attachment status     
    {
        method: "PATCH",
        path: "/attachment/update",
        handler: attachmentController.updateAttachment,
        options: {
            tags: [
                "api", "Attachment"
            ],
            notes: "Endpoint to update attachment status",
            description: "Update Attachment",
            auth: false,
            validate: {
                options: {
                    abortEarly: false
                },
                payload: {
                    data: Joi.array().items(
                        {id: Joi.number().required(), status: Joi.number().required()}
                    )
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre: [
                {
                    method: Common.prefunction
                }
            ]
        }
    },
// Valid attachment or not    
     {
        method: "GET",
        path: "/attachment/valid",
        handler: attachmentController.checkValid,
        options: {
            tags: [
                "api", "Attachment"
            ],
            notes: "Endpoint to check Valid attachment",
            description: "Valid Attachment",
            auth: false,
            validate: {
                options: {
                    abortEarly: false
                },
                query: {
                    id: Joi.number().required()
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre: [
                {
                    method: Common.prefunction
                }
            ]
        }
    },
    {
        method : "POST",
        path : "/zoom-attachment",
        handler : attachmentController.zoomAttachmentWebhook,
        options: {
            tags: ["api", "Attachment"],
            notes: "Webhook to get recording",
            description: "Recording Webhook",
            auth: false,
            validate: {
                options: {
                    abortEarly: false
                },
                payload: {
                    meetingId : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'MEETING_ID_IS_REQUIRED')}),
                    downloadUrl : Joi.string().example("download url").required().error(errors=>{return Common.routeError(errors,'DOWNLOAD_URL_IS_REQUIRED')}),
                    videoUrl : Joi.string().example("download url").required().error(errors=>{return Common.routeError(errors,'DOWNLOAD_URL_IS_REQUIRED')}),
                    passcode: Joi.string().optional().allow(null).default(null),
                    token: Joi.string().optional().allow(null).default(null),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
		method  : "POST",
		path    : "/uploadFromUrl",
		options : {
			handler     : attachmentController.downloadFileFromUrl,
			description : "Download from URL",
			notes       : "Upload from URL",
			tags        : ["api", "Attachment"],
			auth        : false,
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				payload :
				{
					url : Joi.string().trim().required(),
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
    {
		method  : "POST",
		path    : "/excel-to-json",
		options : {
			handler     : attachmentController.convertExcelToJson,
			description : "Download from URL",
			notes       : "Upload from URL",
			tags        : ["api", "Attachment"],
			auth        : false,
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en")
				}).options
				({
					"allowUnknown"  : true
				}),
				payload :
				{
					url : Joi.string().trim().required(),
                    deleteFile: Joi.number().integer().valid(0,1).optional().default(0)
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
    {
		method  : "POST",
		path    : "/generate-pdf",
		options : {
			handler     : attachmentController.generatePdf,
			description : "Download from URL",
			notes       : "Upload from URL",
			tags        : ["api", "Attachment"],
            auth: {strategy: "jwt"},
            validate: {
                headers: Joi.object({
                    authorization: Joi.string().required().description("Token to identify user who is performing the action")
                }).unknown(true),
                options: {
                    abortEarly: false
                },
				payload :
				{
					projectId : Joi.number().required(),
                    version: Joi.number().required(),
                    language: Joi.string().required(),
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	}
]
