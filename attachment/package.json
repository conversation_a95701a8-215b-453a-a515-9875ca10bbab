{"name": "kinn_backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "nodemon server", "cypress:open": "cypress open", "inittest": "start-server-and-test start http://localhost:9000 cypress:open", "cypress:run": "cypress run --record --key ", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "illuminz", "license": "ISC", "dependencies": {"@hapi/boom": "^10.0.0", "@hapi/hapi": "^21.0.0", "@hapi/inert": "^7.0.0", "@hapi/vision": "^7.0.0", "axios": "^1.3.3", "bcrypt": "^5.1.0", "colors": "^1.4.0", "crypto": "^1.0.1", "dotenv": "^16.0.3", "download-file": "^0.1.5", "express": "^4.18.2", "fs": "0.0.1-security", "handlebars": "^4.7.7", "hapi-auth-jwt2": "^10.2.0", "hapi-auto-route": "^3.0.4", "hapi-cron": "^1.1.0", "hapi-i18n": "^3.0.1", "hapi-swagger": "^15.0.0", "http": "^0.0.1-security", "image-thumbnail": "^1.0.15", "joi": "^17.7.0", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.29.4", "mysql2": "^2.3.3", "ngrok": "^4.3.3", "node-csv": "^0.1.2", "node-fetch": "^3.3.0", "node-jose": "^2.1.1", "node-rsa": "^1.1.1", "nodejs-file-downloader": "^4.10.6", "nonce": "^1.0.4", "path": "^0.12.7", "puppeteer": "^19.11.1", "puppeteer-core": "^23.1.0", "querystring": "^0.2.1", "sequelize": "^6.25.5", "sharp": "^0.31.2", "uuid": "^9.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@hapi/code": "^9.0.1", "@hapi/lab": "^25.0.1", "chai": "^4.3.7", "cypress": "^11.0.1", "start-server-and-test": "^1.14.0"}}