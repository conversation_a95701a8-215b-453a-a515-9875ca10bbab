
module.exports = {
    URL:{
            ATTACHMENT_UPDATE       :   `${process.env.ATTACHMENT_DOMAIN}/attachment/update`,
            SEND_EMAIL              :   `${process.env.EMAIL_SERVICE}/email/send`
    },
    ROLE:{
       ADMIN:'admin' 
    },
    STATUS:{
        INACTIVE    :   0,
        ACTIVE      :   1
    },
    TOKENTYPE:{
        SIGNUP          :   1,
        RESETPASSWORD   :   2,
        RESETEMAIL      :   3
    },
    DEVICE_TYPE: {
        WEB         : 1,
        ANDROID     : 2,
        IOS         : 3,
    },
    PAGINATION_LIMIT        :   20,
    MAX_PAGINATION_LIMIT    :   20,
    CRON:[
        {
            name: "fiveMinute<PERSON>ron",
            time: "*/5 * * * *",
            timezone: "America/Danmarkshavn",
            request: {
            method: "GET",
                url: "/cron/fiveMinuteCron"
            },
            onComplete: async (res) => {
                console.log("------------ <PERSON><PERSON> Job Executed ( Every Five Minutes ) --------------");
            }
        },
        {
            name: 'everyHourCron',
            time: '0 * * * *',
            timezone: 'America/Danmarkshavn',
            request: {
            method: 'GET',
                url: '/cron/everyHourCron'
            },
            onComplete: async (res) => {
                await Common.retryRequests();
                console.log('------------Cron Job Executed ( Every Hour ) -----------');
            }
        },
        {
            name: 'everyDayCron',
            time: '0 0 * * *',
            timezone: 'America/Danmarkshavn',
            request: {
            method: 'GET',
                url: '/cron/everyDayCron'
            },
            onComplete: (res) => {
                console.log('------------Cron Job Executed ( Every Day ) -----------');
            }
        },
        {
            name: 'everyMonthCron',
            time: '0 0 1 * *',
            timezone: 'America/Danmarkshavn',
            request: {
            method: 'GET',
                url: '/cron/everyMonthCron'
            },
            onComplete: (res) => {

                console.log('------------Cron Job Executed ( Every Month ) -----------');
            }
        },
        {
            name: 'everyYearCron',
            time: '0 0 1 1 *',
            timezone: 'America/Danmarkshavn',
            request: {
            method: 'GET',
                url: '/cron/everyYearCron'
            },
            onComplete: (res) => {

                console.log('------------Cron Job Executed ( Every Year ) -----------');
            }
        },

    ],
    WEBHOOK: {
        USER_ONBOARDING : [
            process.env.VIDEO_DOMAIN + "/webhook/user",
            process.env.EVENT_DOMAIN + "/webhook/user",
            process.env.PRODUCT_DOMAIN + "/webhook/user",
            process.env.SOUL_WRITING_DOMAIN + "/webhook/user"
        ],
        ZOOM_USER_CREATE:process.env.ZOOM_DOMAIN+"/user",
        ADMIN_SETTIGNS:[
            //process.env.VIDEO_DOMAIN + "/webhook/user",
            process.env.EVENT_DOMAIN + "/settings",
            process.env.PRODUCT_DOMAIN + "/settings",
            process.env.SOUL_WRITING_DOMAIN + "/settings"
        ]
    }
}