const Constants=require('../constants');

const userObject = async(userId, transaction) => {
    let userInfo = await Models.User.findOne({ 
        where: { id: userId },
        attributes: {
            exclude: ['password', 'createdAt', 'updatedAt']
        },
        include: [
            { 
                model: Models.UserProfile,  
                attributes: {
                    exclude: ['password', 'createdAt', 'updatedAt']
                } 
            }
        ],
        transaction
    });
    if(userInfo) userInfo = JSON.parse(JSON.stringify(userInfo))
    return userInfo;
}


module.exports = {
    // users in other services.
    updateUserInServices : async(data, transaction) => {
        const urls = Constants.WEBHOOK.USER_ONBOARDING
        let finalData = {...data};
        finalData["userObject"] = await userObject(data.userId, transaction);
        for(const url of urls)
        {
            const axiosObj = {
                url    : url,
                method : "POST",
                data   : finalData
            };

            await 
                Axios(axiosObj)
                .then(()=>{
                    console.log(`User Inserted on ${axiosObj.url}`)
                })
                .catch(async (error) => {
                    console.log(`User Insert Error on ${axiosObj.url}`)
                    console.log('error',error)
                    await Models.Request.create({
                        axiosObj : axiosObj,
                        sucess   : 0
                    }, {transaction});
                });
        }

        return true;
    },
    //Attachement in Other Services.
    updateAttachmentService : async(reqPayload, transaction) => {
        console.log(reqPayload);
        const url = Constants.URL.ATTACHMENT_UPDATE;

        const axiosObj = {
            url    : url,
            method : "PATCH",
            data   : reqPayload
        };

        await 
            Axios(axiosObj)
            .then()
            .catch(async () => {
                await Models.Request.create({
                    axiosObj : axiosObj,
                    sucess   : 0
                }, {transaction});
            });
    },
    // User on zoom services
    createUserOnZoom:async(reqPayload,transaction)=>{
        
        const url = Constants.WEBHOOK.ZOOM_USER_CREATE;

        const axiosObj = {
            url    : url,
            method : "POST",
            data   : reqPayload
        };
        console.log('axiosObj',axiosObj)
        await 
            Axios(axiosObj)
            .then()
            .catch(async () => {
                await Models.Request.create({
                    axiosObj : axiosObj,
                    sucess   : 0
                }, {transaction});
            });
    },
    // Admin Settings on other services.
    createSettingInOtherServices:async(data,transaction)=>{
        const urls = Constants.WEBHOOK.ADMIN_SETTIGNS
        for(const url of urls)
        {
            const axiosObj = {
                url    : url,
                method : "POST",
                data   : data
            };
            console.log('axiosObj',axiosObj)
            await 
                Axios(axiosObj)
                .then()
                .catch(async () => {
                    await Models.Request.create({
                        axiosObj : axiosObj,
                        sucess   : 0
                    }, {transaction});
                });
        }

        return true;
    }
}