const Joi        = require("joi");
const CronController = require("../controllers/cron.controller");

module.exports = [
    {
        method  : "GET",
		path    : "/cron/fiveMinuteCron",
		handler : CronController.updateServices,
		options : {
			tags        : ["api", "Cron"],
			notes       : "Endpoint to run cron",
			description : "Add User in services kby cron",
			auth        : false,
			validate    : {
				headers    : Joi.object(Common.headers(false)).options({
					allowUnknown : true
				}),
				options    : {
					abortEarly   : false
				},
				failAction : async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre         : [{method: Common.prefunction}]
		}
    }
]