"use strict";
const Joi = require("joi");
const controller = require("../controllers/setting.controller")
module.exports = [
    // Settings ----------
{
	method : "POST",
	path : "/settings",
	handler :controller.createSettings,
	options: {
		tags: ["api", "Admin"],
		notes: "Endpoint to add User By Admin",
		description: "Add User By Admin",
		auth:{strategy:'jwt',scope:["admin","manage-settings","settings"]},
		validate: {
			headers: Joi.object(Common.headers(true)).options({
				allowUnknown: true
			}),
			options: {
				abortEarly: false
			},
			payload:{
					data:Joi.array().example([{'key':'key','value':'value'}]).items(
						Joi.object().example({'key':'key','value':'value'})
					).min(1).optional().default(null),
			},
			failAction: async (req, h, err) => {
				return Common.FailureError(err, req);
			},
			validator: <PERSON><PERSON>
		},
		pre : [{method: Common.prefunction}]
	}
},


{
	method : "GET",
	path : "/settings",
	handler : controller.getSettings,
	options: {
		tags: ["api", "Admin"],
		notes: "Endpoint to Get All Users",
		description: "Get Users By Admin ",
		auth: {strategy: 'jwt', mode: "optional"},
		validate: {
			headers: Joi.object(Common.headers()).options({
				allowUnknown: true
			}),
			options: {
				abortEarly: false
			},
			query: {
				// limit: Joi.number().integer().optional().default(null),
				// pageNumber: Joi.number().integer().min(1).optional().default(null),
				orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
				orderByParameter: Joi.string().valid('createdAt','id').optional().default('id')
			},
			failAction: async (req, h, err) => {
				return Common.FailureError(err, req);
			},
			validator: Joi
		},
		pre : [{method: Common.prefunction}]
	}
},
]