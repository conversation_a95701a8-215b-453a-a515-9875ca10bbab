"use strict";
const Joi = require("joi");
const controller = require("../controllers/acl.controller");
module.exports = [

    {
		method : "Get",
		path : "/role-permissions",
		handler : controller.getRole,
		options: {
			tags: ["api", "ACL"],
			notes: "Endpoint to get Role with permissions",
			description: "Get Role",
			auth: {strategy: 'jwt', scope:['admin','manage_roles','admin_user_management']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
					roleId:Joi.number().required().error(errors=>{return Common.routeError(errors,'ROLE_ID_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},


// Get Roles
  {
		method : "Get",
		path : "/role",
		handler : controller.listRoles,
		options: {
			tags: ["api", "ACL"],
			notes: "Endpoint to get roles",
			description: "Get Roles",
			auth: {strategy: 'jwt', scope:['admin','manage-roles','admin_user_management']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
					limit: Joi.number().integer().optional().default(null),
					allRole:Joi.number().integer().allow(1).optional().default(null),
					name:Joi.string().max(250).optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt','id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
// Delete a Role	
  {
		method : "DELETE",
		path : "/role",
		handler : controller.deleteRole,
		options: {
			tags: ["api", "ACL"],
			notes: "Endpoint to delete Roles",
			description: "Delete Role",
			auth: {strategy: 'jwt', scope:['admin','manage-roles','admin_user_management']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
					roleId:Joi.number().required().error(errors=>{return Common.routeError(errors,'ROLE_ID_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
// Update Roles	
  {
		method : "PATCH",
		path : "/role",
		handler : controller.updateRole,
		options: {
			tags: ["api", "ACL"],
			notes: "Endpoint to update Role with Permissions",
			description: "Update Role",
			auth: {strategy: 'jwt', scope:['admin','manage-roles','admin_user_management']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					roleId:Joi.number().required().error(errors=>{return Common.routeError(errors,'ROLE_ID_IS_REQUIRED')}),
					name:Joi.string().required().error(errors=>{return Common.routeError(errors,'ROLE_NAME_IS_REQUIRED')}),
					permissions:Joi.array().items(Joi.number()).required().min(1).error(errors=>{return Common.routeError(errors,'PERMISSIONS_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
// Create Role-----
  {
		method : "POST",
		path : "/role",
		handler : controller.createRole,
		options: {
			tags: ["api", "ACL"],
			notes: "Endpoint to create a new Role with Permissions",
			description: "Create Roles",
			auth: {strategy: 'jwt', scope:['admin','manage-roles','admin_user_management']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					name:Joi.string().required().error(errors=>{return Common.routeError(errors,'ROLE_NAME_IS_REQUIRED')}),
					permissions:Joi.array().items(Joi.number()).required().min(1).error(errors=>{return Common.routeError(errors,'PERMISSIONS_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
// Get Permissions ------
  {
		method : "GET",
		path : "/permissions",
		handler : controller.getAllPermissions,
		options: {
			tags: ["api", "ACL"],
			notes: "Endpoint to get all permissions",
			description: "Get permissions",
			auth: {strategy: 'jwt', scope:['admin','manage-roles','manage-permissions']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
                query: {
                    limit: Joi.number().integer().optional().default(null),
					searchText:Joi.string().max(250).optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt','id').optional().default('id')
                },
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
// *********************************** ACL Users **********************************************************
	{
		method : "POST",
		path : "/acl-users",
		handler : controller.createUsers,
		options: {
			tags: ["api", "ACL"],
			notes: "Endpoint to create a new User",
			description: "Create Users",
			auth: {strategy: 'jwt', scope:['admin',"admin_user_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					name:Joi.string().required().example('name').error(errors=>{return Common.routeError(errors,'NAME_IS_REQUIRED')}),
					role:Joi.array().items(Joi.number()).example([1,2,3,4]).required().min(1).error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
					email:Joi.string().email().required().example('<EMAIL>').error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		
			method : "PATCH",
			path : "/acl-users",
			handler : controller.updateUsers,
			options: {
				tags: ["api", "ACL"],
				notes: "Endpoint to update a new User",
				description: "Update Users",
				auth: {strategy: 'jwt', scope:['admin','admin_user_management']},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload:{
						id:Joi.number().integer().required().example(1).error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
						name:Joi.string().required().example('name').error(errors=>{return Common.routeError(errors,'NAME_IS_REQUIRED')}),
						role:Joi.array().items(Joi.number()).example([1,2,3,4]).required().min(1).error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
						email:Joi.string().email().required().example('<EMAIL>').error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
	},
	{
		method : "GET",
		path : "/acl-users",
		handler : controller.getUsers,
		options: {
			tags: ["api", "ACL"],
			notes: "Endpoint to get ACL users",
			description: "Get ACL users by admin",
			auth: {strategy: 'jwt',scope:["admin",'admin_user_management']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					limit: Joi.number().integer().optional().default(null),
					status: Joi.number().valid(0,1).optional().default(null),
					searchText: Joi.string().max(250).optional().default(null),
					name: Joi.string().max(250).optional().default(null),
					email: Joi.string().max(250).optional().default(null),
					pageNumber: Joi.number().integer().min(1).optional().default(null),
					orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
					orderByParameter: Joi.string().optional().default('id')
					// orderByParameter: Joi.string().valid('createdAt','id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/acl-users-id",
		handler : controller.getUserById,
		options: {
			tags: ["api", "ACL"],
			notes: "Endpoint to get ACL users",
			description: "Get ACL users by admin",
			auth: {strategy: 'jwt',scope:["admin","admin_user_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "DELETE",
		path : "/acl-users",
		handler : controller.deletUser,
		options: {
			tags: ["api", "ACL"],
			notes: "Endpoint to delete Roles",
			description: "Delete Role",
			auth: {strategy: 'jwt', scope:['admin','manage-roles','admin_user_management']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
					id:Joi.number().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
]


