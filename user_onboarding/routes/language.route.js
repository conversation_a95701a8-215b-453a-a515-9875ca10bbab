"use strict";
const Joi = require("joi");
const controller = require("../controllers/language.controller");
module.exports = [

//Endpoint to  get Language List
	{
		method : "GET",
		path : "/language",
		handler :controller.getRecord,
		options: {
			tags: ["api", "Language"],
			notes: "Endpoint to get Languages",
			description: "Get Languages",
			auth:false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
					limit: Joi.number().integer().optional().default(null),
                    status: Joi.number().valid(0,1).optional().default(null),
                    searchText: Joi.string().max(250).optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt','id','name').optional().default('name')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    // Create a Language
    {
		method : "POST",
		path : "/language",
		handler :controller.createRecord,
		options: {
			tags: ["api", "Language"],
			notes: "Endpoint to create Languages",
			description: "Create Languages",
			auth:false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					name    : Joi.string().max(250).example('English').required().trim().error(errors=>{return Common.routeError(errors,'LANGUAGE_NAME_IS_REQUIRED')}),
					code    : Joi.string().max(250).example('en').required().trim().lowercase().error(errors=>{return Common.routeError(errors,'LANGUAGE_NAME_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
]


