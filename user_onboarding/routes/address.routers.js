"use strict";
const Joi = require("joi");
const controller = require("../controllers/address.controller");
module.exports = [ 
    {
		method : "POST",
		path : "/address",
		handler : controller.create,
		options: {
			tags: ["api", "Address"],
			notes: "Endpoint to add address",
			description:"Create address",
			auth: {strategy: 'jwt'},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
				   title:Joi.string().example('mr').required().error(errors=>{return Common.routeError(errors,'TITLE_IS_REQUIRED')}),
				   fullName:Joi.string().example('hero').required().error(errors=>{return Common.routeError(errors,'fullName_IS_REQUIRED')}),
				   line1:Joi.string().example('mr').optional().default(null),
				   line2:Joi.string().example('mr').optional().default(null),
				   city:Joi.string().example('njb').required().error(errors=>{return Common.routeError(errors,'CITY_IS_REQUIRED')}),
				   zipCode:Joi.number().integer().example(123456).required().error(errors=>{return Common.routeError(errors,'ZIP_CODE_IS_REQUIRED')}),
				   country:Joi.string().example('mr').required().error(errors=>{return Common.routeError(errors,'COUNTERY_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "PATCH",
		path : "/address",
		handler : controller.update,
		options: {
			tags: ["api", "Address"],
			notes: "Endpoint to update address",
			description:"Update address",
			auth: {strategy: 'jwt'},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					id:Joi.number().integer().example(2).required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
				    title:Joi.string().example('mr').optional().default(null),
				    fullName:Joi.string().example('hero').optional().default(null),
				    line1:Joi.string().example('mr').optional().default(null),
				    line2:Joi.string().example('mr').optional().default(null),
				    city:Joi.string().example('njb').optional().default(null),
				    zipCode:Joi.number().integer().example(2211122).optional().default(null),
				    country:Joi.string().example('mr').optional().default(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	
	{
		method : "GET",
		path : "/address",
		handler : controller.get,
		options: {
			tags: ["api", "Address"],
			notes: "Endpoint to get address",
			description:"Fetch group",
			auth: {strategy: 'jwt', scope: ["admin","manage_address","costumer"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					//fullName:Joi.string().max(250).optional().default(null),
					limit: Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	
	{
		method : "DELETE",
		path : "/address",
		handler : controller.delete,
		options: {
			tags: ["api", "Address"],
			notes: "Endpoint to Delete address",
			description:"Delete Ctegory",
			auth: {strategy: 'jwt', scope: ["admin","manage_address","costumer"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},


]