"use strict";
const Joi = require("joi");
const controller = require("../controllers/user.controller");
module.exports = [

	{
		method : "POST",
		path : "/user/resendCode",
		handler : controller.resendVerificationCode,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to resend verification code",
			description: "Resend Verification Code",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					email: Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/user/verify-token",
		handler :controller.verifyToken,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to Verify Token ",
			description: "User can verify token with this url ",
            auth :false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
                query: {
                    token:Joi.string().required().example("token {required}").error(errors=>{return Common.routeError(errors,'TOKEN_IS_REQUIRED')})
                    },
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "POST",
		path : "/user/login",
		handler : controller.login,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to allow user to login to portal with Singpass and Phone",
			description: "User login",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					role			:	Joi.number().integer().example(1).description('1 for admin 2 for member 4 for companion').required().error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
					email			: 	Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
					password		: 	Joi.string().example('abcd12345').required().error(errors=>{return Common.routeError(errors,'PASSWORD_IS_REQUIRED')}),
					deviceType		: 	Joi.number().integer().example(1).allow(1,2,3).description("1-android,2-ios,3-web").optional().default(null),
					deviceToken		:	Joi.string().example('device-token').optional().default(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "POST",
		path : "/user/login-companion",
		handler : controller.companionLogin,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to allow user to login to portal with Singpass and Phone",
			description: "User login",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					role			:	Joi.number().integer().example(1).description('1 for admin 2 for member 4 for companion').required().error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
					email			: 	Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
					password		: 	Joi.string().example('abcd12345').required().error(errors=>{return Common.routeError(errors,'PASSWORD_IS_REQUIRED')}),
					deviceType		: 	Joi.number().integer().example(1).allow(1,2,3).description("1-android,2-ios,3-web").optional().default(null),
					deviceToken		:	Joi.string().example('device-token').optional().default(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "POST",
		path : "/user/resend-verify-email",
		handler : controller.resendSignup,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to allow user to login to portal with Singpass and Phone",
			description: "User login",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					email			: 	Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "POST",
		path : "/user/login-customer",
		handler : controller.customerLogin,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to allow user to login to portal with Singpass and Phone",
			description: "User login",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					role			:	Joi.number().integer().example(1).description('1 for admin 2 for member 4 for companion').required().error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
					email			: 	Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
					password		: 	Joi.string().example('abcd12345').required().error(errors=>{return Common.routeError(errors,'PASSWORD_IS_REQUIRED')}),
					deviceType		: 	Joi.number().integer().example(1).allow(1,2,3).description("1-android,2-ios,3-web").optional().default(null),
					deviceToken		:	Joi.string().example('device-token').optional().default(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/user/forget-password",
		handler : controller.forgetPassword,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to allow user to login to portal with Singpass and Phone",
			description: "User login",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					email		: Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/admin/forget-password",
		handler : controller.forgetPasswordByAdmin,
		options: {
			tags: ["api", "Admin"],
			notes: "allow admin to generate reset password link for users",
			description: "User login",
			auth: {strategy: "jwt", scope: ["admin"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					email: Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/user/reset-password",
		handler : controller.resetPassword,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to allow user to login to portal with Singpass and Phone",
			description: "User login",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					token		: Joi.string().example('eredds').required().error(errors=>{return Common.routeError(errors,'TOKEN_IS_REQUIRED')}),
					password	: Joi.string().example('abcd12345').required().error(errors=>{return Common.routeError(errors,'PASSWORD_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	
	{
		method : "POST",
		path : "/user/signup",
		handler : controller.signup,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to allow user to signup",
			description: "User Signup",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					title			: Joi.string().example('Mr').required().error(errors=>{return Common.routeError(errors,'TITLE_IS_REQUIRED')}),
					firstName		: Joi.string().example('User').required().error(errors=>{return Common.routeError(errors,'FIRST_NAME_IS_REQUIRED')}),
					lastName		: Joi.string().example('Singh').required().error(errors=>{return Common.routeError(errors,'LAST_NAME_IS_REQUIRED')}),
					email			: Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
					password		: Joi.string().example('abcd12345').required().error(errors=>{return Common.routeError(errors,'PASSWORD_IS_REQUIRED')}),
					optionalData	: Joi.object().example({}).optional(),
					deviceType		: Joi.number().integer().example(1).allow(1,2,3).description("1-android , 2-ios 3-web").optional().default(null),
					deviceToken 	:Joi.string().example('device-token').optional()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},


	{
			method : "PATCH",
			path : "/user/profile",
			handler : controller.updateProfile,
			options: {
				tags: ["api", "User"],
				notes: "Endpoint to allow Alter Profile use gender constatnts 0 for mail 1 for female 2 for other",
				description: "Alter Profile",
				auth: {strategy: 'jwt'},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload: {
						email		: Joi.string().example("<EMAIL>").trim().optional().default(null),
						firstName	: Joi.string().example("John {optional}").max(250).optional().default(null),
						lastName	: Joi.string().example("John {optional}").max(250).optional().default(null),
						title		: Joi.string().example("John {optional}").max(250).optional().default(null),
						gender		: Joi.number().integer().example(1).allow(0,1,2).description('use 0 for mail 1 for female 2 for other ').optional().default(null),
						reason		: Joi.string().example("reason {optional}").optional().default(null),
						about		: Joi.string().example("about {optional}").optional().default(null),
						vita		: Joi.string().example("vita {optional}").optional().default(null),
						video		: Joi.string().example("vita {optional}").optional().default(null),
						attachment	: Joi.object().optional().default(null).default(null),
						meetingPrice: Joi.string().example("14.4 {optional}").optional().default(null),
						languages	:Joi.array().example([1,2,3]).items(
							Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'LANGUAGE_ID_IS_REQUIRED')})
						).min(1).optional().default(null),
						scheduleTime	:Joi.number().integer().example(24).optional().default(null),
						reScheduleTime	:Joi.number().integer().example(24).optional().default(null),
						cancelTime		:Joi.number().integer().example(24).optional().default(null),
						countryCode: Joi.string().example("+91").optional().default(null),
						phoneNumber: Joi.string().example("1234567890").optional().default(null),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},

		{
			method : "POST",
			path : "/user/change-password",
			handler : controller.changePassword,
			options: {
				tags: ["api", "User"],
				notes: "Endpoint to allow user to change Password",
				description: "Change Password",
				auth: {strategy: 'jwt'},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload: {
						oldPassword: Joi.string().example("password").trim().max(250).required().error(errors=>{return Common.routeError(errors,'OLD_PASSWORD_IS_REQUIRED')}),
						newPassword: Joi.string().example("newPassword").trim().max(250).required().error(errors=>{return Common.routeError(errors,'NEW_PASSWORD_IS_REQUIRED')}),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},

		{
			method : "GET",
			path : "/users",
			handler : controller.getUsers,
			options: {
				tags: ["api", "Admin"],
				notes: "Endpoint to Get All Users",
				description: "Get Users By Admin ",
				auth: {strategy: 'jwt',scope:["admin","user_listing","companion_management"]},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query: Joi.object({
						limit: Joi.number().integer().optional().default(null),
						status: Joi.number().valid(0,1).optional().default(null),
						searchText: Joi.string().max(250).optional().default(null),
						name: Joi.string().max(250).optional().default(null),
						email: Joi.string().max(250).optional().default(null),
						phone: Joi.string().max(250).optional().default(null),
						pageNumber: Joi.number().integer().min(1).optional().default(null),
						orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
						orderByParameter: Joi.string().optional().default('id'),
						// orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
						// role:Joi.number().integer().optional().default(null).default(null)
						role: Joi.array().single().items(Joi.number().integer().optional()).optional().allow('').default(null),
					}).rename('role[]', 'role', { ignoreUndefined: true }),
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/user-profile",
			handler : controller.getUserProfile,
			options: {
				tags: ["api", "User"],
				notes: "Endpoint to Get User Profile",
				description: "Get User Profile",
				auth: {strategy: 'jwt'},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query: {
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "PATCH",
			path : "/user/status",
			handler : controller.updateUserStatus,
			options: {
				tags: ["api", "Admin"],
				notes: "Endpoint to allow update user profile status",
				description: "Update Profile",
				auth: {strategy: 'jwt',scope:['admin']},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload: {
						userId		: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'USER_ID_IS_REQUIRED')}),
						status		: Joi.number().integer().required().example(1).valid(1,0).error(errors=>{return Common.routeError(errors,'STATUS_IS_REQUIRED')})
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/companion",
			handler : controller.getCompanion,
			options: {
				tags: ["api", "Companion"],
				notes: "Endpoint to Get Companion",
				description: "Get Companion",
				auth: false,
				validate: {
					headers: Joi.object(Common.headers()).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query: {
						limit: Joi.number().integer().optional().default(null).default(null),
						searchText: Joi.string().max(250).optional().default(null),
						status: Joi.number().optional().default(null),
						name: Joi.string().max(250).optional().default(null),
						email: Joi.string().max(250).optional().default(null),
						phone: Joi.string().max(250).optional().default(null),
						pageNumber: Joi.number().integer().min(1).optional().default(1),
						orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
						orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
						random:Joi.number().integer().valid(1,0).optional().default(0),
						role:Joi.number().integer().example(3).valid(3,4).description('3 for student 4 for professional').optional().default(null),
						gender:Joi.number().integer().example(1).valid(1,2).description('Accepted 1 ,2').optional().default(null),
						language:Joi.number().integer().example(1).optional().default(null),
						lowerExperience:Joi.number().integer().example(1).description("lower in years").optional().default(null),
						hignerExperience:Joi.number().integer().example(1).description("Higher in years").optional().default(null),
						rating:Joi.number().integer().example(1).allow(1,2,3,4,5).default(null),
						price:Joi.number().integer().example(1).default(null)
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/my-companion",
			handler : controller.getMyCompanion,
			options: {
				tags: ["api", "Companion"],
				notes: "Endpoint to Get Companion",
				description: "Get Companion",
				auth: {strategy: "jwt"},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query: {
						limit: Joi.number().integer().optional().default(null).default(null),
						searchText: Joi.string().max(250).optional().default(null),
						status: Joi.number().optional().default(null),
						name: Joi.string().max(250).optional().default(null),
						email: Joi.string().max(250).optional().default(null),
						phone: Joi.string().max(250).optional().default(null),
						pageNumber: Joi.number().integer().min(1).optional().default(1),
						orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
						orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
						random:Joi.number().integer().valid(1,0).optional().default(0),
						role:Joi.number().integer().example(3).valid(3,4).description('3 for student 4 for professional').optional().default(null),
						gender:Joi.number().integer().example(1).valid(1,2).description('Accepted 1 ,2').optional().default(null),
						language:Joi.string().example("en").optional().default(null),
						lowerExperience:Joi.number().integer().example(1).description("lower in years").optional().default(null),
						hignerExperience:Joi.number().integer().example(1).description("Higher in years").optional().default(null),
						rating:Joi.number().integer().example(1).allow(1,2,3,4,5).default(null),
						price:Joi.number().integer().example(1).default(null),
						onlyMyCompanions: Joi.number().integer().valid(0,1).optional().default(0)
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/active-companion",
			handler : controller.getActiveCompanion,
			options: {
				tags: ["api", "Companion"],
				notes: "Endpoint to Get Companion",
				description: "Get Companion",
				auth: {strategy: "jwt", mode: "optional"},
				validate: {
					headers: Joi.object(Common.headers()).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query: {
						limit: Joi.number().integer().optional().default(null).default(null),
						searchText: Joi.string().max(250).optional().default(null),
						name: Joi.string().max(250).optional().default(null),
						email: Joi.string().max(250).optional().default(null),
						phone: Joi.string().max(250).optional().default(null),
						pageNumber: Joi.number().integer().min(1).optional().default(1),
						orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
						orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
						random:Joi.number().integer().valid(1,0).optional().default(0),
						role:Joi.number().integer().example(3).valid(3,4).description('3 for student 4 for professional').optional().default(null),
						gender:Joi.number().integer().example(1).valid(1,2).description('Accepted 1 ,2').optional().default(null),
						language:Joi.string().example("en").optional().default(null),
						lowerExperience:Joi.number().integer().example(1).description("lower in years").optional().default(null),
						hignerExperience:Joi.number().integer().example(1).description("Higher in years").optional().default(null),
						rating:Joi.number().integer().example(1).allow(1,2,3,4,5).default(null),
						price:Joi.number().integer().example(1).default(null),
						soulwritingProductId: Joi.number().integer().optional().example(1).default(null),
						meetingProductId: Joi.number().integer().optional().example(1).default(null),
						showData: Joi.string().valid("companion", "student", "student-meeting-count", null).allow(null).optional().default(null)
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},

		{
			method : "GET",
			path : "/companion-id",
			handler : controller.getCompanionById,
			options: {
				tags: ["api", "Companion"],
				notes: "Endpoint to Get Companion by Id",
				description: "Get Companion by Id",
				auth: false,
				validate: {
					headers: Joi.object(Common.headers()).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}).min(1).optional().default(null),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/user-session",
			handler : controller.getUserSessions,
			options: {
				tags: ["api", "User"],
				notes: "Endpoint to get user Session Ids",
				description: "Get User Session Ids",
				auth: false,
				validate: {
					headers: Joi.object(Common.headers()).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query: {
						userId:Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'USER_ID_IS_REQUIRED')}),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},

		{
			method : "POST",
			path : "/user/logout",
			handler : controller.logout,
			options: {
				tags: ["api", "User"],
				notes: "Endpoint to allow user to Logout From a Device",
				description: "Logout From a Device",
				auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload: {
						deviceType:Joi.number().integer().example(1).allow(1,2,3).description("1-android,2-ios,3-web").required().error(errors=>{return Common.routeError(errors,'DEVICE_TYPE_IS_REQUIRED')}),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "PATCH",
			path : "/user/device-token",
			handler : controller.updateDeviceToken,
			options: {
				tags: ["api", "User"],
				notes: "Endpoint to allow user to update device token",
				description: "Endpoint to update Device Token",
				auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload: {
						deviceType		:	Joi.number().integer().example(1).allow(1,2,3).description("1-android,2-ios,3-web").required().error(errors=>{return Common.routeError(errors,'DEVICE_TYPE_IS_REQUIRED')}),
						deviceToken 	:	Joi.string().example('device-token').required().error(errors=>{return Common.routeError(errors,'DEVICE_TOKEN_IS_REQUIRED')})
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/update-user-profiles",
			handler : controller.updateUserProfiles,
			options: {
				tags: ["api", "Settings"],
				notes: "Endpoint to sync UserProfiles",
				description: "Enpoint to sync Userprofile",
				auth: false,
				validate: {
					headers: Joi.object(Common.headers()).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query: {
					code:Joi.string().example("1").required().error(errors=>{return Common.routeError(errors,'CODE_IS_REQUIRED')}),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/create-user-zoom",
			handler : controller.createZoomUser,
			options: {
				tags: ["api", "Settings"],
				notes: "Endpoint to sync Zoom User Accounts",
				description: "Enpoint to sync Zoom User Accounts",
				auth: false,
				validate: {
					headers: Joi.object(Common.headers()).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query: {
					code:Joi.string().example("1").required().error(errors=>{return Common.routeError(errors,'CODE_IS_REQUIRED')}),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "POST",
			path : "/create-dg-user",
			handler : controller.createNewUser,
			options: {
				tags: ["api", "DG-Store User"],
				notes: "Endpoint to add new user",
				description: "Enpoint to add new user",
				auth: false,
				validate: {
					headers: Joi.object(Common.headers()).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload:{
						email		: Joi.string().example("<EMAIL>").trim().optional().default(null),
						firstName	: Joi.string().example("John {optional}").max(250).optional().default(null),
						lastName	: Joi.string().example("John {optional}").max(250).optional().default(null),
						existingUserEmailSend : Joi.number().integer().optional().default(0)
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "POST",
			path : "/admin/create-user",
			handler : controller.createNewUserByAdmin,
			options: {
				tags: ["api", "Admin"],
				notes: "Endpoint to add new user",
				description: "Enpoint to add new user",
				auth: {strategy: "jwt", scope: ["admin"]},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload:{
						title: Joi.string().example("<EMAIL>").trim().required().error(errors=>{return Common.routeError(errors,'TITLE_IS_REQUIRED')}),
						email: Joi.string().example("<EMAIL>").trim().required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
						firstName: Joi.string().example("John {optional}").max(250).required().error(errors=>{return Common.routeError(errors,'FIRST_NAME_IS_REQUIRED')}),
						lastName: Joi.string().example("John {optional}").max(250).required().error(errors=>{return Common.routeError(errors,'LAST_NAME_IS_REQUIRED')}),
						language : Joi.string().optional().default("en")
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "PATCH",
			path : "/admin/update-user",
			handler : controller.updateUserByAdmin,
			options: {
				tags: ["api", "Admin"],
				notes: "Endpoint to add new user",
				description: "Enpoint to add new user",
				auth: {strategy: "jwt", scope: ["admin"]},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload:{
						id: Joi.number().example(1).required().error(errors=>{return Common.routeError(errors,'USER_ID_IS_REQUIRED')}),
						title: Joi.string().example("<EMAIL>").trim().required().error(errors=>{return Common.routeError(errors,'TITLE_IS_REQUIRED')}),
						email: Joi.string().example("<EMAIL>").trim().required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
						firstName: Joi.string().example("John {optional}").max(250).required().error(errors=>{return Common.routeError(errors,'FIRST_NAME_IS_REQUIRED')}),
						lastName: Joi.string().example("John {optional}").max(250).required().error(errors=>{return Common.routeError(errors,'LAST_NAME_IS_REQUIRED')}),
						language : Joi.string().optional().default("en")
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "POST",
			path : "/zoom-status",
			handler : controller.UpdateZoomStatus,
			options: {
				tags: ["api", "Zoom Profile Status"],
				notes: "Endpoint to Update Zoom Profile Status",
				description: "Endpoint to Update Zoom Profile Status",
				auth: false,
				validate: {
					headers: Joi.object(Common.headers()).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload:{
						email		: Joi.string().example("<EMAIL>").trim().required(),
						status		: Joi.number().integer().valid(1,0).example(1).required()
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "POST",
			path : "/google-status",
			handler : controller.UpdateGoogleStatus,
			options: {
				tags: ["api", "Zoom Profile Status"],
				notes: "Endpoint to Update Zoom Profile Status",
				description: "Endpoint to Update Zoom Profile Status",
				auth: false,
				validate: {
					headers: Joi.object(Common.headers()).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload:{
						email		: Joi.string().example("<EMAIL>").trim().required(),
						status		: Joi.number().integer().valid(1,0).example(1).required()
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "POST",
			path : "/earning-history",
			handler : controller.paymentHistory,
			options: {
				tags: ["api", "Webhook Earning History"],
				notes: "Webhook Endpoint to add earning history",
				description: "Webhook Endpoint to add earning history",
				auth: false,
				validate: {
					headers: Joi.object(Common.headers()).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload:{
						meetingId: Joi.number().integer().optional().default(null),
        				projectId: Joi.number().integer().optional().default(null),
        				topicId: Joi.number().integer().optional().default(null),
        				data: Joi.object().optional().default(null),
						amount: Joi.string().optional().default(null),
						amountReceived: Joi.string().optional().default(null), 
						userId: Joi.number().integer().optional().default(null),
						companionId: Joi.number().integer().optional().default(null),
						projectObject: Joi.object().optional().default(null),
						topicObject: Joi.object().optional().default(null),
						meetingObject: Joi.object().optional().default(null),
						userObject: Joi.object().optional().default(null),
						companionObject: Joi.object().optional().default(null)
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "PATCH",
			path : "/earning-history",
			handler : controller.updatedPaymentHistory,
			options: {
				tags: ["api", "Webhook Earning History"],
				notes: "Webhook Endpoint to add earning history",
				description: "Webhook Endpoint to add earning history",
				auth: false,
				validate: {
					headers: Joi.object(Common.headers()).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload:{
						meetingId: Joi.number().integer().optional().default(null),
						updatedMeetingId: Joi.number().integer().optional().default(null),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "PATCH",
			path : "/earning-history-refund",
			handler : controller.refundOrder,
			options: {
				tags: ["api", "Webhook Earning History"],
				notes: "Webhook Endpoint to add earning history refund",
				description: "Webhook Endpoint to add earning history refund",
				auth: false,
				validate: {
					headers: Joi.object(Common.headers()).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload:{
						meetingId: Joi.number().integer().optional().default(null)
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/earning-history",
			handler : controller.listPaymentHistory,
			options: {
				tags: ["api", "Webhook Earning History"],
				notes: "Webhook Endpoint to add earning history",
				description: "Webhook Endpoint to add earning history",
				auth: {strategy: "jwt"},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query:{
						limit: Joi.number().integer().optional().default(null),
						pageNumber : Joi.number().integer().min(1).optional().default(null),
						historyDays : Joi.number().integer().min(1).optional().default(null),
						orderByValue: Joi.string().allow('ASC', 'DESC').optional().default('DESC'),
						orderByParameter: Joi.string().allow('createdAt','id').optional().default('id'),
						type: Joi.string().valid("soul-writiing","meeting","topic","shop").optional().default(null)
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/companion-dashboard-count",
			handler : controller.userPaymentAmount,
			options: {
				tags: ["api", "Webhook Earning History"],
				notes: "Webhook Endpoint to add earning history",
				description: "Webhook Endpoint to add earning history",
				auth: {strategy: "jwt"},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query:{
						
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/user/history",
			handler : controller.userHistoryDetails,
			options: {
				tags: ["api", "Webhook Earning History"],
				notes: "Webhook Endpoint to add earning history",
				description: "Webhook Endpoint to add earning history",
				auth: false,
				validate: {
					headers: Joi.object(Common.headers(false)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query:{
						userId: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'USER_IS_REQUIRED')}),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/admin/request-access",
			handler : controller.userAccessRequestByAdmin,
			options: {
				tags: ["api", "Admin"],
				notes: "Login to user account",
				description: "Login to user account",
				auth: {strategy: "jwt", scope: ["admin"]},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query:{
						userId: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'USER_IS_REQUIRED')}),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/admin/grant-access",
			handler : controller.userAccessGrantToAdmin,
			options: {
				tags: ["api", "Admin"],
				notes: "Login to user account",
				description: "Login to user account",
				auth: false,
				validate: {
					headers: Joi.object(Common.headers(false)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query:{
						key: Joi.string().required().error(errors=>{return Common.routeError(errors,'KEY_IS_REQUIRED')}),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/data-migration",
			handler : controller.exportData,
			options: {
				tags: ["api", "Data Migration"],
				notes: "Login to user account",
				description: "Login to user account",
				auth: false,
				validate: {
					headers: Joi.object(Common.headers(false)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					// query:{
					// 	key: Joi.string().required().error(errors=>{return Common.routeError(errors,'KEY_IS_REQUIRED')}),
					// },
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "POST",
			path : "/user/enable-soul-writing",
			handler : controller.enableSoulWriting,
			options: {
			  tags: ["api", "Meeting Payment"],
			  notes: "Meeting Payment Confirmation",
			  description: "Meeting payment confirmation",
			  auth: {strategy: "jwt",mode:"optional"},
			  validate: {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				payload:{
						data:Joi.string().example('data').required().error(err=>{return Common.routeError(err,'DATA_IS_REQUIRED')}),
				},
				options: {
				  abortEarly: false
				},
				failAction: async (req, h, err) => {
				  return Common.FailureError(err, req);
				},
				validator: Joi
			  },
			  pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/user/soul-writing-status",
			handler : controller.getSoulWritingStatus,
			options: {
			  tags: ["api", "Meeting Payment"],
			  notes: "Meeting Payment Confirmation",
			  description: "Meeting payment confirmation",
			  auth: {strategy: "jwt"},
			  validate: {
				headers: Joi.object(Common.headers(true)).options({
				  allowUnknown: true
				}),
				query:{
					userId:Joi.number().example(1).optional().allow(null).default(null)
				},
				options: {
				  abortEarly: false
				},
				failAction: async (req, h, err) => {
				  return Common.FailureError(err, req);
				},
				validator: Joi
			  },
			  pre : [{method: Common.prefunction}]
			}
		}
]


