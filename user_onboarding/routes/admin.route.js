"use strict";
const Joi = require("joi");
const controller = require("../controllers/admin.controller");
module.exports = [

//Endpoint To add Users 
	{
		method : "POST",
		path : "/users",
		handler :controller.createUser,
		options: {
			tags: ["api", "Admin"],
			notes: "Endpoint to add User By Admin",
			description: "Add User By Admin",
			auth:{strategy:'jwt',scope:["admin","manage-users","user_listing","companion_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					    email		: Joi.string().example("<EMAIL>").trim().required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
						firstName	: Joi.string().example("John {optional}").max(250).optional().default(null),
                        role        : Joi.array().items(Joi.number().integer()).min(1).example([4]).description('2 for member 1 for companion').required().error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
						lastName	: Joi.string().example("John {optional}").max(250).optional().default(null),
						title		: Joi.string().example("John {optional}").max(250).optional().default(null),
						gender		: Joi.number().integer().example(1).allow(0,1,2).description('use 0 for mail 1 for female 2 for other ').optional().default(null),
						reason		: Joi.string().example("reason {optional}").optional().default(null),
						about		: Joi.string().example("about {optional}").optional().default(null),
						vita		: Joi.string().example("vita {optional}").optional().default(null),
                        experience  : Joi.string().example("2015-03-25 {optional}").optional().default(null),
						video		: Joi.string().optional().default(null),
						attachment	: Joi.object().optional().default(null),
						meetingPrice:Joi.string().optional().default(null),
						meetingProductId: Joi.string().optional().allow(null, "").default(null),
						soulwritingProductId: Joi.string().optional().allow(null, "").default(null),
						languages	:Joi.array().example([1,2,3]).items(
							Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'LANGUAGE_ID_IS_REQUIRED')})
						).min(1).optional().default(null),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	// Endpoint to update Companion
	{
		method : "PATCH",
		path : "/users",
		handler :controller.updateUser,
		options: {
			tags: ["api", "Admin"],
			notes: "Endpoint to add User By Admin",
			description: "Add User By Admin",
			auth:{strategy:'jwt',scope:["admin","manage-users","user_listing","companion_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
						id			: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),	
					    email		: Joi.string().example("<EMAIL>").trim().optional().default(null),
						firstName	: Joi.string().example("John {optional}").max(250).optional().default(null),
                        role        : Joi.array().items(Joi.number().integer()).min(1).example([4]).description('2 for member 1 for companion').required().error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
						lastName	: Joi.string().example("John {optional}").max(250).optional().default(null),
						title		: Joi.string().example("John {optional}").max(250).optional().default(null),
						gender		: Joi.number().integer().example(1).allow(0,1,2).description('use 0 for mail 1 for female 2 for other ').optional().default(null),
						reason		: Joi.string().example("reason {optional}").optional().default(null),
						about		: Joi.string().example("about {optional}").optional().default(null),
						vita		: Joi.string().example("vita {optional}").optional().default(null),
                        experience  : Joi.string().example("2015-03-25 {optional}").optional().default(null),
						video		: Joi.string().optional().default(null),
						attachment	: Joi.object().optional().default(null),
						meetingPrice:Joi.string().optional().default(null),
						meetingProductId: Joi.string().optional().allow(null, "").default(null),
						soulwritingProductId: Joi.string().optional().allow(null, "").default(null),
						languages	:Joi.array().example([1,2,3]).items(
							Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'LANGUAGE_ID_IS_REQUIRED')})
						).min(1).optional().default(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
// Delete a Companion
	{
		method : "DELETE",
		path : "/users",
		handler :controller.deleteUser,
		options: {
			tags: ["api", "Admin"],
			notes: "Endpoint to Delete User By Admin",
			description: "Delete User By Admin",
			auth:{strategy:'jwt',scope:["admin","manage-users","user_listing","companion_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
						id	: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/users-id",
		handler :controller.getUserById,
		options: {
			tags: ["api", "Admin"],
			notes: "Endpoint to Get User By Admin",
			description: "Get User By Admin",
			auth:{strategy:'jwt',scope:["admin","manage-users","user_listing","companion_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
						id	: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},


]


