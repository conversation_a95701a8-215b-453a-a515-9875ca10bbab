"use strict";
const Joi = require("joi");
//const controller = require("../controllers/sessionController");
module.exports = [

//Endpoint To Login Admin 
	// {
	// 	method : "PATCH",
	// 	path : "/session",
	// 	handler :controller.update,
	// 	options: {
	// 		tags: ["api", "Session"],
	// 		notes: "Endpoint to update session id",
	// 		description: "update session Id",
	// 		auth:{strategy:'jwt',scope:["owner"]},
	// 		validate: {
	// 			headers: Joi.object(Common.headers(true)).options({
	// 				allowUnknown: true
	// 			}),
	// 			options: {
	// 				abortEarly: false
	// 			},
	// 			payload:{
	// 				id:Joi.number().integer().required().example(1).error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
	// 				sessionId:Joi.string().required().example("0000 {Required}").required().error(errors=>{return Common.routeError(errors,'SESSION_ID_IS_REQUIRED')}),
	// 			},
	// 			failAction: async (req, h, err) => {
	// 				return Common.FailureError(err, req);
	// 			},
	// 			validator: Joi
	// 		},
	// 		pre : [{method: Common.prefunction}]
	// 	}
	// },
]


