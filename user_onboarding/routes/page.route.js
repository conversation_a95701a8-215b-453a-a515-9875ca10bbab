"use strict";
const Joi = require("joi");
const controller = require("../controllers/page.controller");
module.exports = [
    {
		method : "Get",
		path : "/page",
		handler : controller.listPage,
		options: {
			tags: ["api", "Pages"],
			notes: "Endpoint to get all pages",
			description: "Get Pages",
			auth: {strategy: 'jwt',scope: ['admin','static_pages']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
					
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "Get",
		path : "/page/{reference}",
		handler : controller.getPage,
		options: {
			tags: ["api", "Pages"],
			notes: "Endpoint to get page by id",
			description: "Get Page by id",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				params:{
					//reference: Joi.any().integer().required().error(errors=>{return Common.routeError(errors,'REFERENCE_IS_REQUIRED')})
					reference: Joi.alternatives(Joi.number(), Joi.string()).required().error(errors=>{return Common.routeError(errors,'REFERENCE_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "DELETE",
		path : "/page",
		handler : controller.deletePage,
		options: {
			tags: ["api", "Pages"],
			notes: "Endpoint to delete page",
			description: "Delete Page",
			auth: {strategy: 'jwt',scope: ['admin','static_pages']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					id: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'PAGE_ID_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "PATCH",
		path : "/page",
		handler : controller.updatePage,
		options: {
			tags: ["api", "Pages"],
			notes: "Endpoint to update page",
			description: "Update Page",
			auth: {strategy: 'jwt',scope: ['admin','static_pages']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					id: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
					body: Joi.string().example('page html (optional)').optional().default(null),
					title: Joi.string().example('page title (optional)').optional().default(null),
					slug: Joi.string().example('page url (optional)').optional().default(null),
					status: Joi.number().integer().example('0 | 1 (optional)').optional().default(null),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "POST",
		path : "/page",
		handler : controller.createPage,
		options: {
			tags: ["api", "Pages"],
			notes: "Endpoint to create a new page",
			description: "Create Pages",
			auth: {strategy: 'jwt',scope: ['admin','static_pages']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					body: Joi.string().required().error(errors=>{return Common.routeError(errors,'BODY_IS_REQUIRED')}),
					title: Joi.string().required().error(errors=>{return Common.routeError(errors,'TITLE_IS_REQUIRED')}),
					slug: Joi.string().required().error(errors=>{return Common.routeError(errors,'SLUG_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	}
]


