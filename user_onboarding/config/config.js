require('dotenv').config(); 
module.exports = {
    development: {
      username: process.env.MYSQL_USERNAME,
      password: process.env.MYSQL_PASSWORD,
      database: process.env.MYSQL_DATABASE_NAME,
      host: process.env.MYSQL_HOST,
      port: process.env.MYSQL_PORT,
      dialect: process.env.MYSQL_DIALECT,
      seederStorage : "sequelize",
      seederStorageTableName : "sequelize_data"
    },
    test: {
        username: process.env.MYSQL_USERNAME,
        password: process.env.MYSQL_PASSWORD,
        database: process.env.TEST_MYSQL_DATABASE_NAME,
        host: process.env.MYSQL_HOST,
        port: process.env.MYSQL_PORT,
        dialect: process.env.MYSQL_DIALECT,
        seederStorage : "sequelize",
        seederStorageTableName : "sequelize_data"
    },
    production: {
        username: process.env.MYSQL_USERNAME,
        password: process.env.MYSQL_PASSWORD,
        database: process.env.TEST_MYSQL_DATABASE_NAME,
        host: process.env.MYSQL_HOST,
        port: process.env.MYSQL_PORT,
        dialect: process.env.MYSQL_DIALECT,
        seederStorage : "sequelize",
        seederStorageTableName : "sequelize_data"
    }
  };