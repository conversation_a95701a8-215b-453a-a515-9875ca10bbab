// Create a Language 
exports.createRecord=async(req,h)=>{
    const transaction   =   await Models.seqlize.transaction()
    try{
        const {name,code}   =   req.payload;
        let recordExist =   await Models.Language.findOne({where:{code:code}});
        if(recordExist)
        {
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__('SOMETHING_WENT_WRONG'), responseData: {}}).code(400)
        }
        let record  = await Models.Language.create({
            name,
            code
        },{transaction});
        return h.response({success: false, message: req.i18n.__('SUCCESSFULLY_LANGUAGE_CREATED'), responseData: {}}).code(200)
    }
    catch(error)
    {
        console.log(error);
        await transaction.rollback();
        return h.response({success: false, message: req.i18n.__('SOMETHING_WENT_WRONG'), responseData: {}}).code(500);
    }
}

// Get Language List
exports.getRecord=async(req,h)=>{
    try{
        const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT;
        const offset = (req.query.pageNumber - 1) * limit;
        const orderByValue = req.query.orderByValue;
        const orderByParameter = req.query.orderByParameter;
        let where = {}
        if (req.query.status !== null) 
            where = {
                ... where,
                status: req.query.status
            };
        if(req.query.searchText!==null)
        {
            where = {
                ... where,
                name:{
                    [Op.like]:
                        `%${
                            req.query.searchText.trim()
                        }%`
                }
        }
    }
        let options = {
            where,
            order: [
                [orderByParameter, orderByValue]
            ],
            subQuery: false,
            attributes: ['id','name','code','countery','counteryCode','mobileCode']
        };
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                limit,
                offset
            };
        const users = await Models.Language.findAndCountAll(options);
        const totalPages = await Common.getTotalPages(users.count, limit);
        const responseData = {
            totalPages,
            perPage: limit,
            languages: users.rows,
            totalRecords: users.count
        };
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
    }
    catch(error){
        console.log(error);
        return h.response({success: false, message: req.i18n.__('SOMETHING_WENT_WRONG'), responseData: {}}).code(500);
    }
}