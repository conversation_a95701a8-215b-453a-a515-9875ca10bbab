const webhook = require("../services/webhook");
const Webhook=require("../services/webhook")
module.exports = {

// create settings
    createSettings:async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try{
            let {data}=req.payload
            let setting=await Models.Setting.bulkCreate(data, {updateOnDuplicate: ["key","value","type"] ,transaction:transaction});
            await webhook.createSettingInOtherServices(req.payload,transaction)
            await transaction.commit();
            return h.response({success: true, message: req.i18n.__('REQUEST_SUCCESSFULL'), responseData:setting}).code(200);
        }
        catch(error){
            console.log(error);
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__('SOMETHING_WENT_WRONG'), responseData: {}}).code(500);
        }
    },

// get settings
    getSettings:async(req,h)=>{
        try{
        // const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT;
        // const offset = (req.query.pageNumber - 1) * limit;
        const orderByValue = req.query.orderByValue;
        const orderByParameter = req.query.orderByParameter;
        let order=[
            [orderByParameter, orderByValue]
        ]
       
       
        let options = {
            order,
            distinct    :   true,
            include     : []
        };
     
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                // limit,
                // offset
            };
           
        const users         = await Models.Setting.findAndCountAll(options);
        // const totalPages    = await Common.getTotalPages(users.count, limit);
        let loadMore=false
        // if(req.query.pageNumber==null)req.query.pageNumber=1
        // if(totalPages>req.query.pageNumber)
        // {
        //     loadMore=true
        // }
        const responseData  = {
            // totalPages,
            // perPage: limit,
            records: users.rows,
            totalRecords: users.count,
            //loadMore
        };
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
        }
        catch(error){
            return h.response({success: true, message: req.i18n.__("SOMETING_WENT_WRONG"), responseData: responseData}).code(500);
        }
    }
}