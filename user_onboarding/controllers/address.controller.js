
const {updateAttachmentService}=require("../services/webhook")
module.exports = {
    /**
     * Handler for category CRUD
     * <AUTHOR> 
     * @param {*} req 
     * @param {*} h 
     * @returns 
     */

    //create products
    create  :   async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try{
            const {title,fullName,line1,line2,city,zipCode,country}=req.payload
            let userId=req.auth.credentials.userData.User.id
            let record=await Models.Address.create({title,fullName,line1,line2,city,zipCode,country,userId},{transaction})
                await transaction.commit()
                return h.response({success: true,message: req.i18n.__("ADDRESS_ADDED_SUCESSFULLY"),responseData: record}).code(200)
        }
        catch(error)
        {
            console.error('Error in Address Creation',error)
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    // update 
    update  :   async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try{
            const {id,title,fullName,line1,line2,city,zipCode,country}=req.payload
            let userId=req.auth.credentials.userData.User.id
            let record=await Models.Address.findOne({where:{id}})
            if(!record)
            {
                await transaction.rollback()
                return h.response({success: false,message: req.i18n.__("INVALID_RECORD_ID"),responseData: {}}).code(400)

            }
            await record.update(
                {
                    title       :   title   ?   title:record.title,
                    fullName    :   fullName?   fullName:record.fullName,
                    line1       :   line1   ?   line1:record.fullName,
                    line2       :   line2   ?   line2:record.line2,
                    city        :   city    ?   city:record.city,
                    zipCode     :   zipCode ?   zipCode:record.zipCode,
                    country     :   country ?   country:record.country,
                },{transaction})
                await transaction.commit()
                return h.response({success: true,message: req.i18n.__("ADDRESS_UPDATED_SUCESSFULLY"),responseData: record}).code(200)
        }
        catch(error)
        {
            console.error('Error in Address Creation',error)
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    //get category
    get:async(req,h)=>{
        try{
            const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
            const offset = (req.query.pageNumber - 1) * limit
            const orderByValue = req.query.orderByValue
            const orderByParameter = req.query.orderByParameter
            let where = {}
       
        let options = {
            where,
            order       : [
                    [orderByParameter, orderByValue]
                ],
            subQuery    : false,
            include     : []
        };
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                limit,
                offset
            };
        const records          = await Models.Address.findAndCountAll(options);
        const totalPages        = await Common.getTotalPages(records.count, limit);
        const responseData      = {
            totalPages,
            perPage: limit,
            record: records.rows,
            totalRecords: records.count
        };
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
        }
        catch(error)
        {
            console.error('Error in getting category',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    //get by id
    getById:async(req,h)=>{
        try{
            let id=req.query.id
            
            let record=await Models.Address.findOne({where:{id}})
            if(!record)
            {
                return h.response({success: true, message: req.i18n.__("INVALID_RECORD_ID"), responseData: {}}).code(400);
            }
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: record}).code(200);
        }
        catch(error)
        {
            console.error('Error in getting address by id')
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
        }
    },

    //remove or destroy
    delete:async(req,h)=>{
        const transaction = await Models.sequelize.transaction()
        try{
            let id=req.query.id
            let record=await Models.Address.findOne({where:{id}})
            if(!record)
            {
                return h.response({success: true, message: req.i18n.__("INVALID_RECORD_ID"), responseData: {}}).code(400);
            }
            //await Models.CategoryContent.destroy({where:{categoryId:id},transaction})
            await record.destroy({transaction})
            await transaction.commit()
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: record}).code(200);
        }
        catch(error)
        {   
            await transaction.rollback()
            console.error('Error in destroying cat',error)
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500)
        }
    },

    // get all record without pagination and without authentication.
}