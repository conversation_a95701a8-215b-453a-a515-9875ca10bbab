exports.createPage = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const body = req.payload.body;
        const title = req.payload.title;
        const slug = req.payload.slug;

        const slugExists = await Models.Pages.findOne({ where: { slug: slug } });
        if(slugExists) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("SLUG_ALREADY_EXISTS"),responseData: {}}).code(400)
        }

        const createPage = await Models.Pages.create({title, body, slug}, {transaction});

        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("RECORDS_CREATED_SUCCESSFULLY"),responseData: createPage}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.updatePage = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const body = req.payload.body;
        const title = req.payload.title;
        const slug = req.payload.slug;
        const status = req.payload.status;
        const pageId = req.payload.id;

        const pageInfo = await Models.Pages.findOne({ where: { id: pageId } });
        if(!pageInfo) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_PAGE_ID_PROVIDED"),responseData: {}}).code(400)
        }

        let updateObj = {};
        if(body !== null) updateObj['body'] = body;
        if(title !== null) updateObj['title'] = title;
        if(slug !== null) updateObj['slug'] = slug;
        if(status !== null) updateObj['status'] = status;

        const updatePage = await pageInfo.update(updateObj, {transaction});

        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("RECORDS_UPDATED_SUCCESSFULLY"),responseData: updatePage}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.deletePage = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const pageId = req.payload.id;

        const pageInfo = await Models.Pages.findOne({ where: { id: pageId } });
        if(!pageInfo) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_PAGE_ID_PROVIDED"),responseData: {}}).code(400)
        }

        const deletePage = await pageInfo.destroy({transaction});

        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("RECORDS_DELETED_SUCCESSFULLY"),responseData: deletePage}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.listPage = async(req, h) => {
    try {

        const listPages = await Models.Pages.findAll();

        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULLY"),responseData: listPages}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.getPage = async(req, h) => {
    try {
        const reference = req.params.reference;
        let where = {};

        console.log(typeof reference, " ============= reference")
        console.log(reference, " ============= reference")
        console.log(reference, " ============= reference")
        console.log(reference, " ============= reference")

        if(typeof reference === 'string') {
            where = {...where, slug: reference}
        } else if(typeof reference === 'number') {
            where = {...where, id: reference}
        }
        const pageInfo = await Models.Pages.findOne({where: where});

        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULLY"),responseData: pageInfo}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}