const MD5       = require('md5');
const Constants = require('../constants');
const Common    =   require('../common');
const Webhook   = require("../services/webhook.js");
var IP = require("ip");
const {decodeData}=require('../digistore24')
const getLoginData = async (userId, roleId) => {
    try
    {
    let userProfile = {};
    let userRoles = [],
    userPermissions = [];
        
    userProfile = await Models.UserProfile.findOne({
        attributes: ['zoomAccount','googleAccount','firstName','lastName','title','attachment','dob','gender','meetingPrice','rating','scheduleTime','reScheduleTime','createdAt','cancelTime','vita','experience','video'],
        where: {userId: userId}});
    for (const iterator of roleId) {
        let per = await Models.Role.findOne({
            attributes: [
                "id", "name"
            ],
            where: {
                id: iterator
            },
            include: [
                {
                    attributes: ["permissionCode"],
                    model: Models.Permission,
                    required: false
                },
            ]
        });
        // let userLanguages=await Models.User.findOne({where:{id:userId},include:[
        //     {model:Models.Language,through:'userobd_user_languages'}

        // ]});

        // userProfile.dataValues.languages=userLanguages
        userRoles.push(per.dataValues.name);
        let arr = per.dataValues.Permissions;
        let temp = arr.map(o => {
            return o.permissionCode
        });
        userPermissions = [
            ... userPermissions,
            ... temp
        ];
    }
    userPermissions.forEach((o) => {
        o.replace(/ /g, "-").toLowerCase();
        return o.replace(/ /g, "-").toLowerCase();
    })
    return {UserProfile: userProfile, Role: userRoles, Permissions: userPermissions};
}
catch(error)
{
    console.error('error',error);
}
};


const loginAction = async (user) => {
    hasFullAccess = 0;
    let roles = await user.getRoles();
    let rolesIds = []
    let languages=await user.getLanguages({attributes:['id','code','countery','counteryCode','name','mobileCode'],joinTableAttributes:[]});
    for (const iterator of roles) {
        rolesIds.push(iterator.dataValues.id)
    }
    let userData = await getLoginData(user.id, rolesIds);
    if (userData.Role.includes(Constants.ROLE.ADMIN)) {
        hasFullAccess = 1;
    }
    userData.UserProfile.dataValues.languages=languages

    const userIdentifier = user.email;
    hash = crypto.createHmac('sha256', process.env.HASH_SECRET_KEY).update(userIdentifier).digest('hex');


    let responseData = {
        User: user,
        UserProfile: userData.UserProfile,
        Role: userData.Role,
        Permissions: userData.Permissions,
        hasAllAccess: hasFullAccess,
        intercomUuid: hash
    };
    const tokenData = responseData;
   const tokenData1 =
   {
      User: user,
         UserProfile: {
            firstName:userData.UserProfile.dataValues.firstName,
            lastName:userData.UserProfile.dataValues.lastName,
            title:userData.UserProfile.dataValues.title,
            gender:userData.UserProfile.dataValues.gender,
            firstName:userData.UserProfile.dataValues.firstName,
            firstName:userData.UserProfile.dataValues.firstName,
        },
 
        Role: userData.Role,
        Permissions: userData.Permissions,
        hasAllAccess: hasFullAccess
   } ;
    let token = Common.signToken(tokenData1);
    _.assign(responseData, {token: token});
    return responseData;
};


const UserObject = async (id) => {  
    try {
        let user = await Models.User.findOne({where: {id},include: [{model: Models.UserProfile}]})
        return user.dataValues;
    } catch (err) {
        console.log('err', err);
        return {}
    }
}

const encryptString=(str)=>{
    if(str==null) return null;
    return MD5(str)
}

//Registration Mail on Quentn
const registerMailOnQuentn=async(first_name,family_name,mail,userId)=>{
    const transaction=await Models.sequelize.transaction();
    try{
        const ip = IP.address();        ;
        let payload={
            contact : {
               first_name,
               family_name,
               mail,	
               request_ip:ip
            },
            duplicate_check_method : "email",
            duplicate_merge_method : "update_add",
            return_fields : [
               "email-status","mail","first_name","family_name"
            ],
            flood_limit : 5,
            spam_protection : true
         }
         let res = await Axios({
            method:'post',
            url:process.env.BASE_URL,
            data:payload,
            headers: { Authorization: `Bearer ${process.env.API_KEY}` }
            })
            await Models.Quentn.create({
                userId,
                response:res.data
            },{transaction})
            await transaction.commit()
    }
    catch(error){
        await transaction.rollback()
        console.error('Error in Saving email on quentn platform:-',error)
    }
}

const setRequestObject=(req,reqdomain,auth)=>{
    let headers = {}
    if(req.headers?.language !== undefined) headers['language']=req.headers.language;
    if(req.headers?.utcoffset !== undefined) headers['utcoffset']=req.headers.utcoffset;
    if(auth==1 && req.headers?.authorization !== undefined) headers['authorization']=req.headers.authorization;
    return requestObject = {
        url: req.path,
        headers: headers,
        method: 'get',
        baseURL: reqdomain,
    }
}


// Signup with email
exports.signup = async (req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
            let {title,firstName,lastName,email,password,deviceType,deviceToken}   =   req.payload;
            let user    =   await Models.User.findOne({where:{email}});
            if(user)
            {
                await transaction.rollback();
                return h.response({success: false, responseData: {}, message: req.i18n.__("EMAIL_IS_ALREADY_REGISTERD")}).code(400);
            }
            let code            =   Common.generateCode(6);
            let token           =   await Common.signToken(email);
            let tokenExists     =   await Models.Token.findOne({where:{email}});
            if(tokenExists)
            {   
                await tokenExists.update({
                    token,
                    title,
                    code,
                    firstName,
                    lastName,
                    email,
                    deviceType,
                    deviceToken,
                    password:encryptString(password),
                    type:Constants.TOKENTYPE.SIGNUP,
                    status:1
                },{transaction});
            }
            else
            {
                tokenExists=await Models.Token.create({
                    token,
                    title,
                    code,
                    deviceType,
                    deviceToken,
                    firstName,
                    lastName,
                    password:encryptString(password),
                    type:Constants.TOKENTYPE.SIGNUP,
                    status:1,
                    email
                },{transaction});
            }
            await transaction.commit();
            let replacements={
                code,
                name:firstName+' '+lastName,
                token:process.env.BASE_DOMAIN+`verify-token?email=${email}&token=`+token
                        }
            await Common.sendEmail(email,'SEND_VERIFICATION_CODE',replacements, req.headers.language)
            return h.response({success:true,message: req.i18n.__("VERIFICATION_EMAIL_SENT_SUCCESSFULLY"),responseData:tokenExists}).code(200)
        }
    catch (error) {
        console.log(error);
        await transaction.rollback();
        return h.response({success: false, responseData: {}, message: req.i18n.__("SOMETHING_WENT_WRONG")}).code(500);
    }
}

// resend signup
exports.resendSignup = async (req, h) => {
    try {
            let {email} = req.payload;
            let user = await Models.User.findOne({where:{email}});
            if(user) {
                return h.response({success: false, responseData: {}, message: req.i18n.__("EMAIL_IS_ALREADY_REGISTERD")}).code(400);
            }
    
            let tokenExists = await Models.Token.findOne({where:{email, type: 1}, order: [["id", "desc"]]});
            if(!tokenExists) {
                return h.response({success: false, responseData: {}, message: req.i18n.__("GENERATE_NEW_REQUEST")}).code(400);
            }
           
            let replacements={
                code: tokenExists.code,
                name: tokenExists.firstName+' '+ tokenExists.lastName,
                token:process.env.BASE_DOMAIN+`verify-token?email=${email}&token=`+tokenExists.token
            }
            await Common.sendEmail(email,'SEND_VERIFICATION_CODE',replacements, req.headers.language)
            return h.response({success:true,message: req.i18n.__("VERIFICATION_EMAIL_SENT_SUCCESSFULLY"),responseData:tokenExists}).code(200)
        }
    catch (error) {
        console.log(error);
        await transaction.rollback();
        return h.response({success: false, responseData: {}, message: req.i18n.__("SOMETHING_WENT_WRONG")}).code(500);
    }
}

const forgetPasswordLink = async(email, user, language, transaction) => {
    try {
        let token  =   Common.signToken({email});
        let alreadyExist = await Models.Token.findOne({where:{email:email}});
        if(alreadyExist){
            await alreadyExist.update({status:1,token:token,type:Constants.TOKENTYPE.RESETPASSWORD});
        } else{
            await Models.Token.create({email:email,status:1,token:token,type:Constants.TOKENTYPE.RESETPASSWORD},{transaction})
        }

        let roles= await user.getRoles({raw:true,nest:true,attributes:['name'],joinTableAttributes:[]})
        roles=roles.map((e,i)=>{return e.name})
            
        let baseDomain=''
        if(roles.includes("companion")) {
            baseDomain=process.env.COMPANION_BASE_DOMAIN
        } else if(roles.includes("admin")) {
            baseDomain=process.env.ADMIN_BASE_DOMAIN
        } else if(roles.includes("costumer")) {
            baseDomain=process.env.COSTUMER_BASE_DOMAIN
        } else{
            baseDomain=process.env.ADMIN_BASE_DOMAIN
        }

        const userProfile = await Models.UserProfile.findOne({ where: { userId: user.id } });
        let replacements={token:baseDomain+`reset-password?email=${email}&token=`+token, name:userProfile.firstName }
        await Common.sendEmail(email,'IMPORTED_USER_RESET',replacements,language);
        return true
    } catch (error) {
        console.log(error)
        return false;
    }
}

//Login option with Email and Password
exports.login = async (req, h) => {
    const transaction = await Models.sequelize.transaction()
    try {
    let {email,password,deviceToken,deviceType,role}    =   req.payload;
    let user    =   await Models.User.findOne({where:{email:email},attributes:['id','password','email','phoneNumber','countryCode','countryCodeValue']});
            if(!user)
            {
                await transaction.rollback();
                return h.response({success: false, message:req.i18n.__("EMAIL_NOT_REGISTERD"),responseData: {}}).code(400);
            }

            if(user && user.password == null) {
                await forgetPasswordLink(email, user, req.headers.language, transaction);
                await transaction.commit();
                return h.response({success: false, message:req.i18n.__("RESET_YOUR_PASSWORD"),responseData: { isImportedUser: true }}).code(400);
            }


        let roles=await user.getRoles();
        let userRole=[]
        // if(role==4 || role==3)
        // {
        //     role    = 2
        // }

        for (const r of roles) {

            userRole.push(r.dataValues.id)
        }
        let registedRole=userRole[0]
        if(role==2 &&registedRole<role)
        {
            await transaction.rollback();
            return h.response({success: false, message:req.i18n.__("YOU_ARE_NOT_REGISTERD_WITH_US"),responseData: {}}).code(400);
        }
        // if(!userRole.includes(role))
        //     {
            
        //     }
            password=encryptString(password);
            if(password!==user.password)
            {
                await transaction.rollback();
                return h.response({success: false, message:req.i18n.__("PASSWORD_DOSE_NOT_MATCHED"),responseData: {}}).code(400);
            }
            if(user.dataValues.status===Constants.STATUS.INACTIVE)
            {
                await transaction.rollback();
                return h.response({success: false, message:req.i18n.__("ACCOUNT_DEACTIVATED_PLEASE_CONTACT_TO_ADMINISTRATOR"),responseData: {}}).code(400);
            }
            await user.update({lastLoginTime:Date.now()},{transaction});
            let responseData    =   await loginAction(user);
            if(deviceToken!==null)
            {
            let alreadyDeviceTokenExist=await Models.Session.findOne({where:{userId:user.id,deviceType:deviceType}})
            if(alreadyDeviceTokenExist){
                await alreadyDeviceTokenExist.update({deviceToken:deviceToken},{transaction});
            }
            else{
                await Models.Session.create({deviceToken:deviceToken,userId:user.id,deviceType:deviceType},{transaction}); 
            }
            }
           
            await transaction.commit();
            return h.response({success: true, message:req.i18n.__("SUCCESSFULLY_LOGIN"),responseData}).code(200);
    } catch (error) {
        console.log(error);
        await transaction.rollback();
        return h.response({success: false, responseData: {}, message: req.i18n.__("SOMETHING_WENT_WRONG")}).code(500);
    }
}

//Login option with Email and Password
exports.companionLogin = async (req, h) => {
    const transaction = await Models.sequelize.transaction()
    try {
    let {email,password,deviceToken,deviceType,role}    =   req.payload;
    let user    =   await Models.User.findOne({where:{email:email},attributes:['id','password','email','phoneNumber','countryCode','countryCodeValue']});
            if(!user)
            {
                await transaction.rollback();
                return h.response({success: false, message:req.i18n.__("EMAIL_NOT_REGISTERD"),responseData: {}}).code(400);
            }

            if(user && user.password == null) {
                await forgetPasswordLink(email, user, req.headers.language, transaction);
                await transaction.commit();
                return h.response({success: false, message:req.i18n.__("RESET_YOUR_PASSWORD"),responseData: { isImportedUser: true }}).code(400);
            }


        let roles=await user.getRoles();
        let userRole=[]

        for (const r of roles) {
            if(r.dataValues.id !== 3 && r.dataValues.id !== 4) {
                await transaction.rollback();
                return h.response({success: false, message:req.i18n.__("EMAIL_NOT_REGISTERD"),responseData: {}}).code(400);
            }
            userRole.push(r.dataValues.id)
        }
        let registedRole=userRole[0]
        if(role==2 &&registedRole<role)
        {
            await transaction.rollback();
            return h.response({success: false, message:req.i18n.__("YOU_ARE_NOT_REGISTERD_WITH_US"),responseData: {}}).code(400);
        }
        // if(!userRole.includes(role))
        //     {
            
        //     }
            password=encryptString(password);
            if(password!==user.password)
            {
                await transaction.rollback();
                return h.response({success: false, message:req.i18n.__("PASSWORD_DOSE_NOT_MATCHED"),responseData: {}}).code(400);
            }
            if(user.dataValues.status===Constants.STATUS.INACTIVE)
            {
                await transaction.rollback();
                return h.response({success: false, message:req.i18n.__("ACCOUNT_DEACTIVATED_PLEASE_CONTACT_TO_ADMINISTRATOR"),responseData: {}}).code(400);
            }
            await user.update({lastLoginTime:Date.now()},{transaction});
            let responseData    =   await loginAction(user);
            if(deviceToken!==null)
            {
            let alreadyDeviceTokenExist=await Models.Session.findOne({where:{userId:user.id,deviceType:deviceType}})
            if(alreadyDeviceTokenExist){
                await alreadyDeviceTokenExist.update({deviceToken:deviceToken},{transaction});
            }
            else{
                await Models.Session.create({deviceToken:deviceToken,userId:user.id,deviceType:deviceType},{transaction}); 
            }
            }
           
            await transaction.commit();
            return h.response({success: true, message:req.i18n.__("SUCCESSFULLY_LOGIN"),responseData}).code(200);
    } catch (error) {
        console.log(error);
        await transaction.rollback();
        return h.response({success: false, responseData: {}, message: req.i18n.__("SOMETHING_WENT_WRONG")}).code(500);
    }
}

//Login option with Email and Password
exports.customerLogin = async (req, h) => {
    const transaction = await Models.sequelize.transaction()
    try {
    let {email,password,deviceToken,deviceType,role}    =   req.payload;
    let user    =   await Models.User.findOne({where:{email:email},attributes:['id','password','email','phoneNumber','countryCode','countryCodeValue']});
            if(!user)
            {
                await transaction.rollback();
                return h.response({success: false, message:req.i18n.__("EMAIL_NOT_REGISTERD"),responseData: {}}).code(400);
            }

            if(user && user.password == null) {
                await forgetPasswordLink(email, user, req.headers.language, transaction);
                await transaction.commit();
                return h.response({success: false, message:req.i18n.__("RESET_YOUR_PASSWORD"),responseData: { isImportedUser: true }}).code(400);
            }


        let roles=await user.getRoles();
        let userRole=[]

        for (const r of roles) {
            if(r.dataValues.id !== 2) {
                await transaction.rollback();
                return h.response({success: false, message:req.i18n.__("LOGIN_AT_PROFESSIONAL_PLATFORM"),responseData: {}}).code(400);
            }
            userRole.push(r.dataValues.id)
        }
        let registedRole=userRole[0]
        if(role==2 &&registedRole<role)
        {
            await transaction.rollback();
            return h.response({success: false, message:req.i18n.__("YOU_ARE_NOT_REGISTERD_WITH_US"),responseData: {}}).code(400);
        }
        // if(!userRole.includes(role))
        //     {
            
        //     }
            password=encryptString(password);
            if(password!==user.password)
            {
                await transaction.rollback();
                return h.response({success: false, message:req.i18n.__("PASSWORD_DOSE_NOT_MATCHED"),responseData: {}}).code(400);
            }
            if(user.dataValues.status===Constants.STATUS.INACTIVE)
            {
                await transaction.rollback();
                return h.response({success: false, message:req.i18n.__("ACCOUNT_DEACTIVATED_PLEASE_CONTACT_TO_ADMINISTRATOR"),responseData: {}}).code(400);
            }
            await user.update({lastLoginTime:Date.now()},{transaction});
            let responseData    =   await loginAction(user);
            if(deviceToken!==null)
            {
            let alreadyDeviceTokenExist=await Models.Session.findOne({where:{userId:user.id,deviceType:deviceType}})
            if(alreadyDeviceTokenExist){
                await alreadyDeviceTokenExist.update({deviceToken:deviceToken},{transaction});
            }
            else{
                await Models.Session.create({deviceToken:deviceToken,userId:user.id,deviceType:deviceType},{transaction}); 
            }
            }
           
            await transaction.commit();
            return h.response({success: true, message:req.i18n.__("SUCCESSFULLY_LOGIN"),responseData}).code(200);
    } catch (error) {
        console.log(error);
        await transaction.rollback();
        return h.response({success: false, responseData: {}, message: req.i18n.__("SOMETHING_WENT_WRONG")}).code(500);
    }
}


exports.updateProfile = async (req, h) => {
  const transaction = await Models.sequelize.transaction();
  try {
    let isEmailChanged = false;
    let userId = req.auth.credentials.userData.User.id;

    console.log(req.auth.credentials.userData.Role, " ================================= roles");

    const profileExist = await Models.UserProfile.findOne({ where: { userId: userId } });
    if (!profileExist) {
      await transaction.rollback();
      return h
        .response({
          success: false,
          message: req.i18n.__("USER_PROFILE_DOES_NOT_EXIST"),
          responseData: {}
        })
        .code(400);
    }
    if (req.payload.languages !== null) {
      let user = await Models.User.findOne({ where: { id: userId } });
      await user.setLanguages([...req.payload.languages], { transaction });
    }
    if (req.payload.email !== null) {
      let user = await Models.User.findOne({ where: { id: userId } });
      if (user.dataValues.email != req.payload.email) {
        isEmailChanged = true;
        let email = req.payload.email;
        let alreadyinuse = await Models.User.findOne({
          where: {
            [Op.and]: [{ email: email }, { id: { [Op.ne]: [userId] } }]
          }
        });

        if (alreadyinuse) {
          await transaction.rollback();
          return h
            .response({ success: false, message: req.i18n.__("EMAIL_ALREADY_REGISTERD") })
            .code(400);
        }

        let token = Common.signToken(email);
        let tokenExist = await Models.Token.findOne({ where: { email: email } });
        let code = Common.generateCode(4);
        if (tokenExist) {
          await tokenExist.update(
            { token: token, userId: userId, type: Constants.TOKENTYPE.RESETEMAIL },
            { transaction }
          );
        } else {
          tokenExist = await Models.Token.create(
            { userId, email, token, type: Constants.TOKENTYPE.RESETEMAIL },
            { transaction }
          );
        }

        let baseDomain = process.env.BASE_DOMAIN;
        
        if (
          req.auth.credentials.userData.Role.includes("companion") ||
          req.auth.credentials.userData.Role.includes("student")
        ) {
          baseDomain = process.env.COMPANION_BASE_DOMAIN;
        } else if (req.auth.credentials.userData.Role.includes("costumer")) {
          baseDomain = process.env.COSTUMER_BASE_DOMAIN;
        }

        let replacements = {
          code,
          name: req.payload.firstName + " " + req.payload.lastName,
          token: baseDomain + "verify-token?token=" + token
        };
        await Common.sendEmail(email, "CHANGE_EMAIL", replacements, req.headers.language);
      }
    }
    let creationObject = {};
    let userUpdateObj = {};
    if (req.payload.about !== null) creationObject["about"] = req.payload.about;
    if (req.payload.firstName !== null) creationObject["firstName"] = req.payload.firstName;
    if (req.payload.lastName !== null) creationObject["lastName"] = req.payload.lastName;
    if (req.payload.title !== null) creationObject["title"] = req.payload.title;
    if (req.payload.attachment !== null) {
      if (req.payload.attachment.hasOwnProperty("id"))
        creationObject["attachment"] = req.payload.attachment;
      else {
        await transaction.rollback();
        return h
          .response({ success: false, message: req.i18n.__("ATTACHMENT_ID_IS_REQUIED") })
          .code(400);
      }
    }

    if (req.payload.gender !== null) creationObject["gender"] = req.payload.gender;
    if (req.payload.reason !== null) creationObject["reason"] = req.payload.reason;
    if (req.payload.vita !== null) creationObject["vita"] = req.payload.vita;
    if (req.payload.scheduleTime !== null)
      creationObject["scheduleTime"] = req.payload.scheduleTime;
    if (req.payload.meetingPrice !== null)
      creationObject["meetingPrice"] = req.payload.meetingPrice;
    if (req.payload.reScheduleTime !== null)
      creationObject["reScheduleTime"] = req.payload.reScheduleTime;
    if (req.payload.cancelTime !== null) creationObject["cancelTime"] = req.payload.cancelTime;
    creationObject.lastUpdatedBy = req.auth.credentials.userData.User.id;

    if (req.payload.countryCode !== null) userUpdateObj["countryCode"] = req.payload.countryCode;
    if (req.payload.phoneNumber !== null) userUpdateObj["phoneNumber"] = req.payload.phoneNumber;

    await profileExist.update(creationObject, { transaction });
    let user = await Models.User.findOne({ where: { id: userId } });
    user = await user.update(userUpdateObj, { transaction });

    let lang = await user.getLanguages(
      {
        attributes: ["id", "code", "countery", "counteryCode", "name", "mobileCode"],
        joinTableAttributes: []
      },
      { transaction }
    );
    let updatedLanguages = [];
    for (const l of lang) {
      updatedLanguages.push(l.dataValues);
    }

    // Updating All the services with user
    const data = {
      userId: userId,
      firstName: req.payload.firstName ? req.payload.firstName : profileExist.firstName,
      lastName: req.payload.lastName ? req.payload.lastName : profileExist.lastName,
      title: req.payload.title ? req.payload.title : profileExist.title,
      profilePhotoUrl: req.payload.attachment?.path
        ? req.payload.attachment?.path
        : profileExist.attachment?.path || null,
      profilePhotoId: req.payload.attachment?.id
        ? req.payload.attachment?.id
        : profileExist.attachment?.id || null,
      languages: updatedLanguages,
      meetingPrice: req.payload.meetingPrice
        ? req.payload.meetingPrice
        : profileExist.dataValues.meetingPrice,
      reason: profileExist.dataValues.reason,
      rating: profileExist.dataValues.rating,
      scheduleTime: req.payload.scheduleTime ? req.payload.scheduleTime : profileExist.scheduleTime,
      reScheduleTime: req.payload.reScheduleTime
        ? req.payload.reScheduleTime
        : profileExist.reScheduleTime,
      cancelTime: req.payload.cancelTime ? req.payload.cancelTime : profileExist.cancelTime,
      gender: profileExist.dataValues.gender,
      roles: [],
      vita: req.payload.vita ? req.payload.vita : profileExist.vita,
      experience: profileExist.experience,
      email: req.payload.email ? req.payload.email : user.dataValues.email
    };

    await Webhook.updateUserInServices(data, transaction);
    if (creationObject.attachment != null) {
      let reqPayload = {
        data: [
          {
            id: req.payload.attachment.id,
            status: Constants.STATUS.ACTIVE
          }
        ]
      };
      await Webhook.updateAttachmentService(reqPayload, transaction);
    }

    await transaction.commit();
    let user1 = await Models.User.findOne({ where: { id: userId } });
    // sending mail request to third party
    await registerMailOnQuentn(
      profileExist.dataValues.firstName,
      profileExist.dataValues.lastName,
      user1.dataValues.email,
      user1.dataValues.id
    );
    let responseData = await loginAction(user1);
    return h
      .response({
        success: true,
        message: req.i18n.__("PROFILE_UPDATED_SUCCESSFULLY"),
        responseData: responseData,
        isEmailChanged
      })
      .code(200);
  } catch (error) {
    console.log(error);
    await transaction.rollback();
    return h
      .response({ success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {} })
      .code(500);
  }
};

// forget password
exports.forgetPassword  =   async(req,h)=>{
    const transaction = await Models.sequelize.transaction();
    try{
        let {email} =   req.payload;
        // console.log('email',email)
         let token  =   Common.signToken({email});
         let user   =   await Models.User.findOne({where:{email:email}});
         if(!user)
         {
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__('EMAIL_NOT_REGISTERD'), responseData: {}}).code(400);
         }
         let alreadyExist   =   await Models.Token.findOne({where:{email:email}});
         if(alreadyExist){
            await alreadyExist.update({status:1,token:token,type:Constants.TOKENTYPE.RESETPASSWORD});
         }
         else{
            await Models.Token.create({email:email,status:1,token:token,type:Constants.TOKENTYPE.RESETPASSWORD},{transaction})
         }
         let roles= await user.getRoles({raw:true,nest:true,attributes:['name'],joinTableAttributes:[]})
         roles=roles.map((e,i)=>{return e.name})
         console.log('roles',roles)
         let baseDomain=''
         if(roles.includes("companion"))
         {
            baseDomain=process.env.COMPANION_BASE_DOMAIN
         }
         else if(roles.includes("student"))
         {
            baseDomain=process.env.COMPANION_BASE_DOMAIN
         }
         else if(roles.includes("admin"))
         {
            baseDomain=process.env.ADMIN_BASE_DOMAIN
         }
         else if(roles.includes("costumer"))
         {
            baseDomain=process.env.COSTUMER_BASE_DOMAIN
         }
         else{
            baseDomain=process.env.ADMIN_BASE_DOMAIN
         }



            const userProfile = await Models.UserProfile.findOne({ where: { userId: user.id } });

            let replacements={token:baseDomain+`reset-password?email=${email}&token=`+token, name:userProfile.firstName }
            if(user && user.password == null) {
                await Common.sendEmail(email,'IMPORTED_USER_RESET',replacements,req.headers.language);
            } else {
                await Common.sendEmail(email,'RESET_PASSWORD',replacements,req.headers.language)
            }

         await transaction.commit();
         return h.response({success:true,message:req.i18n.__('SUCESSFULLY_MAIL_SENT'),responseData:{}}).code(200)
    }
    catch(error)
    {
        console.log(error);
        await transaction.rollback();
        return h.response({success: false, message: req.i18n.__('SOMETHING_WENT_WRONG'), responseData: {}}).code(500);
    }
}

// forget password by admin
exports.forgetPasswordByAdmin  =   async(req,h)=>{
    const transaction = await Models.sequelize.transaction();
    try{
        let {email} =   req.payload;
        // console.log('email',email)
         let token = Common.signToken({email});
         let user = await Models.User.findOne({where:{email:email}});
         if(!user) {
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__('EMAIL_NOT_REGISTERD'), responseData: {}}).code(400);
         }
         let alreadyExist = await Models.Token.findOne({where:{email:email}});
         if(alreadyExist){
            await alreadyExist.update({status:1,token:token,type:Constants.TOKENTYPE.RESETPASSWORD});
         } else {
            await Models.Token.create({email:email,status:1,token:token,type:Constants.TOKENTYPE.RESETPASSWORD},{transaction})
         }

         await transaction.commit();
         return h.response({success:true,message:req.i18n.__('SUCESSFULLY_MAIL_SENT'),responseData: token}).code(200)
    }
    catch(error)
    {
        console.log(error);
        await transaction.rollback();
        return h.response({success: false, message: req.i18n.__('SOMETHING_WENT_WRONG'), responseData: {}}).code(500);
    }
}


// Resend verification code to email
exports.resendVerificationCode = async (req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        let email = req.payload.email;
        let tokenExists = await Models.Token.findOne({
            where: {
                email: email
            }
        }, {transaction});
        if (! tokenExists) {
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__('RECORD_NOT_FOUND'), responseData: {}}).code(400);
        }
                    let code    = Common.generateCode(4);
                    await tokenExists.update({
                        code    :   code,
                        status  :   1
                    }, {transaction});

                    let user   =   await Models.User.findOne({where:{email:email}});
                    if(!user) {
                        await transaction.rollback();
                        return h.response({success: false, message: req.i18n.__('EMAIL_NOT_REGISTERD'), responseData: {}}).code(400);
                    }
                    const userProfile = await Models.UserProfile.findOne({ where: { userId: user.id } });

                     email          =   tokenExists.dataValues.email
                    let tokenurl    =process.env.BASE_DOMAIN+`reset-password?email=${email}&token=`+tokenExists.dataValues.token
                    let reqPayload  = {
                        replacements: {
                            code    :   code,
                            token   :   tokenurl,
                            name: userProfile.firstName
                        },
                        priority: 'high',
                        code: 'RESET_PASSWORD',
                        recipients: [email]
                    }
                    console.log(reqPayload, " =============== reqPayload")



                    const data = await Common.createRequest(Constants.URL.SEND_EMAIL, 'post', reqPayload, { language: req.headers.language });
                    console.log(data)
                    await transaction.commit();
                    return h.response({success: true, message: req.i18n.__("REQUEST_SUCESSFULL"), responseData: tokenExists.dataValues.token}).code(200);
    } catch (error) {
        console.log(error);
        await transaction.rollback();
        return h.response({success: false, message: req.i18n.__('EXCEPTION_ENCOUNTERED_WHILE_RESENDING_TOKEN'), responseData: {}}).code(500);
    }
}

// reset password
exports.resetPassword       =   async(req,h)=>{
    const transaction=  await Models.sequelize.transaction();
    try{
        let {token,password}        =   req.payload;
        let   validToken            =   await Models.Token.findOne({where:{token:token}});
        if(!validToken)
        {
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__('INVALID_TOKEN'), responseData: {}}).code(400);
        }
        if(validToken.dataValues.status!==Constants.STATUS.ACTIVE)
        {
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__('TOKEN_EXPIRED'), responseData: {}}).code(400);
        }
        await validToken.update({status:Constants.STATUS.INACTIVE},{transaction});
        let user    =   await Models.User.findOne({where:{email:validToken.dataValues.email}});
        if(!user)
        {
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__('EMAIL_DOES_NOT_REGISTERD'), responseData: {}}).code(400);
        } 
        console.log('validTokenEmail',validToken.dataValues.email);
        console.log('userId',user.id)
        password    =  encryptString(password);
        console.log('password',password);
        await user.update({password:password},{transaction});
        await transaction.commit();
        return h.response({success: true, message: req.i18n.__('PASSWORD_RESET_SUCCESSFULL'), responseData: {}}).code(200);
    }
    catch(error){
        console.log(error);
        await transaction.rollback();
        return h.response({success: false, message: req.i18n.__('SOMETHING_WENT_WRONG'), responseData: {}}).code(500);
    }
}

// Verify user token 
exports.verifyToken         = async (req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        let token       = req.query.token;
        let findToken   = await Models.Token.findOne({where: {token: token}});
        if (!findToken) {
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("INVALID_TOKEN"), responseData: {}}).code(400);
        }
        if (findToken.dataValues.status !== Constants.STATUS.ACTIVE) {
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("TOKEN_EXPIRED"), responseData: {}}).code(400);
        }
        if(findToken.dataValues.type===Constants.TOKENTYPE.SIGNUP) {
            let alreadyinuse    =   await Models.User.findOne({where:{email:findToken.dataValues.email}});
            if(alreadyinuse)
            {
                await transaction.rollback();
                return h.response({success:false, message: req.i18n.__("EMAIL_ALREADY_VERIFIED"), responseData: {}}).code(400);
            }
            let title       =   findToken.dataValues.title
            let firstName   =   findToken.dataValues.firstName
            let lastName    =   findToken.dataValues.lastName
            let email       =   findToken.dataValues.email
            let password    =   findToken.dataValues.password
            let deviceType  =   findToken.dataValues.deviceType
            let deviceToken =   findToken.dataValues.deviceToken
            let user        =   await Models.User.create(
                {
                    email,
                    password,
                    status:1,
                    isEmailVerify:1,
                    deviceType:[deviceType],
                    userValidation: {}
                },
                {
                    include: [{model:Models.UserValidation, as: "userValidation"}],
                    transaction,
            })
            if(!user)
            {
                await transaction.rollback();
                return h.response({success:false, message: req.i18n.__("ERROR_IN_SAVING_USER"), responseData: {}}).code(400);
            }
            await Models.UserProfile.create(
                {
                    firstName,
                    lastName,
                    title,
                    userId:user.dataValues.id,
                    createdBy:user.dataValues.id,
                    lastUpdatedBy:user.dataValues.id
                },
                {
                    transaction
                }
            )
            await findToken.update({status:0},{transaction});
            await user.setRoles([2], {transaction: transaction});
            // set user device-token and devicetype
            if(deviceToken)await Models.Session.create({userId:user.dataValues.id,deviceType,deviceToken});
        
            /// Updating All the services with user
            let lang        =   await user.getLanguages({attributes:['id','code','countery','counteryCode','name','mobileCode'],joinTableAttributes:[]},{transaction})
            // let price       =    profileExist.dataValues.meeting_price
            let updatedLanguages=[]
            for (const l of lang) {
                updatedLanguages.push(l.dataValues)
            }

        
        
                // Updating All the services with user
            const data = {
                userId          : user.id,
                firstName       : findToken.dataValues.firstName,
                lastName        : findToken.dataValues.lastName,
                title           : findToken.dataValues.title,
                profilePhotoUrl :   null,
                profilePhotoId  :   null,
                languages       :   updatedLanguages,
                meetingPrice    :   null,
                reason          :   null,
                rating          :   null,
                scheduleTime    :   null,
                reScheduleTime  :   null,
                gender          :   null,
                roles           :   [],
                email           :   email    
            }

            await Webhook.updateUserInServices(data, transaction);








            await transaction.commit();

            let replacements={
                name:findToken.dataValues.firstName+' '+findToken.dataValues.lastName,
                email: email
            }
            await Common.sendEmail(email,'WELCOME_EMAIL',replacements,req.headers.language)



            // call function for user-email register on quentn
            // await registerMailOnQuentn(firstName,lastName,email,user.dataValues.id);

            let saveuser=await Models.User.findOne({where:{id:user.dataValues.id}})
            let response=await loginAction(saveuser);
            return h.response({success: true, message: req.i18n.__("TOKEN_VERIFIED_SUCESSFULLY"), responseData:response}).code(200);
        } else if(findToken.dataValues.type===Constants.TOKENTYPE.RESETPASSWORD) {
            // reset password for user
            await transaction.commit();
            return h.response({success: true, message: req.i18n.__("TOKEN_VERIFIED_SUCESSFULLY"), responseData: {}}).code(200);
        } else if(findToken.dataValues.type===Constants.TOKENTYPE.RESETEMAIL) {
            let user    =   await Models.User.findOne({where:{id:findToken.dataValues.userId}});

            const oldEmail = user?.dataValues?.email || user?.email;

            await user.update({email:findToken.dataValues.email},{transaction});
            await sequelize.query(`UPDATE zoom_token SET email = '${findToken.dataValues.email}' WHERE email = '${oldEmail}'`, {transaction });
            await sequelize.query(`UPDATE event_users SET email = '${findToken.dataValues.email}' WHERE email = '${oldEmail}'`, {transaction });


            let saveuser=await Models.User.findOne({where:{id:user.dataValues.id}})
            let response=await loginAction(saveuser);


            await transaction.commit();
            return h.response({success: true, message: req.i18n.__("EMAIL_CHANGED_SUCCESSFULLY_PLEASE_LOGIN"), responseData: response}).code(200);
        }
        else
        {
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__("INVALID_TOKEN_OPTION"), responseData: {}}).code(400);
        }
    } 
    catch (error) {
        await transaction.rollback();
        console.error(error);
        return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
    }
};

// change password with old password
exports.changePassword      =   async(req,h)=>{
    const transaction = await Models.sequelize.transaction();
    try{
        let {oldPassword,newPassword}   =   req.payload;
        oldPassword                     =   encryptString(oldPassword)
        newPassword                     =   encryptString(newPassword)
        let userId                      =   req.auth.credentials.userData.User.id
        let user                        =   await Models.User.findOne({where:{id:userId}});
        if(!user)
        {
            await transaction.rollback();
            return h.response({success:false,message:req.i18n.__('USER_NOT_FOUND'),responseData:{}}).code(400)
        }
        if(user.dataValues.password==oldPassword)
        {
            await user.update({password:newPassword},{transaction});
            await transaction.commit();
            return h.response({success:true,message:req.i18n.__('PASSWORD_RESET_SUCCESSFULLY'),responseData:{}}).code(200)
        }
        else
        {
            await transaction.rollback();
            return h.response({success:false,message:req.i18n.__('PASSWORD_DOES_NOT_MATCHED'),responseData:{}}).code(400)
        }
        
    }
    catch(error)
    {
        await transaction.rollback();
        console.error(error);
        return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
    }
}

// admin user listing
exports.getUsers            =   async(req,h)    =>  {
    try {
        let roleWhere={
            roleId:{[Op.ne]:1}
        }

        
        // if(roleId===3)
        // {
        //     roleWhere={
        //         roleId:{
        //             [Op.and]:[
        //                     {[Op.notIn]:[1,2,4]},
        //                     {[Op.in]:[3]}
        //             ]
        //         }
        // }
        // }else{
        //     roleWhere={
        //         roleId:{
        //             [Op.and]:[
        //                     {[Op.notIn]:[1,3,4]},
        //                     {[Op.in]:[2]}
        //             ]
        //         }
        //     }
        // }
        const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT;
        const offset = (req.query.pageNumber - 1) * limit;
        const orderByValue = req.query.orderByValue;
        let orderByParameter = req.query.orderByParameter;
        let ordering = [orderByParameter, orderByValue]
        if(orderByParameter === "rating") {
            ordering = [{model: Models.UserProfile}, "rating", orderByValue]
        }
        let where = {},inner_where={};
        if(req.query.role!==null)
        {
            roleWhere={...roleWhere,roleId:req.query.role}
        }
        if (req.query.status !== null) 
            where = {
                ... where,
                status: req.query.status
            };
            replacements={}
        if(req.query.name!==null)
        {
            let name = '*'+req.query.name.replace('@','%40').replace('.','%2E')+'*';
            replacements={...replacements,"name":name,"name2":name}
           inner_where = { ...inner_where, [Op.or]: [
                Sequelize.literal('MATCH(`first_name`,`last_name`) AGAINST(:name IN BOOLEAN MODE)'),
                // Sequelize.literal('MATCH(`last_name`) AGAINST(:name2 IN BOOLEAN MODE)')
              ] }
        }
        if(req.query.phone!==null)
        {
            let phone ='*'+req.query.phone.replace('@','%40').replace('.','%2E')+'*';
            replacements={...replacements,"phone":phone}
           where = { ...where, [Op.or]: [
                Sequelize.literal('MATCH(`phone_number`) AGAINST(:phone IN BOOLEAN MODE)'),
              ] }
        }
        if(req.query.email!==null)
        {
            // let email =  '*'+req.query.email.replace('@','%40').replace('.','%2E')+'*';
            replacements={...replacements,"email":req.query.email}
            where = { ...where, [Op.or]: [
                Sequelize.literal(`email like '%${req.query.email}%'`),
                // Sequelize.literal('MATCH(`email`) AGAINST(:email IN BOOLEAN MODE)'),
              ] }
        }
        let _requiredUserFields = ['id','email','status','createdAt','countryCode','phoneNumber'];
        let _userProfileFields  = ['firstName','lastName','title','gender','attachment','zoomAccount','rating'];
        let _includesOption     = [
            {
                model: Models.UserProfile,
                attributes: _userProfileFields,
                where:inner_where,
            }, {
                model: Models.Role,
                through: {
                    where: {...roleWhere}
                },
                required: true,
                attributes: ["id","name"]
            },
            {
                model       :   Models.Language,
                through     :   'userobd_user_languages'  
                //include     :   [{model:Models.Language,attributes:['id','code','name']}]
            }
        ];
        let options = {
            where,
            order       : [ordering],
            subQuery    : false,
            replacements,
            distinct:true,
            attributes  : ['id','email','status','createdAt','meetingProductId','soulwritingProductId','countryCode','phoneNumber'],
            include     : _includesOption
        };
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                limit,
                offset
            };
        const users         = await Models.User.findAndCountAll(options);
        const totalPages    = await Common.getTotalPages(users.count, limit);
        const responseData  = {
            totalPages,
            perPage: limit,
            users: users.rows,
            totalRecords: users.count
        };
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
    } catch (error) 
    {
        console.log("error", error);
        return h.response({sucess: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
    }
}

// get userprofile
// exports.getUserProfile      =   async(req,h)  =>{
//     try{
//         let userId=req.auth.credentials.userData.User.id;
//         let user=await Models.User.findOne({where:{id:userId},attributes:['id','email']
//             ,include:[{model:Models.UserProfile}]})
//         let languages=await user.getLanguages({attributes:['id','code','countery','counteryCode','mobileCode'],joinTableAttributes:[]});
//         user.dataValues.languages=languages

// // request for user notification and cart 
//             req.path = '/notification-count'
//             requestObject = setRequestObject(req,process.env.NOTIFICATION_DOMAIN,1)
//             requestObject = {...requestObject,params:{}};

//             let notificationResponse = Axios.request(requestObject);

//             req.path = '/cart-value'
//             requestObject = setRequestObject(req,process.env.PRODUCT_DOAMIN,1)

//             let cartResponse = Axios.request(requestObject);
//             await Promise.all([notificationResponse,cartResponse]).then((res) => {
//                 user.notification   = res[0]?.data?.responseData;
//                 user.productsInCart =   res[1]?.data?.responseData?.productInCart
//             }).catch((err) => {
//                 console.log(" ========== ", err)
//                 throw new Error(err);
//             })

//             return h.response({success: true, message: req.i18n.__("SUCESSFULLY_PROFILE_FOUND"), responseData:user}).code(200); 
//     }
//     catch(error){
//         console.log("error", error);
//         return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
//     }
// }

exports.getUserProfile = async (req, h) => {
    try {
        let userId = req.auth.credentials.userData.User.id;
        let user = await Models.User.findOne({
            where:{ id: userId },
            attributes: ['id','password','email','phoneNumber','countryCode','countryCodeValue']
        });
        if(!user) {
            return h.response({success: false, message:req.i18n.__("EMAIL_NOT_REGISTERD"),responseData: {}}).code(400);
        }

        let roles=await user.getRoles();
        let userRole=[]
        for (const r of roles) {
            userRole.push(r.dataValues.id)
        }

        let registedRole=userRole[0]
        // if(role==2 &&registedRole<role) {
        //     return h.response({success: false, message:req.i18n.__("YOU_ARE_NOT_REGISTERD_WITH_US"),responseData: {}}).code(400);
        // }

        // password=encryptString(password);
        // if(password!==user.password) {
        //     return h.response({success: false, message:req.i18n.__("PASSWORD_DOSE_NOT_MATCHED"),responseData: {}}).code(400);
        // }
        if(user.dataValues.status===Constants.STATUS.INACTIVE) {
            return h.response({success: false, message:req.i18n.__("ACCOUNT_DEACTIVATED_PLEASE_CONTACT_TO_ADMINISTRATOR"),responseData: {}}).code(400);
        }

        let responseData = await loginAction(user);
        delete responseData["token"]
        return h.response({success: true, message:req.i18n.__("SUCCESSFULLY_LOGIN"),responseData}).code(200);
    } catch (error) {
        console.log(error);
        return h.response({success: false, responseData: {}, message: req.i18n.__("SOMETHING_WENT_WRONG")}).code(500);
    }
}

// Update User Status by admin
exports.updateUserStatus    =   async(req,h)=>{
    const transaction = await Models.sequelize.transaction();
    try {
    const{userId,status}    =   req.payload;
    let user    =   await Models.User.findOne({where:{id:userId}});
        if(!user)
        {
            await transaction.rollback();
            return h.response({success:false,message:req.i18n.__('INVALID_USER_DETAILS_PROVIDED'),responseData:{}}).code(400)
        }
        await user.update({
            status:status,
            lastUpdatedBy:req.auth.credentials.userData.User.id,
            lastAdminUpdate:Date.now()
        },{transaction});
        await transaction.commit()
        return h.response({sucess:true,message:req.i18n.__('USER_STATUS_CHANGED_SUCCESSFULLY'),responseData:user}).code(200)
    }
    catch(error)
    {
        await transaction.rollback();
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(400)
    }
}


// get Companion
exports.getCompanion = async (req, h) => {
  try {
    let roleWhere = {
      roleId: { [Op.in]: [3, 4] }
    };
    const limit =
      req.query.limit !== null
        ? req.query.limit > Constants.MAX_PAGINATION_LIMIT
          ? Constants.MAX_PAGINATION_LIMIT
          : req.query.limit
        : Constants.PAGINATION_LIMIT;
    const offset = (req.query.pageNumber - 1) * limit;
    const orderByValue = req.query.orderByValue;
    const orderByParameter = req.query.orderByParameter;

    let where = {};
    let inner_where = {};
    let order = [[orderByParameter, orderByValue]];

    if (req.query.status !== null) where = { ...where, status: req.query.status };

    if (req.query.rating !== null) {
      inner_where = {
        ...inner_where,
        rating: {
          [Op.gte]: req.query.rating
        }
      };
    }
    if (req.query.price !== null) {
      if (req.query.price == 1) {
        inner_where = {
          ...inner_where,
          price: Sequelize.literal(`(UserProfile.meeting_price+0.0)= 0`)
        };
      } else if (req.query.price == 2) {
        order = [[Models.User, Sequelize.literal("`UserProfile.meetingPrice`"), "ASC"]];
      } else if (req.query.price == 3) {
        order = [[Models.User, Sequelize.literal("`UserProfile.meetingPrice`"), "DESC"]];
      }
    }
    if (req.query.lowerExperience !== null) {
      if (req.query.hignerExperience == null) {
        req.query.hignerExperience = 0;
      }
      let endDate = Moment().subtract(req.query.hignerExperience, "y");
      endDate = endDate.utc();
      endDate = endDate.format("YYYY-MM-DD");
      let startDate = Moment().subtract(req.query.lowerExperience, "y");
      startDate = startDate.utc();
      startDate = startDate.format("YYYY-MM-DD");
      inner_where = {
        ...inner_where,
        experience: Sequelize.literal(
          `UserProfile.experience >= DATE('${endDate}') and UserProfile.experience <= DATE('${startDate}') `
        )
      };
    }
    if (req.query.gender !== null) {
      inner_where = {
        gender: req.query.gender
      };
    }

    if (req.query.role != null) {
      roleWhere = {
        roleId: { [Op.in]: [req.query.role] }
      };
    }
    if (req.query.random == 1) {
      order = Sequelize.literal("rand()");
    }

    replacements = {};
    if (req.query.name !== null) {
      let name = "*" + req.query.name.replace("@", "%40").replace(".", "%2E") + "*";
      replacements = { ...replacements, name: name, name2: name };
      inner_where = {
        ...inner_where,
        [Op.or]: [
          Sequelize.literal("MATCH(`first_name`,`last_name`) AGAINST(:name IN BOOLEAN MODE)"),
        //   Sequelize.literal("MATCH(`last_name`) AGAINST(:name2 IN BOOLEAN MODE)")
        ]
      };
    }
    if (req.query.phone !== null) {
      let phone = "*" + req.query.phone.replace("@", "%40").replace(".", "%2E") + "*";
      replacements = { ...replacements, phone: phone };
      where = {
        ...where,
        [Op.or]: [Sequelize.literal("MATCH(`phone_number`) AGAINST(:phone IN BOOLEAN MODE)")]
      };
    }
    if (req.query.email !== null) {
      let email = "*" + req.query.email.replace("@", "%40").replace(".", "%2E") + "*";
      replacements = { ...replacements, email: email };
      where = {
        ...where,
        [Op.or]: [Sequelize.literal("MATCH(`email`) AGAINST(:email IN BOOLEAN MODE)")]
      };
    }
    let languageWhere = {};
    if (req.query.language !== null) {
      languageWhere = { language_id: req.query.language };
    }
    let _includesOption = [
      {
        model: Models.UserProfile,
        where: inner_where, required: true
      },
      {
        model: Models.Role,
        through: { where: { ...roleWhere } },
        required: true, attributes: ["name"],
        joinTableAttributes: []
      },
      {
        model: Models.Language, required: false,
        through: { where: languageWhere }
      }
    ];

    let options = {
      where, order, distinct: true, replacements,
      attributes: ["id", "email", "status", "createdAt"],
      include: _includesOption
    };

    if (req.query.pageNumber !== null)
      options = {
        ...options,
        limit,
        offset
      };

    const users = await Models.User.findAndCountAll(options);
    const totalPages = await Common.getTotalPages(users.count, limit);
    let loadMore = false;
    if (req.query.pageNumber == null) req.query.pageNumber = 1;
    if (totalPages > req.query.pageNumber) {
      loadMore = true;
    }
    const responseData = {
      totalPages,
      perPage: limit,
      users: users.rows,
      totalRecords: users.count,
      loadMore
    };
    return h
      .response({
        success: true,
        message: req.i18n.__("REQUEST_SUCCESSFUL"),
        responseData: responseData
      })
      .code(200);
  } catch (error) {
    console.log("error", error);
    return h
      .response({ sucess: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {} })
      .code(500);
  }
};
exports.getMyCompanion = async (req, h) => {
  try {
    let userId = req.auth.credentials.userData.User.id;
    let roleWhere = {
      roleId: { [Op.in]: [3, 4] }
    };
    const limit =
      req.query.limit !== null
        ? req.query.limit > Constants.MAX_PAGINATION_LIMIT
          ? Constants.MAX_PAGINATION_LIMIT
          : req.query.limit
        : Constants.PAGINATION_LIMIT;
    const offset = (req.query.pageNumber - 1) * limit;
    const orderByValue = req.query.orderByValue;
    const orderByParameter = req.query.orderByParameter;

    let where = {};
    let inner_where = {};
    let order = [[orderByParameter, orderByValue]];

    if (req.query.status !== null) where = { ...where, status: req.query.status };

    if (req.query.rating !== null) {
      inner_where = {
        ...inner_where,
        rating: {
          [Op.gte]: req.query.rating
        }
      };
    }
    if (req.query.price !== null) {
      if (req.query.price == 1) {
        inner_where = {
          ...inner_where,
          price: Sequelize.literal(`(UserProfile.meeting_price+0.0)= 0`)
        };
      } else if (req.query.price == 2) {
        order = [[Models.User, Sequelize.literal("`UserProfile.meetingPrice`"), "ASC"]];
      } else if (req.query.price == 3) {
        order = [[Models.User, Sequelize.literal("`UserProfile.meetingPrice`"), "DESC"]];
      }
    }
    if (req.query.lowerExperience !== null) {
      if (req.query.hignerExperience == null) {
        req.query.hignerExperience = 0;
      }
      let endDate = Moment().subtract(req.query.hignerExperience, "y");
      endDate = endDate.utc();
      endDate = endDate.format("YYYY-MM-DD");
      let startDate = Moment().subtract(req.query.lowerExperience, "y");
      startDate = startDate.utc();
      startDate = startDate.format("YYYY-MM-DD");
      inner_where = {
        ...inner_where,
        experience: Sequelize.literal(
          `UserProfile.experience >= DATE('${endDate}') and UserProfile.experience <= DATE('${startDate}') `
        )
      };
    }
    if (req.query.gender !== null) {
      inner_where = {
        gender: req.query.gender
      };
    }

    if (req.query.role != null) {
      roleWhere = {
        roleId: { [Op.in]: [req.query.role] }
      };
    }
    if (req.query.random == 1) {
      order = Sequelize.literal("rand()");
    }

    replacements = {};
    if (req.query.name !== null) {
      let name = "*" + req.query.name.replace("@", "%40").replace(".", "%2E") + "*";
      replacements = { ...replacements, name: name, name2: name };
      inner_where = {
        ...inner_where,
        [Op.or]: [
          Sequelize.literal("MATCH(`first_name`,`last_name`) AGAINST(:name IN BOOLEAN MODE)"),
        //   Sequelize.literal("MATCH(`last_name`) AGAINST(:name2 IN BOOLEAN MODE)")
        ]
      };
    }
    if (req.query.phone !== null) {
      let phone = "*" + req.query.phone.replace("@", "%40").replace(".", "%2E") + "*";
      replacements = { ...replacements, phone: phone };
      where = {
        ...where,
        [Op.or]: [Sequelize.literal("MATCH(`phone_number`) AGAINST(:phone IN BOOLEAN MODE)")]
      };
    }
    if (req.query.email !== null) {
      let email = "*" + req.query.email.replace("@", "%40").replace(".", "%2E") + "*";
      replacements = { ...replacements, email: email };
      where = {
        ...where,
        [Op.or]: [Sequelize.literal("MATCH(`email`) AGAINST(:email IN BOOLEAN MODE)")]
      };
    }
    let languageWhere = {};
    if (req.query.language !== null) {
        const languageInfo = await Models.Language.findOne({where : { code: req.query.language }})
        if(languageInfo) {
            languageWhere = { language_id:languageInfo.id }
        } else {
            languageInfo = {}
        }
    }
    let _includesOption = [
      {
        model: Models.UserProfile,
        where: inner_where, required: true
      },
      {
        model: Models.Role,
        through: { where: { ...roleWhere } },
        required: true, attributes: ["name"],
        joinTableAttributes: []
      },
      {
        model: Models.Language, required: true, as: "userLanguages",
        through: { where: languageWhere }
      },
      {
        model: Models.Language, required: false,
        through: {}
      }
    ];


    if(req.query.onlyMyCompanions === 1) {
        let companions = []
        let companionHistoryInfo = await Models.EarningHistory.findAll({ 
            attributes: ["id", "userId", "companionId"], 
            where: { userId: userId },  group: "companionId" 
        });
        for(let companion of companionHistoryInfo) {
            companions.push(companion.companionId)
        }
        where = {...where, id: companions}
    }



    let options = {
      where, order, distinct: true, replacements,
      attributes: ["id", "email", "status", "createdAt"],
      include: _includesOption
    };

    if (req.query.pageNumber !== null)
      options = {
        ...options,
        limit,
        offset
      };

    const users = await Models.User.findAndCountAll(options);
    const totalPages = await Common.getTotalPages(users.count, limit);
    let loadMore = false;
    if (req.query.pageNumber == null) req.query.pageNumber = 1;
    if (totalPages > req.query.pageNumber) {
      loadMore = true;
    }
    const responseData = {
      totalPages,
      perPage: limit,
      users: users.rows,
      totalRecords: users.count,
      loadMore
    };
    return h
      .response({
        success: true,
        message: req.i18n.__("REQUEST_SUCCESSFUL"),
        responseData: responseData
      })
      .code(200);
  } catch (error) {
    console.log("error", error);
    return h
      .response({ sucess: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {} })
      .code(500);
  }
};

exports.getActiveCompanion = async(req,h)=>{
    try{
        let roleWhere={}
        // let roleWhere={ roleId:{[Op.in]:[3,4]} }
        // to allow the list to be filtered on the basis of student meeting count
        // if(req.query.showAll === 0) {
        //     let definedCount = await Models.Setting.findOne({ where: { key: "STUDENT_MEETING_COUNT" } });
        //     if(definedCount) definedCount = definedCount.value;
        //     else definedCount = null;

        //     if(definedCount !== null) {
        //         let userId = req.auth.credentials.userData.User.id;
        //         const userValidationInfo = await Models.UserValidation.findOne({ where: { userId } });
        //         let count = userValidationInfo ? userValidationInfo.studentMeetingCount : 0;
        //         if(count >= definedCount) roleWhere={ roleId:{[Op.in]:[4]} }
        //     }
        // }
        let totalStudentMeetings = 0;
        let allowStudentMeetings = true;
        let allowdMeetingCount = 0;
        const showData = req.query.showData;
        if(showData == "companion") {
            roleWhere= { roleId: 4 }
        } else if(showData == "student"){
            roleWhere= { roleId: 3 }
        } else if(showData == "student-meeting-count") {
            let definedCount = await Models.Setting.findOne({ where: { key: "STUDENT_MEETING_COUNT" } });
            if(definedCount) definedCount = definedCount.value;
            else definedCount = null;

            if(definedCount !== null) {
                let userId = req.auth.credentials.userData.User.id;
                const userValidationInfo = await Models.UserValidation.findOne({ where: { userId } });
                let count = userValidationInfo ? userValidationInfo.studentMeetingCount : 0;
                if(count >= definedCount) roleWhere={ roleId:{[Op.in]:[4]} }
            }
        } else {
            roleWhere={ roleId:{[Op.in]:[3,4]} }
        }

        let userId = req?.auth?.credentials?.userData?.User?.id;
        if(userId) {
            let definedCount = await Models.Setting.findOne({ where: { key: "STUDENT_MEETING_COUNT" } });
            if(definedCount) definedCount = definedCount.value;
            else definedCount = null;
            allowdMeetingCount = definedCount;
            if(definedCount !== null) {
                const userValidationInfo = await Models.UserValidation.findOne({ where: { userId } });
                let count = userValidationInfo ? userValidationInfo.studentMeetingCount : 0;
                totalStudentMeetings = count;
                if(count >= definedCount) allowStudentMeetings = false;
            }
        }

        //const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT;
        const limit = req.query.limit !== null ? req.query.limit : Constants.PAGINATION_LIMIT;
        const offset = (req.query.pageNumber - 1) * limit;
        const orderByValue = req.query.orderByValue;
        const orderByParameter = req.query.orderByParameter;
        
        let where = {status: 1},inner_where={zoomAccount: 1};

        if(req.query.soulwritingProductId === 1) {
            where = {...where, soulwritingProductId: {[Op.ne]: null} }
        }
        if(req.query.meetingProductId === 1) {
            where = {...where, meetingProductId: {[Op.ne]: null} }
        }

        let order=[
            [orderByParameter, orderByValue]
        ]
        
        if(req.query.rating!==null)
        {
           inner_where={
            ...inner_where,
            rating:{
                [Op.gte]:req.query.rating
            }
           }
        }
        if(req.query.price!==null)
        {
            if(req.query.price==1)
            {
                inner_where={
                    ...inner_where,
                    price:Sequelize.literal(`(UserProfile.meeting_price+0.0)= 0`)
                   }
            }
            else if(req.query.price==2){
                 order=[
                    [Models.User, Sequelize.literal('`UserProfile.meetingPrice`'), 'ASC'],
                ]
            }
            else if(req.query.price==3){
                order=[
                    [Models.User, Sequelize.literal('`UserProfile.meetingPrice`'), 'DESC'],
               ]
           }
        }
        if(req.query.lowerExperience!==null)
        {
            if(req.query.hignerExperience==null)
            {
                req.query.hignerExperience=0
            }
            let endDate =   Moment().subtract(req.query.hignerExperience, 'y')
            endDate     =   endDate.utc()
            endDate     =   endDate.format('YYYY-MM-DD')
            let startDate   =   Moment().subtract(req.query.lowerExperience, 'y')
            startDate       =   startDate.utc()
            startDate       =   startDate.format('YYYY-MM-DD')
           inner_where={
            ...inner_where,
            experience:Sequelize.literal(`UserProfile.experience >= DATE('${endDate}') and UserProfile.experience <= DATE('${startDate}') `),
           }
        }
        if(req.query.gender!==null)
        {
            inner_where={
                gender:req.query.gender
            }
        }

        if(req.query.role!=null)
        {
            roleWhere={
                roleId:{[Op.in]:[req.query.role]}
            }
        }
        if(req.query.random==1)
        {
            order= Sequelize.literal('rand()')
        }
        
            replacements={}
        if(req.query.name!==null)
        {
            let name = '*'+req.query.name.replace('@','%40').replace('.','%2E')+'*';
            replacements={...replacements,"name":name,"name2":name}
           inner_where = { ...inner_where, [Op.or]: [
                Sequelize.literal('MATCH(`first_name`,`last_name`) AGAINST(:name IN BOOLEAN MODE)'),
                // Sequelize.literal('MATCH(`last_name`) AGAINST(:name2 IN BOOLEAN MODE)')
              ] }
        }
        if(req.query.phone!==null)
        {
            let phone ='*'+req.query.phone.replace('@','%40').replace('.','%2E')+'*';
            replacements={...replacements,"phone":phone}
           where = { ...where, [Op.or]: [
                Sequelize.literal('MATCH(`phone_number`) AGAINST(:phone IN BOOLEAN MODE)'),
              ] }
        }
        if(req.query.email!==null)
        {
            let email =  '*'+req.query.email.replace('@','%40').replace('.','%2E')+'*';
            replacements={...replacements,"email":email}
            where = { ...where, [Op.or]: [
                Sequelize.literal('MATCH(`email`) AGAINST(:email IN BOOLEAN MODE)'),
              ] }
        }
        let languageWhere={}
        if(req.query.language!==null) {
            const languageInfo = await Models.Language.findOne({where : { code: req.query.language }})
            if(languageInfo) {
                languageWhere = { language_id:languageInfo.id }
            } else {
                languageInfo = {}
            }
        }
        let _includesOption = [
          {
            model: Models.UserProfile,
            where: inner_where,
            required: true
          },
          {
            model: Models.Role,
            through: {
              where: { ...roleWhere }
            },
            required: true,
            attributes: ["name"],
            joinTableAttributes: []
          },
          {
            model: Models.Language,
            through: { where: languageWhere },
            as: "userLanguages",
            required: true
          },
          {
            model: Models.Language,
            through: {},
            required: false
          }
        ];
        let options = {
            // subQuery: false,
            where,
            order,
            distinct    :   true,
            replacements,
            attributes  : ['id','email','status','createdAt','soulwritingProductId','meetingProductId'],
            include     : _includesOption
        };
     
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                limit,
                offset
            };
           
        const users         = await Models.User.findAndCountAll(options);
        const totalPages    = await Common.getTotalPages(users.count, limit);
        let loadMore=false
        if(req.query.pageNumber==null)req.query.pageNumber=1
        if(totalPages>req.query.pageNumber)
        {
            loadMore=true
        }
        const responseData  = {
            totalPages,
            perPage: limit,
            users: users.rows,
            totalRecords: users.count,
            loadMore,
            studentMeetingMeta: { totalStudentMeetings, allowStudentMeetings, allowdMeetingCount }
        };
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
    }   
    catch(error){
        console.log("error", error);
        return h.response({sucess: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
    }
}

// Get Companion By Id

exports.getCompanionById    =   async(req,h)=>{
    try{
        let id=req.query.id
        let user=await Models.User.findOne({
            where:{id},
            attributes: { exclude: ["password"] },
            include:[
                {
                    model: Models.UserProfile
                }, 
                {
                    model       :   Models.Language,
                    through     :   'userobd_user_languages'
                },
                {
                    model: Models.Role,
                    through: {}
                },
            ]
        })
        if(!user)
        {
            return h.response({success:false,message: req.i18n.__("RECORD_NOT_FOUND"), responseData: {}}).code(400)
        }
        return h.response({success:true,message: req.i18n.__("SUCCESSFULLY_FATCHED"), responseData:user}).code(200)
    }
    catch(error)
    {
        console.log(error)
        return h.response({sucess: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
    }
}

// Get Notification Ids
exports.getUserSessions     =   async(req,h)=>{
    //const transaction = await Models.sequelize.transaction();
    try{
        let response=[]
        let records=await Models.Session.findAll({
            where:{
                userId:req.query.userId
            },
            attributes:['deviceToken']
        })
    for (const r of records) {
    response.push(r.dataValues.deviceToken);
    }
        return h.response({sessionIds:response}).code(200)
    }
    catch(error)
    {
        console.log('error',error)
        //await transaction.rollback();
        return h.response({sucess:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500)
    }
}

exports.updateUserProfiles  =   async(req,h)=>{
    const transaction = await Models.sequelize.transaction();
    try{
        if(req.query.code!=='<EMAIL>')
        {
            await transaction.rollback()
            return h.response({success:false,error:'Invalid Code'})
        }
        
        const users=await Models.User.findAll()
        for (const user of users) {
            const profileExist=await Models.UserProfile.findOne({where:{userId:user.dataValues.id}})
            let lang        =   await user.getLanguages({attributes:['id','code','countery','counteryCode','name','mobileCode'],joinTableAttributes:[]},{transaction})
            let updatedLanguages=[]
    for (const l of lang) {
        updatedLanguages.push(l.dataValues)
    }

        // Updating All the services with user
        const data = {
            userId          :   user.dataValues.id                  ?  user.dataValues.id           :   null,
            firstName       :   profileExist?.firstName             ?  profileExist?.firstName      :   null,
            lastName        :   profileExist?.lastName              ?  profileExist?.lastName       :   null,
            title           :   profileExist?.title                 ?  profileExist?.title          :   null,
            profilePhotoUrl :   profileExist?.attachment?.path      ?  profileExist?.path           :   null,
            profilePhotoId  :   profileExist?.attachment?.id        ?  profileExist?.id             :   null,
            languages       :   updatedLanguages               ,
            meetingPrice    :   profileExist?.meetingPrice          ?  profileExist?.meetingPrice   :   null,
            reason          :   profileExist?.reason                ?  profileExist?.reason         :   null,
            rating          :   profileExist?.rating                ?  profileExist?.rating         :   null,
            scheduleTime    :   profileExist?.scheduleTime          ?  profileExist?.scheduleTime       :   null,
            reScheduleTime  :   profileExist?.reScheduleTime        ?  profileExist?.reScheduleTime       :   null,
            gender          :   profileExist?.gender    ,
            cancelTime      :   profileExist?.cancelTime,
            roles           :   [],
            email           :   user.dataValues.email, 
            vita            :   profileExist.vita                   ?   profileExist.vita           :   null,   
            experience      :   profileExist.experience             ?   profileExist.experience     :   null,   

        }
        console.log('data',data)
        await Webhook.updateUserInServices(data, transaction);
        }
        await transaction.commit()
        return h.response({success:true,message:"Successfully Profile Updated"})
    }
    catch(error)
    {
        await transaction.rollback()
        console.error('Error in Syncincing Profiles',error)
        return h.response({success:false,error:error.toString()})
    }
}

// Logout apis
exports.logout              =   async(req,h)=>{
    const transaction=await Models.sequelize.transaction();
    try{
        const {deviceType}=req.payload
        const userId=req.auth.credentials.userData.User.id
        let record=await Models.Session.findOne({where:{userId:userId,deviceType:deviceType}})
        if(record) await record.destroy({transaction})
        await transaction.commit()
        return h.response({success:false,message:req.i18n.__('SUCCESSFULLY_LOGOUT'),responseData:record}).code(200)
    }
    catch(error)
    {
        await transaction.rollback()
        console.error('Error in Logout api',error)
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)

    }
}

exports.updateDeviceToken   =   async(req,h)=>{
    const transaction=await Models.sequelize.transaction();
    try{
        const {deviceType,deviceToken}=req.payload
        const userId    =   req.auth.credentials.userData.User.id
        let record      =   await Models.Session.findOne({where:{userId:userId,deviceType:deviceType}})
        if(record) await record.update({deviceToken},{transaction})
        else
        record=await Models.Session.create({userId:userId,deviceType:deviceType,deviceToken:deviceToken},{transaction:transaction})
        await transaction.commit()
        return h.response({success:false,message:req.i18n.__('SUCCESSFULLY_TOKEN_UPDATED'),responseData:record}).code(200)
    }
    catch(error)
    {
        await transaction.rollback()
        console.error('Error in Logout api',error)
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)

    }
}


exports.createZoomUser  =   async(req,h)=>{
    const transaction=await Models.sequelize.transaction();
    try{
        if(req.query.code!=='<EMAIL>')
        {
            await transaction.rollback()
            return h.response({success:false,error:'Invalid Code'})
        }
        let roleWhere={roleId:4}
        const users=await Models.User.findAll({raw:true,nest:true,attributes:['email'],include:[
            {model:Models.UserProfile,attributes:['firstName','lastName']}
            ,{
            model: Models.Role,
            through: {
                where: {...roleWhere}
            },
            required: true,
            attributes: []
        }]})
        for (const user of users) {
            let reqPayload={
                email:user.email,
                firstName:user.firstName?user.firstName:'',
                lastName:user.lastName?user.lastName:'',
                displayName:user.firstName?user.firstName:''
            }
            await Webhook.createUserOnZoom(reqPayload, transaction);
        }
        await transaction.commit()
        return h.response({success:true,message:"Successfully Profile Updated"})
    }
    catch(error)
    {
        await transaction.rollback()
        console.error('Error in Logout api',error)
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)

    }
}

const sendEmail = async(data,code,language = "en")=>{
    try{
        // let data = { replacements, priority:'high', code, recipients }

        let replacements = {}
        let recipients = []
        if(code === "USER_BY_ADMIN") {
            if(data?.userId) {
                let url = null;
                if(data.role === "customer") {
                    url = `${process.env.COSTUMER_BASE_DOMAIN}signin`
                } else if(data.role === "professional") {
                    url = `${process.env.COMPANION_BASE_DOMAIN}signin`
                } else if(data.role === "admin") {
                    url = process.env.ADMIN_BASE_DOMAIN
                }

                const userInfo = await Models.User.findOne({ where: { id: data?.userId }, raw: true });
                const userProfileInfo = await Models.UserProfile.findOne({ where: { userId: data?.userId }, raw: true });
                replacements = {...replacements,link: url, email: data?.email, name: userProfileInfo?.firstName,password: data?.password}
                recipients.push(userInfo?.email)
            }
        } else if(code === "EMAIL_WITHOUT_PASSWORD") {
            const userInfo = await Models.User.findOne({ where: { id: data?.userId }, raw: true });
            const userProfileInfo = await Models.UserProfile.findOne({ where: { userId: data?.userId }, raw: true });
            replacements = {...replacements, email: data?.email, name: userProfileInfo?.firstName}
            recipients.push(userInfo?.email)
        } else {
            return
        }


        console.log(replacements, recipients, " ==================== replacements, recipients")


        const requestObj = {
            url : `${process.env.EMAIL_SERVICE}/email/send`,
            method: "post",
            data: { replacements, priority:'high', code, recipients },
            headers: {language}
        }
        
        // axoisObj  =   createAxoisObj(URL.SENDEMAIL,'post',data)
        //await createRequest(axoisObj,transaction)
        let res=await Axios(requestObj).then(async(res)=>{
            console.log('Sucessfully Send Email.......... ')
            console.log(res.data)
            return res.data
        })
        .catch(async (error) => {
            console.log('hi catch in Email .........',error)
            return {}
        });
        return res
    } catch(error) {
        console.log('Error in Sending email',error)
    }
}

exports.createNewUser   =   async(req,h)=>{
    const transaction=await Models.sequelize.transaction();
    try{
        //let createdNewAccount = 0;
        let{email,firstName,lastName, existingUserEmailSend}=req.payload;
        let alreadyExist=await Models.User.findOne({where:{email:email}});
        console.log(" ================ 11111")
        console.log(" ================ 11111")
        console.log(" ================ 11111")
        console.log(" ================ 11111")
        if(alreadyExist) {
            console.log(" ================== 2222")
            console.log(" ================== 2222")
            console.log(" ================== 2222")
            console.log(" ================== 2222")
            await transaction.commit();
            if(existingUserEmailSend === 1) {
                const code = "EMAIL_WITHOUT_PASSWORD";
                let data = { userId: alreadyExist.id, email: email }
                sendEmail(data, code, req.headers.language)
            }
            return h.response({userId: alreadyExist.id}).code(200)
        }
        let accountDeleted=await Models.User.findOne({paranoid:false,where:{email:email, deleted_at:{ [Op.ne]:null } }})
            
        if(accountDeleted) {
            await accountDeleted.restore({transaction})
            await transaction.commit();
            return h.response({userId:accountDeleted.dataValues.id}).code(200)
        }

        let password=Common.generateRandomPassword(12)

        let user=await Models.User.create({
            email, password:encryptString(password),UserProfile:[ { firstName, lastName } ], userValidation: {}
        },{transaction,include:[ {model:Models.UserProfile}, {model:Models.UserValidation, as: "userValidation"}]})
            
        await user.setRoles([2], {transaction});
        const webhookData = {
          userId: user.id, firstName: firstName, lastName: lastName, 
          title: null, profilePhotoUrl: null, profilePhotoId: null,
          languages: null, meetingPrice: null, scheduleTime: null, reScheduleTime: null,
          cancelTime: null, gender: null, reason: null, email, vita: null, experience: null
        };
        await Webhook.updateUserInServices(webhookData, transaction)
           
        await transaction.commit();

        let data = { userId: user.id, password: password, email: email, role: "customer" }
        let code = "USER_BY_ADMIN"
        sendEmail(data, code, req.headers.language)

        // let replacements={email,name:firstName+' '+lastName,url:'https://portal.kuby.info'}
        // await Common.sendEmail(email,'ACCOUNT_CREATED',replacements)
        return h.response({userId:user.id}).code(200)
    }
    catch(error)
    {
        await transaction.rollback()
        console.error('Error in Logout api',error)
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)

    }   
}

exports.createNewUserByAdmin   =   async(req,h)=>{
    const transaction=await Models.sequelize.transaction();
    try{
        //let createdNewAccount = 0;
        let{title, email,firstName,lastName, language}=req.payload;
        let alreadyExist=await Models.User.findOne({where:{email:email}});
        if(alreadyExist) {
            await transaction.rollback();
            return h.response({success:false,message: req.i18n.__("USER_ALREADY_EXISTS"), responseData: {}}).code(400)
        }
        let accountDeleted=await Models.User.findOne({paranoid:false,where:{email:email, deleted_at:{ [Op.ne]:null } }})
            
        if(accountDeleted) {
            await transaction.rollback();
            return h.response({success:false,message: req.i18n.__("ACCOUNT_SUSPENDED"), responseData: {}}).code(400)
        }

        let password=Common.generateRandomPassword(12)

        let user=await Models.User.create({
            email, password:encryptString(password),UserProfile:[ { firstName, lastName, title } ], userValidation: {}
        },{transaction,include:[ {model:Models.UserProfile}, {model:Models.UserValidation, as: "userValidation"}]})
            
        await user.setRoles([2], {transaction});
        const webhookData = {
          userId: user.id, firstName: firstName, lastName: lastName, 
          title: title, profilePhotoUrl: null, profilePhotoId: null,
          languages: null, meetingPrice: null, scheduleTime: null, reScheduleTime: null,
          cancelTime: null, gender: null, reason: null, email, vita: null, experience: null
        };
        await Webhook.updateUserInServices(webhookData, transaction)
           
        await transaction.commit();

        let data = { userId: user.id, password: password, email: email, role: "customer" }
        let code = "USER_BY_ADMIN"
        sendEmail(data, code, language)

        return h.response({success:true,message: req.i18n.__("REQUEST_SUCCESSFULL"), responseData:user}).code(200)
    }
    catch(error) {
        await transaction.rollback()
        console.error('Error in Logout api',error)
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)

    }   
}

exports.updateUserByAdmin = async(req,h)=>{
    const transaction=await Models.sequelize.transaction();
    try{
        let{id, title, email,firstName,lastName, language}=req.payload;
        let alreadyExist=await Models.User.findOne({where:{id}});
        if(!alreadyExist) {
            await transaction.rollback();
            return h.response({success:false,message: req.i18n.__("INVALID_ID_PROVIDED"), responseData: {}}).code(400)
        }

        await Models.User.update({ email }, { where: {id: id}, transaction });
        await Models.UserProfile.update({ title, firstName, lastName }, { where: {userId: id}, transaction });

        let user = await Models.User.findOne({
            where: { id: id },
            include:[ {model:Models.UserProfile}],
            transaction
        })
            
        const webhookData = {
          userId: user.id, firstName: firstName, lastName: lastName, 
          title: title, profilePhotoUrl: null, profilePhotoId: null,
          languages: null, meetingPrice: null, scheduleTime: null, reScheduleTime: null,
          cancelTime: null, gender: null, reason: null, email, vita: null, experience: null
        };
        await Webhook.updateUserInServices(webhookData, transaction)
           
        await transaction.commit();

        return h.response({success:true,message: req.i18n.__("REQUEST_SUCCESSFULL"), responseData:user}).code(200)
    }
    catch(error)
    {
        await transaction.rollback()
        console.error('Error in Logout api',error)
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)

    }   
}


// Update zoom status
exports.UpdateZoomStatus=async(req,h)=>{
    const transaction=await Models.sequelize.transaction();
    try{
        const {email,status}=req.payload
        let user= await Models.User.findOne({where:{email:email}})
        if(!user)
        {
        await transaction.rollback()
        return h.response({success:false,message:req.i18n.__('USER_NOT_FOUND'),responseData:{}}).code(400)
        }
        let profile=await Models.UserProfile.findOne({where:{userId:user.dataValues.id}})
        const data = await profile.update({zoomAccount:status},{transaction});


        const url = `${process.env.EVENT_DOMAIN}/webhook/update-zoom-id-status`;
        let webhookPayload = { userId: user.dataValues.id, zoomStatus: status }
        const axiosObj = {
            url    : url,
            method : "POST",
            data   : webhookPayload
        };

        await Axios(axiosObj).then(()=>{ console.log(`User Inserted on ${axiosObj.url}`) })
        .catch(async (error) => {
            console.log(`User Insert Error on ${axiosObj.url}`)
            console.log('error',error)
        });

        await transaction.commit()
        return h.response({success:true,message:req.i18n.__('SUCCESSFULLY_UPDATED'),responseData:{profile}}).code(200)
    }
    catch(error)
    {
        await transaction.rollback()
        console.error('Error in Logout api',error)
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
    }
}

exports.UpdateGoogleStatus=async(req,h)=>{
    const transaction=await Models.sequelize.transaction();
    try{
        const {email,status}=req.payload
        let user= await Models.User.findOne({where:{email:email}})
        if(!user) {
            await transaction.rollback()
            return h.response({success:false,message:req.i18n.__('USER_NOT_FOUND'),responseData:{}}).code(400)
        }
        let profile=await Models.UserProfile.findOne({where:{userId:user.dataValues.id}})
        await profile.update({googleAccount:status},{transaction})
        await transaction.commit()
        return h.response({success:true,message:req.i18n.__('SUCCESSFULLY_UPDATED'),responseData:{profile}}).code(200)
    } catch(error) {
        await transaction.rollback()
        console.error('Error in Logout api',error)
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
    }
}

exports.paymentHistory = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const { meetingId, projectId, data, amount, amountReceived, userId, companionId, projectObject, meetingObject, userObject, companionObject } = req.payload;

        await Models.EarningHistory.create(req.payload, {transaction});

        await transaction.commit();
        return h.response({success:true,message:req.i18n.__('SUCCESSFULLY_UPDATED'),responseData:{}}).code(200)
    } catch (error) {
        await transaction.rollback();
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
    }
}

exports.updatedPaymentHistory = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const { meetingId, updatedMeetingId } = req.payload;

        const findMeeting = await Models.EarningHistory.findOne({ where: { meetingId: meetingId } });
        console.log(" ===================== ")
        console.log(" ===================== ")
        console.log(" ===================== ")
        console.log(" ===================== ")
        console.log(" ===================== ")
        console.log(" ===================== ")
        console.log(" ===================== ")
        console.log(" ===================== ")
        console.log(" ===================== ")
        console.log(" ===================== ")
        console.log(" ===================== ")
        console.log(" ===================== ")
        console.log(" ===================== ")
        console.log(" ===================== ")
        console.log(" ===================== ")
        if(!findMeeting) {
            await transaction.rollback()
            return h.response({success:false,message:req.i18n.__('INVALID_MEETING_ID_PROVIDED'),responseData:{}}).code(400)
        }
        await findMeeting.update({meetingId: updatedMeetingId}, {transaction});

        await transaction.commit();
        return h.response({success:true,message:req.i18n.__('SUCCESSFULLY_UPDATED'),responseData:{}}).code(200)
    } catch (error) {
        await transaction.rollback();
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
    }
}

exports.listPaymentHistory = async(req, h) => {
    try {
        const createdById = req.auth.credentials.userData.User.id;

        let where = {}
        if(req.auth.credentials.userData.Role[0] === "companion") {
            where =  {...where, companionId: createdById}
        } else if(req.auth.credentials.userData.Role[0] === "costumer") {
            where =  {...where, userId: createdById}
        } else {
            where = {...where, [Op.or]: [{companionId: createdById}, {companionId: createdById}]}
        }

        const limit = (req.query.limit !== null )
        ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit
        : Constants.PAGINATION_LIMIT;
        const offset = (req.query.pageNumber-1) * limit;
        const orderByValue = req.query.orderByValue;
        const orderByParameter = req.query.orderByParameter;
        // let where = {companionId: createdById}

        if(req.query.type === "soul-writing") {
            where = {...where, projectId: {[Op.ne]: null}}
        }
        if(req.query.type === "meeting") {
            where = {...where, meetingId: {[Op.ne]: null}}
        }
        if(req.query.type === "topic") {
            where = {...where, topicId: {[Op.ne]: null}}
        }

        let options = {where: where, order: [[orderByParameter,orderByValue]]}
        if(req.query.pageNumber !== null) options={...options,limit,offset};

        let EarningHistory = await Models.EarningHistory.findAndCountAll(options)

        const totalPages = await Common.getTotalPages(EarningHistory.count,limit);
        const responseData = {
            totalPages,
            perPage:limit,
            totalRecords: EarningHistory.count,
            earningHistory: EarningHistory.rows,
            baseUrl: process.env.NODE_SERVER_PUBLIC_API,
        }

        return h.response({success:true,message:req.i18n.__('SUCCESSFULLY_UPDATED'),responseData:responseData}).code(200)
    } catch (error) {
        console.log(error)
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
    }
}

exports.userPaymentAmount = async(req, h) => {
    try {
        const createdById = req.auth.credentials.userData.User.id;

        // let paymentAmount = await Models.EarningHistory.sum("amountReceived", { where: { companionId: createdById, isRefunded: 0 } });

        const [eventPayment, metaE] = await sequelize.query(`SELECT total_earning FROM event_users where user_id = ${createdById};`);
        const [soulwritingPayment, metaS] = await sequelize.query(`SELECT total_earning FROM soul_writing_users where user_id = ${createdById};`);
        let eventAmount = eventPayment?.[0]?.total_earning ? eventPayment?.[0]?.total_earning : 0;
        let soulwritingAmount = soulwritingPayment?.[0]?.total_earning ? soulwritingPayment?.[0]?.total_earning : 0;
        
        let totalAmount = eventAmount + soulwritingAmount;
        console.log(" ================================ ", totalAmount)
        console.log(" ================================ ", totalAmount)
        console.log(" ================================ ", totalAmount)
        console.log(" ================================ ", totalAmount)
        console.log(" ================================ ", totalAmount)
        console.log(" ================================ ", totalAmount)
        return h.response({success:true,message:req.i18n.__('SUCCESSFULLY_UPDATED'),responseData:totalAmount}).code(200)
    } catch (error) {
        console.log(error)
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
    }
}

exports.refundOrder = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const meetingId = req.payload.meetingId;

        let findEarning = await Models.EarningHistory.findOne({ where: { meetingId } });
        if(!findEarning) {
            await transaction.rollback()
            return h.response({success:false,message:req.i18n.__('INVALID_MEETING_ID_PROVIDED'),responseData:{}}).code(400)
        }
        await findEarning.update({ isRefunded: 1 });

        await transaction.commit();
        return h.response({success:true,message:req.i18n.__('SUCCESSFULLY_UPDATED'),responseData:{}}).code(200)
    } catch (error) {
        console.log(error)
        await transaction.rollback();
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)   
    }
}

 

exports.userHistoryDetails = async(req, h) => {
    try {
        const userId = req.query.userId;
        const type = req.query.type || "customer";
        let responseData={}
        const userInfo = await Models.User.findOne({ 
            where: { id: userId }, 
            attributes: ["id","status","countryCodeValue","countryCode","phoneNumber","email"],
            include: [{ 
                model: Models.UserProfile,
                attributes: ["id","userId","firstName","lastName","title","attachment","dob","gender","reason","about","vita","experience","video","rating"]
            }]
        });
        if(!userInfo) {
            return h.response({success:false,message:req.i18n.__('INVALID_USER_ID_PROVIDED'),responseData:{}}).code(400)
        }
        responseData["userInfo"] = userInfo;
        // soulwriting list api
        let soulWritingEndPoint = `${process.env.SOUL_WRITING_DOMAIN}/soul-writing/project/customer?userId=${userId}`
        let soulwritingResponse = Axios({ method:'get', url:soulWritingEndPoint, data:{}, headers: {} })
        // meeting history api
        let meetingEndPoint = `${process.env.EVENT_DOMAIN}/completed-meetings/customer?userId=${userId}`
        let meetingResponse = Axios({ method:'get', url:meetingEndPoint, data:{}, headers: {} })
        
        await Promise.all([soulwritingResponse, meetingResponse]).then((res) => {
            responseData["soulwritings"] = res[0]?.data?.responseData
            responseData["meetings"] = res[1]?.data?.responseData
        }).catch((err) => {
            throw new Error(err);
        })

        return h.response({success:true,message: req.i18n.__("SUCCESSFULLY_FATCHED"), responseData}).code(200)
    } catch (error) {
        console.log(error)
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
    }
}

exports.userAccessRequestByAdmin = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const userId = req.query.userId;
        const validUntil = Moment().add(60,"seconds").format();
        console.log(" =================== ", validUntil)
        console.log(" =================== ", validUntil)
        console.log(" =================== ", validUntil)
        console.log(" =================== ", validUntil)
        const key = generateUniqueString()
        const createRecord = await Models.AdminUserLogin.create({ userId, validUntil, key }, { transaction });

        await transaction.commit();
        return h.response({success:true,message: req.i18n.__("REQUEST_SUCCESSFULL"), responseData: createRecord}).code(200)
    } catch (error) {
        console.log(error)
        await transaction.rollback();
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
    }
}

const generateUniqueString = () => {
    const timestamp = new Date().getTime();
    const randomString = Math.random().toString(36).substring(2,18); // You can adjust the substring values for desired length
  
    return `${timestamp}-${randomString}`;
}

exports.userAccessGrantToAdmin = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const key = req.query.key;

        const keyRequestInfo = await Models.AdminUserLogin.findOne({ where: { key } });
        if(!keyRequestInfo) {
            await transaction.rollback();
            return h.response({success:false,message:req.i18n.__('INVALID_KEY_PROVIDED'),responseData:{}}).code(400);
        }

        let userInfo = await Models.User.findOne({
            where: {id: keyRequestInfo.userId},
            attributes:['id','password','email','phoneNumber','countryCode','countryCodeValue']
        });
        
        if(!userInfo) {
            await transaction.rollback();
            return h.response({success: false, message:req.i18n.__("INVALID_USER"),responseData: {}}).code(400);
        }

        let responseData = await loginAction(userInfo);
        await keyRequestInfo.update({status: 0}, { transaction });
        await transaction.commit();
        return h.response({success:true,message: req.i18n.__("REQUEST_SUCCESSFULL"), responseData: responseData}).code(200)
    } catch (error) {
        console.log(error)
        await transaction.rollback();
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
    }
}

const dateConversion = (excelDate,excelTime) => {
    console.log({excelDate,excelTime})
    // Convert Excel date to JavaScript Date object
    const baseDate = new Date(Date.UTC(1899, 11, 30)); // Use UTC to avoid time zone issues
    const convertedDate = new Date(baseDate.getTime() + excelDate * 24 * 60 * 60 * 1000);

    // Convert Excel time to hours, minutes, seconds, and milliseconds
    const totalSeconds = Math.round(excelTime * 24 * 3600);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    const milliseconds = Math.round((excelTime * 24 * 3600 - totalSeconds) * 1000);

    // Set the time part in the converted date
    convertedDate.setUTCHours(hours, minutes, seconds, milliseconds);

    console.log(convertedDate)
    // Create an object to represent the JSON
    return convertedDate.toISOString()

}

// const splitExcelDateTime = (excelDateTime) => {
//     const excelDate = Math.floor(excelDateTime);
//     const excelTime = excelDateTime - excelDate;
//     return { excelDate, excelTime };
// };

// const createImportedUser = async(payload) => {
//     const transaction = await Models.sequelize.transaction();
//     try {
//         const {firstName, lastName, email, createdAt} = payload;
//         let title = null;
//         const importStatus = {
//             notImported: 0,
//             newImport: 1,
//             importedButNotUsed: 2
//         }
//         let alreadyinuse = await Models.User.findOne({ where: { email: email}, transaction});
//         if (alreadyinuse) {
//             if(alreadyinuse.importStatus == importStatus.newImport) {
//                 await transaction.commit();
//                 return true;
//             }
//             await alreadyinuse.update({ importStatus: importStatus.importedButNotUsed, importedUserData: payload }, { transaction });
//             await transaction.commit();
//             return true;
//         }


//         let password = null;

//         let user = await Models.User.create(
//             { email, password, status: 1, isEmailVerify: 1, importStatus: importStatus.newImport, importedUserData: payload,
//                 createdAt: new Date(createdAt), userValidation: {} },
//             {include: [{ model: Models.UserValidation, as: "userValidation" }], transaction }
//         );

//         if(!user) {
//             await transaction.rollback();
//             return false;
//         }
            
//         await Models.UserProfile.create({
//             firstName, lastName, title,
//             userId:user.dataValues.id,
//             createdBy:user.dataValues.id,
//             lastUpdatedBy:user.dataValues.id,
//             createdAt: new Date(createdAt)
//         }, { transaction });
       
//         await user.setRoles([2], {transaction: transaction});
        
//         /// Updating All the services with user
//         let lang = await user.getLanguages({attributes:['id','code','countery','counteryCode','name','mobileCode'],joinTableAttributes:[]},{transaction})

//         let updatedLanguages=[]
//         for (const l of lang) {
//             updatedLanguages.push(l.dataValues)
//         }

//         if(updatedLanguages) updatedLanguages = JSON.stringify(updatedLanguages);

//         let userObjectData = await Models.User.findOne({
//             where: { id: user.id },
//             attributes: {
//                 exclude: ['password', 'createdAt', 'updatedAt']
//             },
//             include: [
//                 { 
//                     model: Models.UserProfile,  
//                     attributes: {
//                         exclude: ['password', 'createdAt', 'updatedAt']
//                     } 
//                 }
//             ],
//             transaction
//         });
//         if(userObjectData) userObjectData = JSON.stringify(userObjectData);


//         await sequelize.query(
//             'INSERT INTO `prdmng_users` (`user_id`,`first_name`,`last_name`,`title`,`profile_photo_url`,`profile_photo_id`,`gender`,`user_object`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `user_id`=VALUES(`user_id`),`title`=VALUES(`title`),`profile_photo_url`=VALUES(`profile_photo_url`),`profile_photo_id`=VALUES(`profile_photo_id`),`gender`=VALUES(`gender`),`user_object`=VALUES(`user_object`),`updated_at`=VALUES(`updated_at`);',
//             {
//                 replacements: [user.id, firstName, lastName, null, null, null, null, userObjectData, new Date(createdAt), new Date()],
//                 transaction
//             }
//         );
        
//         await sequelize.query(
//             'INSERT INTO `event_users` (`user_id`,`first_name`,`last_name`,`title`,`profile_photo_url`,`profile_photo_id`,`languages`,`meeting_price`,`reason`,`rating`,`schedule_time`,`re_schedule_time`,`gender`,`email`,`total_earning`,`soulwriting_product_id`,`meeting_product_id`,`zoom_account`,`user_object`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `user_id`=VALUES(`user_id`),`email`=VALUES(`email`),`title`=VALUES(`title`),`profile_photo_url`=VALUES(`profile_photo_url`),`profile_photo_id`=VALUES(`profile_photo_id`),`languages`=VALUES(`languages`),`meeting_price`=VALUES(`meeting_price`),`reason`=VALUES(`reason`),`rating`=VALUES(`rating`),`schedule_time`=VALUES(`schedule_time`),`re_schedule_time`=VALUES(`re_schedule_time`),`gender`=VALUES(`gender`),`user_object`=VALUES(`user_object`),`updated_at`=VALUES(`updated_at`);',
//             {
//                 replacements: [user.id, firstName, lastName, null, null, null, updatedLanguages, null, null, null, null, null,null, email,null,null,null,null, userObjectData, new Date(createdAt), new Date()],
//                 transaction
//             }
//         );
        
//         await sequelize.query(
//             'INSERT INTO `vidMgt_users` (`user_id`,`first_name`,`last_name`,`title`,`profile_photo_url`,`profile_photo_id`,`user_object`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `user_id`=VALUES(`user_id`),`title`=VALUES(`title`),`profile_photo_url`=VALUES(`profile_photo_url`),`profile_photo_id`=VALUES(`profile_photo_id`),`user_object`=VALUES(`user_object`),`updated_at`=VALUES(`updated_at`);',
//             {
//                 replacements: [user.id, firstName, lastName, null, null, null, userObjectData, new Date(createdAt), new Date()],
//                 transaction
//             }
//         );
        
//         await sequelize.query(
//             'INSERT INTO `soul_writing_users` (`user_id`,`first_name`,`last_name`,`title`,`profile_photo_url`,`profile_photo_id`,`gender`,`email`,`total_earning`,`soulwriting_product_id`,`meeting_product_id`,`user_object`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `user_id`=VALUES(`user_id`),`email`=VALUES(`email`),`title`=VALUES(`title`),`profile_photo_url`=VALUES(`profile_photo_url`),`profile_photo_id`=VALUES(`profile_photo_id`),`gender`=VALUES(`gender`),`user_object`=VALUES(`user_object`),`updated_at`=VALUES(`updated_at`);',
//             {
//                 replacements: [user.id, firstName, lastName, null, null, null,null,email,null,null,null, userObjectData, new Date(createdAt), new Date()],
//                 transaction
//             }
//         );

//         await transaction.commit();
//         return true;
//     } catch (error) {
//         console.log(error)
//         await transaction.rollback();
//         return false;
//     }
// }

// const importUsersFromSheet = async() => {
//     try {
//         const userJson1 = require("../exports/user1.json");
//         const userJson2 = require("../exports/user2.json");
//         const oAuthUsers = require("../exports/oAuthUsers.json");
//         const dgStoreProducts = require("../exports/dgStoreProduct.json");
//         let responseData1 = [];
//         let responseData2 = [];
//         let totalRecords = 0;
//         let totalValidRecords = 0;
//         let successRecords = 0;
//         let failedRecords = 0;
//         // return userJson2.length
//         // let count = 1;

//         let usersArray = [];


//         for(let item of userJson2) {
//             console.log(" ================= 11111111111111111111")
//             if(item.user_login) {
//                 totalRecords += 1;
//                 const emailSchema = Joi.string().email().required();
//                 const validateEmail = emailSchema.validate(item.user_login);
//                 if (validateEmail.error) continue;
//                 totalValidRecords += 1;

//                 // let email = "imported_user_"+count+"@yopmail.com"
//                 let email = item.user_login;
//                 console.log(item.user_registered, " item.user_registered ========== ")
//                 const dateAndTime = splitExcelDateTime(item.user_registered);
//                 console.log(dateAndTime, " ========== ")
//                 let createdAt = null
//                 if(!isNaN(dateAndTime.excelDate)) {
//                     createdAt = new Date(dateConversion(dateAndTime.excelDate, dateAndTime.excelTime)); 
//                 } else {
//                     createdAt = new Date(item.user_registered)
//                 }

//                 let firstName = null;
//                 let lastName = "";
//                 if(item.first_name) {
//                     firstName = item.first_name;
//                     lastName = item.last_name;
//                 }

//                 usersArray.push({ 
//                     firstName: firstName, 
//                     lastName: lastName, 
//                     email: email,
//                     createdAt 
//                 })

//                 // const response = await createImportedUser({ 
//                 //     firstName: item.first_name, 
//                 //     lastName: item.last_name, 
//                 //     email: email, createdAt
//                 // });
//                 //  if(response === true) { successRecords += 1 }
//                 // else{ failedRecords += 1 }
//                 // responseData2.push({ email: item.user_login, status: response });
//             }
//             // count = count + 1;
//         }
        
//         for(let item of oAuthUsers) {
//             console.log(" ================= 22222222222222222222222")
//             if(item.email) {
//                 totalRecords += 1;
//                 const emailSchema = Joi.string().email().required();
//                 const validateEmail = emailSchema.validate(item.email);
//                 if (validateEmail.error) continue;
//                 totalValidRecords += 1;

//                 // let email = "imported_user_"+count+"@yopmail.com"
//                 let email = item.email;

//                 let firstName = null;
//                 let lastName = "";
//                 if(item.first_name) {
//                     firstName = item.given_name;
//                     lastName = item.family_name;
//                 }

//                 usersArray.push({ 
//                     firstName: firstName, 
//                     lastName: lastName, 
//                     email: email , createdAt: item.created_at
//                 })

//                 // const response = await createImportedUser({
//                 //     firstName: firstName, 
//                 //     lastName: lastName, 
//                 //     email: email , createdAt: item.created_at
//                 // });
//                 // if(response === true) { successRecords += 1 }
//                 // else{ failedRecords += 1 }
//                 // responseData2.push({ email: item.email, status: response });
//             }
//             // count = count + 1;
//         }

//         for(let item of userJson1) {
//             if(item.user_login) {
//                 totalRecords += 1;
//                 const emailSchema = Joi.string().email().required();
//                 const validateEmail = emailSchema.validate(item.user_login);
//                 if (validateEmail.error) continue;
//                 totalValidRecords += 1;

//                 // let email = "imported_user_"+count+"@yopmail.com"
//                 let email = item.user_login;

//                 const dateAndTime = splitExcelDateTime(item.user_registered)

//                 let createdAt = null
//                 if(!isNaN(dateAndTime.excelDate)) {
//                     createdAt = new Date(dateConversion(dateAndTime.excelDate, dateAndTime.excelTime)); 
//                 } else {
//                     createdAt = new Date(item.user_registered)
//                 } 

//                 let firstName = null;
//                 let lastName = "";
//                 if(item.first_name) {
//                     firstName = item.first_name;
//                     lastName = item.last_name;
//                 }

//                 usersArray.push({ 
//                     firstName: firstName, 
//                     lastName: lastName, 
//                     email: email, createdAt
//                 })

//                 // const response = await createImportedUser({ 
//                 //     firstName: firstName, 
//                 //     lastName: lastName, 
//                 //     email: email, createdAt
//                 // });
//                 // if(response === true) { successRecords += 1 }
//                 // else{ failedRecords += 1 }
//                 // responseData1.push({ email: item.user_login, status: response });
//             }
//             // count = count + 1;
//         }


//         for(let item of usersArray) {
//             let firstName = item.email;
//             if(item.firstName) {
//                 firstName = item.firstName;
//                 lastName = item.lastName;
//             } else {
//                 const filteredRec1 = dgStoreProducts.find((data) => data["E-Mail"] == item.email);
//                 if(filteredRec1 && filteredRec1["fn"]) {
//                     firstName = filteredRec1["fn"];
//                     lastName = filteredRec1["ln"];
//                 } else {
//                     const filteredRec2 = userJson1.find((data) => data["user_login"] == item.email) 
//                     if(filteredRec2 && filteredRec2["first_name"]) {
//                         firstName = filteredRec2["first_name"];
//                         lastName = filteredRec2["last_name"];
//                     } else {
//                         const filteredRec3 = userJson2.find((data) => data["user_login"] == item.email) 
//                         if(filteredRec3 && filteredRec3["first_name"]) {
//                             firstName = filteredRec3["first_name"];
//                             lastName = filteredRec3["last_name"];
//                         }
//                     }
//                 }
//             }

//             if(firstName) {
//                 firstName = firstName.toString().replaceAll("Ã¶","ö").replaceAll("Ã¼","ü").replaceAll("ÃŸ","ß").replaceAll("Ã¤","ä")
//             }
//             if(lastName) {
//                 lastName = lastName.toString().replaceAll("Ã¶","ö").replaceAll("Ã¼","ü").replaceAll("ÃŸ","ß").replaceAll("Ã¤","ä")
//             }

//             await createImportedUser({ 
//                 firstName: firstName, 
//                 lastName: lastName, 
//                 email: item.email, 
//                 createdAt: item.createdAt
//             });
//         }


//         //console.log(totalRecords,totalValidRecords,successRecords,failedRecords)
//         return true;

//     } catch (error) {
//         console.log(error)
//         return false;
//     }
// }

// const deleteImportedUser = async() => {
//     try {
//         const usersList = await Models.User.findAll({ where: { importStatus: 1 } });

//         for(let item of usersList) {
//             const transaction = await Models.sequelize.transaction();
//             try {
//                 await sequelize.query(`DELETE FROM userobd_users WHERE id = ${item.id}`, {transaction });
//                 await sequelize.query(`DELETE FROM prdmng_users WHERE user_id = ${item.id}`, {transaction });
//                 await sequelize.query(`DELETE FROM event_users WHERE user_id = ${item.id}`, {transaction });
//                 await sequelize.query(`DELETE FROM soul_writing_users WHERE user_id = ${item.id}`, {transaction });
//                 await sequelize.query(`DELETE FROM vidmgt_purchased_topics WHERE import_status = 1 and user_id = ${item.id}`, {transaction });
//                 await sequelize.query(`DELETE FROM vidMgt_users WHERE user_id = ${item.id}`, {transaction });
//                 await transaction.commit();
//             } catch (error) {
//                 await transaction.rollback();
//             }
           
//         }

//         await Models.User.update({ importStatus: 0, importedUserData: null, wordpressUserId: null }, { where: { importStatus: 2 } })

//         return true
//     } catch (error) {
//         console.log(error)
//         return false
//     }
// }

// const importKubyProductsFromSheet = async() => {
//     const transaction = await Models.sequelize.transaction();
//     try {
//         const kubyProducts = require("../exports/kubyProduct.json");
//         const dgStoreProducts = require("../exports/dgStoreProduct.json");

//         const newArray = [];

//         for(let item_1 of kubyProducts) {
//             for(let item_2 of dgStoreProducts) {
//                 if(item_1["digistore product id"] === item_2["Prd-ID"]) {
//                     const newObj = {
//                         "digistoreId": item_1["digistore product id"],
//                         "videoId": item_1["self practice id"],
//                         "email": item_2["E-Mail"],
//                         "date": item_2["Date"],
//                         "time": item_2["time"],
//                         "fullObject": item_2
//                     };
//                     newArray.push(newObj);
//                 }

//             }
//         }

//         // return newArray
//         let totalRecords = 0;
//         let passedRecords = 0;
//         let validUsers = 0;
//         let validVideos = 0;
//         for(let item of newArray) {
//             totalRecords++;
//             // find user id
//             const userInfo = await Models.User.findOne({ where: { email: item.email } });
//             if(!userInfo) continue;
//             validUsers++;
//             // find video id
//             const [videoInfo, videoMeta] = await sequelize.query(`SELECT id, is_subscription_based FROM vidmgt_videos where id = ${item.videoId}`);
//             if(!videoInfo?.[0]?.id) continue;
//             validVideos++;
//             // check if user already purchased the product
//             const [purchaseInfo, purchaseMeta] = await sequelize.query(`SELECT id FROM vidmgt_purchased_topics where video_id = ${item.videoId} and user_id = ${userInfo.id}`);

//             if(purchaseInfo?.[0]?.id) continue;

//             const purchasedDate = new Date(dateConversion(item.date, item.time));
//             passedRecords++

//             const isSubscriptionBased = videoInfo[0].is_subscription_based;

//             let endDate = null;
//             if(isSubscriptionBased) {
//                 endDate = Moment(new Date()).add(30, "days");
//                 endDate = new Date(endDate)
//             }

//             await sequelize.query(
//                 'INSERT INTO `vidmgt_purchased_topics` (`user_id`,`video_id`,`purchase_date`,`starting_date`,`has_passed`,`active_subscription`,`status`,`end_date`,`is_shop_product`,`created_at`,`updated_at`,`import_status`,`imported_object`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `user_id`=VALUES(`user_id`),`video_id`=VALUES(`video_id`),`purchase_date`=VALUES(`purchase_date`),`starting_date`=VALUES(`starting_date`),`has_passed`=VALUES(`has_passed`),`active_subscription`=VALUES(`active_subscription`),`status`=VALUES(`status`),`end_date`=VALUES(`end_date`),`is_shop_product`=VALUES(`is_shop_product`),`created_at`=VALUES(`created_at`),`updated_at`=VALUES(`updated_at`),`import_status`=VALUES(`import_status`),`imported_object`=VALUES(`imported_object`)',
//                 {
//                     replacements: [userInfo.id, item.videoId, purchasedDate, purchasedDate, 0, 1, 1, endDate, 0, purchasedDate, new Date(), 1, JSON.stringify(item.fullObject)],
//                     transaction
//                 }
//             );
//         }

//         console.log({ totalRecords, passedRecords, validUsers, validVideos })

//         await transaction.commit();
//         return true;
//     } catch (error) {
//         await transaction.rollback();
//         return false;
//     }
// }



// const importaddonsFromSheet = async() => {
//     const transaction = await Models.sequelize.transaction();
//     try {
//         const kubyProducts = require("../exports/kubyProduct.json");
//         const dgStoreProducts = require("../exports/addons.json");

//         const newArray = [];
        

//         for(let item_1 of kubyProducts) {
//             for(let item_2 of dgStoreProducts) {
//                 if(item_1["digistore product id"] === item_2["Prd-ID"]) {
//                     const newObj = {
//                         "digistoreId": item_1["digistore product id"],
//                         "videoId": item_1["self practice id"],
//                         "email": item_2["E-Mail"],
//                         "date": item_2["Date"],
//                         "time": item_2["time"],
//                         "fullObject": item_2
//                     };
//                     newArray.push(newObj);
//                 }

//             }
//         }

//         // return newArray
//         let totalRecords = 0;
//         let passedRecords = 0;
//         let validUsers = 0;
//         let validVideos = 0;
//         for(let item of newArray) {
//             totalRecords++;
//             // find user id
//             const userInfo = await Models.User.findOne({ where: { email: item.email } });
//             if(!userInfo) continue;
//             validUsers++;
//             // find video id
//             const [videoInfo, videoMeta] = await sequelize.query(`SELECT id, is_subscription_based FROM vidmgt_videos where id = ${item.videoId}`);
//             if(!videoInfo?.[0]?.id) continue;
//             validVideos++;
//             // check if user already purchased the product
//             const [purchaseInfo, purchaseMeta] = await sequelize.query(`SELECT id FROM vidmgt_purchased_topics where video_id = ${item.videoId} and user_id = ${userInfo.id}`);

//             if(purchaseInfo?.[0]?.id) continue;

//             const purchasedDate = new Date(dateConversion(item.date, item.time));
//             passedRecords++

//             const isSubscriptionBased = videoInfo[0].is_subscription_based;

//             let endDate = null;
//             if(isSubscriptionBased) {
//                 endDate = Moment(new Date()).add(30, "days");
//                 endDate = new Date(endDate)
//             }

//             await sequelize.query(
//                 'INSERT INTO `vidmgt_purchased_topics` (`user_id`,`video_id`,`purchase_date`,`starting_date`,`has_passed`,`active_subscription`,`status`,`end_date`,`is_shop_product`,`created_at`,`updated_at`,`import_status`,`imported_object`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `user_id`=VALUES(`user_id`),`video_id`=VALUES(`video_id`),`purchase_date`=VALUES(`purchase_date`),`starting_date`=VALUES(`starting_date`),`has_passed`=VALUES(`has_passed`),`active_subscription`=VALUES(`active_subscription`),`status`=VALUES(`status`),`end_date`=VALUES(`end_date`),`is_shop_product`=VALUES(`is_shop_product`),`created_at`=VALUES(`created_at`),`updated_at`=VALUES(`updated_at`),`import_status`=VALUES(`import_status`),`imported_object`=VALUES(`imported_object`)',
//                 {
//                     replacements: [userInfo.id, item.videoId, purchasedDate, purchasedDate, 0, 1, 1, endDate, 0, purchasedDate, new Date(), 1, JSON.stringify(item.fullObject)],
//                     transaction
//                 }
//             );
//         }

//         console.log({ totalRecords, passedRecords, validUsers, validVideos })

//         await transaction.commit();
//         return true;
//     } catch (error) {
//         await transaction.rollback();
//         return false;
//     }
// }

// const userAndProductFromSheet = async() => {
//     const transaction = await Models.sequelize.transaction();
//     try {
//         const kubyProducts = require("../exports/kubyProduct.json");
//         const dgStoreProducts = require("../exports/latestProducts.json");


//         for(let item of dgStoreProducts) {
//             if(item["E-Mail"]) {
//                 const emailSchema = Joi.string().email().required();
//                 const validateEmail = emailSchema.validate(item["E-Mail"]);
//                 if (validateEmail.error) continue;

//                 const purchasedDate = new Date(dateConversion(item.Date, item.time));
//                 await createImportedUser({ firstName: item.fn, lastName: item.ln, email: item["E-Mail"], createdAt: purchasedDate });
//             }
//         }




//         const newArray = [];
//         for(let item_1 of kubyProducts) {
//             for(let item_2 of dgStoreProducts) {
//                 if(item_1["digistore product id"] === item_2["Prd-ID"]) {
//                     const newObj = {
//                         "digistoreId": item_1["digistore product id"],
//                         "videoId": item_1["self practice id"],
//                         "email": item_2["E-Mail"],
//                         "date": item_2["Date"],
//                         "time": item_2["time"],
//                         "fullObject": item_2
//                     };
//                     newArray.push(newObj);
//                 }

//             }
//         }

//         // return newArray
//         let totalRecords = 0;
//         let passedRecords = 0;
//         let validUsers = 0;
//         let validVideos = 0;
//         for(let item of newArray) {
//             totalRecords++;
//             // find user id
//             const userInfo = await Models.User.findOne({ where: { email: item.email } });
//             if(!userInfo) continue;
//             validUsers++;
//             // find video id
//             const [videoInfo, videoMeta] = await sequelize.query(`SELECT id, is_subscription_based FROM vidmgt_videos where id = ${item.videoId}`);
//             if(!videoInfo?.[0]?.id) continue;
//             validVideos++;
//             // check if user already purchased the product
//             const [purchaseInfo, purchaseMeta] = await sequelize.query(`SELECT id FROM vidmgt_purchased_topics where video_id = ${item.videoId} and user_id = ${userInfo.id}`);

//             if(purchaseInfo?.[0]?.id) continue;

//             const purchasedDate = new Date(dateConversion(item.date, item.time));
//             passedRecords++

//             const isSubscriptionBased = videoInfo[0].is_subscription_based;

//             let endDate = null;
//             if(isSubscriptionBased) {
//                 endDate = Moment(new Date()).add(30, "days");
//                 endDate = new Date(endDate)
//             }

//             await sequelize.query(
//                 'INSERT INTO `vidmgt_purchased_topics` (`user_id`,`video_id`,`purchase_date`,`starting_date`,`has_passed`,`active_subscription`,`status`,`end_date`,`is_shop_product`,`created_at`,`updated_at`,`import_status`,`imported_object`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `user_id`=VALUES(`user_id`),`video_id`=VALUES(`video_id`),`purchase_date`=VALUES(`purchase_date`),`starting_date`=VALUES(`starting_date`),`has_passed`=VALUES(`has_passed`),`active_subscription`=VALUES(`active_subscription`),`status`=VALUES(`status`),`end_date`=VALUES(`end_date`),`is_shop_product`=VALUES(`is_shop_product`),`created_at`=VALUES(`created_at`),`updated_at`=VALUES(`updated_at`),`import_status`=VALUES(`import_status`),`imported_object`=VALUES(`imported_object`)',
//                 {
//                     replacements: [userInfo.id, item.videoId, purchasedDate, purchasedDate, 0, 1, 1, endDate, 0, purchasedDate, new Date(), 1, JSON.stringify(item.fullObject)],
//                     transaction
//                 }
//             );
//         }

//         console.log({ totalRecords, passedRecords, validUsers, validVideos })

//         await transaction.commit();
//         return true;
//     } catch (error) {
//         await transaction.rollback();
//         return false;
//     }
// }




// const deleteImportedKubyProducts = async() => {
//     try {
//         await sequelize.query(`DELETE FROM vidmgt_purchased_topics WHERE import_status = 1`);
//         return true
//     } catch (error) {
//         console.log(error)
//         return false
//     }
// }

// const importSoulwriting = async() => {
//     try {
//         const mongoUsers = require("../exports/mongoUsers.json");
//         const soulwritingProjects = require("../exports/soulwritingProjects.json");
//         const soulwritingLines = require("../exports/soulwritingLines.json");

//         const newArray = [];
//         for(let project of soulwritingProjects) {
//             let email = null;
//             let lines = null;
//             let breakFlag = false;
//             let companionEmail = null;
//             for(let user of mongoUsers) {
//                 if(project.sender == user._id) {
//                     email = user.profile.email;
//                     if(breakFlag === true) {
//                         break;
//                     }
//                     breakFlag = true;
//                 }
//                 if(project.recipient == user._id) {
//                     companionEmail = user.profile.email;
//                     if(breakFlag === true) {
//                         break;
//                     }
//                     breakFlag = true;
//                 }
//             }

//             let contentLines = [];

//             for(let line of soulwritingLines) {
//                 if(project._id == line.soulLetter) {
//                     contentLines.push(line)
//                     // lines = line;
//                     // break;
//                 }
//             }

//             if(email) {
//                 newArray.push({
//                     companionEmail: companionEmail ? companionEmail : null,
//                     email: email,
//                     projectDetails: project,
//                     projectLines: contentLines,
//                     createdAt: new Date(project.createTimeStamp.$date)
//                 })
//             }
//         }

//         let totalCount = 0;
//         let insertedCount = 0;


//         for(let item of newArray) {
//             totalCount += 1;
//             const userInfo = await Models.User.findOne({ where: { email: item.email } });
//             if(!userInfo) continue;

//             let companionId = null;
//             if(item.companionEmail) {
//                 const companionInfo = await Models.User.findOne({ where: { email: item.companionEmail } });
//                 if(companionInfo) {
//                     companionId = companionInfo.id;
//                 }
//             }

//             await sequelize.query(
//                 'INSERT INTO `soul_writing_history` (`user_id`,`companion_id`,`project_details`,`content_details`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `user_id`=VALUES(`user_id`),`companion_id`=VALUES(`companion_id`),`project_details`=VALUES(`project_details`),`content_details`=VALUES(`content_details`),`created_at`=VALUES(`created_at`),`updated_at`=VALUES(`updated_at`);',
//                 {
//                     replacements: [userInfo.id, companionId, JSON.stringify(item.projectDetails), JSON.stringify(item.projectLines), new Date(item.createdAt), new Date()],
//                     // transaction
//                 }
//             );
//             insertedCount += 1;
//         }

//         console.log({ totalCount, insertedCount })
//         return true
//     } catch (error) {
//         console.log(error)
//         return false
//     }
// }

// const importMeetings = async() => {
//     try {
//         const mongoUsers = require("../exports/mongoUsers.json");
//         const meetings = require("../exports/oneToOneMeeting.json");

//         const newArray = [];
//         for(let item of meetings) {
//             let email = null;
//             let companionEmail = null;
//             let breakFlag = false;
//             for(let user of mongoUsers) {
//                 if(item.attendees && item.attendees.length > 0 && item.attendees[0] == user._id) {
//                     email = user.profile.email;
//                     if(breakFlag === true) {
//                         break;
//                     }
//                     breakFlag = true;
//                 }
//                 if(item.host == user._id) {
//                     companionEmail = user.profile.email;
//                     if(breakFlag === true) {
//                         break;
//                     }
//                     breakFlag = true;
//                 }
//             }

//             if(email) {
//                 newArray.push({
//                     email: email,
//                     companionEmail: companionEmail ? companionEmail : null,
//                     meetingDetails: item,
//                     createdAt: new Date(item.createTimeStamp.$date)
//                 })
//             }
//         }

//         let totalCount = 0;
//         let insertedCount = 0;


//         for(let item of newArray) {
//             totalCount += 1;
//             const userInfo = await Models.User.findOne({ where: { email: item.email } });
//             if(!userInfo) continue;

//             let companionId = null;
//             if(item.companionEmail) {
//                 const companionInfo = await Models.User.findOne({ where: { email: item.companionEmail } });
//                 if(companionInfo) {
//                     companionId = companionInfo.id;
//                 }
//             }

//             console.log(item.createdAt, " =============== ")

//             await sequelize.query(
//                 'INSERT INTO `event_meeting_history` (`user_id`,`companion_id`,`meeting_details`,`status`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `user_id`=VALUES(`user_id`),`companion_id`=VALUES(`companion_id`),`meeting_details`=VALUES(`meeting_details`),`status`=VALUES(`status`),`created_at`=VALUES(`created_at`),`updated_at`=VALUES(`updated_at`);',
//                 {
//                     replacements: [userInfo.id, companionId, JSON.stringify(item.meetingDetails), 1, item.createdAt == "Invalid Date" ? new Date() : new Date(item.createdAt), new Date()]
//                 }
//             );
//             insertedCount += 1;
//         }

//         console.log({ totalCount, insertedCount })
//         return true
//     } catch (error) {
//         console.log(error)
//         return false
//     }
// }

// const updateImportedUserEmails = async() => {
//     const transaction = await Models.sequelize.transaction();
//     try {
//         const importedUsers = await Models.User.findAll({ where: { importStatus: 1 } });
//         let count = 1;
//         for(let item of importedUsers) {
//             // await UPDATE table_name
//             // SET column1 = value1, column2 = value2, ...
//             // WHERE condition;
//             let email = "imported_user_"+count+"@yopmail.com"
//             await sequelize.query(`UPDATE userobd_users SET email = '${email}' WHERE id = ${item.id}`, {transaction });
//             await sequelize.query(`UPDATE event_users SET email = '${email}' WHERE user_id = ${item.id}`, {transaction });
//             await sequelize.query(`UPDATE soul_writing_users SET email = '${email}' WHERE user_id = ${item.id}`, {transaction });

//             count++;
//         }
//         await transaction.commit();
//         return true;
//     } catch (error) {
//         console.log(error)
//         await transaction.rollback();
//         return false;
//     }
// }

// const exportEnglishProduct = async() => {
//     try {
//         const productList = require("../exports/englishProduct.json");

//         for(let item of productList) {
//             if(item.email) {
//                 const emailSchema = Joi.string().email().required();
//                 const validateEmail = emailSchema.validate(item.email);
//                 if (validateEmail.error) continue;
//                 await createImportedUser({ firstName: item.fn, lastName: item.ln, email: item.email, createdAt: new Date() });
//             }
//         }


//         for(let item of productList) {
//             // find user id
//             const userInfo = await Models.User.findOne({ where: { email: item.email } });
//             if(!userInfo) continue;
//             // find video id
//             const [videoInfo, videoMeta] = await sequelize.query(`SELECT id FROM vidmgt_videos where id = ${item["KUBYportal-Self-practice-ID"]}`);
//             if(!videoInfo?.[0]?.id) continue;
//             // check if user already purchased the product
//             const [purchaseInfo, purchaseMeta] = await sequelize.query(`SELECT id FROM vidmgt_purchased_topics where video_id = ${item["KUBYportal-Self-practice-ID"]} and user_id = ${userInfo.id}`);

//             if(purchaseInfo?.[0]?.id) continue;

//             // const purchasedDate = new Date(dateConversion(item.date, item.time));
//             await sequelize.query(
//                 'INSERT INTO `vidmgt_purchased_topics` (`user_id`,`video_id`,`purchase_date`,`starting_date`,`has_passed`,`active_subscription`,`status`,`end_date`,`is_shop_product`,`created_at`,`updated_at`,`import_status`,`imported_object`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `user_id`=VALUES(`user_id`),`video_id`=VALUES(`video_id`),`purchase_date`=VALUES(`purchase_date`),`starting_date`=VALUES(`starting_date`),`has_passed`=VALUES(`has_passed`),`active_subscription`=VALUES(`active_subscription`),`status`=VALUES(`status`),`end_date`=VALUES(`end_date`),`is_shop_product`=VALUES(`is_shop_product`),`created_at`=VALUES(`created_at`),`updated_at`=VALUES(`updated_at`),`import_status`=VALUES(`import_status`),`imported_object`=VALUES(`imported_object`)',
//                 {
//                     replacements: [userInfo.id, item["KUBYportal-Self-practice-ID"], new Date(), new Date(), 0, 1, 1, null, 0, new Date(), new Date(), 1, JSON.stringify(item)]
//                 }
//             );
//         }

//         return true;
//     } catch (error) {
//         console.log(error)
//         return false;
//     }
// }

exports.enableSoulWriting = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try{
        //enable soul writing for the user
        let {data} = req.payload;
        let userId    =  req.auth.isAuthenticated?req.auth.credentials.userData.User.id:null;
        console.log(userId);
        let decryprData = await decodeData(data);
        let language=req.headers.language;
        if(!userId){
            const data = {
				email: decryprData?.buyer_email, 
                firstName: decryprData.buyer_first_name,
				lastName: decryprData.buyer_last_name, 
                existingUserEmailSend: 1
			}
			let responseData = await Axios({
				method: "post", 
                headers: {language: language}, 
                data: data,
				url: `${process.env.USER_ONBOARDING_DOMAIN}/create-dg-user`
			});
			userId = responseData.data.userId;
        }
        let user=await Models.User.findOne({where:{id:userId}});
        if(user){
            await user.update({soulWritingStatus:true,transactionDetails:decryprData});
            await transaction.commit();
            let soulWritingProductId = await Models.Setting.findOne({where:{key:'SOULWRITING_PRODUCT_ID'}});
            let soulWritingProductPrice = await Models.Setting.findOne({where:{key:'SOULWRITING_PRODUCT_PRICE'}});
            return h.response({success:true,message:req.i18n.__('SOUL_WRITING_ENABLED_SUCCESSFULLY'),responseData:{soulWritingStatus:true,soulWritingProductId:soulWritingProductId,soulWritingProductPrice:soulWritingProductPrice}}).code(200)
        }else{
            await transaction.rollback();
            return h.response({success:false,message:req.i18n.__('USER_NOT_FOUND'),responseData:{}}).code(400) 
        }
    }catch(err){
        console.log(err)
        await transaction.rollback();
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
    }
}

exports.getSoulWritingStatus = async(req, h) => {
    try{
        const authUserId    =   req.auth.credentials.userData.User.id
        let userId=req.query.userId?req.query.userId:authUserId;

        let user = await Models.User.findOne({where:{id:userId}});
        if(user){
            let soulWritingProductId = await Models.Setting.findOne({where:{key:'SOULWRITING_PRODUCT_ID'}});
            let soulWritingProductPrice = await Models.Setting.findOne({where:{key:'SOULWRITING_PRODUCT_PRICE'}});
            if(!user.soulWritingStatus) {
                const [purchasedTopic, metaE] = await sequelize.query(`SELECT * FROM kuby_production.vidMgt_videos v Inner JOIN  vidMgt_purchased_topics vt on vt.video_id = v.id where v.topic_type = "Seminars" and vt.user_id = ${userId} and vt.status = 1 and vt.deleted_at is null and v.deleted_at is null;`);
                if(purchasedTopic && purchasedTopic.length > 0) {
                    user.soulWritingStatus = 1;
                }
            }
            return h.response({success:true,message:req.i18n.__('STATUS_FETCHED_SUCCESSFULLY'),responseData:{soulWritingStatus:user.soulWritingStatus,soulWritingProductId:soulWritingProductId,soulWritingProductPrice:soulWritingProductPrice}}).code(200)
        }else{
            return h.response({success:false,message:req.i18n.__('USER_NOT_FOUND'),responseData:{}}).code(400)
        }
    }catch(err){
        console.log(err)
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
    }
}


exports.exportData = async(req, h) => {
    // const userImport = await importUsersFromSheet();
    // const productsImport = await importKubyProductsFromSheet();
    // const soulwritingImport = await importSoulwriting();
    // const meetingImport = await importMeetings();

    return h.response({success:true,message: req.i18n.__("REQUEST_SUCCESSFULL"), responseData: {
        //userImport, productsImport,soulwritingImport,meetingImport
    }}).code(200)
}

// setTimeout(async() => {
//     // const userImport = await importUsersFromSheet();
//     // const productsImport = await importKubyProductsFromSheet();
//     // const soulwritingImport = await importSoulwriting();
//     // const meetingImport = await importMeetings();
//     // const englishProducts = await exportEnglishProduct();
//     // const upadteEmails = await updateImportedUserEmails();
//     // const addonsImport = await importaddonsFromSheet();
//     // console.log(userImport, " ================ userImport")
//     // console.log(productsImport, " ================ productsImport")
//     // console.log(soulwritingImport, " ================ soulwritingImport")
//     // console.log(meetingImport, " ================ meetingImport")
//     // console.log(englishProducts, " ================ meetingImport")
//     // console.log(upadteEmails, " ================ meetingImport")
//     // console.log(addonsImport, " ================ addonsImport")
// }, 10000)


// setTimeout(() => {
//     importUsersFromSheet().then(res => console.log(res)).catch(err => console.log(err))
// }, 10000);
// setTimeout(() => {
//     importKubyProductsFromSheet().then(res => {console.log(res), console.log(res.length)}).catch(err => console.log(err))
// }, 10000);

// setTimeout(() => {
//     deleteImportedUser().then(res => console.log(res)).catch(err => console.log(err))
// }, 10000);

// setTimeout(() => {
//     importSoulwriting().then(res => console.log(res)).catch(err => console.log(err))
// }, 10000);

// setTimeout(() => {
//     userAndProductFromSheet().then(res => console.log(res)).catch(err => console.log(err))
// }, 10000);