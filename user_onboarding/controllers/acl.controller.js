const MD5=require('md5')
const encryptString=(str)=>{
    if(str==null) return null;
    return MD5(str)
}

const Webhook   = require("../services/webhook.js");

  exports.getAllPermissions = async(req,h)=>{
    try{
        const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT;
        const offset = (req.query.pageNumber - 1) * limit;
        const orderByValue = req.query.orderByValue;
        const orderByParameter = req.query.orderByParameter;
        let where={
            isSystemGenerated:0
        }
        if(req.query.searchText!==null)
        {
            where = {
                ... where,
                permissionCode:{
                    [Op.like]:
                        `%${
                            req.query.searchText.trim()
                        }%`
                }
                }
        }
        let _includesOption=[]
        let options = {
            where,
            order       : [[orderByParameter, orderByValue]],
            include     : _includesOption
        };
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                limit,
                offset
            };
        const permissions   = await Models.Permission.findAndCountAll(options);
        const totalPages    = await Common.getTotalPages(permissions.count, limit);
        const responseData  = {
            totalPages,
            perPage: limit,
            permissions: permissions.rows,
            totalRecords: permissions.count
        };
        return h.response({success:true,message:req.i18n.__("SUCESSFULLY_FOUND"),responseData:responseData}).code(200);
    }catch(err){
        console.log('Something Error comes---',err)
        return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
    }
  }
  
  exports.createRole=async(req,h)=>{
    const transaction = await Models.sequelize.transaction();
    try{
        let name=req.payload.name;
        let permissions = req.payload.permissions;
        let doExists = await Models.Role.findOne({where:{name:name}});
        if(!doExists){
            let newRole = await Models.Role.create({name:name},{transaction:transaction});
            await newRole.setPermissions(permissions,{transaction:transaction});
            await transaction.commit();
            return h.response({success:true,responseData:newRole,message: req.i18n.__("ROLE_CREATED_SUCCESSFULLY")}).code(200);
        }else{
            return h.response({success:false,message:req.i18n.__("ROLE_ALREADY_EXISTS"),responseData:{}}).code(400);
        }
    }catch(err){
        await transaction.rollback();
        console.log('Something Error comes---',err)
        return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
    }
  }
  
  exports.updateRole=async(req,h)=>{
    const transaction = await Models.sequelize.transaction();
    try{
        let name=req.payload.name;
        let permissions = req.payload.permissions;
        let roleId = req.payload.roleId;
        let doExists = await Models.Role.findOne({where:{id:roleId}});
        if(doExists){
            doExists.update({name:name},{where:{id:roleId},transaction:transaction});
            await doExists.setPermissions(permissions,{transaction:transaction});
            await transaction.commit();
            return h.response({success:true,responseData:doExists,message: req.i18n.__("ROLE_UPDATED_SUCCESSFULLY")}).code(200);
        }else{
            await transaction.rollback();
            return h.response({success:false,responseData:{},message: req.i18n.__("ROLE_NOT_EXISTS")}).code(400);
        }
    }catch(err){
        await transaction.rollback();
        console.log('Something Error comes---',err)
        return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
    }
  }
  
  exports.deleteRole=async(req,h)=>{
    const transaction = await Models.sequelize.transaction();
    try{
        let roleId = req.query.roleId;
        let doExists = await Models.Role.findOne({where:{id:roleId}});
        if(doExists){
            doExists.destroy();
            await transaction.commit();
            return h.response({responseData:doExists,message: req.i18n.__("ROLE_DELETED_SUCCESSFULLY"),success:true}).code(200);
        }else{
            return h.response({success:false,responseData:{},message: req.i18n.__("ROLE_NOT_EXISTS")}).code(400);
        }
    }catch(err){
        await transaction.rollback();
        console.log('Something Error comes---',err)
        return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
    }
  }
  
  exports.listRoles=async(req,h)=>{
    try{
        const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT;
        const offset = (req.query.pageNumber - 1) * limit;
        const orderByValue = req.query.orderByValue;
        const orderByParameter = req.query.orderByParameter;
        if(req.query.allRole!==null)
        {
            let role=await Models.Role.findAll({where:{
                isSystemGenerated:0
            }})
            const responseData  = {
                totalPages:0,
                perPage: 0,
                roles: role,
                totalRecords: role.count
            };
            return h.response({success:true,message:req.i18n.__("SUCESSFULLY_FOUND"),responseData:responseData}).code(200);
        }
            let where={
                isSystemGenerated:0
            }
        if(req.query.name!==null)
        {
            where = {
                ... where,
                name:{
                    [Op.like]:
                        `%${
                            req.query.name.trim()
                        }%`
                }
                }
        }
        let _includesOption=[]
        let options = {
            where,
            order       : [[orderByParameter, orderByValue]],
            include     : _includesOption
        };
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                limit,
                offset
            };
        const roles   = await Models.Role.findAndCountAll(options);
        const totalPages    = await Common.getTotalPages(roles.count, limit);
        const responseData  = {
            totalPages,
            perPage: limit,
            roles: roles.rows,
            totalRecords: roles.count
        };
        return h.response({success:true,message:req.i18n.__("SUCESSFULLY_FOUND"),responseData:responseData}).code(200);
    }
    catch(err){
        console.log('Something Error comes---',err)
        return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
    }
  }
  
  exports.getRole = async(req,h)=>{
    try{
        let roleId = req.query.roleId;
        let Role = await Models.Role.findOne(
            {
                where:{id:roleId},
                include:[
                    {
                        model:Models.Permission
                    }
                ]
            });
            let responseData={
                role:Role
            }
        return h.response({success:false,responseData:responseData,message: req.i18n.__("REQUEST_PROCESSED_SUCCESSFULLY")}).code(200);
    }
    catch(err){
        console.log('Something Error comes---',err)
        return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
    }
  }
  
  exports.getUserById=async(req,h)=>{
    try{
        let {id}=req.query;
        
        let user=await Models.User.findOne({
            where:{id},
            include:[
                {model:Models.UserProfile},
                {
                    model:Models.Role,
                    
                    through: {},
                    attributes:['id','name'],
                    joinTableAttributes:[]
                }
            ]
        })
        if(!user)
        {
        return h.response({success: false, message: req.i18n.__("USER_NOT_FOUND"), responseData: {}}).code(400);
        }
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: user}).code(200);

    }
    catch(error){
        console.log('Something Error comes---',err)
        return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
    }
  }
//*************************************Users Controller ********************************************** */


const sendEmail = async(data,code,language = "en")=>{
    try{
        // let data = { replacements, priority:'high', code, recipients }

        let replacements = {}
        let recipients = []
        if(code === "USER_BY_ADMIN") {
            if(data?.userId) {
                let url = null;
                if(data.role === "customer") {
                    url = `${process.env.COSTUMER_BASE_DOMAIN}signin`
                } else if(data.role === "professional") {
                    url = `${process.env.COMPANION_BASE_DOMAIN}signin`
                } else if(data.role === "admin") {
                    url = process.env.ADMIN_BASE_DOMAIN
                }

                const userInfo = await Models.User.findOne({ where: { id: data?.userId }, raw: true });
                const userProfileInfo = await Models.UserProfile.findOne({ where: { userId: data?.userId }, raw: true });
                replacements = {...replacements, link: url, email:data?.email, name: userProfileInfo?.firstName,password: data?.password}
                recipients.push(userInfo?.email)
            }
        } else {
            return
        }


        console.log(replacements, recipients, " ==================== replacements, recipients")


        const requestObj = {
            url : `${process.env.EMAIL_SERVICE}/email/send`,
            method: "post",
            data: { replacements, priority:'high', code, recipients },
            headers: {language}
        }
        
        // axoisObj  =   createAxoisObj(URL.SENDEMAIL,'post',data)
        //await createRequest(axoisObj,transaction)
        let res=await Axios(requestObj).then(async(res)=>{
            console.log('Sucessfully Send Email.......... ')
            console.log(res.data)
            return res.data
        })
        .catch(async (error) => {
            console.log('hi catch in Email .........',error)
            return {}
        });
        return res
    } catch(error) {
        console.log('Error in Sending email',error)
    }
}
// create Users by admin
exports.createUsers=async(req,h)=>{
    const transaction = await Models.sequelize.transaction()
    try{
        let {name,role,email}=req.payload
        let firstName=name
        let alreadyExist=await Models.User.findOne({where:{email:email}})
        if(alreadyExist)
        {
            await transaction.rollback()
            return h.response({success:false,message:req.i18n.__("EMAIL_IS_ALREADY_REGISTERD"),responseData:{}}).code(400)
        }
        // let password=encryptString(email)
        let password=Common.generateRandomPassword(12)
        let user=await Models.User.create({
            password: encryptString(password),
            email,
            isAdmin:1,
            createdBy:req.auth.credentials.userData.User.id,
            lastUpdatedBy:req.auth.credentials.userData.User.id,
            UserProfile:{
                firstName,
                createdBy:req.auth.credentials.userData.User.id,
                lastUpdatedBy:req.auth.credentials.userData.User.id
            },
            userValidation: {}
        },
        {
            include:[{model:Models.UserProfile}, {model:Models.UserValidation, as: "userValidation"}],
            transaction
        })
        await user.setRoles([...role], {transaction: transaction});
        sendEmailNotification = true;




                // Updating All the services with user
                const data = {
                  userId: user.id,
                  firstName: firstName,
                  roles: [],
                  email: email
                };
        
                await Webhook.updateUserInServices(data, transaction);




        await transaction.commit();


        if(sendEmailNotification) {

            let emailUserRole = null;
            if(role.include(2)) {
                emailUserRole = "customer"
            } else if(role.include(3)) {
                emailUserRole = "professional"
            } else if(role.include(4)) {
                emailUserRole = "professional"
            } else {
                emailUserRole = "admin"
            }

            let data = { userId: user.id, password: password, email: email, role: emailUserRole }
            let code = "USER_BY_ADMIN"
            sendEmail(data, code, req.headers.language)
        }


        return h.response({success:true,message:req.i18n.__("USER_ADDED_SUCESSFULLY"),responseData:user}).code(200)

    }
    catch(error)
    {
        await transaction.rollback();
        console.log('Something Error comes---',error)
        return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500)
    }
}

// Update Users by admin
exports.updateUsers=async(req,h)=>{
    const transaction = await Models.sequelize.transaction()
    try{
        let {id,name,role,email}=req.payload
        let user=await Models.User.findOne({where:{id}})
        if(!user)
        {
            await transaction.rollback()
            return h.response({success:false,message:req.i18n.__("USER_DOES_NOT_REGISERD"),responseData:{}}).code(400)
        }
            await user.update({email},{transaction})   
            await Models.UserProfile.update({firstName:name},{where:{userId:id},transaction});
            await user.setRoles([...role], {transaction: transaction})
            await transaction.commit();
        return h.response({success:true,message:req.i18n.__("USER_ADDED_SUCESSFULLY"),responseData:user}).code(200)
    }
    catch(error)
    {
        await transaction.rollback();
        console.log('Something Error comes---',error)
        return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500)
    }
}

// Get Users by admin 
exports.getUsers=async(req,h)=>{
    try{
        roleWhere={}
        const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT;
        const offset = (req.query.pageNumber - 1) * limit;
        const orderByValue = req.query.orderByValue;
        const orderByParameter = req.query.orderByParameter;
        let where = {isAdmin:1},inner_where={};
        if (req.query.status !== null) 
            where = {
                ... where,
                status: req.query.status
            };
            replacements={}
        if(req.query.name!==null)
        {
            let name = '*'+req.query.name.replace('@','%40').replace('.','%2E')+'*';
            replacements={...replacements,"name":name}
           inner_where = { ...inner_where, [Op.or]: [
                Sequelize.literal('MATCH(`first_name`,`last_name`) AGAINST(:name IN BOOLEAN MODE)'),
              ] }
        }
        // if(req.query.name!==null)
        // {
        //     let name = '*'+req.query.name.replace('@','%40').replace('.','%2E')+'*';
        //     replacements={...replacements,"name2":name}
        //    inner_where = { ...inner_where, [Op.or]: [
        //         Sequelize.literal('MATCH(`last_name`) AGAINST(:name2 IN BOOLEAN MODE)'),
        //       ] }
        // }
        if(req.query.email!==null)
        {
            let email =  '*'+req.query.email.replace('@','%40').replace('.','%2E')+'*';
            replacements={...replacements,"email":email}
            where = { ...where, [Op.or]: [
                Sequelize.literal('MATCH(`email`) AGAINST(:email IN BOOLEAN MODE)'),
              ] }
        }
        let _requiredUserFields = ['id','email','status','createdAt'];
        let _userProfileFields  = ['firstName','lastName','title','gender','attachment'];
        let _includesOption     = [
            {
                model: Models.UserProfile,
                attributes: _userProfileFields,
                where:inner_where,
            }, {
                model: Models.Role,
                through: {
                    where: {...roleWhere},
                    
                },
                attributes:['id','name'],
                joinTableAttributes:[],
                required: true,
            }
        ];
        let options = {
            where,
            order       : [
                    [orderByParameter, orderByValue]
                ],
            subQuery    : false,
            replacements,
            attributes  : ['id','email','status','createdAt'],
            include     : _includesOption
        };
        if (req.query.pageNumber !== null) 
            options = {
                ... options,
                limit,
                offset
            };
        const users         = await Models.User.findAndCountAll(options);
        const totalPages    = await Common.getTotalPages(users.count, limit);
        const responseData  = {
            totalPages,
            perPage: limit,
            users: users.rows,
            totalRecords: users.count
        };
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
    }catch(err){
        console.log('Something Error comes---',err)
        return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
    }
}

// delete Users
exports.deletUser=async(req,h)=>{
    try{
        let id = req.query.id;
        let doExists = await Models.User.findOne({where:{id}});
        if(doExists){
            doExists.destroy();
            return h.response({responseData:doExists,message: req.i18n.__("USER_DELETED_SUCCESSFULLY"),success:true}).code(200);
        }else{
            return h.response({success:false,responseData:{},message: req.i18n.__("USER_NOT_EXISTS")}).code(400);
        }
    }catch(err){
        console.log('Something Error comes---',err)
        return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
    }
}
