const MD5     = require('md5');
const Common  = require('../common');
const Webhook = require("../services/webhook.js");

const encryptString=(str)=>{
    if(str==null) return null;
    return MD5(str)
}

const sendEmail = async(data,code, language)=>{
    try{
        // let data = { replacements, priority:'high', code, recipients }

        let replacements = {}
        let recipients = []
        if(code === "USER_BY_ADMIN") {
            if(data?.userId) {

                let url = null;
                if(data.role === "customer") {
                    url = `${process.env.COSTUMER_BASE_DOMAIN}signin`
                } else if(data.role === "professional") {
                    url = `${process.env.COMPANION_BASE_DOMAIN}signin`
                } else if(data.role === "admin") {
                    url = process.env.ADMIN_BASE_DOMAIN
                }

                const userInfo = await Models.User.findOne({ where: { id: data?.userId }, raw: true });
                const userProfileInfo = await Models.UserProfile.findOne({ where: { userId: data?.userId }, raw: true });
                replacements = {...replacements, link: url, email: data?.email, name: userProfileInfo?.firstName, password: data?.password}
                recipients.push(userInfo?.email)
            }
        } else {
            return
        }


        console.log(replacements, recipients, " ==================== replacements, recipients")


        const requestObj = {
            url : `${process.env.EMAIL_SERVICE}/email/send`,
            method: "post",
            data: { replacements, priority:'high', code, recipients },
            headers: {language}
        }
        
        // axoisObj  =   createAxoisObj(URL.SENDEMAIL,'post',data)
        //await createRequest(axoisObj,transaction)
        let res=await Axios(requestObj).then(async(res)=>{
            console.log('Sucessfully Send Email.......... ')
            console.log(res.data)
            return res.data
        })
        .catch(async (error) => {
            console.log('hi catch in Email .........',error)
            return {}
        });
        return res
    } catch(error) {
        console.log('Error in Sending email',error)
    }
}


module.exports={
    createUser:async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try{
            const{email,firstName,role,lastName,title,gender,reason,about,vita,experience,video,attachment,languages,meetingPrice}=req.payload;
            let{soulwritingProductId,meetingProductId}=req.payload;
            if(soulwritingProductId === "") soulwritingProductId = null;
            if(meetingProductId === "") meetingProductId = null;
            let alreadyExist=await Models.User.findOne({where:{email:email}});
            if(alreadyExist)
            {
                await transaction.rollback();
                return h.response({success: false, message: req.i18n.__('EMAIL_IS_ALREADY_REGISTERD'), responseData: {}}).code(400)
            }
            let accountDeleted=await Models.User.findOne({paranoid:false,where:{email:email,
                deleted_at:{
                    [Op.ne]:null
                }
            }})
            
            if(accountDeleted)
            {
                await accountDeleted.restore({transaction})
                await transaction.commit();
                return h.response({success: true, message: req.i18n.__('ACCOUNT_RESTORED_SUCCESSFULLY'),responseData:accountDeleted}).code(200);

            }
            let password = Common.generateRandomPassword(12)
            let user=await Models.User.create({
                email,
                password:encryptString(password),
                lastAdminUpdate:new Date(),soulwritingProductId,meetingProductId,
                createdBy:req.auth.credentials.userData.User.id,
                lastUpdatedBy:req.auth.credentials.userData.User.id,
                userValidation: {},
                UserProfile:[
                    {
                        firstName,
                        lastName,
                        title,
                        attachment,
                        experience: experience ? Moment(experience, "YYYY-MM-DD") : null,
                        gender,
                        createdBy:req.auth.credentials.userData.User.id,
                        reason,
                        about,
                        vita,
                        meetingPrice,
                        video,
                        lastUpdatedBy:req.auth.credentials.userData.User.id
                    }
                ]
            },{transaction,include:[
                {model:Models.UserProfile}, {model:Models.UserValidation, as: "userValidation"}]})
                await user.setRoles([...role], {transaction});
                languages ? await user.setLanguages([...languages],{transaction}) : "";
            // update attchment status
            let data=[]
            if (attachment !== null) {
                if (attachment.hasOwnProperty('id')) 
                data.push({id:attachment.id,status:1})
                else {
                    await transaction.rollback();
                    return h.response({success: false, message: req.i18n.__('ATTACHMENT_ID_IS_REQUIED')}).code(400);
                }
            }
            await Common.updateAttachmentStatus(data, transaction);
             let lang        =   await user.getLanguages({attributes:['id','code','countery','counteryCode','name'],joinTableAttributes:[]},{transaction})
               
            const webhookData = {
                userId          :    user.id,
                firstName       :   firstName,
                lastName        :   lastName,
                title           :   title,
                profilePhotoUrl :   attachment?.path || null,
                profilePhotoId  :   attachment?.id || null,
                languages       :   null,
                meetingPrice    :   meetingPrice ? meetingPrice : null, 
                scheduleTime    :   user.dataValues.UserProfile.dataValues.scheduleTime ,
                reScheduleTime  :   user.dataValues.UserProfile.dataValues.reScheduleTime,
                cancelTime      :   user.dataValues.UserProfile.dataValues.cancelTime ,
                gender          :   user.dataValues.UserProfile.dataValues.gender,
                reason          :   user.dataValues.UserProfile.dataValues.reason,
                email ,
                vita            :   user.dataValues.UserProfile.dataValues.vita || null,   
                experience      :   user.dataValues.UserProfile.dataValues.experience || null,  
                soulwritingProductId,
                meetingProductId,
            }
            console.log(" ============================================= ")
            console.log(" ============================================= ")
            console.log(" ============================================= ")
            console.log(" ============================================= ")
            console.log(webhookData, " ===================== webhookData")
            console.log(" ============================================= ")
            console.log(" ============================================= ")
            console.log(" ============================================= ")
            console.log(" ============================================= ")
            await Webhook.updateUserInServices(webhookData, transaction);
            let reqPayload={
                email,
                firstName,
                lastName,
                displayName:firstName+lastName
            }
            await Webhook.createUserOnZoom(reqPayload, transaction);
            let sendEmailNotification = true;
            await transaction.commit()

            if(sendEmailNotification) {
                let emailUserRole = null;
                if(role.includes(2)) {
                    emailUserRole = "customer"
                } else if(role.includes(3)) {
                    emailUserRole = "professional"
                } else if(role.includes(4)) {
                    emailUserRole = "professional"
                } else {
                    emailUserRole = "admin"
                }

                let data = { userId: user.id, password: password, email: email, role: emailUserRole }
                let code = "USER_BY_ADMIN"
                sendEmail(data, code, req.headers.language)
            }

            return h.response({success: true, message: req.i18n.__('USER_ADDED_SUCCESSFULLY'), responseData:user}).code(200);
        }
        catch(error){
            console.log(error);
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__('SOMETHING_WENT_WRONG'), responseData: {}}).code(500);
        }   
    },
// update user by admin    
    updateUser:async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try{
            const{id,email,status,firstName,role,meetingPrice,lastName,title,gender,reason,about,vita,experience,video,attachment}=req.payload;
            
            let{soulwritingProductId,meetingProductId, languages}=req.payload;
            if(soulwritingProductId === "") soulwritingProductId = null;
            if(meetingProductId === "") meetingProductId = null;
            let user=await Models.User.findOne({where:{id}});
            if(!user)
            {
                await transaction.rollback();
                return h.response({success: false, message: req.i18n.__('USER_DOES_NOT_EXISTS'), responseData: {}}).code(400)
            }
            if(email)
            {
                if(email!=user.dataValues.email)
                {
                    let alreadyInUse=await Models.User.findOne({where:{email}})
                    if(alreadyInUse){
                        await transaction.rollback();
                        return h.response({success: false, message: req.i18n.__('EMAIL_IS_ALREADY_REGISTERD'), responseData: {}}).code(400)
                    }
                    await user.update({email:email},{transaction})
                }
            }
            if(status)
            {
                await user.update({status},{transaction})
            }

            let profile=await Models.UserProfile.findOne({where:{userId:id}})
            let updateObject={}
            let updateUserObj={}
            let reqdata=[]
            if(firstName)updateObject.firstName=firstName
            if(lastName)updateObject.lastName=lastName
            if(title)updateObject.title=title
            if(gender)updateObject.gender=gender
            if(reason)updateObject.reason=reason
            if(about)updateObject.about=about
            if(vita)updateObject.vita=vita
            if(experience)updateObject.experience=Moment(experience, "YYYY-MM-DD")
            if(video){ updateObject.video=video }
            if(meetingPrice)updateObject.meetingPrice=meetingPrice
            updateUserObj.soulwritingProductId=soulwritingProductId
            updateUserObj.meetingProductId=meetingProductId
            if(attachment)
            {
                if(!attachment.hasOwnProperty('id'))
                {
                    await transaction.rollback();
                    return h.response({success:false,message: req.i18n.__('ATTACHMENT_ID_IS_REQUIRED'), responseData:user}).code(400)
                }
                // if(attachment.dataValues.video!=null){
                //     reqdata.push({id:attachment.dataValues.video.id,status:0})
                // }
                reqdata.push({id:attachment.id,status:1})
                await Webhook.updateAttachmentService({data : reqdata}, transaction);
                updateObject.attachment=attachment
            }

            await user.update({...updateUserObj}, {transaction});
            await profile.update({...updateObject},{transaction})
            await user.setRoles([...role],{transaction})
            languages ? await user.setLanguages([...languages],{transaction}) : "";

            // Updating All the services with user
            const data = {
                userId          : user.id,
                firstName       : firstName,
                lastName        : lastName,
                title           : title,
                profilePhotoUrl : attachment?.path || null,
                profilePhotoId  : attachment?.id  || null,
                email,
                meetingPrice : meetingPrice ? meetingPrice : null
            }

            if(soulwritingProductId)data["soulwritingProductId"]=soulwritingProductId
            if(meetingProductId)data["meetingProductId"]=meetingProductId

            await Webhook.updateUserInServices(data, transaction);

            await transaction.commit()
            return h.response({success: true, message: req.i18n.__('USER_UPDATED_SUCCESSFULLY'), responseData:user}).code(200);
        }
        catch(error){
            console.log(error);
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__('SOMETHING_WENT_WRONG'), responseData: {}}).code(500);
        }  
    },
// delete user
    deleteUser:async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try{
            const {id}=req.query
            let user=await Models.User.findOne({where:{id}});
            if(!user)
            {
                await transaction.rollback();
                return h.response({success: false, message: req.i18n.__('USER_NOT_FOUND'), responseData: {}}).code(400);
            }
            await user.destroy({transaction});
            //await Models.UserProfile.destroy({where:{userId:id},transaction})
            await transaction.commit();
            return h.response({success: true, message: req.i18n.__('SUCESSFULLY_DELETED'), responseData:user}).code(200);
        }
        catch(error){
            console.log(error);
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__('SOMETHING_WENT_WRONG'), responseData: {}}).code(500);
        }
    },
// get user by id
    getUserById:async(req,h)=>{
        try{
            const {id}=req.query
            let user=await Models.User.findOne({where:{id},attributes:['id','email','soulwritingProductId','meetingProductId']
            ,include:[{model:Models.UserProfile},
                {
                    model: Models.Role, through: {}, attributes: ['id','name'],
                //  joinTableAttributes:[]
                },
            ]})
            if(!user)
            {
                return h.response({success: false, message: req.i18n.__('INVALID_USER_ID'), responseData: {}}).code(400);
            }
            let languages=await user.getLanguages({attributes:['id','code','countery','counteryCode'],joinTableAttributes:[]});
            user.dataValues.languages=languages
            return h.response({success: true, message: req.i18n.__('SUCESSFULLY_FOUND'), responseData:user}).code(200)
        }
        catch(error)
        {
            console.log(error);
            return h.response({success: false, message: req.i18n.__('SOMETHING_WENT_WRONG'), responseData: {}}).code(500);
        }
    } 

    //Settings 


}