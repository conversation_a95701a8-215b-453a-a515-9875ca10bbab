"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let Quentn = sequelize.define(
      "Quentn",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        userId          :   { type: DataTypes.INTEGER, allowNull: false },
        response        :   {type:DataTypes.JSON,allowNull:false},
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "userobd_quentns"
      }
    );

    Quentn.associate = (models) => {
        Quentn.belongsTo(models.User,{ foreignKey: "userId" } );
    };

    return Quentn;
  };  