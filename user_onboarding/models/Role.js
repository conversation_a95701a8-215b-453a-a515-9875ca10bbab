"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let Role = sequelize.define(
      "Role",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        name: { type: DataTypes.STRING, allowNull: false },
        isSystemGenerated:{ type: DataTypes.INTEGER, defaultValue: Constants.STATUS.INACTIVE},
        status: { type: DataTypes.INTEGER, defaultValue: Constants.STATUS.ACTIVE }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "userobd_roles"
      }
    );

    Role.associate = (models) => {
      Role.belongsToMany(models.Permission, { through: 'userobd_role_permissions', foreignKey: 'roleId', otherKey: 'permissionId' });
      Role.belongsToMany(models.User, { through: 'userobd_user_roles', foreignKey: 'roleId', otherKey: 'userId' });
    };

    return Role;
  };  