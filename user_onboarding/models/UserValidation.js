const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let UserValidation = sequelize.define(
      "UserValidation",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        userId: { type: DataTypes.INTEGER, allowNull: false },
        studentMeetingCount: { type: DataTypes.INTEGER, allowNull: false, defaultValue: 0 },
        companionMeetingCount: { type: DataTypes.INTEGER, allowNull: false, defaultValue: 0 }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "user_validations"
      }
    );
    UserValidation.associate = function(models) {
    
    };

    return UserValidation;
  };  