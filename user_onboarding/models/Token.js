"use strict";
const Constants = require('../constants');
module.exports = (sequelize, DataTypes) => {
    let Token = sequelize.define(
      "Token",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        userId        : {type: DataTypes.INTEGER, defaultValue:null },
        status        : {type: DataTypes.INTEGER, defaultValue:1},
        firstName     : {type:DataTypes.STRING,allowNull:true},
        lastName      : {type:DataTypes.STRING,allowNull:true},
        title         : {type:DataTypes.STRING,allowNull:true},
        email         : {type:DataTypes.STRING,defaultValue:null},
        password      : {type:DataTypes.STRING,allowNull:true},
        token         : {type:DataTypes.TEXT,allowNull:true},
        code          : {type:DataTypes.STRING,allowNull:true},
        type          : {type: DataTypes.INTEGER, defaultValue:null},
        deviceType    : {type: DataTypes.INTEGER, defaultValue:null},
        deviceToken   : {type:DataTypes.STRING,defaultValue:null},
        optionalData  : {type:DataTypes.JSON,defaultValue:{}}
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "userobd_tokens"
      }
    );
    return Token;
};