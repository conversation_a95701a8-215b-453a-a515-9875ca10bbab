"use strict";

const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let EarningHistory = sequelize.define(
      "EarningHistory",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        meetingId: {type:DataTypes.INTEGER,defaultValue:null},
        projectId: {type:DataTypes.INTEGER,defaultValue:null},
        topicId: {type:DataTypes.INTEGER,defaultValue:null},
        data: {type:DataTypes.JSON,defaultValue:null},
        amount: {type:DataTypes.FLOAT,defaultValue:null},
        amountReceived: {type:DataTypes.FLOAT,defaultValue:null}, 
        userId: {type:DataTypes.INTEGER,defaultValue:null},
        companionId: {type:DataTypes.INTEGER,defaultValue:null},
        topicObject: { type: DataTypes.JSON, defaultValue: null },
        projectObject: { type: DataTypes.JSON, defaultValue: null },
        meetingObject: { type: DataTypes.JSON, defaultValue: null },
        userObject: { type: DataTypes.JSON, defaultValue: null },
        companionObject: { type: DataTypes.JSON, defaultValue: null },
        isRefunded: { type: DataTypes.INTEGER, defaultValue: 0 }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "userobd_earning_history"
      }
    );

    EarningHistory.associate = (models) => {
      
    };


    return EarningHistory;
};  
