const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let User = sequelize.define(
      "User",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        step              : { type: DataTypes.INTEGER, defaultValue: 0 },
        loginType         : { type: DataTypes.INTEGER, defaultValue: null },
        password          : { type: DataTypes.STRING,allowNull: true, defaultValue: null },
        status            : { type: DataTypes.INTEGER, defaultValue: Constants.STATUS.ACTIVE },
        countryCodeValue  : {type: DataTypes.STRING, defaultValue: null },
        countryCode       : { type: DataTypes.STRING, defaultValue: null },
        phoneNumber       : { type: DataTypes.STRING, defaultValue: null },
        soulwritingProductId: { type: DataTypes.STRING, defaultValue: null },
        meetingProductId: { type: DataTypes.STRING, defaultValue: null },
        dgStoreId         : {type: DataTypes.STRING, defaultValue: null},
        isEmailVerify     : {type: DataTypes.INTEGER, defaultValue: Constants.STATUS.ACTIVE },
        email             : { type: DataTypes.STRING, allowNull: true, defaultValue: null, unique: 'Email'},
        lastLoginTime     : {type:DataTypes.DATE,allowNull:true,defaultValue:null},
        lastAdminUpdate   : {type:DataTypes.DATE,allowNull:true,defaultValue:null},
        createdBy         : {type:DataTypes.INTEGER},
        deviceType        : {type:DataTypes.JSON},
        isAdmin           : {type:DataTypes.INTEGER,defaultValue:0},
        lastUpdatedBy     : {type:DataTypes.INTEGER},
        importStatus: { type: DataTypes.INTEGER, allowNull: false, defaultValue: 0, comment: "0 -> not imported, 1 -> imported new user, 2 -> user already exists" },
        importedUserData: { type: DataTypes.JSON, allowNull: true, defaultValue: null },
        wordpressUserId: { type: DataTypes.INTEGER, allowNull: true, defaultValue: null },
        soulWritingStatus:{ type: DataTypes.BOOLEAN, allowNull: true,defaultValue:null, defaultValue: null },
        transactionDetails:{ type: DataTypes.JSON, allowNull: true, defaultValue: null },
      },
      {
        paranoid: true,
        underscored: true,
        indexes: [
          {type: 'FULLTEXT', name: 'UserEmail', fields: ['email']},
          {type: 'FULLTEXT', name: 'UserPhone', fields: ['phone_number']},
          {type: 'FULLTEXT', name: 'UserDgStore', fields: ['dg_store_id']},

        ],
        tableName: "userobd_users"
      }
    );
    User.associate = function(models) {
      User.belongsTo(models.User, { foreignKey: "createdBy" })
      User.belongsTo(models.User, { foreignKey: "lastUpdatedBy" })
      User.hasOne(models.UserProfile, { foreignKey: "userId" });
      User.belongsToMany(models.Language, { through: 'userobd_user_languages' });
      User.belongsToMany(models.Language, { through: 'userobd_user_languages', as: "userLanguages" });
      User.belongsToMany(models.Role, { through: 'userobd_user_roles', foreignKey: 'userId', otherKey: 'roleId' });
      User.hasOne(models.UserValidation, { foreignKey: "userId", as: "userValidation" });
    };

    return User;
  };  


// ALTER TABLE `kuby`.`userobd_users` 
// ADD COLUMN `import_status` INT NULL DEFAULT 0 AFTER `soulwriting_product_id`,
// ADD COLUMN `imported_user_data` JSON NULL DEFAULT NULL AFTER `import_status`,
// ADD COLUMN `wordpress_user_id` INT NULL DEFAULT NULL AFTER `imported_user_data`;
