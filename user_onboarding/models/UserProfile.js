const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
  let UserProfile = sequelize.define(
    "UserProfile",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      userId: { type: DataTypes.INTEGER},
      isSingpassProfile:{
        type: DataTypes.INTEGER,default:0
      },
      firstName       : { type: DataTypes.STRING,  defaultValue: null },
      lastName        : { type: DataTypes.STRING,  defaultValue: null },
      title           : { type: DataTypes.STRING,  defaultValue: null },
      attachment      : { type: DataTypes.JSON,    defaultValue: null },
      dob             : { type: DataTypes.DATE,    defaultValue: null },
      gender          : { type: DataTypes.INTEGER, defaultValue: null },
      createdBy       : { type: DataTypes.INTEGER, defaultValue:null},
      reason          : { type: DataTypes.TEXT,    defaultValue: null},
      about           : { type: DataTypes.TEXT,    defaultValue: null},
      vita            : { type: DataTypes.TEXT('long'),    defaultValue: null},
      experience      : { type: DataTypes.DATE,    defaultValue:null},
      video           : { type: DataTypes.STRING,  defaultValue: null},
      meetingPrice    : { type: DataTypes.STRING,  defaultValue: null},
      lastUpdatedBy   : { type: DataTypes.INTEGER, efaultValue: null},
      rating          : { type: DataTypes.INTEGER, defaultValue:null},
      scheduleTime    : { type: DataTypes.INTEGER, defaultValue:24},
      reScheduleTime  : { type: DataTypes.INTEGER, defaultValue:6},
      cancelTime      : { type: DataTypes.INTEGER, defaultValue:6},
      zoomAccount     : { type: DataTypes.INTEGER, defaultValue:0},
      googleAccount     : { type: DataTypes.INTEGER, defaultValue:0},
    },
    {
      paranoid: true,
      underscored: true, 
      indexes: [
        {type: 'FULLTEXT', name: 'UserName', fields: ['first_name','last_name']},
        // {type: 'FULLTEXT', name: 'UserLastName', fields: ['last_name']},
      ],
      tableName: "userobd_profiles"
    }
  );

  UserProfile.associate = (models) => {
    UserProfile.belongsTo(models.User, { foreignKey: "createdBy" });
    UserProfile.belongsTo(models.User, { foreignKey: "lastUpdatedBy" });
    UserProfile.belongsTo(models.User, { foreignKey: "userId" });
  };

  return UserProfile;
};