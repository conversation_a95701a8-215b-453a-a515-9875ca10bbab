"use strict";

const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let Language = sequelize.define(
      "Language",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        status: { type: DataTypes.INTEGER, defaultValue: null },
        isDefault: { type: DataTypes.INTEGER, defaultValue: null },
        code: { type: DataTypes.STRING, allowNull: false, unique:'language' },
        countery: { type: DataTypes.STRING, allowNull: true },
        counteryCode: { type: DataTypes.STRING, allowNull: true },
        mobileCode:{type: DataTypes.STRING, allowNull: true },
        name: { type: DataTypes.STRING, allowNull: false, unique:'language' }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "userobd_languages"
      }
    );

    Language.associate = (models) => {
      Language.belongsToMany(models.User, { through: 'userobd_user_languages'})
      Language.belongsToMany(models.User, { through: 'userobd_user_languages', as: "userLanguages"})
    };


    return Language;
};  
