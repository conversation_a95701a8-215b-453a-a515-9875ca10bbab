"use strict";

const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let Permission = sequelize.define(
      "Permission",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        name:{ type: DataTypes.STRING, allowNull: false },
        isSystemGenerated:{ type: DataTypes.INTEGER, defaultValue: Constants.STATUS.INACTIVE},
        permissionCode: { type: DataTypes.STRING, allowNull: false },
        status: { type: DataTypes.INTEGER, defaultValue: Constants.STATUS.ACTIVE }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "userobd_permissions"
      }
    );

    Permission.associate = (models) => {
      Permission.belongsToMany(models.Role, { through: 'userobd_role_permissions', foreignKey: 'permissionId', otherKey: 'roleId' });
    };

    return Permission;
  };  