"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let Session = sequelize.define(
      "Session",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        userId          :   { type: DataTypes.INTEGER, allowNull: false },
        deviceToken     :   {type: DataTypes.STRING, allowNull: true},
        deviceType      :   {type:DataTypes.STRING,allowNull:true}
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "userobd_sessions"
      }
    );

    // Role.associate = (models) => {
    //   Role.belongsToMany(models.Permission, { through: 'rolePermissions', foreignKey: 'roleId', otherKey: 'permissionId' });
    //   Role.belongsToMany(models.User, { through: 'user_userRoles', foreignKey: 'roleId', otherKey: 'userId' });
    // };

    return Session;
  };  