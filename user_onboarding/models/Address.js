"use strict";
const Constants = require('../constants');
module.exports = (sequelize, DataTypes) => {
    let Address = sequelize.define(
      "Address",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        userId          : {type: DataTypes.INTEGER, defaultValue:null },
        title           : {type: DataTypes.STRING, defaultValue:1},
        fullName        : {type:DataTypes.STRING,allowNull:true},
        line1           : {type:DataTypes.TEXT,allowNull:true},
        line2           : {type:DataTypes.TEXT,defaultValue:null},
        city            : {type:DataTypes.STRING,allowNull:true},
        zipCode         : {type:DataTypes.INTEGER,allowNull:true},
        country         : {type:DataTypes.STRING,allowNull:true}
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "userobd_addresses"
      }
    );
    Address.associate = function(models) {
        Address.belongsTo(models.User, { foreignKey: "userId" })
      }
    return Address;
};