"use strict";
module.exports = (sequelize, DataTypes) => {
    let AdminUserLogin = sequelize.define(
      "AdminUserLogin",
      {
        id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false },
        userId: {type: DataTypes.INTEGER, defaultValue:null },
        key: { type: DataTypes.STRING, allowNull: false },
        validUntil: { type: DataTypes.DATE, allowNull: false },
        status: { type: DataTypes.INTEGER, defaultValue: 1 }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "userobd_admin_user_login"
      }
    );
    AdminUserLogin.associate = function(models) {}
    return AdminUserLogin;
};