"use strict";
module.exports = (sequelize, DataTypes) => {
    let Pages = sequelize.define(
      "Pages",
      {
        id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false },
        title: { type: DataTypes.STRING, allowNull: false },
        body: { type:DataTypes.TEXT('long'), allowNull:false },
        slug: { type:DataTypes.STRING,allowNull:false },
        status: { type:DataTypes.INTEGER,defaultValue:1 },
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "userobd_pages"
      }
    );
    Pages.associate = function(models) {

    }
    return Pages;
};