'use strict';

module.exports = {
  async up (queryInterface, Sequelize) {
    // return await queryInterface.bulkInsert('userobd_role_permissions',[
    //   // {role_id:2,permission_id:1,created_at: new Date(),updated_at: new Date()},
    //   // {role_id:2,permission_id:2,created_at: new Date(),updated_at: new Date()},
    //   // {role_id:4,permission_id:3,created_at: new Date(),updated_at: new Date()},
    // ]
    //)
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  }
};
