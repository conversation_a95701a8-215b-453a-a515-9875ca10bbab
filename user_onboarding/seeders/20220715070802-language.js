'use strict';
const Constants= require('../constants');
const { v4: uuidv4 } = require('uuid');

module.exports = {
  async up (queryInterface, Sequelize) {
   await queryInterface.bulkInsert('userobd_languages',[
    { name:"English", status:Constants.STATUS.ACTIVE, code:'en',  countery:"United States",countery_code:"", is_default:Constants.STATUS.ACTIVE,   created_at:new Date(),  updated_at:new Date()},
    { name:"Arabic",  status:Constants.STATUS.ACTIVE, code:'ar',  countery:"Saudi Arab",countery_code:"", is_default:Constants.STATUS.INACTIVE, created_at:new Date(),  updated_at:new Date()},
    { name:"French",  status:Constants.STATUS.ACTIVE, code:'fr',  countery:"France",countery_code:"", is_default:Constants.STATUS.INACTIVE, created_at:new Date(),  updated_at:new Date()},
    { name:"German",  status:Constants.STATUS.ACTIVE, code:'de',  countery:"Germany",countery_code:"", is_default:Constants.STATUS.INACTIVE, created_at:new Date(),  updated_at:new Date()},
    { name:"Spanish", status:Constants.STATUS.ACTIVE, code:'es',  countery:"Spain",countery_code:"", is_default:Constants.STATUS.INACTIVE, created_at:new Date(),  updated_at:new Date()},
  ])
  },
  
  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  }
};
