'use strict';

module.exports = {
  async up (queryInterface, Sequelize) {
    // return queryInterface.bulkInsert('userobd_permissions',[
    //   // {permission_code:'manage-ownerpets',status:1,created_at: new Date(),updated_at: new Date()},
    //   // {permission_code:'manage-clinics',status:1,created_at: new Date(),updated_at: new Date()},
    //   // {permission_code:'manage-clinics-members',status:1,created_at: new Date(),updated_at: new Date()}
    // ])
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  }
};
