'use strict';

module.exports = {
  async up (queryInterface, Sequelize) {
    return queryInterface.bulkInsert('userobd_roles',[
      {name:'admin',    status:1, is_system_generated:1,  created_at: new Date(), updated_at: new Date()},
      {name:'costumer', status:1, is_system_generated:1,  created_at: new Date(), updated_at: new Date()},
      {name:'student',  status:1, is_system_generated:1,  created_at: new Date(), updated_at: new Date()},
      {name:'companion',status:1, is_system_generated:1,  created_at: new Date(), updated_at: new Date()},
    ])
  },

  async down (queryInterface, Sequelize) {
  }
};
