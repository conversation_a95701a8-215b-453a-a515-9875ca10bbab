'use strict';
const MD5       = require('md5');
require('dotenv').config();


module.exports = {
  async up (queryInterface, Sequelize) {
    return queryInterface.bulkInsert('userobd_users',[
      {
        email     :   process.env.GLOBAL_ADMIN_EMAIL,
        password  :   MD5(process.env.GLOBAL_ADMIN_PASSWORD),
        status    :   1,
        is_email_verify :  1,
        created_at  : new Date(),
        updated_at  : new Date()
      }
    ])
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  }
};
