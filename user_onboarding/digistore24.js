var CryptoJS = require("crypto-js");
var runner = require("child_process").execSync;
// const  phpScriptPath = '/mnt/kuby/nodejs/kuby/kuby_backend/product_management/digistore24.php';
const  phpScriptPath = process.env.DIGISTORE_SCRIPT_PATH;

const key = 'ylaWmC8dIZ2RxL2loxtd';

module.exports={
    decodeData:async(queryString)=>{
        let argsString = queryString + ' | ' + key;
        const encodedWord = CryptoJS.enc.Utf8.parse(argsString);
        const encoded = CryptoJS.enc.Base64.stringify(encodedWord);
        let phpResponse = runner("php " + phpScriptPath + " " +encoded).toString();
        console.log(JSON.parse(phpResponse))
        return JSON.parse(phpResponse)
    }
}