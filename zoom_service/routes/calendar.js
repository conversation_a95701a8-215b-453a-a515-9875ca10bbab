const calendarController = require('../controllers/calendarController');
module.exports = [
        {
        method : "POST",
        path : "/calendar/authorize",
        handler : calendarController.authorizeApp,
        options: {
            tags: ["api", "Google Calendar"],
            notes: "Endpoint to authorize google account",
            description: "Authorize Google",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    email: Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),                     
                    code: Joi.string().example('asdfgrtyucvbnsdfg').required().error(errors=>{return Common.routeError(errors,'CODE_IS_REQUIRED')}),  
                    userId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'CODE_IS_REQUIRED')}),  
                    role: Joi.string().example('companion | customer').required().error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),  
                    //clientId: Joi.string().required().error(errors=>{return Common.routeError(errors,'CLIENT_ID_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
        {
        method : "POST",
        path : "/calendar/de-authorize",
        handler : calendarController.deAuthorizeApp,
        options: {
            tags: ["api", "Google Calendar"],
            notes: "Endpoint to de-authorize Google account",
            description: "De-Authorize Google",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {
                    email: Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
                    role: Joi.string().example('companion | customer').required().error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/calendar/event",
        handler : calendarController.postCalendarEvent,
        options: {
            tags: ["api", "Google Calendar"],
            notes: "Endpoint to create google event",
            description: "Create Google Meeting",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {
                    meetingId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'MEETING_ID_IS_REQUIRED')}), 
                    userId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'USER_ID_IS_REQUIRED')}), 
                    companionId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'COMPANION_ID_IS_REQUIRED')}),
                    summary : Joi.string().example('Title is required').required().error(errors=>{return Common.routeError(errors,'SUMMARY_IS_REQUIRED')}),
                    description : Joi.string().example('Zoom Meeting Title').required().error(errors=>{return Common.routeError(errors,'agenda_IS_REQUIRED')}),
                    startDate: Joi.date().min(Moment().subtract(100,'years')).required().error(errors=>{return Common.routeError(errors,'START_DATE_IS_REQUIRED')}),
                    endDate: Joi.date().min(Moment().subtract(100,'years')).required().error(errors=>{return Common.routeError(errors,'START_DATE_IS_REQUIRED')}),
                    joinLink : Joi.string().example('Title is required').optional().default(null),
                    startLink : Joi.string().example('Title is required').optional().default(null)
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "PATCH",
        path : "/calendar/event",
        handler : calendarController.rescheduleCalendarEvent,
        options: {
            tags: ["api", "Google Calendar"],
            notes: "Endpoint to create google event",
            description: "Create Google Meeting",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {
                    updatedMeetingId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'MEETING_ID_IS_REQUIRED')}), 
                    meetingId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'MEETING_ID_IS_REQUIRED')}), 
                    userId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'USER_ID_IS_REQUIRED')}), 
                    companionId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'COMPANION_ID_IS_REQUIRED')}),
                    summary : Joi.string().example('Title is required').required().error(errors=>{return Common.routeError(errors,'SUMMARY_IS_REQUIRED')}),
                    description : Joi.string().example('Zoom Meeting Title').required().error(errors=>{return Common.routeError(errors,'agenda_IS_REQUIRED')}),
                    startDate: Joi.date().min(Moment().subtract(100,'years')).required().error(errors=>{return Common.routeError(errors,'START_DATE_IS_REQUIRED')}),
                    endDate: Joi.date().min(Moment().subtract(100,'years')).required().error(errors=>{return Common.routeError(errors,'START_DATE_IS_REQUIRED')}),
                    joinLink : Joi.string().example('Title is required').optional().default(null),
                    startLink : Joi.string().example('Title is required').optional().default(null)
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "DELETE",
        path : "/calendar/event",
        handler : calendarController.deleteCalendarEvent,
        options: {
            tags: ["api", "Google Calendar"],
            notes: "Endpoint to create google event",
            description: "Create Google Meeting",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {
                    meetingId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'MEETING_ID_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    }
]