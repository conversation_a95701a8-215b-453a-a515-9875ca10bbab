const zoomController = require('../controllers/zoomController');
module.exports = [
        {
        method : "POST",
        path : "/zoom/authorize",
        handler : zoomController.authorizeApp,
        options: {
            tags: ["api", "Zoom"],
            notes: "Endpoint to authorize zoom account",
            description: "Authorize Zoom",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    email: Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),                     
                    code: Joi.string().example('asdfgrtyucvbnsdfg').required().error(errors=>{return Common.routeError(errors,'CODE_IS_REQUIRED')}),  
                    userId: Joi.number().integer().example('1 (optional)').optional().default(null),  
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
        {
        method : "POST",
        path : "/zoom/de-authorize",
        handler : zoomController.deAuthorizeApp,
        options: {
            tags: ["api", "Zoom"],
            notes: "Endpoint to de-authorize zoom account",
            description: "Authorize Zoom",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    email: Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/meeting",
        handler : zoomController.createMeeting,
        options: {
            tags: ["api", "Meeting"],
            notes: "Endpoint to create meetings",
            description: "Create Meetings",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {                        
                    topic : Joi.string().example('Zoom Meeting Title').required().error(errors=>{return Common.routeError(errors,'TOPIC_IS_REQUIRED')}),
                    agenda : Joi.string().example('Zoom Meeting Title').required().error(errors=>{return Common.routeError(errors,'agenda_IS_REQUIRED')}),
                    duration : Joi.string().example('45').required().error(errors=>{return Common.routeError(errors,'DURATION_IS_REQUIRED')}),
                    start_time: Joi.date().min(Moment().subtract(100,'years')).required().error(errors=>{return Common.routeError(errors,'START_TIME_IS_REQUIRED')}),
                    hostEmail : Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'HOST_IS_REQUIRED')}),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/meeting/{meetingId}",
        handler : zoomController.getMeetingDetails,
        options: {
            tags: ["api", "Meeting"],
            notes: "Endpoint to get meeting details",
            description: "Get Meetings",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {                        
                    meetingId : Joi.string().example("Meeting Id").required().error(errors=>{return Common.routeError(errors,'MEETING_ID_IS_REQUIRED')})
                },
                query: {
                    email : Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "PATCH",
        path : "/meeting",
        handler : zoomController.updateMeeting,
        options: {
            tags: ["api", "Meeting"],
            notes: "Endpoint to update meeting details",
            description: "Update Meetings",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {                        
                    topic : Joi.string().example('Zoom Meeting Title').optional().default(null),
                    agenda : Joi.string().example('Zoom Meeting Title').optional().default(null),
                    duration : Joi.string().example('45').optional().default(null),
                    start_time: Joi.date().min(Moment().subtract(100,'years')).optional().default(null),
                    meetingId : Joi.string().example("Meeting Id").required().error(errors=>{return Common.routeError(errors,'MEETING_ID_IS_REQUIRED')}),
                    email : Joi.string().email().example("email").required().error(errors=>{return Common.routeError(errors,'EMAIl_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "DELETE",
        path : "/meeting",
        handler : zoomController.deleteMeeting,
        options: {
            tags: ["api", "Meeting"],
            notes: "Endpoint to delete meeting",
            description: "DELETE Meetings",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {                        
                    meetingId : Joi.string().example("Meeting Id").required().error(errors=>{return Common.routeError(errors,'MEETING_ID_IS_REQUIRED')}),
                    email : Joi.string().email().example("email").required().error(errors=>{return Common.routeError(errors,'EMAIl_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/meeting/{meetingId}/recording/{email}",
        handler : zoomController.getRecordings,
        options: {
            tags: ["api", "Recordings"],
            notes: "Endpoint to Delete Users",
            description: "Delete Users",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {   
                    meetingId : Joi.string().example('Meeting Id').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
                    email : Joi.string().example('Meeting Id').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/zoom/webhook",
        handler : zoomController.zoomWebhooks,
        options: {
            tags: ["api", "Zoom"],
            notes: "Webhook to get recording",
            description: "Recording Webhook",
            auth: false,
            validate: {
                options: {
                    abortEarly: false
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/zoom/replace-refresh-tokens",
        handler : zoomController.updateAllRefreshToken,
        options: {
            tags: ["api", "Zoom"],
            notes: "Webhook to get recording",
            description: "Recording Webhook",
            auth: false,
            validate: {
                options: {
                    abortEarly: false
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    }
]