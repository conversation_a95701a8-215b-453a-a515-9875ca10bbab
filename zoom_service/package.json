{"name": "sqauds", "version": "1.0.0", "description": "Zoom API", "main": "server.js", "scripts": {}, "repository": {"type": "git", "url": "git+https://github.com/illuminzcode/yoga.git"}, "keywords": ["zoom"], "author": "illuminz", "license": "ISC", "dependencies": {"@hapi/boom": "^10.0.0", "@hapi/hapi": "^21.2.1", "@hapi/inert": "^7.0.0", "@hapi/vision": "^7.0.0", "axios": "^1.2.6", "bcrypt": "^5.1.0", "dotenv": "^16.0.3", "fs": "0.0.1-security", "googleapis": "^118.0.0", "handlebars": "^4.7.7", "hapi-auth-jwt2": "^10.4.0", "hapi-auto-route": "^3.0.4", "hapi-i18n": "^3.0.1", "hapi-swagger": "^15.0.0", "http": "^0.0.1-security", "joi": "^17.7.0", "jsonwebtoken": "^9.0.0", "jsrsasign": "^10.6.1", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.29.4", "mysql2": "^3.1.0", "ngrok": "^4.3.3", "path": "^0.12.7", "sequelize": "^6.28.0", "uuid": "^9.0.0", "verify-apple-id-token": "^3.0.1"}}