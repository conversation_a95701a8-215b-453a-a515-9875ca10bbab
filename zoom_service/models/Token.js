"use strict";
module.exports = (sequelize, DataTypes) => {
    let Token = sequelize.define(
      "Token",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        email: {type: DataTypes.STRING, allowNull: false, unique: "email-type"},
        userId: {type: DataTypes.INTEGER, defaultValue:null },
        refreshToken: {type: DataTypes.TEXT, defaultValue:null },
        accessToken: {type: DataTypes.TEXT, defaultValue:null },
        type: {type: DataTypes.STRING, allowNull: false, unique: "email-type"},
        clientId: {type: DataTypes.STRING, allowNull: true}
      },
      {
        paranoid: false,
        underscored: true,
        tableName: "zoom_token"
      }
    );

    return Token;
};