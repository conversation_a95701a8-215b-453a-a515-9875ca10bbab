"use strict";
module.exports = (sequelize, DataTypes) => {
    let ZoomMeeting = sequelize.define(
      "ZoomMeeting",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        meetingId: { type: DataTypes.STRING, allowNull: false, unique: true},
        startDate: { type: DataTypes.DATE, defaultValue: null},
        endDate: { type: DataTypes.DATE, defaultValue: null},
        duration: { type: DataTypes.STRING, defaultValue: null},
        meetingUrl: { type: DataTypes.STRING, allowNull: false},
        meetingObj: { type: DataTypes.JSON, allowNull: false },
        attachmentObj: { type: DataTypes.JSON, defaultValue: null }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "zoom_meetings"
      }
    );

    return ZoomMeeting;
};