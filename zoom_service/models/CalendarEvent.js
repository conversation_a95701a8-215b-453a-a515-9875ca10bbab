"use strict";
module.exports = (sequelize, DataTypes) => {
    let CalendarEvent = sequelize.define(
      "CalendarEvent",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        meetingId: { type: DataTypes.STRING, allowNull: false },
        userId: { type: DataTypes.INTEGER, allowNull: false },
        role: { type: DataTypes.STRING, allowNull: false },
        googleEventId: { type: DataTypes.STRING, allowNull: false }
      },
      {
        underscored: true,
        tableName: "zoom_calendar_events"
      }
    );

    return CalendarEvent;
};