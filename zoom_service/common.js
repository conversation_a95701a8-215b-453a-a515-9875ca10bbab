decrypt = (text) => {
  let decipher = crypto.createDecipheriv(
    process.env.ALGORITHM, 
    process.env.PRIVATE_KEY, 
    process.env.IV
  );
  return decipher.update(text, "hex", "utf8") + decipher.final("utf8");
}

encrypt = (text) => {
  let cipher = crypto.createCipheriv(
    process.env.ALGORITHM, 
    process.env.PRIVATE_KEY, 
    process.env.IV
  );
  return cipher.update(text, "utf8", "hex") + cipher.final("hex");
}

exports.signToken = (tokenData, expiresIn) => {
  return Jwt.sign(
    { data: encrypt(JSON.stringify(tokenData))},
    process.env.PRIVATE_KEY,
    { expiresIn: expiresIn }
  );
};

exports.generateToken = (apiKey, apiSecret, expiresIn = null) => {
  let today = new Date();
  today.setHours(today.getHours() + 1);
  return Jwt.sign(
    {"iss": apiKey, "exp": today.getTime()}, apiSecret,'HS256'
  );
};

exports.prefunction = (req,h) => {
  let allowedMethods = typeof req._route.settings.app.allowedMethods!='undefined'?req._route.settings.app.allowedMethods:[];
  if(allowedMethods.length>0 && allowedMethods.indexOf(req.method) == -1)
    return this.generateError(req,405,{})  

  global.LanguageCodes = process.env.ALL_LANGUAGE_CODE.split(',');
  global.LanguageIds = process.env.ALL_LANGUAGE_ID.split(',').map(function(item) {
    return parseInt(item, 10);
  });
  global.utcOffset = req.headers.utcoffset;
  return true;
}

exports.routeError = (errors,message) => {
  console.log(errors);
  errors.forEach(err=>{ 
  switch(err.code){
    case "any.required":
      err.message=message;
      break
      }
    });
    return errors
}

exports.axiosRequest = async (requestUrl,requestMethod,requestHeader,requestBody,responseType = "json") => {
  try {
    let responseData;
    let requestObject = {
      url: requestUrl,
      headers: requestHeader,
      method: requestMethod.toLowerCase(),
      responseType: responseType
    }
    requestObject = (requestMethod.toLowerCase() === 'get') ? {...requestObject,params:requestBody} : {...requestObject,data:requestBody}
    responseData = await Axios(requestObject);
    responseData = responseData.data;
    console.log(responseData)
    stringifiedResponse = JSON.stringify(responseData)
    return responseData
  } catch({response}) {
    return response ? response.data : 'AXIOS_REQUEST_FAILED'
  }
}

exports.validateToken = async (token) => {
  let fetchtoken = JSON.parse(decrypt(token.data));
  const expiryTime = Moment(token.exp * 1000);
  if (expiryTime > Moment()) {
    return {
      isValid: true,
      credentials: { 
        userData: fetchtoken, 
        // scope: fetchtoken.permissions
        scope: [...fetchtoken.Role,...fetchtoken.Permissions] 
      }
    }; 
  }
  return {
    isValid: false
  };
};

exports.convertToUTC = (date,offset) => {
  console.log(date);
  let utcDate = Moment(date).utcOffset(offset, true);
  console.log(utcDate);
  return utcDate;
}

exports.headers = (authorized) => {
	let Globalheaders = {
        language: Joi.string().optional().default(process.env.DEFAULT_LANGUANGE_CODE),
        utcoffset: Joi.string().optional().default(0)
    };
	if (authorized){
        _.assign(Globalheaders, {authorization: Joi.string().required().description("Token to identify user who is performing the action")});
	}
	return Globalheaders;
};

exports.sendOTP = async (phoneNumber) => {
  return {phoneNumber:phoneNumber,pinId:process.env.MASTER_OTP}
}

exports.generateCode = (requestedlength) => {
  const char = '1234567890'; //Random Generate Every Time From This Given Char
  const length = typeof requestedlength !='undefined' ? requestedlength : 4;
  let randomvalue = '';
  for ( let i = 0; i < length; i++) {
    const value = Math.floor(Math.random() * char.length);
    randomvalue += char.substring(value, value + 1).toUpperCase();
  }
  return randomvalue;
}

exports.FailureError = (err,req) => {
  const updatedError = err;
	updatedError.output.payload.message = [];
	let customMessages = {};
	if (err.isJoi && Array.isArray(err.details) && err.details.length > 0){
		err.details.forEach((error) => {
			customMessages[error.context.label] = req.i18n.__(error.message);
		});
	}
	delete updatedError.output.payload.validation;
	updatedError.output.payload.error =  req.i18n.__('BAD_REQUEST');
  console.log('err.details.type', err.details)
  if(err.details[0].type === 'string.email') {
    updatedError.output.payload.message = req.i18n.__(
      "PLEASE_ENTER_A_VALID_EMAIL"
    );
  } else {
    updatedError.output.payload.message = req.i18n.__(
      "ERROR_WHILE_VALIDATING_REQUEST"
    );
  }
	updatedError.output.payload.errors = customMessages;
	return updatedError;
}

exports.generateError = (req,statusCode,message,error)=>{
  let customError = {status:false,responseData:{}};
  switch(statusCode) {
    case 400:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__(message);
      break;
    case 401:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__('UNAUTHORIZED_REQUEST');
      break;
    case 403:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__('PERMISSION_DENIED');
      break;
    case 422:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__(message);
      break;
    case 500:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__('INTERNAL_SERVER_ERROR');
      break;
    default:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__('UNKNOWN_ERROR_OCCURED');
      break;
  }
  return customError;
}

exports.getTotalPages = async (records, perpage) => {
  let totalPages = Math.ceil(records / perpage);
  return totalPages;
};


exports.generatePassword = async(length) => {
   let password = "";
    const str = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    const num = "0123456789";
     const symbol = "!@#$%^&*";
      password += num.charAt(Math.floor(Math.random() * num.length));
       for (let i = 0; i < length - 2; i++) {
         password += str.charAt(Math.floor(Math.random() * str.length)); }
         password += symbol.charAt(Math.floor(Math.random() * symbol.length)); 
         return password;}