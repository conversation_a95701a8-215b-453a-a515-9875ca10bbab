const { google } = require('googleapis');
const { axiosRequest } = require("../common");
// const timeZone = require("../timezones.json");

const calendarTokenType = "calendar";
let client_id = "953220753965-isf6jaai2fd32psurlqis2gi1cjn2tgj.apps.googleusercontent.com";
let client_secret = "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";
// let client_id = "541039242854-jki157iimrf9qg97d4ovd4n6kqn2nhgk.apps.googleusercontent.com";
// let client_secret = "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";

const googleAuth = async(role) => {
    let redirectUrl;
    if(role === "customer") {
      // redirectUrl = ["https://portal.kuby.info/user/profile", "http://localhost:3005/user/profile"]
      redirectUrl = [process.env.CALENDAR_PORTAL_REDIRECT_URL]
    } else if(role === "companion") {
      // redirectUrl = ["https://professional.kuby.info/user/profile", "http://localhost:3006/user/profile"]
      redirectUrl = [process.env.CALENDAR_PROFESSIONAL_REDIRECT_URL]
    } else {
      return
    }
    let auth = new google.auth.OAuth2(client_id,client_secret,redirectUrl);
    return auth;
}

const requestToken = async (user_id, auth, client_id) => {
  try {
    let getSavedToken = await Models.Token.findOne({where: {user_id: user_id, type: calendarTokenType}});
    if(!getSavedToken) {
      return {success: false, message: "TOKEN_NOT_SYNCED"};
    }
    let refresh_token = getSavedToken.refreshToken
    let requestNewToken = await axiosRequest(
      `https://www.googleapis.com/oauth2/v4/token`,
      "post",
        {}, 
      {client_id: client_id,client_secret: client_secret,refresh_token: refresh_token,grant_type: "refresh_token"}
    );

    let access_token = requestNewToken.access_token;

    // let verification = await verifySocialLogin(access_token);
  
    // if(!verification) {
    //     return {success: false, message: "INVALID_TOKEN_AUTHENTICATION_FAILED"};
    // }
    let credentials = {access_token: access_token,token_type: "Bearer", refresh_token: refresh_token};
    auth.setCredentials(credentials); 
    return {success: true, message: "SUCCESSFULLY_VALIDATED"};
} catch (error) {
    return {success: false, message: "ENTERED_IN_CATCH_BLOCK_OF_TOKEN_VERIFICATION"}; 
}
}

const insertEvent = async (event, auth) => {
    try {
      const calendar = google.calendar({ version: "v3", auth });
      let response = await calendar.events.insert({ auth: auth,calendarId: "primary",  sendUpdates : "all", sendNotifications: true,resource: event});
      if (response["status"] == 200 && response["statusText"] === "OK") {
        return {success: 1, response: response};
      } else {
        return {success: 0, response: response};
      }
    } catch (error) 
    {console.log(`Error at insertEvent --> ${error}`);return 0;}
};

const deleteEvent = async (eventId, auth) => {
    try {
    const calendar = google.calendar({ version: "v3", auth });
    let response = await calendar.events.delete({auth: auth,calendarId: "primary",eventId: eventId});
    if (response.data === "") {return 1} else {return 0}
    } catch (error) {
    console.log(`Error at deleteEvent --> ${error}`);
    return 0;
    }
};

// const getTimeZone = async() => {
//     let findOffsset = parseInt(utcOffset) / 60
//     const found = timeZone.find(element => element.offset == findOffsset);
//     return found.utc[0]
// }
  
const createEvent = async(summary, description, start, end) => {
// let timeZone = await getTimeZone();
let event = {
    summary: summary, 
    description: description,
    start: {dateTime: start, timeZone: "utc"},
    end: {dateTime: end,timeZone: "utc"},
    reminders: {useDefault: false,overrides: [{ method: "email", minutes: 24 * 60 },{ method: "popup", minutes: 10 }]},
    colorId: 4,
    sendUpdates: "all",
    status: "confirmed"
};
console.log(event, " ====================== event")
return event
}

const verifySocialLogin = async (accessToken) => {
    try {
        let url = `https://oauth2.googleapis.com/tokeninfo?id_token=${accessToken}`;

        let responseData = await axiosRequest(url,'GET', {}, {});
        console.log(responseData, " ============ responseData")
        return true


    } catch (err) {
      console.log(err)
      return Common.generateError(req,500,"EXCEPTION_ENCOUNTERED_WHILE_VERIFIENG_SOCIAL_LOGIN",err);
    }
};
  
exports.authorizeApp=async(req,h)=>{
    try {
        let {code, email, userId, role, clientId} = req.payload;

        let auth = await googleAuth(role);
        let {tokens} = await auth.getToken(code);
        console.log(tokens)
        let refreshToken = tokens.refresh_token;
        let accessToken = tokens.access_token;
      console.log()
        await Models.Token.upsert({ email: email, userId, userId, accessToken, refreshToken, type: calendarTokenType, clientId: clientId }, { where: { email: email, type: calendarTokenType } });

        let userGoogleStatus = `${process.env.USER_ONBOARDING_DOMAIN}/google-status`;
        let payload = { email: email, status: 1 }
    
        let profileResponse = await axiosRequest(userGoogleStatus,'POST', {}, payload);

        return h.response({ success: true, message: req.i18n.__("USER_SUCCESSFULLY_AUTHORIZE"), responseData: profileResponse }).code(200);
  } catch (error) {
        console.log(error, "error");
        // await transaction.rollback();
        return h.response({ success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {} }).code(500);
  }
}

exports.deAuthorizeApp=async(req,h)=>{
    const transaction = await Models.sequelize.transaction();
    try {
        const { email, role } = req.payload;

        let userToken = await Models.Token.findOne({ where: { email: email, type: calendarTokenType } });
        if(userToken) {
           // await transaction.rollback();
           // return h.response({success: false,message: req.i18n.__("RECORD_NOT_FOUND"),responseData: {}}).code(400) 
           await Models.Token.destroy({where: { email: email, type: calendarTokenType }, force: true, transaction});
        }


        let userGoogleStatus = `${process.env.USER_ONBOARDING_DOMAIN}/google-status`;
        let payload = { email: email, status: 0 }
    
        let profileResponse = await axiosRequest(userGoogleStatus,'POST', {}, payload);
        await transaction.commit();

        return h.response({ success: true, message: req.i18n.__("USER_SUCCESSFULLY_DEAUTHORIZE"), responseData: profileResponse }).code(200);
    } catch (error) {
        console.log(error, "err");
        await transaction.rollback();
        return h.response({ success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {} }).code(500);
    }
}

exports.postCalendarEvent = async (req, h) => {
    try {
        let { meetingId, userId, companionId, summary, description, startDate, endDate, joinLink, startLink } = req.payload;

        // description = `<p>${description}</p><br><br><p><a href=${joinLink}>${joinLink}</a></p>`
        let users = [
          {userId: userId, role: "customer", description: `<p>${description}</p><br><br><p><a href=${joinLink}>${joinLink}</a></p>`}, 
          {userId: companionId, role: "companion", description: `<p>${description}</p><br><br><p><a href=${startLink}>${startLink}</a></p>`}
        ]
        for(let userObj of users) {
            let user = userObj.userId
            let role = userObj.role
            let newDesc = userObj.description;
            let auth = await googleAuth(role);
            let tokenVerification = await requestToken(user, auth);
            if(!tokenVerification.success) continue;

            let event = await createEvent(summary, newDesc, startDate, endDate)

            console.log("========== 5")
            console.log("========== 5")
            console.log("========== 5")
            let data = await insertEvent(event, auth);
            console.log("========== 6")
            console.log("========== 6")
            console.log("========== 6")
            if(!data?.response?.data?.id) continue;
            console.log("========== 7")
            console.log("========== 7")
            console.log("========== 7")
            await Models.CalendarEvent.create({ meetingId: meetingId, userId: user, googleEventId: data?.response?.data?.id, role: role })
          }
          // if(data) {return data} else {return {data: data, err:"err"}}

        return h.response({ success: true, message: req.i18n.__("EVENT_SYNCED_WITH_GOOGLE"), responseData: {} }).code(200);
    } catch (error) {
      console.log(error);
      return error;
    }
};

exports.rescheduleCalendarEvent = async(req, h) => {
  try {
    const { meetingId, updatedMeetingId, userId, companionId, summary, description, startDate, endDate, joinLink } = req.payload;

    const findExistingEvents = await Models.CalendarEvent.findAll( { where: { meetingId: meetingId } } );
    for(let record of findExistingEvents) {
      let auth = await googleAuth(record.role);
      let tokenVerification = await requestToken(record.userId, auth);
      if(!tokenVerification.success) continue;
      await deleteEvent(record.googleEventId, auth)
    }

    let users = [{userId: userId, role: "customer"}, {userId: companionId, role: "companion"}]
    for(let userObj of users) {
      let user = userObj.userId
      let role = userObj.role
      let auth = await googleAuth(role);
      let tokenVerification = await requestToken(user, auth);
      if(!tokenVerification.success) continue;
      let attendeesEmail = [];
      let event = await createEvent(summary, description, startDate, endDate)
      let data = await insertEvent(event, auth);
      if(!data?.response?.data?.id) continue;
      await Models.CalendarEvent.create({ meetingId: updatedMeetingId, userId: user, googleEventId: data?.response?.data?.id, role: role })
    }

    return h.response({ success: true, message: req.i18n.__("EVENT_SYNCED_WITH_GOOGLE"), responseData: {} }).code(200);
  } catch (error) {
    console.log(error);
    return error;
  }
}

exports.deleteCalendarEvent = async(req, h) => {
  try {
    const { meetingId } = req.payload;

    const findExistingEvents = await Models.CalendarEvent.findAll( { where: { meetingId: meetingId } } );
    for(let record of findExistingEvents) {
      let auth = await googleAuth(record.role);
      let tokenVerification = await requestToken(record.userId, auth);
      if(!tokenVerification.success) continue;
      await deleteEvent(record.googleEventId, auth)
    }

    return h.response({ success: true, message: req.i18n.__("EVENT_SYNCED_WITH_GOOGLE"), responseData: {} }).code(200);
  } catch (error) {
    console.log(error);
    return error;
  }
}
