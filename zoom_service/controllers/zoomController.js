const { axiosRequest } = require("../common");

const clientId      = process.env.ENVIRONMENT === "live" ? process.env.ZOOM_CLIENT_ID       : process.env.TEST_CLIENT_ID;
const clientSecret  = process.env.ENVIRONMENT === "live" ? process.env.ZOOM_CLIENT_SECRET   : process.env.TEST_CLIENT_SECRET;
const redirectUrl   = process.env.ENVIRONMENT === "live" ? process.env.ZOOM_REDIRECT_URL    : process.env.ZOOM_REDIRECT_URL;

const tokenUrl      = "https://zoom.us/oauth/token";
const zoomApiUrl    = `https://api.zoom.us/v2`;

const zoomTokenType = "zoom";

const settings = {
    allow_multiple_devices: true, approval_type: 2, audio: "both", auto_recording: "cloud",
    email_notification: false, focus_mode: true, host_video: true,jbh_time: 0, join_before_host: false,
    mute_upon_entry: false, participant_video: true, registrants_confirmation_email: false
}

const genereteRefreshToken = async (code) => {
    const url = `${tokenUrl}?grant_type=authorization_code&code=${code}&redirect_uri=${redirectUrl}`;
    console.log(url, " ================ redirect url")
    const authorization = "Basic " + Buffer.from(clientId + ":" + clientSecret).toString("base64");
    const headers = { Authorization: authorization };
    const token = await axiosRequest(url, "post", headers, {});
    return {success: true, token: token};
};

const generateAccessToken = async (email) => {
    try {
        const userTokenInfo = await Models.Token.findOne({where: { email: email, type: zoomTokenType }});
        console.log(userTokenInfo, " ========================= userTokenInfo")
        if(!userTokenInfo) {
            console.log(" =============================== unauth request user")
            return { success: false, message: "USER_NOT_AUTHORIZED_TO_MAKE_REQUEST" }
        }
        const oldRefreshToken = userTokenInfo.refreshToken;
        const url = `${tokenUrl}?grant_type=refresh_token&refresh_token=${oldRefreshToken}`;
        const authorization = "Basic " + Buffer.from(clientId + ":" + clientSecret).toString("base64");
        const headers = { Authorization: authorization };
    
        const token = await axiosRequest(url, "post", headers, {});
        console.log(token, " ============================ zoom token")
        const accessToken = token.access_token;
        const refreshToken = token.refresh_token;
        await userTokenInfo.update({ accessToken, refreshToken });
        return {success: true, token: token}
    } catch (error) {
        console.log(error, " ==================== error token gen")
        return {success: false, token: null}
    }

}

const userDetails = async (header) => {
    let url = `${zoomApiUrl}/users/me`;
    let userData = await axiosRequest(url,'GET', header, {});
    console.log('userData in get api' ,userData)
    if(!userData?.id) {
        return {success: false, message: "USER_NOT_FOUND"}
    }
    // if(!userData?.type !== 2) {
    //     return {success: false, message: "USER_MUST_HAVE_LICSENED_ACCOUNT"}
    // }
    return {success: true, message: "USER_SUCCESSFULLY_FOUND"};
}

const revokeRefreshToken = async(header, refreshToken) => {
    // const authHeader = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');
    const endpoint = `https://zoom.us/oauth/revoke?token=${refreshToken}`;
    console.log(header)
    
try {
    const revokeToken = await axiosRequest(endpoint,'post', header, {});
    console.log(revokeToken, " ============== revokeToken")
} catch (error) {
    console.log(error, " ==================== revoke error")
}

    // try {
    //   const response = await axios.post(endpoint, null, {
    //     headers: header,
    //     params: {
    //       token: refreshToken
    //     }
    //   });
      
    //   console.log('Refresh token revoked successfully');
    // } catch (error) {
    //   console.error('Failed to revoke refresh token:', error.response.data);
    // }
  }

exports.authorizeApp = async (req, h) => {
  try {
    const { code, email, userId } = req.payload;
    const tokenData = await genereteRefreshToken(code);
    if(!tokenData.success) {
        return h.response({success: false,message: req.i18n.__(tokenData.message),responseData: {}}).code(400)
    }
    const accessToken = tokenData.token.access_token;
    const refreshToken = tokenData.token.refresh_token;

    const header = { "Authorization": "Bearer" + " " + accessToken };
    let userInfo = await userDetails(header);
    if(!userInfo.success) {
        return h.response({success: false,message: req.i18n.__(userInfo.message),responseData: {}}).code(400)   
    }

    await Models.Token.upsert({ email: email, userId, userId, accessToken, refreshToken, type: zoomTokenType }, { where: { email: email, type:zoomTokenType } });

    let userZoomStatus = `${process.env.USER_ONBOARDING_DOMAIN}/zoom-status`;
    let payload = { email: email, status: 1 }

    let profileResponse = await axiosRequest(userZoomStatus,'POST', {}, payload);

    return h
      .response({ success: true, message: req.i18n.__("USER_SUCCESSFULLY_AUTHORIZE"), responseData: profileResponse })
      .code(200);
  } catch (error) {
    console.log(error, "err");
    return h
      .response({ success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {} })
      .code(500);
  }
};

exports.deAuthorizeApp = async (req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const { email } = req.payload;

        let userToken = await Models.Token.findOne({ where: { email: email, type: zoomTokenType } });

        if(userToken) {
            // const tokenData = await generateAccessToken(email);
            // if(!tokenData.success) {
            //     return h.response({success: false,message: req.i18n.__(tokenData.message),responseData: {}}).code(400)
            // }
            // console.log(tokenData, " ================ tokenData")
            // const access_token = tokenData.token.access_token;
            // const header = { "Authorization": "Bearer" + " " + access_token };
            // const refreshToken = userToken.refreshToken;
            // await revokeRefreshToken(header, access_token);

            //await transaction.rollback();
            //return h.response({success: false,message: req.i18n.__("RECORD_NOT_FOUND"),responseData: {}}).code(400) 
        }

        const removeToken = await Models.Token.destroy({where: { email: email, type: zoomTokenType }, force: true, transaction});
            
        await transaction.commit();

        let userZoomStatus = `${process.env.USER_ONBOARDING_DOMAIN}/zoom-status`;
        let payload = { email: email, status: 0 }
    
        let profileResponse = await axiosRequest(userZoomStatus,'POST', {}, payload);

        return h.response({ success: true, message: req.i18n.__("USER_SUCCESSFULLY_AUTHORIZE"), responseData: profileResponse }).code(200);
    } catch (error) {
        console.log(error, "error");
        await transaction.rollback();
        return h.response({ success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {} }).code(500);
    }
};

exports.createMeeting = async (req, h) => {
    try {
        let { topic, start_time, duration, agenda, timezone, hostEmail } = req.payload;
        const tokenData = await generateAccessToken(hostEmail);
        if(!tokenData.success) {
            return h.response({success: false,message: req.i18n.__(tokenData.message),responseData: {}}).code(400)
        }
        const access_token = tokenData.token.access_token;
        const header = { "Authorization": "Bearer" + " " + access_token };

        if(!timezone) { timezone = "UTC" };
        const payload = {
            topic: topic, type: 2, start_time: start_time,
            duration: duration, agenda: agenda,
            timezone: timezone,
            settings: settings
        };

        let url = `${zoomApiUrl}/users/me/meetings`;
        const meetingData = await axiosRequest(url,'post', header, payload);

        const meetingId = meetingData.id;
        const joinLink = meetingData.join_url;
        const responseData = { zoomId: meetingId, joinLink: meetingData.join_url, startLink: meetingData.start_url };

        await Models.ZoomMeeting.create({ meetingId: meetingId, meetingUrl: joinLink, meetingObj: meetingData });

        return h.response({success:true,message:req.i18n.__('ZOOM_MEETING_CREATED_SUCCESSFULLY'),responseData}).code(200);
    } catch (error) {
        console.log(error, "err");
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),data:{}}).code(500);
    }
}

exports.getMeetingDetails = async(req, h) => {
    try {
        const {meetingId} = req.params;
        const {email} = req.query;
        const tokenData = await generateAccessToken(email);
        if(!tokenData.success) {
            return h.response({success: false,message: req.i18n.__(tokenData.message),responseData: {}}).code(400)
        }
        const access_token = tokenData.token.access_token;
        const header = { "Authorization": "Bearer" + " " + access_token }
        let url = `${zoomApiUrl}/meetings/${meetingId}`;
        const meetingData = await axiosRequest(url,'get', header, {});
        return h.response({success:true,message:req.i18n.__('REQUEST_SUCCESSFULL'),data:{meetingData}}).code(200);
    } catch (error) {
        console.log(error, "err");
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),data:{}}).code(500);
    }
}

exports.updateMeeting = async(req, h) => {
    try {
        const { topic, start_time, duration, agenda, meetingId, email } = req.payload;
        let updatedObj = {}
        if(topic !== null) { updatedObj["topic"] = topic }
        if(start_time !== null) { updatedObj["start_time"] = start_time }
        if(duration !== null) { updatedObj["duration"] = duration }
        if(agenda !== null) { updatedObj["agenda"] = agenda }

        let tokenData = await generateAccessToken(email);
        if(!tokenData.success) {
            return h.response({success: false,message: req.i18n.__(tokenData.message),responseData: {}}).code(400)
        }
        let access_token = tokenData.token.access_token;

        const header = { "Authorization": "Bearer" + " " + access_token };
        let payload = {...updatedObj}
        let url = `${zoomApiUrl}/meetings/${meetingId}`;
        let meetingData = await axiosRequest(url,'patch', header, payload);
        // await Models.ZoomMeeting.update({  }, { where: { meetingId: meetingId } });
        return h.response({success:true,message:req.i18n.__('ZOOM_MEETING_UPDATED_SUCCESSFULLY'),data:{meetingData}}).code(200);
    } catch (error) {
        console.log(error, "err");
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),data:{}}).code(500);
    }
}

exports.deleteMeeting = async(req, h) => {
    try {
        const {meetingId, email} = req.payload;
        const tokenData = await generateAccessToken(email);
        if(!tokenData.success) {
            return h.response({success: false,message: req.i18n.__(tokenData.message),responseData: {}}).code(400)
        }
        const access_token = tokenData.token.access_token;
        const header = { "Authorization": "Bearer" + " " + access_token };
        let url = `${zoomApiUrl}/meetings/${meetingId}`;
        let payload = {meetingId: meetingId}
        let meetingData = await axiosRequest(url,'DELETE', header, payload);
        await Models.ZoomMeeting.destroy({ where: { meetingId: meetingId } });
        return h.response({success:true,message:req.i18n.__('ZOOM_MEETING_DELETED_SUCCESSFULLY'),data:{meetingData}}).code(200);
    } catch (error) {
        console.log(error, "err");
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),data:{}}).code(500);
    }
}

exports.getRecordings = async (req, h) => {
    try {
        let meetingId = req.params.meetingId;
        let email = req.params.email;
        let url = `${zoomApiUrl}/meetings/${meetingId}/recordings?include_fields=download_access_token&ttl=604800`;
        let tokenData = await generateAccessToken(email);
        if(!tokenData.success) {
            return h.response({success: false,message: req.i18n.__(tokenData.message),responseData: {}}).code(400)
        }
        let access_token = tokenData.token.access_token;
        const header = { "Authorization": "Bearer" + " " + access_token };
        let recordingData = await axiosRequest(url,'GET', header, {});
        return h.response({success:true,message:req.i18n.__('REQUEST_SUCCESSFULL'),data:{recordingData}}).code(200);
    } catch (error) {
        console.log(error, "err");
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),data:{}}).code(500);   
    }
}

exports.zoomWebhooks = async(req, h) => {
    try {
        let headers = req.headers;
        let body = req.payload;

        console.log(" ==================== zoom webhook response ================")
        console.log(" ==================== zoom webhook response ================")
        console.log(" ==================== zoom webhook response ================")
        console.log(" ==================== zoom webhook response ================")

        console.log({ headers, body })

        console.log(body, " =========== webhook res")
        
        if(body.event === "endpoint.url_validation") {
            if(headers.authorization === process.env.ZOOM_VERIFICATION_TOKEN) {
                const hashForValidate = crypto.createHmac('sha256', "nnCgHzCgS0uSSEOAI8kEVw").update(body.payload.plainToken).digest('hex')
                let responseData = { plainToken: body.payload.plainToken, encryptedToken: hashForValidate }
                return h.response(responseData).code(200);
            }
        }

        if(body.event === "meeting.ended") {
            const meetingId = body.payload.object.id;
            const startDate = body.payload.object.start_time;
            const endDate = body.payload.object.end_time;
            const duration = (new Date(endDate).getTime() - new Date(startDate)) / 60000;
            await Models.ZoomMeeting.update({ startDate: startDate, endDate: endDate, duration: duration }, { where: { meetingId: meetingId } });
            let eventUrl = `${process.env.EVENT_MANAGEMENT_GATEWAY}/zoom-details`;
            
            let responseData = await axiosRequest(eventUrl,'POST', {}, { zoomId: meetingId,zoomDuration: duration.toString() });

            console.log(responseData, " ==================== response data")
        }
        
        if(body.event === "recording.completed") {
            const meetingId = body.payload.object.id;
            let downloadUrl = null;
            let videoUrl = null;

            for(let url of  body.payload.object.recording_files) {
                if(url.recording_type === "audio_only") {
                    downloadUrl = url.download_url;
                }
                if(url.recording_type === "shared_screen_with_speaker_view") {
                    videoUrl = url.download_url;
                }
            }


            if(videoUrl === null) {
                for(let url of  body.payload.object.recording_files) {
                    if(url.recording_type === "active_speaker") {
                        videoUrl = url.download_url;
                    } else if(url.recording_type === "gallery_view") {
                        videoUrl = url.download_url;
                    }
                }
            }

            let passcode = body.payload.object.password;
            
            await Models.ZoomMeeting.update({ attachmentObj: body }, { where: { meetingId: meetingId } });
            const payload = {
                meetingId: meetingId,
                downloadUrl: downloadUrl,
                videoUrl: videoUrl,
                passcode,
                token: body.download_token
            }

            let attachmentUrl = `${process.env.FILE_MANAGEMENT_GATEWAY}/zoom-attachment`;
            let responseData = await axiosRequest(attachmentUrl,'POST', {}, payload);
            console.log(responseData, " ============= recording for meeting id ============== ", meetingId);
        }

        if(body.event === "app_deauthorized") {
            console.log(" ======================= ")
            console.log(" ======================= ")
            console.log(" ======================= ")
            console.log(body.payload);
            console.log(" ======================= ")
            console.log(" ======================= ")
            console.log(" ======================= ")
        }

        return h.response({success:true,message:req.i18n.__('ZOOM_USER_DELETED_SUCCESSFULLY'),data:{}}).code(200);
    } catch (error) {
        console.log(error, "err in recording");
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG_WITH_RECORDING'),data:{}}).code(500);
    }
}

exports.updateAllRefreshToken = async(req, h) => {
    try {
        const allTokens = await Models.Token.findAll({ where: { type: zoomTokenType } });

        for(let item of allTokens) {
            const tokenData = await generateAccessToken(item.email);
        }

        return h.response({success:true,message:req.i18n.__('qwertyuiop'),data:{}}).code(200);
    } catch (error) {
        return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG_WITH_RECORDING'),data:{}}).code(500);
    }
}