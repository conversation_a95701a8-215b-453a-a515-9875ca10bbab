const Common= require('./common')

module.exports = {
    URL:
        {
            ATTACHMENT_UPDATE   :   `${process.env.ATTACHMENT_DOMAIN}/attachment/update`,
            SEND_NOTIFICATION   :   `${process.env.NOTIFICATION_DOMAIN}/notification`,
            ZOOM_CREATE         :   `${process.env.<PERSON><PERSON><PERSON>_DOMAIN}/meeting`,
            ZOOM_UPDATE         :   `${process.env.ZO<PERSON>_DOMAIN}/meeting`,
            ZOOM_DELETE         :   `${process.env.ZOOM_DOMAIN}/meeting`,
            SENDEMAIL           :   `${process.env.EMAIL_DOMAIN}/email/send`,
        },
    STATUS:{
        INACTIVE:0,
        ACTIVE:1
    },

ZO<PERSON>_PAYMENT_DURATION: 3,
MIN_PAYMENT_AMOUNT: 1,
REQUEST:{
ACCEPT:1,
REJACT:0
},
TYPE:{
    'DAY':1,
    'WeekDay':2,
    'Month':3,
    'Year':4
},
USER_TYPE:{
'OWNER':1,
'PARTICIPENT':2,
'GUEST':3,
},
USER_STATUS:{
'INVITED':10,
"ACCEPTED":1,
"REJACTED":0
},

EVENT_STATUS: {
    NOT_BOOKED: 0,
    BOOKED: 1,
    RESCHEDULED: 2,
    CANCELLED_WITH_REFUND: 3,
    CANCELLED_WITHOUT_REFUND: 4,
    NOT_JOINED: 5,
    COMPLETED_WITH_COMPLETED_PAYMENT: 6,
    COMPLETED_WITH_PENDING_PAYMENT: 7
},

PAGINATION_LIMIT:20,
    MAX_PAGINATION_LIMIT:20,
CRON:[
    {
        name: 'everyMinuteCrone',
        time: '*/38 * * * *',
        timezone: 'America/Danmarkshavn',
        request: {
        method: 'GET',
            url: '/cron/everyFiveMinutesCrone'
        },
        onComplete: async(res) => {

            console.log('-----^^^^^^ Every Minutes Crone Started ^^^^^^-----');
            await Common.EveryMinuteCrone()
            console.log('-----^^^^^^ Every Minutes Crone Finished ^^^^^^-----');
        }
    },
    {
        name: 'notJoinedCallsToHistory',
        time: '*/38 * * * *',
        timezone: 'America/Danmarkshavn',
        request: {
        method: 'GET',
            url: '/cron/notJoinedCallsToHistory'
        },
        onComplete: async(res) => {
            console.log('-----^^^^^^ notJoinedCallsToHistory Started ^^^^^^-----');
            await Common.notJoinedCallsToHistory();
            console.log('-----^^^^^^ notJoinedCallsToHistory Finished ^^^^^^-----');
        }
    },
    {
        name: 'meeting reminder',
        time: '*/18 * * * *',
        timezone: 'America/Danmarkshavn',
        request: {
        method: 'GET',
            url: '/cron/meetingReminder'
        },
        onComplete: async(res) => {
            console.log('-----^^^^^^ meetingReminder started ^^^^^^-----');
            await Common.meetingReminder();
            console.log('-----^^^^^^ meetingReminder Finished ^^^^^^-----');
        }
    },
    {
        name: 'payment reminder',
        time: '0 * * * *',
        timezone: 'America/Danmarkshavn',
        request: {
        method: 'GET',
            url: '/cron/paymentReminder'
        },
        onComplete: async(res) => {
            console.log('-----^^^^^^ meetingReminder started ^^^^^^-----');
            await Common.paymentReminder();
            console.log('-----^^^^^^ meetingReminder Finished ^^^^^^-----');
        }
    },
],
}