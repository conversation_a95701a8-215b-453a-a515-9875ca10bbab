exports.createGroupType = async (req,h) => {
    const transaction = await Models.sequelize.transaction();
    try{
        let groupType = req.payload.groupType.trim();
        let groupTypeCode = req.payload.groupType.trim().toLowerCase().replace(new RegExp(" ", "g"),'-');
        let doExists = await Models.GroupType.findOne({where:{groupTypeCode:groupTypeCode}});
        if(!doExists){
            let newGroupType = await Models.GroupType.create({
                groupType:groupType,
                groupTypeCode:groupTypeCode
            },{transaction:transaction});
            if(newGroupType){
                await transaction.commit();
                return h.response({responseData:newGroupType,message: req.i18n.__("GROUP_TYPE_CREATED_SUCCESSFULLY")}).code(200)

            }
            else{
                await transaction.rollback();
                return Common.generateError(req,400,'UNABLE_TO_CREATE_GROUP_TYPE',{});
            }
        }else{
            return Common.generateError(req,400,'GROUP_TYPE_ALREADY_EXISTS',{});
        }
    }catch(err){
        await transaction.rollback();
        return Common.generateError(req,500,'SOMETHING_WENT_WRONG_WITH_EXCEPTION',err);
    }
}

exports.createGroup = async (req,h) => {
    const transaction = await Models.sequelize.transaction();
    try{
        let languageId = LanguageIds[LanguageCodes.indexOf(req.headers.language)];
        let defaultLanguage=process.env.DEFAULT_LANGUANGE_CODE_ID;
        let AuthUser = 1
        //let AuthAccount = req.auth.credentials.userData.User.account_id;
        let groupName = req.payload.groupName.trim();
        let groupTypeId = req.payload.groupTypeId;
        let groupParentId = typeof req.payload.groupParentId!='undefined'?req.payload.groupParentId:null;
        let groupCode =  req.payload.groupName.trim().toLowerCase().replace(new RegExp(" ", "g"),'-');
        let defaultDefined = await Models.Group.findOne(
            {
                where:{
                    groupCode:groupCode,
                    groupTypeId:groupTypeId,
                    parentId:groupParentId
                },include:[{model:Models.GroupContent,where:{languageId:defaultLanguage},as:"mainContent"}]
            });
        doExists={};
        if(defaultLanguage!=languageId)
            doExists = await Models.Group.findOne({
                where:{
                    groupCode:groupCode,
                    groupTypeId:groupTypeId,
                    //account_id:AuthAccount,
                    parentId:groupParentId
                },include:[{model:Models.GroupContent,where:{languageId:languageId},as:"defaultContent"}]
            });
        else
            doExists = defaultDefined;
        if(!doExists){ 
            let Group = {};
            if(defaultDefined){
                Group = await Models.GroupContent.create({
                    languageId:languageId,
                    group_id:defaultDefined.id,
                    groupName:groupName
                },{transaction:transaction});

            }else{
                let newGroupContent=[];
                if(!defaultDefined && defaultLanguage!=languageId){
                    newGroupContent.push(
                        {
                            languageId:defaultLanguage,
                            groupName:groupName
                        }
                    );
                }
                newGroupContent.push(
                    {
                        languageId:languageId,
                        groupName:groupName
                    }
                );
                Group = await Models.Group.create({
                    groupCode:groupCode,
                    parentId:groupParentId,
                    groupTypeId:groupTypeId,
                    userId:AuthUser,
                    //account_id:AuthAccount,
                    status:Constants.STATUS.ACTIVE,
                    GroupContents:newGroupContent
                },{
                    include:[
                        {model:Models.GroupContent}
                    ],
                    transaction:transaction
                });
            }
            if(Group){
                await transaction.commit();
                return h.response({responseData:Group,message: req.i18n.__("GROUP_CREATED_SUCCESSFULLY")}).code(200)
            }else{
                await transaction.rollback();
                return Common.generateError(req,400,'UNABLE_TO_CREATE_GROUP',err);
            }
        }else{
            return Common.generateError(req,400,'GROUP_ALREADY_EXISTS',{});
        }
    }catch(err){
        await transaction.rollback();
        return Common.generateError(req,500,'SOMETHING_WENT_WRONG_WITH_EXCEPTION',err);
    }
}

const getGroupChildren=async(groupId)=>{
    let allChildren = [groupId];
    let groupChildren = await Models.Group.findAll({attributes:['id'],where:{parentId:groupId}});
    if(groupChildren.length){
        for(const child of groupChildren){
            allChildren = _.union(allChildren,await getGroupChildren(child.id));
        }
        return allChildren;
    }else{
        return [groupId];
    }
}

exports.updateGroup = async (req,h) => {
    const transaction = await Models.sequelize.transaction();
    try{
        let languageId = LanguageIds[LanguageCodes.indexOf(req.headers.language)];
        let defaultLanguage=process.env.DEFAULT_LANGUANGE_CODE_ID;
        let AuthUser =1;
       // let AuthAccount = req.auth.credentials.userData.User.account_id;
        let groupName = req.payload.groupName.trim();
        let groupId = req.payload.groupId;
        let groupTypeId = req.payload.groupTypeId;
        let groupParentId = req.payload.groupParentId;
        let groupCode =  req.payload.groupName.trim().toLowerCase().replace(new RegExp(" ", "g"),'-');
        let doExists = await Models.Group.findOne(
            {
                where:{
                    id:groupId,
                    groupTypeId:groupTypeId,
                    //account_id:AuthAccount
                },
                include:[
                    {model:Models.GroupContent,where:{languageId:languageId}}
                ]
            });
        if(doExists){ 
            let AllChild = await getGroupChildren(groupId);
            console.log(doExists);
            if(AllChild.indexOf[groupParentId]!=-1){
                await doExists.update({
                    parentId:groupParentId,
                    groupCode:groupCode,
                },{transaction:transaction});
                await doExists.GroupContents[0].update({groupName:groupName},{transaction:transaction})
                await transaction.commit();
                return h.response({responseData:doExists,message: req.i18n.__("GROUP_UPDATED_SUCCESSFULLY")}).code(200)
            }else{
                return Common.generateError(req,400,'CHILD_GROUP_CANNOT_BE_DEFINED_AS_PARENT',err);
            }
        }else{
            return Common.generateError(req,400,'GROUP_DOES_NOT_EXISTS',{});
        }
    }catch(err){
        await transaction.rollback();
        return Common.generateError(req,500,'SOMETHING_WENT_WRONG_WITH_EXCEPTION',err);
    }
}

const groupTree=async(groupId,languageId)=>{
    let defaultLanguage=process.env.DEFAULT_LANGUANGE_CODE_ID;
    let contentType = languageId==defaultLanguage?'defaultContent':'mainContent';
    let currentLevel = await Models.Group.findOne({where:{id:groupId},include:[{model:Models.GroupContent,where:{languageId:languageId},as:contentType}]});
    let AllChildren = await Models.Group.findAll({where:{parentId:groupId},include:[{model:Models.GroupContent,where:{languageId:languageId},as:contentType}]});
    if(AllChildren.length){
        currentLevel.dataValues.children=[];
        for(const group of AllChildren){
            currentLevel.dataValues.children.push(await groupTree(group.id,languageId));
        }
        return currentLevel;
    }else{
        return currentLevel;
    }
}

exports.getGroup = async(req,h)=>{
    try{
        let groupType = await Models.GroupType.findOne({where:{groupTypeCode:req.query.groupType}});
        let languageId = LanguageIds[LanguageCodes.indexOf(req.headers.language)];
        let defaultLanguage=process.env.DEFAULT_LANGUANGE_CODE_ID;
        let completeTree = req.query.completeTree;
        if(groupType){
            let groupTypeId = groupType.id;
            let groupId = typeof req.query.groupId!='undefined'?req.query.groupId:null;
            let groupParentId = typeof req.query.groupParentId!='undefined'?req.query.groupParentId:null;
            if(groupTypeId && !groupId && !groupParentId){
                if(completeTree){
                    let completeTree=[];
                    let groups = await Models.Group.findAll({
                        attributes:['id','groupTypeId','parentId','groupCode',[sequelize.fn('CONVERT_TZ', sequelize.col('createdAt'),'+00:00','+05:30'), 'created_at']],
                        where:{
                            groupTypeId:groupTypeId,
                            parentId:null
                        },
                        include:[
                            {
                                model:Models.GroupContent,
                                where:{languageId:languageId},
                                as:"mainContent",
                                required: false
                            },{
                                model:Models.GroupContent,
                                as:"defaultContent",
                                where:{languageId:defaultLanguage},
                                required: false,
                            }
                        ]
                    });
                    for(const group of groups){
                        completeTree.push(await groupTree(group.id,languageId))
                    }
                    return h.response({responseData:completeTree}).code(200)
                }else{
                    let groups = await Models.Group.findAll({
                       // attributes:['id','groupTypeId','parentId','groupCode',[Sequelize.fn('DATE_ADD', Sequelize.col('`Group`.`created_at`'),Sequelize.literal('INTERVAL '+utcOffset+' MINUTE')), 'created_at']],
                       attributes:['id','groupTypeId','parentId','groupCode',[Sequelize.fn('CONVERT_TZ', Sequelize.col('`Group`.`created_at`'),'UTC','Asia/Calcutta'), 'created_at']],
                       where:{
                            groupTypeId:groupTypeId,
                            parentId:null
                        },
                        include:[
                            {
                                model:Models.GroupContent,
                                where:{languageId:languageId},
                                as:"mainContent",
                                required: false
                            },{
                                model:Models.GroupContent,
                                as:"defaultContent",
                                where:{languageId:defaultLanguage},
                                required: false,
                            }
                        ]
                    });
                    return h.response({responseData:groups}).code(200);
                }
            }else if(groupId){
                let group = await Models.Group.findOne(
                    {
                        where:{
                            id:groupId
                        },
                        include:[
                            {
                                model:Models.GroupContent,
                                where:{languageId:languageId},
                                as:"mainContent",
                                required: false
                            },{
                                model:Models.GroupContent,
                                as:"defaultContent",
                                where:{languageId:defaultLanguage},
                                required: false,
                            }
                        ]
                    }
                );
                return h.response({responseData:group}).code(200);
            }else if(groupParentId){
                let groups = await Models.Group.findAll(
                    {
                        where:{
                            id:groupParentId
                        },
                        include:[
                            {
                                model:Models.Group, as:'subGroups',
                                include:[
                                    {
                                        model:Models.GroupContent,
                                        where:{languageId:languageId},
                                        as:"mainContent",
                                        required: false
                                    },{
                                        model:Models.GroupContent,
                                        as:"defaultContent",
                                        where:{languageId:defaultLanguage},
                                        required: false,
                                    }
                                ]
                            },
                            {
                                model:Models.GroupContent,
                                where:{languageId:languageId},
                                as:"mainContent",
                                required: false
                            },{
                                model:Models.GroupContent,
                                as:"defaultContent",
                                where:{languageId:defaultLanguage},
                                required: false,
                            }
                        ]}
                );
                return h.response({responseData:groups}).code(200);
            }else{
                return h.response({responseData:{}}).code(200);
            }
        }else{
            return Common.generateError(req,400,'GROUP_TYPE_DOES_NOT_EXISTS',err);
        }
    }catch(err){
        return Common.generateError(req,500,'SOMETHING_WENT_WRONG_WITH_EXCEPTION',err);
    }
}