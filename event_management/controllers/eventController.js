const { Op,QueryTypes, where } = require("sequelize");
const Constants = require("../constants");
const { RRule, rrulestr} = require('rrule');
const {messages}=require('../message')
const WebhookServices=require("../services/webhook")
const Zoom=require('../services/zoom')
const Common=require('../common')
const {decodeData}=require('../digistore24')
const Notification=require('../services/notification')

const moment = require('moment-timezone');
require("moment/min/locales");
// Create Events

const createNotification = async(data) => {
  try {
      const requestObj = {
          url : `${process.env.NOTIFICATION_DOMAIN}/notification`,
          data: data, method: "post", headers: {}
      }
      
      await Axios(requestObj).then(async(response)=>{ return response.data }).catch(async (error) => { return {} });
      return true
  } catch (error) {
      console.log(error)
      return
  }
}

const checkPendingInvoiceEvent = async(userId) => {
  try {
    let pendingInvoice = await Models.Invoice.findOne({where:{userId, isPaid:0}});
    if(!pendingInvoice) {
      return { success: false, message: "NO_INVOICE_PENDING", data: {} }
    }
    let responseData = {}

    const eventId = pendingInvoice.eventId;
    const eventInfo = await Models.Event.findOne({ where: { id: eventId } });
    const participants = await Models.Participants.findOne({ where: { eventId, role: "companion" } });
    const companionId = participants.userId;
    const userInfo = await Models.User.findOne({ where: { userId: companionId } });
    
    responseData["pendingInvoice"] = pendingInvoice;
    responseData["eventInfo"] = eventInfo;
    responseData["companionInfo"] = userInfo;
    
    return { success: true, message: "PLEASE_PAY_YOUR_INVOICE_FIRST", data: responseData, responseData: pendingInvoice }

  } catch (error) {
    console.log(error)
    return { success: true, message: "ERROR_IN_CATCH_BLOCK", data: {} }
  }
}

const checkForStudentLimit = async (userId, companionId) => {
  // let definedCount = await Models.Setting.findOne({ where: { key: "STUDENT_MEETING_COUNT" } });
  const role = await sequelize.query(
    `select u.id, ur.role_id roleId from userobd_users u INNER JOIN userobd_user_roles ur on ur.user_id = u.id where u.id = ${companionId};`,
    { type: QueryTypes.SELECT }
  );

  console.log(role, " ======================================== role")
  console.log(role, " ======================================== role")
  console.log(role, " ======================================== role")
  console.log(role, " ======================================== role")


  if (role && role.length > 0) {
    if(role[0].roleId == 3) {

      let definedCount = await Models.Setting.findOne({ where: { key: "STUDENT_MEETING_COUNT" } });
      if(definedCount) {
        definedCount = definedCount.value;
      } else {
        definedCount = null;
      }
      if(definedCount !== null) {
        const userValidationInfo = await sequelize.query(
          `select student_meeting_count from user_validations where user_id = ${userId};`,
          { type: QueryTypes.SELECT }
        );
  
        if(userValidationInfo && userValidationInfo.length > 0) {
          const studentMeetingCount = userValidationInfo[0].student_meeting_count;
          if(studentMeetingCount >= definedCount) return false;
        }
      }
    }
  }

  // if (role && role.length > 0) {
  //   let checkCount = role[0].student_meeting_count;
  //   if(checkCount > definedCount) {
  //     return false;
  //   }
  // }

  return true;
};

const updateStudentLimit = async (userId, companionId, transaction) => {
  const role = await sequelize.query(
    `select u.id, ur.role_id roleId from userobd_users u INNER JOIN userobd_user_roles ur on ur.user_id = u.id where u.id = ${companionId};`,
    { type: QueryTypes.SELECT }
  );

  if (role && role.length > 0) {
    if(role[0].roleId == 3) {
      await sequelize.query(
        `UPDATE user_validations SET student_meeting_count = student_meeting_count + 1 WHERE user_id = ${userId};`,
        { type: QueryTypes.UPDATE, transaction }
      );
    }
    if(role[0].roleId == 4) {
      await sequelize.query(
        `UPDATE user_validations SET companion_meeting_count = companion_meeting_count + 1 WHERE user_id = ${userId};`,
        { type: QueryTypes.UPDATE, transaction }
      );
    }
  }

  return true;
};

exports.create = async (req, h) => {
  const transaction = await Models.sequelize.transaction();
  try {
    let validData = await validate(req.payload);
    if(!(validData.status)) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__(validData.data),responseData: {}}).code(400)
    }
    const timezone = req.headers.timezone;
    let owner = req.auth.credentials.userData.User.id;
    let { 
      title, occurance, groupId, endDate, timings, description, link, duration,
      day, month, week, type, participant, remark, frequency, allDay, slots
    } = req.payload;
    let startDate = Moment.utc(req.payload.startDate);
    let email = null;
    
    if (endDate !== null) endDate = Moment.utc(endDate)
    if (day!==null && day.length===0) day = null
    if (week!==null && week.length===0) week = null
    if (month!==null && month.length===0) month = null
    if (frequency!==null && frequency.length===0) frequency = null
  
    let eventHandlers = [{ userId:owner, role:'owner'}];

    const pendingInvoiceStatus = await checkPendingInvoiceEvent(owner);
    console.log(" ================== pendingInvoiceStatus ====================== ", pendingInvoiceStatus)
    if(pendingInvoiceStatus?.success === true) {
      await transaction.rollback()
      return h.response({success: false,message: req.i18n.__("PLEASE_PAY_YOUR_INVOICE_FIRST"), responseData: pendingInvoiceStatus?.data}).code(400);
    }

    let schedule = [];
    let returnData = req.payload.returnData;
    let groupExist = await Models.Group.findOne({where: {id: groupId}});
    if (!groupExist) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("INVALID_GROUP_ID"),responseData: {}}).code(400);
    }
    
    if(timings!==null && timings.length>0){
    await timings.forEach((e, i) => {
      let obj={}
      if(e.hasOwnProperty("start")) obj['start'] = Moment.utc(e.start); 
      if(e.hasOwnProperty("end")) obj['end'] = Moment.utc(e.end);
      if(e.hasOwnProperty("startTime")) obj['startTime'] = e.startTime-utcOffset;
      if(e.hasOwnProperty("endTime")) obj['endTime'] = e.endTime-utcOffset; 
      if(e.hasOwnProperty("duration")) obj['duration'] = e.duration; 
      if(e.hasOwnProperty("timingType")) obj['timingType'] = e.timingType;
      if(req.headers.timezone) {
        obj['offset'] = req.headers.timezone;
      }
      schedule.push({...obj});
    });
  }
    if(type!==null) {
      if(frequency == null || frequency.length===0) {
        await transaction.rollback();
        return h.response({success: false, message: req.i18n.__("FREQUENCY_IS_REQUIRED"),responseData: {}}).code(400);
      }
    }


    let meetingProductId = null;
    let zoomConnectedAccount = null;
   // Event participents
   let validCompanionId = null;
    for (const iterator of participant) {
      let validId = await Models.User.findOne({where: {userId: iterator.userId}});
      if (!validId) {
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("INVALID_USER_ID"),responseData: {}}).code(400)
      }

      if(iterator.role == "companion") {
        meetingProductId = validId.meetingProductId;
        zoomConnectedAccount = validId.zoomAccount;
        validCompanionId = iterator.userId;
      }

      eventHandlers.push({ userId: iterator.userId,role:iterator.role });
    }

    let studentValidation = await checkForStudentLimit(owner, validCompanionId);
    if(studentValidation === false) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("STUDENT_MEETING_LIMIT_REACHED"),responseData: {}}).code(400)
    }


    if(meetingProductId === null) {
      await transaction.rollback();
      return h.response({ success: false, message: req.i18n.__("COMPANION_NEED_TO_SETUP_THE_DIGISTORE_ACCOUNT"), responseData: {}}).code(400)
    }
    if(zoomConnectedAccount !== 1) {
      await transaction.rollback();
      return h.response({ success: false, message: req.i18n.__("COMPANION_NEED_TO_SETUP_THE_ZOOM_ACCOUNT"), responseData: {}}).code(400)
    }

    let rrString=await generateRsting(startDate,endDate,type,frequency,day,week,month,occurance);
    if(!rrString.success) {
      await transaction.rollback();
      return h.response({ success: false, message: req.i18n.__(rrString.message), responseData: {}}).code(400)
    }
    let data={type,frequency,day,week,month,occurance,slots}
    
    let saveobj={ 
      timezone, data, groupId, startDate, endDate, title, duration, description, returnData, remark, rrString:rrString.data, link, slots, allDay, createdBy: owner, updatedBy: owner, prefferedLanguage: req.headers.language, Timings: [...schedule], Participants: [...eventHandlers]
    }

    let saveRecord = await Models.Event.create(
      {...saveobj},
      {include: [{model: Models.Participants,as: "Participants"},{model: Models.Timing}],transaction}
    );
    if (!saveRecord) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("ERROR_IN_SAVING_EVENT"),responseData: {}}).code(400);
    }
    let res=await createFrequncies(saveRecord.dataValues.id,type,frequency,day,week,month,occurance,transaction);
    if(!res.status){
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__(res.message),responseData: {}}).code(400);
    }
    if(occurance!==null) {
      const rule=RRule.fromString(rrString.data);
      let oc=rule.all();
      await saveRecord.update({endDate:oc[oc.length-1]},{transaction});
    }
    let meetingPrice=0
    for (const iterator of eventHandlers) {
      let userData=await Models.User.findOne({where:{userId:iterator.userId}})
      let notificationData={
        replacements:{
          start_date:startDate,
          user_name:userData.firstName+' '+userData?.lastName
        },
        typeId:1,
        data:{eventId:saveRecord.dataValues.id},
        userId:iterator.userId 
      }
      if(iterator.userId!=req.auth.credentials.userData.User.id) {
        // find email and update in to 
        let user=await Models.User.findOne({where:{userId:iterator.userId}})
        email        = user.dataValues.email
        meetingPrice =  user.dataValues.meetingPrice
      }
      //email
      // await WebhookServices.sendNotification(notificationData,transaction);
    }

    // generating payment LInk ------
    if(groupId==2) {
      let chargeAmount=parseFloat(Constants.ZOOM_PAYMENT_DURATION * meetingPrice).toFixed(2)
      // let keyValue=await Models.Setting.findOne({where:{key:'ZOOM_MEETING_PRODUCT_ID'},attributes:['value'],raw:true,nest:true})

      // if(!keyValue) {
      //   await transaction.rollback()
      //   return h.response({success: false,message: req.i18n.__("SOMETING_WRONG_IN_ADMIN_CONFIGRATION"),responseData:{}}).code(400);
      // }

      // let ZOOM_MEETING_PRODUCT_ID=keyValue.value
      console.log(`https://www.digistore24.com/api/call/createBuyUrl/?product_id=${meetingProductId}&payment_plan[first_amount]=${chargeAmount}&valid_until=forever&tracking[custom]=${saveRecord.dataValues.id}&tracking[type]=booking`)
      let data =  await Common.DgStoreRequest(`https://www.digistore24.com/api/call/createBuyUrl/?product_id=${meetingProductId}&payment_plan[first_amount]=${chargeAmount}&valid_until=forever&tracking[custom]=${saveRecord.dataValues.id},booking`)


      console.log(data, " ================= payment link")


      let expireDate = null;
      if(data?.data?.valid_until) {
        expireDate = Moment.utc(data.data.valid_until)
      } else {
        expireDate = startDate
      }
      let url = data.data.url
      await saveRecord.update({paymentLink:url,orderId:data.data.id,expireDate,amount:chargeAmount, totalAmount:chargeAmount},{transaction})

      await Models.Invoice.create({eventId: saveRecord.dataValues.id,paymentLink: url,userId: owner,amount:chargeAmount.toString(), isPaid: 2},{transaction});
    }

    await transaction.commit();

    let eventId= saveRecord.dataValues.id;
    let response= await Models.Event.findOne({where:{id:eventId},include:[
      {model: Models.Participants,as: "Participants"},
      {model: Models.Timing},
      {model: Models.Frequency}
    ]})
    return h.response({success: true,message: req.i18n.__(messages[groupId]["created"]),responseData: response,}).code(200);
  } catch (err) {
    console.log("error", err);
    await transaction.rollback();
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {},}).code(500);
  }
};

exports.testDgstore = async(req,h) => {
  try {

    let event1509 =  await Common.DgStoreRequest(`https://www.digistore24.com/api/call/createBuyUrl/?product_id=${555958}&payment_plan[first_amount]=${182.54}&valid_until=forever&tracking[custom]=${1509},complete-payment`)

    let event1548 =  await Common.DgStoreRequest(`https://www.digistore24.com/api/call/createBuyUrl/?product_id=${521743}&payment_plan[first_amount]=${23.32}&valid_until=forever&tracking[custom]=${1548},complete-payment`)

    let event1655 =  await Common.DgStoreRequest(`https://www.digistore24.com/api/call/createBuyUrl/?product_id=${555963}&payment_plan[first_amount]=${306.72}&valid_until=forever&tracking[custom]=${1655},complete-payment`)

    let event2264 =  await Common.DgStoreRequest(`https://www.digistore24.com/api/call/createBuyUrl/?product_id=${555963}&payment_plan[first_amount]=${15.97}&valid_until=forever&tracking[custom]=${2264},complete-payment`)

    let event2326 =  await Common.DgStoreRequest(`https://www.digistore24.com/api/call/createBuyUrl/?product_id=${555960}&payment_plan[first_amount]=${217.32}&valid_until=forever&tracking[custom]=${2326},complete-payment`)

    return h.response({success: true,message: "REQUEST_SUCCESSFULL",responseData: {
      event1509, event1548, event1655, event2264, event2326
    }}).code(200);

  } catch (error) {
    console.log(error)
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {},}).code(500);
  }

}

// Event create in bulk
exports.bulkCreate =  async(req,h)  =>{
  const transaction   = await Models.sequelize.transaction();
  try{
    console.log(req.headers.utcoffset, " =================== req.headers.offset")
    let {data}          =  req.payload;
    let creationObject  = [];
    let owner         = req.auth.credentials.userData.User.id;
    let slotsAvailableEmail = false;;

    if(data && data.length < 1) {      
      await Models.Event.destroy({ where: { createdBy: owner, isCustomSlot: 1 }, transaction });
    }

    for(const item of data) {
      if(item.isCustomSlot === 1) {
        await Models.Event.destroy({ where: { createdBy: owner, isCustomSlot: 1 }, transaction });
        break;
      }
    }


    for (const item of data) {
      let title         = item.title;
      let occurance     = item.occurance;
      let groupId       = item.groupId;
      let startDate     = Moment.utc(item.startDate);
      let endDate       = item.endDate;
      let timings       = item.timings;
      let description   = item.description;
      let link          = item.link;
      let duration      = item.duration;
      let day           = item.day
      let month         = item.month
      let week          = item.week
      let type          = item.type;
      let participant   = item.participant;
      let remark        = item.remark;
      let frequency     = item.frequency;
      let allDay        = item.allDay;
      let slots         = item.slots;
      let isCustomSlot = item.isCustomSlot

      if (endDate !== null)                   endDate                     = Moment.utc(endDate);
      if (day!==null && day.length===0)       day                         = null
      if (week!==null && week.length===0)     week                        = null
      if (month!==null && month.length===0)   month                       = null
      if (frequency!==null && frequency.length===0)frequency              = null
   
    
      let eventHandlers   = [{userId:owner,role:'owner'}];
      let schedule        = [];
      let returnData      = item.returnData;
      let groupExist      = await Models.Group.findOne({where: {id: groupId}});
      if (!groupExist) {
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("INVALID_GROUP_ID"),responseData:{}}).code(400);
      }
    

      if(timings!==null && timings.length>0){
        await timings.forEach((e, i) => {
          let obj={}
          if(e.hasOwnProperty("start"))       obj['start']      = e.start; 
          if(e.hasOwnProperty("end"))         obj['end']        = e.end;
          if(e.hasOwnProperty("startTime"))   obj['startTime']  = e.startTime;
          if(e.hasOwnProperty("endTime"))     obj['endTime']    = e.endTime; 
          // if(e.hasOwnProperty("startTime"))   obj['startTime']  = e.startTime-utcOffset;
          // if(e.hasOwnProperty("endTime"))     obj['endTime']    = e.endTime-utcOffset; 
          if(e.hasOwnProperty("duration"))    obj['duration']   = e.duration; 
          if(e.hasOwnProperty('timingType')) obj['timingType']  = e.timingType;
          if(req.headers.timezone) {
            obj['offset'] = req.headers.timezone;
          }
          schedule.push({...obj});
        });
      }
      if(type!==null) {
        if(frequency == null || frequency.length===0)
        {
          await transaction.rollback();
          return h.response({success: false, message: req.i18n.__("FREQUENCY_IS_REQUIRED"),responseData: {}}).code(400);
        }
      }
  
      // Event participents
      for (const iterator of participant) {
        let validId = await Models.User.findOne({where: {userId: iterator.userId}});
        if (!validId) {
          await transaction.rollback();
          return h.response({success: false,message: req.i18n.__("INVALID_USER_ID"),responseData: {}}).code(400)
        }
        eventHandlers.push({ userId: iterator.userId,role:iterator.role });
      }
      let rrString=await generateRsting(startDate,endDate,type,frequency,day,week,month,occurance);
      if(!rrString.success) {
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__(rrString.message),responseData: {}}).code(400)
      }
      let data={type,frequency,day,week,month,occurance,slots}
      let saveobj={
        data, groupId, startDate, endDate, title, duration, isCustomSlot,
        description, returnData, remark, rrString:rrString.data,
        link, slots, allDay, createdBy: owner, updatedBy: owner,
        Timings: [...schedule], Participants: [...eventHandlers]
      }
      let saveRecord = await Models.Event.create({...saveobj},
        {
          include: [
            {
              model: Models.Participants,
              as: "Participants",
            },
            { model: Models.Timing  }
          ],
          transaction,
        }
      );
      if (!saveRecord) {
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("ERROR_IN_SAVING_EVENT"),responseData: {}}).code(400);
      }
    
      let res = await createFrequncies(saveRecord.dataValues.id,type,frequency,day,week,month,occurance,transaction);
      if(!res.status){
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__(res.message),responseData: {}}).code(400);
      }
      if(occurance!==null) {
        const rule=RRule.fromString(rrString.data);
        let oc=rule.all();
        await saveRecord.update({endDate:oc[oc.length-1]},{transaction});
      }
      creationObject.push(saveRecord.dataValues);

      console.log(returnData.event, " ==================== returnData")
      console.log(returnData && returnData.event && returnData.event.length > 0, " ==================== returnData && returnData.event && returnData.event.lenght > 0")
      // [ { type: 1, isCustomSlot: 1, timings: [ [Object] ], slots: 2 } ] 
      if(returnData && returnData.event && returnData.event.length > 0) {
        console.log(" ertyuioiuytr ")
        for(let newItem of returnData.event) {
          if(newItem.slots > 0) {
            console.log(newItem.slots, " newItem.slots =========================== ")
            slotsAvailableEmail = true;
            break;
          }
        }
      }

    }
    await transaction.commit();

    console.log(slotsAvailableEmail, " ============================= EventSlotReminder")
    console.log(slotsAvailableEmail, " ============================= EventSlotReminder")
    console.log(slotsAvailableEmail, " ============================= EventSlotReminder")
    console.log(slotsAvailableEmail, " ============================= EventSlotReminder")
    console.log(slotsAvailableEmail, " ============================= EventSlotReminder")
    console.log(slotsAvailableEmail, " ============================= EventSlotReminder")
    console.log(slotsAvailableEmail, " ============================= EventSlotReminder")

    if(slotsAvailableEmail === true) {
      let allRec = await Models.SlotReminder.findAll({ where: { companionId: owner } });
      const companionInfo = await Models.User.findOne({ where: { userId: owner } }); 
      for(let item of allRec) {
        const userInfo = await Models.User.findOne({ where: { userId: item.userId } });

        const replacements = { 
          companion: companionInfo?.firstName + " " + companionInfo?.lastName,
          name: userInfo.firstName, 
          link: `${process.env.PORTAL_DOMAIN_URL}dashboard/kuby-companion?tabstatus=professionals&companionId=${owner}&type=2`
        }
        const code = "SLOTS_AVAILABLE";
        const recipients = [userInfo.email];
        const language = req.headers.language;
        const sentEmail = await Notification.sendEmail(replacements,code,recipients,null, language)
        await Models.SlotReminder.destroy({ where: {id: item.id} })
      }
    }

    return h.response({success: true,message: req.i18n.__(messages["1"]["created"]),responseData:{events:creationObject}}).code(200);
  }
  catch(error){
    console.error('Error in event bulk creation:-',error);
    await transaction.rollback();
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
  }
}


//update Events
exports.update = async (req, h) => {
  const transaction = await Models.sequelize.transaction();
  try {
    
    let id = req.payload.id;
    let returnData=req.payload.returnData;
    let userId=req.auth.credentials.userData.User.id;
    let updatedBy=req.auth.credentials.userData.User.id
    let createdBy=req.auth.credentials.userData.User.id
    let record=await Models.Event.findOne({where:{id}});
    if(!record)
    {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("RECORD_NOT_FOUND"),responseData: {}}).code(400);}
    let clinicId=req.payload.clinicId;

    let createClinic={}
    if(req.payload.clinicName!==null)     createClinic.clinicName=req.payload.clinicName
    if(req.payload.clinicAddress!==null)  createClinic.clinicAddress=req.payload.clinicAddress
    if(req.payload.clinicContact!==null)  createClinic.clinicContact=req.payload.clinicContact;
    if(req.payload.vetName!==null)        createClinic.vetName=req.payload.vetName
    let title = req.payload.title;
    let groupId = req.payload.groupId;
    let startDate = Moment.utc(req.payload.startDate);
    let endDate = req.payload.endDate;
    let timings = req.payload.timings
    let description = req.payload.description;
    let link = req.payload.link;
    let day=req.payload.day
    let month=req.payload.month
    let week= req.payload.week
    let duration=req.payload.duration
    let type = req.payload.type;
    let participant = req.payload.participant;
    let attachment = req.payload.attachment;
    let remark = req.payload.remark;
    let occurance=req.payload.occurance;
    let frequency=req.payload.frequency;
    let allDay=req.payload.allDay;
    let option=req.payload.option
    let eventHandlers = [{
      userId:userId,
      role:'owner',
    }];
    if (day!==null && day.length===0)day=null
    if (week!==null && week.length===0)week=null
    if (month!==null && month.length===0)month=null
    if (frequency!==null && frequency.length===0)frequency=null
    let schedule = [];
    let groupExist = await Models.Group.findOne({where: {id: groupId}});
    if (!groupExist) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("INVALID_GROUP_ID"),responseData: {}}).code(400);
    }
    // chnage all timing to Moment
    if (endDate !== null) endDate = Moment.utc(endDate); else null 
    if(timings!==null && timings.length>0){
      await timings.forEach((e, i) => {
        let obj={}
        if(e.hasOwnProperty("start")) obj['start']=Moment.utc(e.start); 
        if(e.hasOwnProperty("end")) obj['end']=Moment.utc(e.end);
        if(e.hasOwnProperty("startTime")) obj['startTime']=e.startTime-utcOffset;
        if(e.hasOwnProperty("endTime")) obj['endTime']=e.endTime-utcOffset; 
        if(e.hasOwnProperty("duration")) obj['duration']=e.duration; 
        if(e.hasOwnProperty('timingType')) obj['timingType']  = e.timingType;
        if(req.headers.timezone) {
          obj['offset'] = req.headers.timezone;
        }
        schedule.push({...obj});
      });
    }
  
    if(type!==null){
      if(frequency===null)
      {await transaction.rollback();return h.response({success: false,message: req.i18n.__("FREQUENCY_IS_REQUIRED"),responseData: {}}).code(400)}}
   // Event Paricioents
   if(participant!==null )
    for (const iterator of participant) {
      let validId = await Models.User.findOne({where: {userId: iterator.userId}});
      if (!validId) {
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("INVALID_USER_ID"),responseData: {},}).code(400)}
        eventHandlers.push({ userId: iterator.userId,role:iterator.role });
    }

    
switch (option) {
  case 1: //update only today occurance
    {
      let old_event_type=await Models.Frequency.findOne({where:{eventId:record.dataValues.id},transaction});
     
      let oldEventOcc=_.cloneDeep(record.dataValues.data.occurance);
      let oldEventData=_.cloneDeep(record.dataValues.data);
      let newEventData=_.cloneDeep(record.dataValues.data);
      let newEventOcc=null;
      let nwRstring={}
      if(!old_event_type)
      {
        //update Existing Event and create all records here only------
        let updates={
          title,
          description,
          allDay,
          groupId,
          startDate,
          endDate,
          createdBy,
          updatedBy,
          returnData,
          clinicId
        }
        await record.update(updates,{transaction});
        await Models.Attachment.destroy({where:{eventId:record.dataValues.id}});
        await Models.Timing.destroy({where:{eventId:record.dataValues.id}});
        await Models.Participants.destroy({where:{eventId:record.dataValues.id}});
        await Models.EventPet.destroy({where:{eventId:record.dataValues.id}});
        await Models.EventClinic.destroy({where:{eventId:record.dataValues.id}});
      //  if(createClinic!=={})
      //  {
      //   await Models.EventClinic.create({...createClinic,eventId:record.dataValues.id},{transaction});
      //   for (const i of eventHandlers) 
      //     await Models.Participants.create({...i,eventId:record.dataValues.id},{transaction});
      //   for (const i of paricipentPets) 
      //   await Models.EventPet.create({...i,eventId:record.dataValues.id},{transaction});
      //   for (const i of eventAttachments) 
      //   await Models.Attachment.create({...i,eventId:record.dataValues.id},{transaction})
      //  }
       await transaction.commit();
       let eve= await Models.Event.findOne({where:{id:record.dataValues.id},include:[
        {model: Models.Participants,as: "Participants",attributes: ['user_id','type']},
        {model: Models.Timing,attributes: ['start_time','end_time','start','end','rr_string']},
        {model: Models.Attachment,attributes:['attachment']},
        {model:Models.EventLog,attributes:['createdBy','timingId','eventDate']},
        {model: Models.Frequency,attributes:['frequency','type','day','week','month','occurance']},
        {model:Models.EventClinic,attributes:['clinic_name','clinic_address','clinic_contact','vet_name']},
        {model:Models.Clinic,attributes:['clinic_id'],as:'Clinic_id'},
        { model:Models.EventPet,attributes:['pet_id'],include:[{model:Models.Pet,attributes:['petId','data']}]}
      ]})
      return h.response({success:true,message: req.i18n.__("SUCESSFULLY_UPDATED"),responseData:eve}).code(200)
      }
      startDate=startDate.add(old_event_type.dataValues.frequency*old_event_type.dataValues.type,'d');
      if(oldEventOcc!==null)
      {
        
        const rule = RRule.fromString(record.dataValues.rrString);
        let endDateOldEvent=_.cloneDeep(startDate);
        endDateOldEvent=Moment.utc(endDateOldEvent).add(-1,'d');
      let occ=rule.between(Moment.utc(record.dataValues.startDate).toDate(),endDateOldEvent.toDate());
      newEventOcc=oldEventOcc-(occ.length+2);//-1 for on that day
      oldEventOcc=(occ.length+2)-1;
      oldEventData.occurance=oldEventOcc;
      newEventData.occurance=newEventOcc;
      }
      nwRstring=await generateRsting(startDate,record.dataValues.endDate,record.dataValues.data.type,record.dataValues.data.frequency,record.dataValues.data.day,record.dataValues.data.week,record.dataValues.data.month,newEventOcc);
      if(!nwRstring.success)
      {
    await transaction.rollback();
    return h.response({success: false,message: req.i18n.__(nwRstring.message),responseData: {}}).code(400)
      }
    
    let newEvent=await Models.Event.create(
      {
            groupId:record.dataValues.groupId,
            startDate,
            rrString:nwRstring.data,
            endDate:record.dataValues.endDate,
            title:record.dataValues.title,
            duration:record.dataValues.duration,
            description:record.dataValues.description,
            remark:record.dataValues.remark,
            link:record.dataValues.link,
            returnData,
            allDay:record.dataValues.allDay,
            clinicId:record.dataValues.clinicId,
            createdBy:userId,
            updatedBy: userId,
            data:newEventData,
            EventPets:[...paricipentPets],
            EventClinic:{...createClinic}
      },
      {transaction}
    )
    // set frequncy
    let res=await createFrequncies(newEvent.dataValues.id,record.dataValues.data.type,record.dataValues.data.frequency,record.dataValues.data.day,record.dataValues.data.week,record.dataValues.data.month,newEventOcc,transaction);
    if(!res.status){
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__(res.message),responseData: {}}).code(400);
    }
    // timing 
    let oldTimings=await Models.Timing.findAll({where:{eventId:record.dataValues.id}});
    for (const iterator of oldTimings) {
      await Models.Timing.create({
        eventId:newEvent.dataValues.id,
        start:iterator.dataValues.start,
        end:iterator.dataValues.end,
        duration:iterator.dataValues.duration,
        timingType:iterator.dataValues.timingType,
      },{transaction})
    }
    
   
    //participents
    let oldParticipents= await Models.Participants.findAll({where:{eventId:record.dataValues.id}});
    for (const iterator of oldParticipents) {
      await Models.Participants.create({
        eventId:newEvent.dataValues.id,
        userId:iterator.dataValues.userId,
        type:iterator.dataValues.type,
        status:iterator.dataValues.status,
        role:iterator.dataValues.role
      },{transaction})
    }
    
    // calculate old Event rrString
    let oldEventRrString=await generateRsting(startDate,record.dataValues.endDate,record.dataValues.data.type,record.dataValues.data.frequency,record.dataValues.data.day,record.dataValues.data.week,record.dataValues.data.month,oldEventOcc);
      if(!oldEventRrString.success)
      {
    await transaction.rollback();
    return h.response({
    success: false,
    message: req.i18n.__(oldEventRrString.message),
    responseData: {},
    }).code(400)
      }
      if(record.dataValues.rrString===null)
      {
        await record.destroy();
        // retun new event details
      }
      else
        await record.update({endDate:startDate.add(-2,'d'),rrString:oldEventRrString.data,data:oldEventData},{transaction});
      
        let onceSaveobj={
      groupId,
      startDate:startDate.add(1,'d'),
      endDate:startDate,
      title,
      duration,
      rrString:'',
      description,
      remark,
      link,
      returnData,
      allDay,
      clinicId,
      createdBy:userId,
      updatedBy: userId,
      Timings: [...schedule],
      Attachments: [...eventAttachments],
      Participants: [...eventHandlers],
      EventPets:[...paricipentPets],
      EventClinic:{...createClinic}
        }
        let onceEvent = await Models.Event.create(
        {
        ...onceSaveobj
        },
        {include: [{model: Models.Participants,as: "Participants"},
          {model: Models.Timing},
          {model: Models.Attachment}],transaction})
        await transaction.commit();
        return h.response({sucess:true,message:req.i18n.__(messages[groupId]["updated"]),responseData:onceEvent}).code(200);
        }
        break;
  case 2:{
       // update all upcoming ocuurance
      let old_event_type=await Models.Frequency.findOne({where:{eventId:record.dataValues.id},transaction});
      let oldEventOcc=_.cloneDeep(record.dataValues.data.occurance);
      let oldEventData=_.cloneDeep(record.dataValues.data);
        oldEventEndDate=Moment.utc(_.cloneDeep(startDate));
        oldEventEndDate=oldEventEndDate.add(-1,'d');
        // record old event 
    // if(Moment.utc(record.dataValues.startDate).diff(startDate,'days')===0)
    // {
    //   // update previous event with new data
    //   await record.update({endDate:endDate,startDate:startDate,rrString:updatedOldrrString.data,data:oldEventData},{transaction});
    // }
       // on same day s
      // if(occurance!==null)
      // {
      //   occurance=record.dataValues.data.occurance;
        
      //   let newocc=record.dataValues.data.occurance-occ.length;
      //   oldEventOcc=occurance-newocc;
      //   occurance=newocc;
      //   oldEventOcc=newocc;
      //   newEventData.occurance=occurance;
      //   oldEventData.occurance=oldEventOcc;
      // }
if(record.dataValues.data.occurance!==null)
{
  const rule = RRule.fromString(record.dataValues.rrString);
  let eDate=Moment.utc(_.cloneDeep(startDate));
  let occ=rule.between(Moment.utc(record.dataValues.startDate).toDate(),eDate.toDate())
  oldEventOcc=(occ.length+2)-1;
  oldEventData.occurance=oldEventOcc;
  await Models.Frequency.update({occurance:oldEventOcc},{where:{eventId:record.dataValues.id}},{transaction});
}

    let nwRstring=await generateRsting(startDate,record.dataValues.endDate,record.dataValues.data.type,record.dataValues.data.frequency,record.dataValues.data.day,record.dataValues.data.week,record.dataValues.data.month,occurance);
    if(!nwRstring.success)
    {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__(rrString.message),responseData: {}}).code(400)
    }
    if(occurance!==null)
    {
      nwRstring=await generateRsting(startDate,null,record.dataValues.data.type,record.dataValues.data.frequency,record.dataValues.data.day,record.dataValues.data.week,record.dataValues.data.month,occurance);
    if(!nwRstring.success)
    {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__(rrString.message),responseData: {}}).code(400)
    }
      const rule=RRule.fromString(nwRstring.data);
      let oc=rule.all();
      oc=oc[oc.length-1]
      endDate=oc
    } 
    else{
      endDate=record.dataValues.endDate;
    }
      let newEvent= await Models.Event.create(
        {
                groupId,
                startDate,
                rrString:nwRstring.data,
                endDate,
                title,
                returnData,
                duration,
                description,
                remark,
                link,
                clinicId,
                allDay,
                createdBy:userId,
                updatedBy:userId,
                Timings: [...schedule],
                Attachments: [...eventAttachments],
                Participants: [...eventHandlers],
                data:{type,day,week,month,occurance,frequency}
        },{
          include: [
            {
              model: Models.Participants,
              as: "Participants",
            },
            {
              model: Models.Timing,
            }
          ],
          transaction,
        }
      );
      if (!newEvent) {
        await transaction.rollback();
        return h
          .response({
            success: false,
            message: req.i18n.__("ERROR_IN_SAVING_EVENT"),
            responseData: {},
          })
          .code(400);
      }
      let eventId=newEvent.dataValues.id;
      let res=await createFrequncies(eventId,type,frequency,day,week,month,occurance,transaction);
      if(!res.status)
      {
        await transaction.rollback();
        return h.response({sucess:true,message:req.i18n.__(res.message),responseData:{}}).code(400)
      }
      //create new r string for old event
      let updatedOldrrString=null;
      if(record.dataValues.rrString!==null && record.dataValues.rrString!=='')
      updatedOldrrString=await generateRsting(record.dataValues.startDate,oldEventEndDate,oldEventData.type,oldEventData.frequency,oldEventData.day,oldEventData.week,oldEventData.month,oldEventOcc);
      if(updatedOldrrString!==null && updatedOldrrString.success===false)
      {
        await transaction.rollback();
        return h.response({sucess:true,message:req.i18n.__("ERROR_IN_CREATION_OF_RRULE_STRING"),responseData:response}).code(400)
      }
      if(updatedOldrrString==null)
      {
        updatedOldrrString={
          data:null
        }
      }
     
    
      await record.update({endDate:oldEventEndDate,rrString:updatedOldrrString.data,data:oldEventData},{transaction});
      await transaction.commit();
      return h.response({sucess:true,message:req.i18n.__("SUCESSFULLY_UPDATED"),responseData:newEvent}).code(200)
  }
  case 3:{
    let validData=await validate(req.payload);
    if(!(validData.status))
    {
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__(validData.data),responseData: {}}).code(400)
    }
    let title = req.payload.title;
    let owner=req.auth.credentials.userData.User.id;
    let occurance=req.payload.occurance;
    let groupId = req.payload.groupId;
    let startDate = Moment.utc(req.payload.startDate);
    let endDate = req.payload.endDate;
    let timings = req.payload.timings;
    let description = req.payload.description;
    let link = req.payload.link;
    let duration=req.payload.duration;
    let day=req.payload.day
    let month=req.payload.month
    let week= req.payload.week
    let type = req.payload.type;
    let participant = req.payload.participant;
    let attachment = req.payload.attachment;
    let remark = req.payload.remark;
    let frequency=req.payload.frequency;
    let allDay=req.payload.allDay;
    let pet=req.payload.pet;
    let paricipentPets=[];
    let slots=req.payload.slots;
    
    if (endDate !== null) endDate = Moment.utc(endDate);
    if (day!==null && day.length===0)day=null
    if (week!==null && week.length===0)week=null
    if (month!==null && month.length===0)month=null
    if (frequency!==null && frequency.length===0)frequency=null
    if (pet!==null && pet.length===0)pet=null
    
    let eventHandlers = [
      {
        userId:owner,
        role:'owner',
      }
    ];
    
    let schedule = [];
    let returnData=req.payload.returnData;
    let groupExist = await Models.Group.findOne({where: {id: groupId}});
    if (!groupExist) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("INVALID_GROUP_ID"),responseData: {},}).code(400);
    }
    //for appointments
    // if(groupExist.dataValues.groupCode==='appointment')
    // {
    //   if(clinicId!==null)
    //     {
    //       let exist=await Models.Clinic.findOne({where:{clinicId:clinicId}});
    //       if(!exist)
    //         {
    //         await transaction.rollback();
    //         return h.response({success: false,message: req.i18n.__("INVALID_CLINIC_ID"),responseData: {}}).code(400)
    //         }
    //         let result=await validAppointment(clinicId,startDate,endDate,groupId);
    //         if(!result)
    //         {
    //           await transaction.rollback();
    //           return h.response({success: false,message: req.i18n.__("NOT_A_SCHEDULE"),responseData: {}}).code(400)
    //         }
    //     }
    // }
    // chnage all timing to Moment
    if(timings!==null && timings.length>0){
    await timings.forEach((e, i) => {
      let obj={}
      if(e.hasOwnProperty("start")) obj['start']=Moment.utc(e.start); 
      if(e.hasOwnProperty("end")) obj['end']=Moment.utc(e.end);
      if(e.hasOwnProperty("startTime")) obj['startTime']=e.startTime-utcOffset;
      if(e.hasOwnProperty("endTime")) obj['endTime']=e.endTime-utcOffset; 
      if(e.hasOwnProperty("duration")) obj['duration']=e.duration; 
      if(e.hasOwnProperty("timingType")) obj['timingType']=e.timingType;
      if(req.headers.timezone) {
        obj['offset'] = req.headers.timezone;
      }
      schedule.push({...obj});
    });
  }
    if(type!==null)
    {
      if(frequency == null || frequency.length===0)
      {
        await transaction.rollback();
        return h.response({success: false, message: req.i18n.__("FREQUENCY_IS_REQUIRED"),responseData: {}}).code(400);
      }

  }
    let rrString=await generateRsting(startDate,endDate,type,frequency,day,week,month,occurance);
    if(!rrString.success)
    {
      await transaction.rollback();
      return h.response({
        success: false,
        message: req.i18n.__(rrString.message),
        responseData: {},
      }).code(400)
    }
    let data={type,frequency,day,week,month,occurance,slots}
    let saveobj={
      data,
      groupId,
      startDate,
      endDate,
      title,
      duration,
      description,
      returnData,
      remark,
      rrString:rrString.data,
      link,
      clinicId,
      slots,
      allDay,
      createdBy: owner,
      updatedBy: owner,
      Timings: [...schedule],
      Participants: [...eventHandlers]
    }
    let saveRecord = await Models.Event.create(
      {
        ...saveobj
      },
      {
        include: [
          {
            model: Models.Participants,
            as: "Participants",
          },
          {
            model: Models.Timing,
          }
        ],
        transaction,
      }
    );
    if (!saveRecord) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("ERROR_IN_SAVING_EVENT"),responseData: {}}).code(400);
    }
    let res=await createFrequncies(saveRecord.dataValues.id,type,frequency,day,week,month,occurance,transaction);
    if(!res.status){
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__(res.message),responseData: {}}).code(400);
    }
    if(occurance!==null)
    {
      const rule=RRule.fromString(rrString.data);
    let oc=rule.all();
      await saveRecord.update({endDate:oc[oc.length-1]},{transaction});
    }
    // delete old event
    await Models.EventLog.destroy({
      where: {
        eventId: id,
      },
    });
    await Models.Participants.destroy({
      where: {
        eventId: id,
      },
    });
   
    await Models.Timing.destroy({
      where: {
        eventId: id,
      },
    });
    await Models.Frequency.destroy({
      where:{
        eventId:id
      }});
    await record.destroy();
    await transaction.commit();

    let eventId= saveRecord.dataValues.id;
    let response= await Models.Event.findOne({where:{id:eventId},include:[
      {model: Models.Participants,as: "Participants"},
      {model: Models.Timing}
    ]})
    return h.response({success: true,message: req.i18n.__(messages[groupId]["updated"]),responseData: response,}).code(200);
  }
  default:{
    await transaction.rollback();
    return h.response({success: false,message: req.i18n.__("WRONG_OPTION_CODE"),responseData: {}}).code(400)
  }
}
  } catch (err) {
    console.log("error", err);
    await transaction.rollback();
    return h
      .response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
};


// Bulk Update
exports.bulkUpdate=async(req,h)=>{
  const transaction   = await Models.sequelize.transaction();
  try{
    const createdBy=req.auth.credentials.userData.User.id;
    await Models.Event.destroy({where:{groupId:1,createdBy, isCustomSlot: 0},transaction})

    let {data}          =  req.payload;
    let creationObject  = [];
    for (const item of data) {
    let title         = item.title;
    let owner         = req.auth.credentials.userData.User.id;
    let occurance     = item.occurance;
    let groupId       = item.groupId;
    let startDate     = Moment.utc(item.startDate);
    let endDate       = item.endDate;
    let timings       = item.timings;
    let description   = item.description;
    let link          = item.link;
    let duration      = item.duration;
    let day           = item.day
    let month         = item.month
    let week          = item.week
    let type          = item.type;
    let participant   = item.participant;
    let remark        = item.remark;
    let frequency     = item.frequency;
    let allDay        = item.allDay;
    let pet           = item.pet;
    let slots         = item.slots;

    if (endDate !== null)                   endDate                     = Moment.utc(endDate);
    if (day!==null && day.length===0)       day                         = null
    if (week!==null && week.length===0)     week                        = null
    if (month!==null && month.length===0)   month                       = null
    if (frequency!==null && frequency.length===0)frequency              = null
   
    
    let eventHandlers   = [{userId:owner,role:'owner'}];
    let schedule        = [];
    let returnData      = item.returnData;
    let groupExist      = await Models.Group.findOne({where: {id: groupId}});
    if (!groupExist) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("INVALID_GROUP_ID"),responseData:{}}).code(400);
    }
    

    if(timings!==null && timings.length>0){
    await timings.forEach((e, i) => {
      let obj={}
      if(e.hasOwnProperty("start"))       obj['start']      = e.start; 
      if(e.hasOwnProperty("end"))         obj['end']        = e.end;
      if(e.hasOwnProperty("startTime"))   obj['startTime']  = e.startTime;
      if(e.hasOwnProperty("endTime"))     obj['endTime']    = e.endTime;
      // if(e.hasOwnProperty("startTime"))   obj['startTime']  = e.startTime-utcOffset;
      // if(e.hasOwnProperty("endTime"))     obj['endTime']    = e.endTime-utcOffset; 
      if(e.hasOwnProperty("duration"))    obj['duration']   = e.duration; 
      if(e.hasOwnProperty('timingType')) obj['timingType']  = e.timingType;
      if(req.headers.timezone) {
        obj['offset'] = req.headers.timezone;
      }
      schedule.push({...obj});
    });
  }
    if(type!==null)
    {
      if(frequency == null || frequency.length===0)
      {
        await transaction.rollback();
        return h.response({success: false, message: req.i18n.__("FREQUENCY_IS_REQUIRED"),responseData: {}}).code(400);
      }
  }
  
   // Event participents
    for (const iterator of participant) {
      let validId = await Models.User.findOne({where: {userId: iterator.userId}});
      if (!validId) {
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("INVALID_USER_ID"),responseData: {}}).code(400)
      }
      eventHandlers.push({ userId: iterator.userId,role:iterator.role });
    }
    let rrString=await generateRsting(startDate,endDate,type,frequency,day,week,month,occurance);
    if(!rrString.success)
    {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__(rrString.message),responseData: {}}).code(400)
    }
    let data={type,frequency,day,week,month,occurance,slots}
    let saveobj={
      data,
      groupId,
      startDate,
      endDate,
      title,
      duration,
      description,
      returnData,
      remark,
      rrString:rrString.data,
      link,
      slots,
      allDay,
      createdBy: owner,
      updatedBy: owner,
      Timings: [...schedule],
      Participants: [...eventHandlers]
    }
    let saveRecord = await Models.Event.create({...saveobj},
      {
        include: [
          {
            model: Models.Participants,
            as: "Participants",
          },
          { model: Models.Timing  }
        ],
        transaction,
      }
    );
    if (!saveRecord) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("ERROR_IN_SAVING_EVENT"),responseData: {}}).code(400);
    }
    
    let res = await createFrequncies(saveRecord.dataValues.id,type,frequency,day,week,month,occurance,transaction);
    if(!res.status){
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__(res.message),responseData: {}}).code(400);
    }
    if(occurance!==null)
    {
      const rule=RRule.fromString(rrString.data);
      let oc=rule.all();
      await saveRecord.update({endDate:oc[oc.length-1]},{transaction});
    }
    creationObject.push(saveRecord.dataValues);
    }
    await transaction.commit();
    return h.response({success: true,message: req.i18n.__(messages["1"]["updated"]),responseData:{events:creationObject}}).code(200);
  }
  catch(error){
    console.error('Error in event bulk creation:-',error);
    await transaction.rollback();
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
  }
}

const checkTimeDiff =(time, slot) => {
  let currentDate = Moment(new Date()).utc()
  console.log(currentDate, " --------------- currentDate")
  let slotDate = Moment(slot).utc()
  console.log(slotDate, " --------------- slotDate")
  let diff = slotDate.diff(currentDate, "seconds");
  let skip = diff < time * 60 ? true : false
  return skip;
}
// Get  events task List
exports.get = async (req, h) => {
  try {
  //  console.log('query',req.query)
    let userId = req.query.companionId
    let conversion = Common.minutesToHours(utcOffset);
    let conversionInverse = Common.minutesToHoursInverse(utcOffset);
    let qlocalStartDate=Moment(req.query.startDate).utc().format('YYYY-MM-DD HH:mm:ss')
    let qlocalEndDate=Moment(req.query.endDate).utc().format('YYYY-MM-DD HH:mm:ss')
    console.log(qlocalStartDate, " =============== req.query.startDate");
    console.log(req.query.startDate, " req.query.startDate");
      // qlocalStartDate=qlocalStartDate.add(utcOffset,'m').format('YYYY-MM-DD HH:mm:ss')
      // qlocalEndDate=qlocalEndDate.add(utcOffset,'m').format('YYYY-MM-DD HH:mm:ss')
      // console.log('qlocalEndDate',qlocalEndDate)
      // console.log('qlocalStartDate',qlocalStartDate)
    let events_data=[]
    //  events_data = await sequelize.query(
    //   `WITH recursive Date_Ranges AS
    //   (select (:qlocalStartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < (:qlocalEndDate))
    //   select group_concat(e.id separator ',') as events, (dr.Date) as Date FROM event_events as e
    //   LEFT JOIN event_groups as eg on eg.id = e.group_id
    //   LEFT JOIN event_participants as ep on (ep.event_id =e.id)
    //   LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
    //   LEFT JOIN event_frequencies as ef on ef.event_id = e.id
    //   INNER JOIN Date_Ranges as dr
    //   where eg.id=:qgroupid
    //   and e.deleted_at is null
    //   and e.status=1
    //   and (if(isnull(:quserid),1,ep.user_id=:quserid))
    //   and (DATE(e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null))
    //   and (
    //       ef.id is null and (DATE(e.start_date)<=dr.Date and (e.end_date is null or DATE(e.end_date)>=dr.Date))
    //       OR (ef.type=1 and ((e.start_date)<=dr.Date and ((e.end_date)>=dr.Date or e.end_date is null) and mod(Datediff(e.start_date,dr.Date),ef.frequency)=0))
    //       OR(ef.type=2 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null)) and WEEKDAY(dr.date) = ef.day and mod(Datediff(IF(WEEKDAY(e.start_date)<ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),IF(WEEKDAY(e.start_date)>ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),e.start_date)),dr.Date),ef.frequency*7)=0)
    //       OR (ef.type=3 and (
    //               (ef.week is null and ef.day=day(dr.Date))
    //               OR (ef.week>0 and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
    //               OR (ef.week=0 and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
    //           )
    //       )
    //       OR (ef.type=4 and (
    //               (ef.week is null and ef.day=day(dr.Date) and ef.month=month(dr.Date))
    //               OR (ef.week>0 and ef.month=month(dr.Date) and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
    //               OR (ef.week=0 and ef.month=month(dr.Date) and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
    //           )
    //       )
    //   )
    //   group by Date
    //   `,
    //   {replacements:{
    //     qstartDate:  req.query.startDate, 
    //     qlocalStartDate,
    //     qendDate: req.query.endDate ,
    //     qlocalEndDate,
    //   qgroupid:1,quserid:userId
    // },type: QueryTypes.SELECT})
    events_data = await sequelize.query(
        `WITH recursive Date_Ranges AS
        (select (:qlocalStartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < (:qlocalEndDate))
        select group_concat(e.id separator ',') as events, (dr.Date) as Date FROM event_events as e
        LEFT JOIN event_groups as eg on eg.id = e.group_id
        LEFT JOIN event_participants as ep on (ep.event_id =e.id)
        LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
        LEFT JOIN event_frequencies as ef on ef.event_id = e.id
        INNER JOIN Date_Ranges as dr
        where eg.id=:qgroupid
        and e.deleted_at is null
        and e.status=1
        and (if(isnull(:quserid),1,ep.user_id=:quserid))
        and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=:qendDate and ((CONVERT_TZ(e.end_date,'+00:00',:conversionInverse)>=:qstartDate) or e.end_date is null))
        and (
            ef.id is null and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=dr.Date and (e.end_date is null or date(CONVERT_TZ(e.end_date,'+00:00',:conversionInverse))>=date(dr.Date)))
            OR (ef.type=1 and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=dr.Date and (CONVERT_TZ(e.end_date,'+00:00',:conversionInverse)>=dr.Date or e.end_date is null) and mod(Datediff(e.start_date,dr.Date),ef.frequency)=0))
            OR(ef.type=2 and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=dr.Date and (CONVERT_TZ(e.end_date,'+00:00',:conversionInverse)>=dr.Date or e.end_date is null)) and ef.day= WEEKDAY(CONVERT_TZ(dr.Date,'+00:00',:conversion)) )
            OR (ef.type=3 and (
                    (ef.week is null and ef.day=day(CONVERT_TZ(dr.Date,'+00:00',:conversion)))
                    OR (ef.week>0 and DATE(ADDDATE(DATE(CONCAT(year(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-',month(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-',month(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = DATE(CONVERT_TZ(dr.Date,'+00:00',:conversion)))
                    OR (ef.week=0 and DATE(LAST_DAY(CONVERT_TZ(dr.Date,'+00:00',:conversion)) - ((7 + WEEKDAY(LAST_DAY(CONVERT_TZ(dr.Date,'+00:00',:conversion))) - ef.day) % 7)) = dr.Date)
                )
            )
            OR (ef.type=4 and (
                    (ef.week is null and ef.day=day(CONVERT_TZ(dr.Date,'+00:00',:conversion)) and ef.month=month(CONVERT_TZ(dr.Date,'+00:00',:conversion)))
                    OR (ef.week>0 and ef.month=month(CONVERT_TZ(dr.Date,'+00:00',:conversion)) and DATE(ADDDATE(DATE(CONCAT(year(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-',month(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-',month(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = CONVERT_TZ(dr.Date,'+00:00',:conversion))
                    OR (ef.week=0 and ef.month=month(CONVERT_TZ(dr.Date,'+00:00',:conversion)) and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = CONVERT_TZ(dr.Date,'+00:00',:conversion))
                )
            )
        )
        group by Date
        `,
        {replacements:{
          qstartDate:  req.query.startDate, 
          qlocalStartDate,
          qendDate: req.query.endDate ,
          qlocalEndDate,
          conversion:conversion,
          conversionInverse:conversionInverse,
        qgroupid:1,quserid:userId
      },type: QueryTypes.SELECT})
    console.log(" ======================================================================== ")
    console.log(" ======================================================================== ")
    console.log(" ======================================================================== ")
    console.log(" ======================================================================== ")
    console.log(" ======================================================================== ")
    console.log(events_data)
    console.log(" ======================================================================== ")
    console.log(" ======================================================================== ")
    console.log(" ======================================================================== ")
    console.log(" ======================================================================== ")
    console.log(" ======================================================================== ")
    let workingHours=[]

    for (const i of events_data) {
      // console.log(i, " ============================== to test data")
      // let skip = checkTimeDiff(6, i.dateTime);
      // if(skip) continue;
      let obj={}
      let events=(i.events).split(',')
      let eventData=[]
      let iteration = 0;
      let dataToAdd = null;
      for (const e of events) {
        iteration = 1;
        let data=await Models.Event.findOne({
          where:{id:e},
          attributes:['id','groupId','isCustomSlot','slots','duration','allDay','startDate','endDate','title','description','rrString'],
          include:[
            {model:Models.Timing,attributes:['start','end','startTime','endTime','offset']},
            {model:Models.Frequency,attributes:[]}
          ]});
          if(data.isCustomSlot) {
            eventData.push(data)
            break;
          }
          if(iteration === events.length) eventData.push(data)
      }
      obj[Moment(i.Date).utc().toISOString()]={
        event:eventData
      }
      workingHours.push(obj)
    }
    // and (if(isnull(:quserid),1,ep.user_id=:quserid))
    //   and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=:qendDate and ((CONVERT_TZ(e.end_date,'+00:00',:conversionInverse)>=:qstartDate) or e.end_date is null))


    // ef.id is null and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=dr.Date and (e.end_date is null or date(CONVERT_TZ(e.end_date,'+00:00',:conversionInverse))>=date(dr.Date)))
    let meetings=await sequelize.query(
      `WITH recursive Date_Ranges AS
      (select (:qlocalStartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < (:qlocalEndDate))
      select group_concat(e.id separator ',') as events ,dr.Date as Date FROM event_events as e
      LEFT JOIN event_groups as eg on eg.id = e.group_id
      LEFT JOIN event_participants as ep on ep.event_id =e.id
      LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
      LEFT JOIN event_frequencies as ef on ef.event_id = e.id
      INNER JOIN Date_Ranges as dr
      where eg.id=:qgroupid 
      and e.deleted_at is null
      and e.status=1
       and (if(isnull(:quserid),1,ep.user_id=:quserid))
      and ((e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null))


      and (
       ef.id is null and (DATE(e.start_date)<=dr.Date and (e.end_date is null or DATE(e.end_date)>=dr.Date))

          OR (ef.type=1 and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=dr.Date and (CONVERT_TZ(e.end_date,'+00:00',:conversionInverse)>=dr.Date or e.end_date is null) and mod(Datediff(e.start_date,dr.Date),ef.frequency)=0))
          OR(ef.type=2 and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=dr.Date and (CONVERT_TZ(e.end_date,'+00:00',:conversionInverse)>=dr.Date or e.end_date is null)) and WEEKDAY(dr.date) = ef.day and mod(Datediff(IF(WEEKDAY(e.start_date)<ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),IF(WEEKDAY(e.start_date)>ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),e.start_date)),dr.Date),ef.frequency*7)=0)
          OR (ef.type=3 and (
                  (ef.week is null and ef.day=day(dr.Date))
                  OR (ef.week>0 and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
                  OR (ef.week=0 and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
              )
          )
          OR (ef.type=4 and (
                  (ef.week is null and ef.day=day(dr.Date) and ef.month=month(dr.Date))
                  OR (ef.week>0 and ef.month=month(dr.Date) and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
                  OR (ef.week=0 and ef.month=month(dr.Date) and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
              )
          )
      )
      group by Date`,
      {replacements:{
        qstartDate:  req.query.startDate, 
        qlocalStartDate,
        conversion:conversion,
        conversionInverse:conversionInverse,
        qendDate: req.query.endDate ,
        qlocalEndDate,
      qgroupid:2,quserid:userId
    },type: QueryTypes.SELECT})
    let meeting=[]
    for (const i of meetings) {
      let obj={}
      let events=(i.events).split(',')
      let eventData=[]
      for (const e of events) {
        let data=await Models.Event.findOne({
          where:{id:e, paymentStatus: 1},
          attributes:['id','groupId','slots','duration','allDay','startDate','endDate','title','description','rrString','paymentStatus'],
          include:[
            {model:Models.Timing,attributes:['start','end','startTime','endTime']},
            {model:Models.Frequency,attributes:[]}
          ]})
          data ? eventData.push(data) : "";
      }
      obj[i.Date]={
        event:eventData
      }
      meeting.push(obj)
    }
    console.log('meeting',meeting.length)
  console.log('meeting',meeting)
    responseData={
      workingHours:workingHours,
      meetings:meeting
    }
  
    return h.response({success: true,message: req.i18n.__("SUCCESSFUL"),responseData}).code(200);
  } catch (err) {
    console.log("error", err);
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
};

// exports.getCopyForTest = async (req, h) => {
//   try {
//   //  console.log('query',req.query)
//     let userId = req.query.companionId
//     let conversion = Common.minutesToHours(utcOffset);
//     let conversionInverse = Common.minutesToHoursInverse(utcOffset);
//     let qlocalStartDate=Moment(req.query.startDate).utc().format('YYYY-MM-DD HH:mm:ss')
//     let qlocalEndDate=Moment(req.query.endDate).utc().format('YYYY-MM-DD HH:mm:ss')
//     console.log(qlocalStartDate, " =============== req.query.startDate");
//     console.log(req.query.startDate, " req.query.startDate");
//       // qlocalStartDate=qlocalStartDate.add(utcOffset,'m').format('YYYY-MM-DD HH:mm:ss')
//       // qlocalEndDate=qlocalEndDate.add(utcOffset,'m').format('YYYY-MM-DD HH:mm:ss')
//       // console.log('qlocalEndDate',qlocalEndDate)
//       // console.log('qlocalStartDate',qlocalStartDate)
//     let events_data=[]
//     //  events_data = await sequelize.query(
//     //   `WITH recursive Date_Ranges AS
//     //   (select (:qlocalStartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < (:qlocalEndDate))
//     //   select group_concat(e.id separator ',') as events, (dr.Date) as Date FROM event_events as e
//     //   LEFT JOIN event_groups as eg on eg.id = e.group_id
//     //   LEFT JOIN event_participants as ep on (ep.event_id =e.id)
//     //   LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
//     //   LEFT JOIN event_frequencies as ef on ef.event_id = e.id
//     //   INNER JOIN Date_Ranges as dr
//     //   where eg.id=:qgroupid
//     //   and e.deleted_at is null
//     //   and e.status=1
//     //   and (if(isnull(:quserid),1,ep.user_id=:quserid))
//     //   and (DATE(e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null))
//     //   and (
//     //       ef.id is null and (DATE(e.start_date)<=dr.Date and (e.end_date is null or DATE(e.end_date)>=dr.Date))
//     //       OR (ef.type=1 and ((e.start_date)<=dr.Date and ((e.end_date)>=dr.Date or e.end_date is null) and mod(Datediff(e.start_date,dr.Date),ef.frequency)=0))
//     //       OR(ef.type=2 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null)) and WEEKDAY(dr.date) = ef.day and mod(Datediff(IF(WEEKDAY(e.start_date)<ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),IF(WEEKDAY(e.start_date)>ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),e.start_date)),dr.Date),ef.frequency*7)=0)
//     //       OR (ef.type=3 and (
//     //               (ef.week is null and ef.day=day(dr.Date))
//     //               OR (ef.week>0 and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
//     //               OR (ef.week=0 and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
//     //           )
//     //       )
//     //       OR (ef.type=4 and (
//     //               (ef.week is null and ef.day=day(dr.Date) and ef.month=month(dr.Date))
//     //               OR (ef.week>0 and ef.month=month(dr.Date) and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
//     //               OR (ef.week=0 and ef.month=month(dr.Date) and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
//     //           )
//     //       )
//     //   )
//     //   group by Date
//     //   `,
//     //   {replacements:{
//     //     qstartDate:  req.query.startDate, 
//     //     qlocalStartDate,
//     //     qendDate: req.query.endDate ,
//     //     qlocalEndDate,
//     //   qgroupid:1,quserid:userId
//     // },type: QueryTypes.SELECT})
//     events_data = await sequelize.query(
//         `WITH recursive Date_Ranges AS
//         (select (:qlocalStartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < (:qlocalEndDate))
//         select group_concat(e.id separator ',') as events, (dr.Date) as Date FROM event_events as e
//         LEFT JOIN event_groups as eg on eg.id = e.group_id
//         LEFT JOIN event_participants as ep on (ep.event_id =e.id)
//         LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
//         LEFT JOIN event_frequencies as ef on ef.event_id = e.id
//         INNER JOIN Date_Ranges as dr
//         where eg.id=:qgroupid
//         and e.deleted_at is null
//         and e.status=1
//         and (if(isnull(:quserid),1,ep.user_id=:quserid))
//         and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=:qendDate and ((CONVERT_TZ(e.end_date,'+00:00',:conversionInverse)>=:qstartDate) or e.end_date is null))
//         and (
//             ef.id is null and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=dr.Date and (e.end_date is null or date(CONVERT_TZ(e.end_date,'+00:00',:conversionInverse))>=date(dr.Date)))
//             OR (ef.type=1 and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=dr.Date and (CONVERT_TZ(e.end_date,'+00:00',:conversionInverse)>=dr.Date or e.end_date is null) and mod(Datediff(e.start_date,dr.Date),ef.frequency)=0))
//             OR(ef.type=2 and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=dr.Date and (CONVERT_TZ(e.end_date,'+00:00',:conversionInverse)>=dr.Date or e.end_date is null)) and ef.day= WEEKDAY(CONVERT_TZ(dr.Date,'+00:00',:conversion)) )
//             OR (ef.type=3 and (
//                     (ef.week is null and ef.day=day(CONVERT_TZ(dr.Date,'+00:00',:conversion)))
//                     OR (ef.week>0 and DATE(ADDDATE(DATE(CONCAT(year(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-',month(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-',month(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = DATE(CONVERT_TZ(dr.Date,'+00:00',:conversion)))
//                     OR (ef.week=0 and DATE(LAST_DAY(CONVERT_TZ(dr.Date,'+00:00',:conversion)) - ((7 + WEEKDAY(LAST_DAY(CONVERT_TZ(dr.Date,'+00:00',:conversion))) - ef.day) % 7)) = dr.Date)
//                 )
//             )
//             OR (ef.type=4 and (
//                     (ef.week is null and ef.day=day(CONVERT_TZ(dr.Date,'+00:00',:conversion)) and ef.month=month(CONVERT_TZ(dr.Date,'+00:00',:conversion)))
//                     OR (ef.week>0 and ef.month=month(CONVERT_TZ(dr.Date,'+00:00',:conversion)) and DATE(ADDDATE(DATE(CONCAT(year(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-',month(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-',month(CONVERT_TZ(dr.Date,'+00:00',:conversion)),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = CONVERT_TZ(dr.Date,'+00:00',:conversion))
//                     OR (ef.week=0 and ef.month=month(CONVERT_TZ(dr.Date,'+00:00',:conversion)) and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = CONVERT_TZ(dr.Date,'+00:00',:conversion))
//                 )
//             )
//         )
//         group by Date
//         `,
//         {replacements:{
//           qstartDate:  req.query.startDate, 
//           qlocalStartDate,
//           qendDate: req.query.endDate ,
//           qlocalEndDate,
//           conversion:conversion,
//           conversionInverse:conversionInverse,
//         qgroupid:1,quserid:userId
//       },type: QueryTypes.SELECT})
//     console.log(" ======================================================================== ")
//     console.log(" ======================================================================== ")
//     console.log(" ======================================================================== ")
//     console.log(" ======================================================================== ")
//     console.log(" ======================================================================== ")
//     console.log(events_data)
//     console.log(" ======================================================================== ")
//     console.log(" ======================================================================== ")
//     console.log(" ======================================================================== ")
//     console.log(" ======================================================================== ")
//     console.log(" ======================================================================== ")
//     let workingHours=[]

//     for (const i of events_data) {
//       // console.log(i, " ============================== to test data")
//       // let skip = checkTimeDiff(6, i.dateTime);
//       // if(skip) continue;
//       let obj={}
//       let events=(i.events).split(',')
//       let eventData=[]
//       let iteration = 0;
//       let dataToAdd = null;
//       for (const e of events) {
//         iteration = 1;
//         let data=await Models.Event.findOne({
//           where:{id:e},
//           attributes:['id','groupId','isCustomSlot','slots','duration','allDay','startDate','endDate','title','description','rrString'],
//           include:[
//             {model:Models.Timing,attributes:['start','end','startTime','endTime','offset']},
//             {model:Models.Frequency,attributes:[]}
//           ]});
//           if(data.isCustomSlot) {
//             eventData.push(data)
//             break;
//           }
//           if(iteration === events.length) eventData.push(data)
//       }
//       obj[Moment(i.Date).utc().toISOString()]={
//         event:eventData
//       }
//       workingHours.push(obj)
//     }
//     // and (if(isnull(:quserid),1,ep.user_id=:quserid))
//     //   and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=:qendDate and ((CONVERT_TZ(e.end_date,'+00:00',:conversionInverse)>=:qstartDate) or e.end_date is null))


//     // ef.id is null and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=dr.Date and (e.end_date is null or date(CONVERT_TZ(e.end_date,'+00:00',:conversionInverse))>=date(dr.Date)))
//     let meetings=await sequelize.query(
//       `WITH recursive Date_Ranges AS
//       (select (:qlocalStartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < (:qlocalEndDate))
//       select group_concat(e.id separator ',') as events ,dr.Date as Date FROM event_events as e
//       LEFT JOIN event_groups as eg on eg.id = e.group_id
//       LEFT JOIN event_participants as ep on ep.event_id =e.id
//       LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
//       LEFT JOIN event_frequencies as ef on ef.event_id = e.id
//       INNER JOIN Date_Ranges as dr
//       where eg.id=:qgroupid 
//       and e.deleted_at is null
//       and e.status=1
//        and (if(isnull(:quserid),1,ep.user_id=:quserid))
//       and ((e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null))


//       and (
//        ef.id is null and (DATE(e.start_date)<=dr.Date and (e.end_date is null or DATE(e.end_date)>=dr.Date))

//           OR (ef.type=1 and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=dr.Date and (CONVERT_TZ(e.end_date,'+00:00',:conversionInverse)>=dr.Date or e.end_date is null) and mod(Datediff(e.start_date,dr.Date),ef.frequency)=0))
//           OR(ef.type=2 and (CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=dr.Date and (CONVERT_TZ(e.end_date,'+00:00',:conversionInverse)>=dr.Date or e.end_date is null)) and WEEKDAY(dr.date) = ef.day and mod(Datediff(IF(WEEKDAY(e.start_date)<ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),IF(WEEKDAY(e.start_date)>ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),e.start_date)),dr.Date),ef.frequency*7)=0)
//           OR (ef.type=3 and (
//                   (ef.week is null and ef.day=day(dr.Date))
//                   OR (ef.week>0 and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
//                   OR (ef.week=0 and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
//               )
//           )
//           OR (ef.type=4 and (
//                   (ef.week is null and ef.day=day(dr.Date) and ef.month=month(dr.Date))
//                   OR (ef.week>0 and ef.month=month(dr.Date) and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
//                   OR (ef.week=0 and ef.month=month(dr.Date) and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
//               )
//           )
//       )
//       group by Date`,
//       {replacements:{
//         qstartDate:  req.query.startDate, 
//         qlocalStartDate,
//         conversion:conversion,
//         conversionInverse:conversionInverse,
//         qendDate: req.query.endDate ,
//         qlocalEndDate,
//       qgroupid:2,quserid:userId
//     },type: QueryTypes.SELECT})
//     let meeting=[]
//     for (const i of meetings) {
//       let obj={}
//       let events=(i.events).split(',')
//       let eventData=[]
//       for (const e of events) {
//         let data=await Models.Event.findOne({
//           where:{id:e, paymentStatus: 1},
//           attributes:['id','groupId','slots','duration','allDay','startDate','endDate','title','description','rrString','paymentStatus'],
//           include:[
//             {model:Models.Timing,attributes:['start','end','startTime','endTime']},
//             {model:Models.Frequency,attributes:[]}
//           ]})
//           data ? eventData.push(data) : "";
//       }
//       obj[i.Date]={
//         event:eventData
//       }
//       meeting.push(obj)
//     }
//     console.log('meeting',meeting.length)
//   console.log('meeting',meeting)
//     responseData={
//       workingHours:workingHours,
//       meetings:meeting
//     }
  
//     return h.response({success: true,message: req.i18n.__("SUCCESSFUL"),responseData}).code(200);
//   } catch (err) {
//     console.log("error", err);
//     return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
//   }
// };


exports.getAllSlots=async(req,h)=>{
  try {
      // let userId = req.query.companionId
      let qlocalStartDate=Moment(req.query.startDate).utc().format('YYYY-MM-DD HH:mm:ss')
      let qlocalEndDate=Moment(req.query.endDate).utc().format('YYYY-MM-DD HH:mm:ss')
      let companions=[]
      let events_data=[]
       events_data = await sequelize.query(
        `WITH recursive Date_Ranges AS
        (select Date(:qlocalStartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < Date(:qlocalEndDate))
        select group_concat(e.id separator ',') as events, dr.Date as Date FROM event_events as e
        LEFT JOIN event_groups as eg on eg.id = e.group_id
        LEFT JOIN event_participants as ep on (ep.event_id =e.id)
        LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
        LEFT JOIN event_frequencies as ef on ef.event_id = e.id
        INNER JOIN Date_Ranges as dr
        where eg.id=:qgroupid 
        and e.deleted_at is null
        and e.status=1
        -- and (if(isnull(:quserid),1,ep.user_id=:quserid))
        and (DATE(e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null))
        and (
            ef.id is null and (DATE(e.start_date)<=dr.Date and (e.end_date is null or DATE(e.end_date)>=dr.Date))
            OR (ef.type=1 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null) and mod(Datediff(e.start_date,dr.Date),ef.frequency)=0))
            OR(ef.type=2 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null)) and WEEKDAY(dr.date) = ef.day and mod(Datediff(IF(WEEKDAY(e.start_date)<ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),IF(WEEKDAY(e.start_date)>ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),e.start_date)),dr.Date),ef.frequency*7)=0)
            OR (ef.type=3 and (
                    (ef.week is null and ef.day=day(dr.Date))
                    OR (ef.week>0 and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
                    OR (ef.week=0 and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
                )
            )
            OR (ef.type=4 and (
                    (ef.week is null and ef.day=day(dr.Date) and ef.month=month(dr.Date))
                    OR (ef.week>0 and ef.month=month(dr.Date) and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
                    OR (ef.week=0 and ef.month=month(dr.Date) and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
                )
            )
        )
        group by Date
        `,
        {replacements:{
          qstartDate:  req.query.startDate, 
          qlocalStartDate,
          qendDate: req.query.endDate ,
          qlocalEndDate,
        qgroupid:1
      },type: QueryTypes.SELECT})

// console.log(" ================================================================= ")
// console.log(" ================================================================= ")
// console.log(" ================================================================= ")
// console.log(" ================================================================= ")

//       console.log(events_data)

//       console.log(" ================================================================= ")
//       console.log(" ================================================================= ")
//       console.log(" ================================================================= ")
//       console.log(" ================================================================= ")

      let workingHours=[]
  
      for (const i of events_data) {

        let obj={}
        let events=(i.events).split(',')
        let eventData=[]
        for (const e of events) {

          let data=await Models.Event.findOne({
            where:{id:e},
            attributes:['id','groupId','isCustomSlot','slots','duration','allDay','startDate','endDate','title','description','rrString'],
            include:[
              {model:Models.Timing,attributes:['start','end','startTime','endTime']},
              {model:Models.Frequency,attributes:[]},
              {model:Models.Participants,as:'Participants' ,attributes:['userId','role']}
            ]})
            console.log(" =========================aa======================================== ")
            console.log(" ==========================aaa======================================= ")
            console.log(" ===========================aaaa====================================== ")
            console.log(" =========================aaaaa======================================== ")
            console.log(data)
    
            console.log(" =========================aa======================================== ")
            console.log(" ==========================aaa======================================= ")
            console.log(" ===========================aaaa====================================== ")
            console.log(" =========================aaaaa======================================== ")
            eventData.push(data)
           companions.push(data.dataValues.Participants[0].dataValues.userId)



        }
        obj[i.Date]={
          event:eventData
        }
        workingHours.push(obj)
      }
  
      let meetings=await sequelize.query(
        `WITH recursive Date_Ranges AS
        (select Date(:qlocalStartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < Date(:qlocalEndDate))
        select group_concat(e.id separator ',') as events ,dr.Date as Date FROM event_events as e
        LEFT JOIN event_groups as eg on eg.id = e.group_id
        LEFT JOIN event_participants as ep on ep.event_id =e.id
        LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
        LEFT JOIN event_frequencies as ef on ef.event_id = e.id
        INNER JOIN Date_Ranges as dr
        where eg.id=:qgroupid 
        and e.deleted_at is null
        and e.status=1
        -- and (if(isnull(:quserid),1,ep.user_id=:quserid))
        and (DATE(e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null))
        and (
            ef.id is null and (DATE(e.start_date)<=dr.Date and (e.end_date is null or DATE(e.end_date)>=dr.Date))
            OR (ef.type=1 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null) and mod(Datediff(e.start_date,dr.Date),ef.frequency)=0))
            OR(ef.type=2 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null)) and WEEKDAY(dr.date) = ef.day and mod(Datediff(IF(WEEKDAY(e.start_date)<ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),IF(WEEKDAY(e.start_date)>ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),e.start_date)),dr.Date),ef.frequency*7)=0)
            OR (ef.type=3 and (
                    (ef.week is null and ef.day=day(dr.Date))
                    OR (ef.week>0 and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
                    OR (ef.week=0 and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
                )
            )
            OR (ef.type=4 and (
                    (ef.week is null and ef.day=day(dr.Date) and ef.month=month(dr.Date))
                    OR (ef.week>0 and ef.month=month(dr.Date) and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
                    OR (ef.week=0 and ef.month=month(dr.Date) and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
                )
            )
        )
        group by Date`,
        {replacements:{
          qstartDate:  req.query.startDate, 
          qlocalStartDate,
          qendDate: req.query.endDate ,
          qlocalEndDate,
        qgroupid:2
      },type: QueryTypes.SELECT})
      let meeting=[]
      for (const i of meetings) {
        let obj={}
        let events=(i.events).split(',')
        let eventData=[]
        for (const e of events) {
          let data=await Models.Event.findOne({
            where:{id:e},
            attributes:['id','groupId','slots','duration','allDay','startDate','endDate','title','description','rrString'],
            include:[
              {model:Models.Timing,attributes:['start','end','startTime','endTime']},
              {model:Models.Frequency,attributes:[]}
            ]})
            eventData.push(data)
        }
        obj[i.Date]={
          event:eventData
        }
        meeting.push(obj)
      }

      companions = [...new Set(companions)];
      let companionData=await Models.User.findAll({where:{userId:{
        [Op.in]:[...companions]
      }}})
    //   console.log('meeting',meeting.length)
    // console.log('meeting',meeting)
      responseData={
        workingHours:workingHours,
        meetings:meeting,
        companions:companions
      }
    
      return h.response({success: true,message: req.i18n.__("SUCCESSFUL"),responseData}).code(200);
    } catch (err) {
      console.log("error", err);
      return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}




exports.bulkGet=async(req,h)=>{
  try{
    let userId=req.auth.credentials.userData.User.id
    let groupId=req.query.groupId
    let isCustomSlot = req.query.isCustomSlot;
    let where = {createdBy:userId, groupId, isCustomSlot: isCustomSlot}
    if(isCustomSlot === 1) {
      if(req.query.startDate) {
        where = {...where, startDate: {[Op.gte]: req.query.startDate}}
      }
    }

    let events=await Models.Event.findAll({
      where,
      include:[{model:Models.Timing},{model:Models.Frequency}]
    })

    let responseData={
      events,
      count:events.length
    }
    return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_FOUND"),responseData:responseData}).code(200)
  }
  catch(error)
  {
    console.error('error in bulk get',error)
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500)
  }
}

// Delete Event
exports.delete = async (req, h) => {
  const transaction = await Models.sequelize.transaction();
  try {
    let id = req.query.id;
    let scheduleDate=req.query.scheduleDate;
    let record = await Models.Event.findOne({where: {id}})
    if (!record) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("RECORD_NOT_FOUND"),responseData: {}}).code(400)}
        let groupId=record.dataValues.groupId
      // for preventive care medication and task 
      if(groupId===1 || groupId===3 || groupId===7)
      {
        if(scheduleDate!==null)
        {
          scheduleDate=Moment.utc(scheduleDate)
          if(record.dataValues.endDate!==null)endDate=Moment.utc(record.dataValues.endDate)
          else endDate=scheduleDate
          startDate=Moment.utc(record.dataValues.startDate)
          if(scheduleDate.diff(startDate,"days")>1)
          {
            endDate.add(-1,'d')
            await record.update({endDate:endDate},{transaction})
            await transaction.commit()
            return h.response({success: true,message: req.i18n.__(messages[groupId]["delete"]),responseData: {}}).code(200);
          }
        
        }
      }

    await Models.EventLog.destroy({
      where: {
        eventId: id,
      },
    });
    await Models.Participants.destroy({
      where: {
        eventId: id,
      },
    });
   
    await Models.Timing.destroy({
      where: {
        eventId: id,
      },
    });
    await Models.Attachment.destroy({
      where: {
        eventId: id,
      },
    });
    await Models.Frequency.destroy({
      where:{
        eventId:id
      }});
    await record.destroy();
    await transaction.commit();
    return h
      .response({
        success: true,
        message: req.i18n.__(messages[groupId]["delete"]),
        responseData: {}
      })
      .code(200);
  } catch (err) {
    console.log("error", err);
    await transaction.rollback();
    return h
      .response({
        success: false,
        message: req.i18n.__("SOMETHING_WENT_WRONG"),
        responseData: {},
      })
      .code(500);
  }
};



// Get By Id
exports.getByid = async (req, h) => {
  try {
    let id = req.query.id;

    let userRole = req.auth.credentials.userData.Role[0];

    console.log(userRole, " ===================== user role")
    console.log(req.auth.credentials.userData.Role, " ===================== user req.auth.credentials.userData.Role")


    let userId=req.auth.credentials.userData.User.id;

    if(userRole != "admin") {
      const participants = await Models.Participants.findOne({ where: {userId, eventId: id} });
      if(!participants) {
        return h.response({success: false,message: req.i18n.__("NOT_ALLOWED_TO_CHECK_RECORDS"),responseData: {}}).code(400);
      }

    }



    let record = await Models.Event.findOne({
      where: {
        id,
      },
      include: [
  {
    model:Models.Timing,
    attributes:['id',"start","end","duration",'start_time','end_time','timingType'],
  },
  {
    model: Models.Participants,
    attributes: ["userId","role"],
    as: "Participants",
    include:[{model:Models.User}]
  },
    {
      model:Models.EventLog,
      attributes:["eventDate","createdBy",'attachment'],
      //include:[{model:Models.Timing,attributes:['startTime','endTime','start','end']}]
    },
      ],
    });
    if(!record)
    {
      return h.response({success: false,message: req.i18n.__("RECORD_NOT_FOUND"),responseData: {}}).code(400);
    }
      return h.response({
        success: true,message: req.i18n.__("SUCESSFULLY_FOUND"),responseData: record}).code(200);
  } catch (ex) {
    console.log("error", ex);

    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
};


exports.getCalander = async (req, h) => {
  try {
   
    let userId = req.auth.credentials.userData.User.id;

    const flag = req.query.flag;

    let qlocalStartDate=Moment(req.query.startDate).utc().format('YYYY-MM-DD HH:mm:ss')
    let qlocalEndDate=Moment(req.query.endDate).utc().format('YYYY-MM-DD HH:mm:ss')
      // qlocalStartDate=qlocalStartDate.add(utcOffset,'m').format('YYYY-MM-DD HH:mm:ss')
      // qlocalEndDate=qlocalEndDate.add(utcOffset,'m').format('YYYY-MM-DD HH:mm:ss')
    let events_data=[]
    let inOp='10,1'
    if(req.query.status==10)
    {
      inOp=`10`
    }
    else if(req.query.status==1)
    {
      inOp=`1`
    }
     events_data = await sequelize.query(

      `WITH recursive Date_Ranges AS
      (select (:qlocalStartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < (:qlocalEndDate))
      select group_concat(e.id separator ',') as events, dr.Date as Date FROM event_events as e
      LEFT JOIN event_groups as eg on eg.id = e.group_id
      LEFT JOIN event_participants as ep on (ep.event_id =e.id)
      LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
      LEFT JOIN event_frequencies as ef on ef.event_id = e.id
      INNER JOIN Date_Ranges as dr
      where eg.id=:qgroupid 
      and e.deleted_at is null
      and e.status in (${inOp})
      and (if(isnull(:quserid),1,ep.user_id=:quserid))
      and ((e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null))
      and (
          ef.id is null and (DATE(e.start_date)<=dr.Date and (e.end_date is null or DATE(e.end_date)>=dr.Date))
          OR (ef.type=1 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null) and mod(Datediff(e.start_date,dr.Date),ef.frequency)=0))
          OR(ef.type=2 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null)) and WEEKDAY(dr.date) = ef.day and mod(Datediff(IF(WEEKDAY(e.start_date)<ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),IF(WEEKDAY(e.start_date)>ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),e.start_date)),dr.Date),ef.frequency*7)=0)
          OR (ef.type=3 and (
                  (ef.week is null and ef.day=day(dr.Date))
                  OR (ef.week>0 and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
                  OR (ef.week=0 and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
              )
          )
          OR (ef.type=4 and (
                  (ef.week is null and ef.day=day(dr.Date) and ef.month=month(dr.Date))
                  OR (ef.week>0 and ef.month=month(dr.Date) and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
                  OR (ef.week=0 and ef.month=month(dr.Date) and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
              )
          )
      )
  
      `,
      {replacements:{
        qstartDate:req.query.startDate , 
        qlocalStartDate,
        qendDate:req.query.endDate ,
        qlocalEndDate,
      qgroupid:2,quserid:userId
    },type: QueryTypes.SELECT})
    console.log('events_data',events_data)
    let meetings=[]
    let eventData=[]
    
    for (const i of events_data) {
      let obj={}
      
      if(i.events!==null)
      {
      let events=(i.events).split(',');
      events = [...new Set(events)];
      for (const e of events) {

        let attributes = [];
        if(flag === "companion") {
          attributes = ['id','groupId','slots','duration','allDay','startDate','endDate','title','description','rrString','status','zoomId','zoomLink','startLink']
        } else {
          attributes = ['id','groupId','slots','duration','allDay','startDate','endDate','title','description','rrString','status','zoomId','zoomLink']
        }

        let data=await Models.Event.findOne({
          where:{id:e, paymentStatus: 1},
          attributes:attributes,
          include:[
            {model:Models.Timing,attributes:['start','end','startTime','endTime']},
            {model:Models.Frequency},
            {model:Models.Participants,as:'Participants',attributes:['userId','role'],include:[
              {model:Models.User}
            ]}
          ]})
          if(data === null) continue;
          data.dataValues.eventDate=i.Date
          eventData.push(data)
      }
    }
      // console.log('obj[i.Date]',i.Date)
      // obj[(i.Date)]={
      //   event:eventData
      // }
      // console.log('eventData',eventData)
    }

    
    responseData={
      meetings:eventData,
    }
    return h.response({success: true,message: req.i18n.__("SUCCESSFUL"),responseData}).code(200);
  } catch (err) {
    console.log("error", err);
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
};


// mark as complete 
exports.markComplete = async (req, h) => {
  const transaction = await Models.sequelize.transaction();
  try {
    let eventId = req.payload.eventId;
    let timingId = req.payload.timingId;
    let scheduleDate = Moment.utc(req.payload.scheduleDate)
    let userId=req.auth.credentials.userData.User.id;
    let attachment=req.payload.attachment;
    let findEvent = await Models.Event.findOne({
      where: {
        id: eventId
      },
    });
    if (!findEvent) {
      await transaction.rollback();
      return h
        .response({
          success: false,
          message: req.i18n.__("INVALID_EVENT_ID"),
          responseData: {},
        })
        .code(400);
    }
    if(findEvent.dataValues.status===10)
    {
      await transaction.rollback();
      return h
        .response({
          success: false,
          message: req.i18n.__("REQUEST_TO_RESCHEDULE_APPOINTMENT_IS_PENDING"),
          responseData: {},
        })
        .code(400);
    }
    
  let findUser=await Models.Participants.findOne({where:{eventId,userId:userId}}); 
  if(!findUser)
  {
    await transaction.rollback();
    return h.response({
       success: false,
      message: req.i18n.__("USER_IS_NOT_EVENT_PARTICIPENT"),
      responseData: {}
    }).code(400)
  }
  if(timingId!==null)
  {
    let findTiming = await Models.Timing.findOne({
      where: {
        id: timingId,
      },
    });
    if (!findTiming) {
      await transaction.rollback();
      return h
        .response({
          success: false,
          message: req.i18n.__("INVALID_TIMING_ID"),
          responseData: {},
        })
        .code(400);
    }
  }

    let findDate = Moment.utc(_.cloneDeep(scheduleDate));
  
    let where={
      eventId,
      createdBy:userId,
      timingId: timingId,
      eventDate: {
        [Op.gte]:findDate.startOf('day').toDate(),
        [Op.lte]:findDate.add(1,'d').toDate()
      }
    }
    let recordExist = await Models.EventLog.findOne({
      where: {
        ...where
        },
      },
    );
    if (recordExist) {
      await transaction.rollback();
      return h
        .response({
          success: false,
          message: req.i18n.__("ALREADY_MARKED_AS_COMPLETE"),
          responseData: {},
        })
        .code(400);
    }
    let saveRecord = await Models.EventLog.create(
      {
        eventId,
        timingId,
        attachment,
        eventDate:scheduleDate,
        createdBy:userId,
      },
      { transaction }
    );
    if (!saveRecord) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("ERROR_IN_SAVING_RECORD"),responseData: {}}).code(400)
        }
    
    await transaction.commit();
    if(attachment!==null)
    {
      for(const a of attachment)
      {
    if(!(a.hasOwnProperty('id')))
    {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("ATTACHMENT_ID_IS_REQUIRED"),responseData: {},}).code(400);
    }
    let reqPayload = {
      data: [
          {
              id: a.id,
              status: Constants.STATUS.ACTIVE
          }
      ]
  }
  await Common.createRequest(Constants.URL.ATTACHMENT_UPDATE, 'patch', reqPayload)
}
  }
    return h.response({success: true,message: req.i18n.__("SUCESSFULLY_SAVED"),responseData: saveRecord}).code(200);
  } catch (error) {
    console.log("error", error);
    await transaction.rollback();
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500)}
};

// bulk mark complete
exports.bulkMarkComplete  = async(req,h)=>{
  const transaction = await Models.sequelize.transaction();
  try {
    let data=req.payload.data;
    let res=[]
    for (const d of data) {
    let eventId = d.eventId;
    let timingId = d.timingId;
    let scheduleDate = Moment.utc(d.scheduleDate)
    let userId=req.auth.credentials.userData.User.id;
    let attachment=d.attachment;
    let findEvent = await Models.Event.findOne({
      where: {
        id: eventId,
        status: 1,
      },
    });
    if (!findEvent) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("INVALID_EVENT_ID"),responseData: {}}).code(400);
    }
  let findUser=await Models.Participants.findOne({where:{eventId,userId:userId}}); 
  if(!findUser)
  {
    await transaction.rollback();
    return h.response({success: false,message: req.i18n.__("USER_IS_NOT_EVENT_PARTICIPENT"),responseData: {}}).code(400)
  }
  if(timingId!==null)
  {
    let findTiming = await Models.Timing.findOne({where: {id: timingId}});
    if (!findTiming) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("INVALID_TIMING_ID"),responseData: {}}).code(400);
    }
  }

    let findDate = Moment.utc(_.cloneDeep(scheduleDate));
  
    let where={
      eventId,
      createdBy:userId,
      timingId: timingId,
      eventDate: {
        [Op.gte]:findDate.startOf('day').toDate(),
        [Op.lte]:findDate.add(1,'d').toDate()
      }
    }
    let recordExist = await Models.EventLog.findOne({where: {...where}});
    if (recordExist) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("ALREADY_MARKED_AS_COMPLETE"),responseData: {}}).code(400);
    }
    let saveRecord = await Models.EventLog.create(
      {
        eventId,
        timingId,
        attachment,
        eventDate:scheduleDate,
        createdBy:userId,
      },
      { transaction }
    );
    if (!saveRecord) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("ERROR_IN_SAVING_RECORD"),responseData: {}}) .code(400);
    }
    res.push(saveRecord)
    //await transaction.commit();
    if(attachment!==null)
    {
      for(const a of attachment)
      {
    if(!(a.hasOwnProperty('id')))
    {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("ATTACHMENT_ID_IS_REQUIRED"),responseData: {}}).code(400);
    }
    
    let reqPayload = {
      data: [
          {
              id: a.id,
              status: Constants.STATUS.ACTIVE
          }
      ]
  }
  await Common.createRequest(Constants.URL.ATTACHMENT_UPDATE, 'patch', reqPayload)
    } 
  }
}
    await transaction.commit();
    return h.response({success: true,message: req.i18n.__("SUCESSFULLY_SAVED"),responseData: res}).code(200);
  } catch (error) {
    console.log("error", error);
    await transaction.rollback();
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
}

// get Events Status
exports.getEventsStatus = async  (req,h)=>{
try{
  let startDate   =   req.query.startDate
  let endDate     =   req.query.endDate
  let groupId     =   req.query.groupId
  let userId      =   req.auth.credentials.userData.User.id
  let res=await sequelize.query(
    `WITH recursive Date_Ranges AS
    (select Date(:qlocalStartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < Date(:qlocalEndDate))
    select 
    dr.Date,
    sum(IF(el.event_date is not null,1,0)) as completed, 
    sum(
  IF(
      el.event_date is null 
      and date(e.start_date) <= date(dr.Date) 
      and concat(date(dr.Date),'  ',time(e.start_date)) <= :qendDate
      and date(e.end_date) >= date(dr.Date)
      and if(date(dr.Date)=date(:qendDate),time(e.end_date)<=time(:qendDate),1),
      1,0)) as missed,
      sum(
  IF(
      el.event_date is null 
      and date(e.start_date) <= date(dr.Date) 
      and concat(date(dr.Date),'  ',time(e.start_date)) <= :qendDate
      and date(e.end_date) >= date(dr.Date)
      and if(date(dr.Date)=date(:qendDate),time(e.end_date)>time(:qendDate),0),
      1,0)) as ongoing
      
    from Date_Ranges as dr
    LEFT JOIN event_events as e on e.start_date <= :qendDate and (e.end_date>=:qstartDate or e.end_date is null)
  LEFT JOIN event_participants as ep on ep.event_id =e.id
    LEFT JOIN event_frequencies as ef on ef.event_id = e.id
  LEFT JOIN event_log as el on el.event_id=e.id and (el.event_date is not null and date(el.event_date) = date(dr.Date))
    
    where e.group_id=:qgroupid 
     and e.deleted_at is null
     and ep.user_id=:quserid 
   and (DATE(e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null))
    and 
    ( 
  (ef.id is null and ((e.start_date)<=:qendDate and (e.end_date is null or (e.end_date)>=:qstartDate)))
         OR 
  (ef.type=1 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null) and mod(Datediff(e.start_date,dr.Date),ef.frequency)=0))
           OR(ef.type=2 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null)) and WEEKDAY(dr.date) = ef.day and mod(Datediff(IF(WEEKDAY(e.start_date)<ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),IF(WEEKDAY(e.start_date)>ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),e.start_date)),dr.Date),ef.frequency*7)=0)
           OR (ef.type=3 and (
                   (ef.week is null and ef.day=day(dr.Date))
                   OR (ef.week>0 and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
                   OR (ef.week=0 and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
               )
           )
           OR (ef.type=4 and (
                   (ef.week is null and ef.day=day(dr.Date) and ef.month=month(dr.Date))
                   OR (ef.week>0 and ef.month=month(dr.Date) and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
                   OR (ef.week=0 and ef.month=month(dr.Date) and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
               )
           )
    )
group by dr.Date`,
    {
      replacements:{
      qstartDate: Moment(startDate).format('YYYY-MM-DD HH:mm:ss') , 
      qlocalStartDate:Moment(Moment(startDate).add(utcOffset,'m')).format('YYYY-MM-DD HH:mm:ss'),
      qendDate: Moment(endDate).format('YYYY-MM-DD HH:mm:ss') ,
      qlocalEndDate:Moment(Moment(endDate).add(utcOffset,'m')).format('YYYY-MM-DD HH:mm:ss'),
      qgroupid:groupId,
      quserid:userId
  },
  type: QueryTypes.SELECT}
  );
  console.log(res,res);
  
  let obj={}
  for(const i of res)
  {
   
    obj[i.Date]={
      completed:parseInt(i.completed),
      missed:parseInt(i.missed),
      ongoing:parseInt(i.ongoing)
    }
  }
  let responsedata={
    eventStatus:obj
  }
  return h.response({success: true,message: req.i18n.__("SUCESSFULL"),responseData:responsedata}).code(200);

}
catch(error)
{
  console.log('error',error);
  return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
}
}


exports.rescheduleEvent=async(req,h)=>{
  const transaction=await Models.sequelize.transaction();
  try{
    const language = req.headers.language;
    let eventId=req.payload.eventId;
    let existEvent=await Models.Event.findOne({where:{id:eventId}})
    if(!existEvent) {
      await transaction.rollback();
      return h.response({sucess:false,message:req.i18n.__('MEETING_NOT_FOUND'),responseData:{}}).code(400);
    }
    
    await existEvent.update({status:10, eventStatus: 0},{transaction});
    // create a new Events   
    let title       = req.payload.title;
    let owner       = req.auth.credentials.userData.User.id;
    let groupId     = req.payload.groupId;
    let startDate   = Moment.utc(req.payload.startDate);
    let endDate     = req.payload.endDate;
    let timings     = req.payload.timings;
    let description = req.payload.description?req.payload.description:existEvent.dataValues.description;
    let link = req.payload.link; 
    let participant = req.payload.participant;
    let allDay=req.payload.allDay;
    if (endDate !== null) endDate = Moment.utc(endDate)
    const offset = req.headers.utcoffset ? req.headers.utcoffset : 0;
    let eventHandlers = [
      {
        userId:owner,
        role:'owner',
      }
    ];
    
    let schedule = [];
    let returnData=req.payload.returnData;
    let groupExist = await Models.Group.findOne({where: {id: groupId}});
    if (!groupExist) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("INVALID_GROUP_ID"),responseData: {}}).code(400);
    }
    
    if(timings!==null && timings.length>0){
    await timings.forEach((e, i) => {
      let obj={}
      if(e.hasOwnProperty("start")) obj['start']=Moment.utc(e.start); 
      if(e.hasOwnProperty("end")) obj['end']=Moment.utc(e.end);
      if(e.hasOwnProperty("startTime")) obj['startTime']=e.startTime-utcOffset;
      if(e.hasOwnProperty("endTime")) obj['endTime']=e.endTime-utcOffset; 
      if(e.hasOwnProperty("duration")) obj['duration']=e.duration; 
      if(e.hasOwnProperty("timingType")) obj['timingType']=e.timingType;
      schedule.push({...obj});
    });
  }
    
  let comapanionId=null
   // Event participents
    for (const iterator of participant) {
      let validId = await Models.User.findOne({where: {userId: iterator.userId}});
      if (!validId) {
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("INVALID_USER_ID"),responseData: {}}).code(400)
      }
      comapanionId=iterator.userId
      eventHandlers.push({ userId: iterator.userId,role:iterator.role });
    }
    
    let saveobj={
      data:{}, groupId, startDate, endDate, eventStatus: Constants.EVENT_STATUS.RESCHEDULED,
      title, description, returnData, remark:null, rrString:null, link,
      slots:null, allDay, createdBy: owner, updatedBy: owner, paymentStatus: 1, prefferedLanguage: language,
      Timings: [...schedule], Participants: [...eventHandlers]
    }
    let saveRecord = await Models.Event.create(
      {
        ...saveobj
      },
      {
        include: [
          {
            model: Models.Participants,
            as: "Participants",
          },
          {
            model: Models.Timing,
          }
        ],
        transaction,
      }
    );
    if (!saveRecord) {
      await transaction.rollback();
      return h
        .response({
          success: false,
          message: req.i18n.__("ERROR_IN_SAVING_EVENT"),
          responseData: {},
        })
        .code(400);
    }
    // reschedule event api 

    // find comapanion email
    let companion=await Models.User.findOne({where:{userId:comapanionId}})
    companionemail=companion.email
    await Zoom.update(saveRecord.dataValues.id,existEvent.dataValues.zoomId,existEvent.dataValues.zoomLink,existEvent.dataValues.startLink,companionemail,transaction)
   
    // for (const iterator of eventHandlers) {
    //   let userData=await Models.User.findOne({where:{userId:iterator.userId}})
    //   let notificationData={
    //     replacements:{
    //       new_start_date:startDate,
    //       start_date:existEvent.startDate,
    //       user_name:userData.firstName+' '+userData?.lastName
    //     },
    //     typeId:2,
    //     data:{eventId:saveRecord.dataValues.id},
    //     userId:iterator.userId 
    //   }
    //   await WebhookServices.sendNotification(notificationData,transaction);
    // }


    // send Emails And Notifications
    await Notification.send(saveRecord.dataValues.id,'MEETING_RESCHEDULED',2,transaction,offset,language )

    // updating orders-------
    let order=await Models.Order.findOne({where:{eventId:existEvent.dataValues.id}})
    if(order)
    {
      await order.update({eventId:saveRecord.dataValues.id},{transaction})
    }
    
    // updating orders-------
    let invoice=await Models.Invoice.findOne({where:{eventId:existEvent.dataValues.id}})
    if(invoice) {
      await invoice.update({eventId:saveRecord.dataValues.id},{transaction})
    }
// updating amoun
await saveRecord.update({orderId:existEvent.dataValues.orderId,amount:existEvent.dataValues.amount},{transaction})


let earningHistoryObj = {
  meetingId: eventId,
  updatedMeetingId: saveRecord.dataValues.id
}

let responseData=await Axios({
  method: 'PATCH', url: `${process.env.USER_ONBOARDING_DOMAIN}/earning-history`, headers:{}, data: earningHistoryObj
});

console.log(saveRecord)


const userInfo22 = await Models.User.findOne({where: {userId: owner}}); 

let topicTitle = "";
if(req.headers.language == "en") {
  topicTitle = `One-to-one meeting with ${userInfo22.firstName} ${userInfo22.lastName}`;
} else {
  topicTitle = `Einzel-Gespräch mit ${userInfo22.firstName} ${userInfo22.lastName}`
}

const calendarData = {
  meetingId: eventId, updatedMeetingId: saveRecord.dataValues.id,
  userId: owner, companionId: comapanionId, summary: topicTitle,
  description: saveRecord.dataValues.description, startDate: saveRecord.dataValues.startDate,
  endDate: saveRecord.dataValues.endDate, joinLink: existEvent.dataValues.zoomLink || "",
  startLink: existEvent.dataValues.startLink || ""
}

console.log("========================================")
console.log("========================================")
console.log("========================================")
console.log("========================================")
console.log(calendarData)
console.log("========================================")
console.log("========================================")
console.log("========================================")
console.log("========================================")

Axios({ method: 'patch', url: `${process.env.ZOOM_DOMAIN}/calendar/event`, headers:{}, data: calendarData });

    await transaction.commit();
    let id= saveRecord.dataValues.id;
    let response= await Models.Event.findOne({where:{id:id},include:[
      {model: Models.Participants,as: "Participants"},
      {model: Models.Timing},
      {model: Models.Frequency}
    ]})
    return h.response({success: true,message: req.i18n.__(messages[groupId]["rescheduled"]),responseData: response}).code(200);
    
    
  }
  catch(error)
  {
    await transaction.rollback();
    console.error(error);
    return h.response({sucess:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
  }
}


exports.confirmAppointmant=async(req,h)=>{
  const transaction=await Models.sequelize.transaction();

  try{
   let eventId=req.payload.eventId;
   let option=req.payload.option;
   let notificationId=req.payload.notificationId;
   let record= await Models.Reschedule.findOne({where:{eventId:eventId}});
   if(!record)
   {
    await transaction.rollback();
    return h.response({sucess:false,message:req.i18n.__('NO_REQUEST_FOUND'),responseData:{}}).code(400);
   }
  let event=await Models.Event.findOne({where:{id:eventId}});
   if(!event)
   {
    await transaction.rollback();
    return h.response({sucess:false,message:req.i18n.__('APPOINTMENT_NOT_FOUND'),responseData:{}}).code(400);
   }
  //--------------
  let res={}
  let vetId=event.dataValues.updatedBy
  if(option===Constants.REQUEST.ACCEPT)
  {
  await event.update({
    startDate:record.dataValues.startDate,
    endDate:record.dataValues.endDate,
    updatedBy:req.auth.credentials.userData.User.id,
    status:1
  })
  // send notification for update 
  res=await updateNotification(notificationId,4,{eventId:eventId})
  let payload={
    typeId:6,
    replacements:{
      start_date:record.dataValues.startDate,
      title:event.dataValues.title
    },
    userId:vetId,
    data:{}
  }
  await Common.createRequest(Constants.URL.SOCKET_NOTIFICATION_SEND, 'post', payload)
}else{
  await event.update({
    updatedBy:req.auth.credentials.userData.User.id,
    status:1
  })
   // send notification for update 
   res=await updateNotification(notificationId,5,{eventId:eventId})
   let payload={
    typeId:7,
    replacements:{
      start_date:record.dataValues.startDate,
      title:event.dataValues.title
    },
    userId:vetId,
    data:{}
  }
  await Common.createRequest(Constants.URL.SOCKET_NOTIFICATION_SEND, 'post', payload)
}
  await record.destroy();
  await transaction.commit();
    return h.response({sucess:true,message:req.i18n.__('REQUEST_SUCCESSFULL'),responseData:{
      event:event,
      notification:res
    }}).code(200)
  }
  catch(error)
  {
    await transaction.rollback();
    console.error(error);
    return h.response({sucess:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
  }
 
}


exports.cancelMeeting=async(req,h)=>{
  const transaction=await Models.sequelize.transaction()
  try{
    const language = req.headers.language;
    const {id,reason}=req.query;
    const offset = req.headers.utcoffset ? req.headers.utcoffset : 0;
    let event=await Models.Event.findOne({where:{id}})
    if(!event)
    {
      await transaction.rollback();
      return h.response({success:false,message:req.i18n.__('RECORD_NOT_FOUND'),responseData:{}}).code(400)
    }
    
    if(event.status==10)
    {
      await transaction.rollback();
      return h.response({success:false,message:req.i18n.__('MEETING_IS_ALREADY_RESCHEDULED'),responseData:{}}).code(400)
    }
    if(event.status==0)
    {
      await transaction.rollback()
      return h.response({success:false,message:req.i18n.__('MEETING_IS_ALREADY_CANCELLED'),responseData:{}}).code(400)
    }
    await event.update({status:0,remark:reason,updatedBy:req.auth.credentials.userData.User.id,eventStatus: Constants.EVENT_STATUS.CANCELLED_WITH_REFUND},{transaction});
    // delete by zomm 
    if(event.dataValues.zoomId!==null)
    {
      let user= await Models.Participants.findOne({where:{eventId:id,role:'companion'},raw:true,nest:true})
      if(user)
      {
        let userDetails=await Models.User.findOne({where:{userId:user.userId},raw:true,nest:true})
        await Zoom.delete(event.dataValues.zoomId,userDetails.email,transaction)
      }
        //await Zoom.delete(event.dataValues.zoomId,transaction)
    }
    // refund order to user 

    
    let order=await Models.Order.findOne({where:{eventId:id}})
    if(order)
    {
      console.log("Refunding Payment")
      let data =  await Common.DgStoreRequest(`https://www.digistore24.com/api/call/refundPurchase/?purchase_id=${order.data.order_id}`)
      let refund= await Models.Refund.create({eventId:id,userId:req.auth.credentials.userData.User.id,amount:order.amount,orderId:order.id,data:data.data},{transaction})
    }

    let participants=await Models.Participants.findAll({where:{eventId:id},include:[{model:Models.User, attributes:['firstName','lastName','userId']}]})


    // for (const participant of participants) {
    //   let notificationData={
    //     replacements:{
    //       //new_start_date:startDate,
    //       start_date:event.startDate,
    //       user_name:participant?.dataValues?.User?.dataValues?.firstName+' '+participant?.dataValues?.User?.dataValues?.lastName
    //     },
    //     typeId:3,
    //     data:{eventId:event.dataValues.id},
    //     userId:participant?.dataValues?.User?.dataValues?.userId 
    //   }
    //   await WebhookServices.sendNotification(notificationData,transaction);

    // }
    // sending Notifications

    
    if(req.auth.credentials.userData.Role.includes('costumer')) {
      await Notification.send(id,'MEETING_CANCELLED',3,transaction, offset, language)
    } else {
      await Notification.send(id,'MEETING_CANCELLED_BY_COMPANION',3,transaction, offset, language)
    }

    let responseData=await Axios({
      method: 'patch', url: `${process.env.USER_ONBOARDING_DOMAIN}/earning-history-refund`, headers:{}, data: { meetingId: id }
    });

    console.log(responseData, " ==== refund response")

    const calendarData = {
      meetingId: id
    }
    
    Axios({ method: 'delete', url: `${process.env.ZOOM_DOMAIN}/calendar/event`, headers:{}, data: calendarData });


    await transaction.commit()
    return h.response({success:true,message:req.i18n.__('MEETING_SUCCESSFULLY_CANCELLED'),responseData:event}).code(200)
  }
  catch(error)
  {
    console.log('error',error)
    await transaction.rollback()
    return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
  }
}


// companion Upcoming meetings
exports.upcominMeetings=async(req,h)=>{
  try{
    const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
    const offset = (req.query.pageNumber - 1) * limit
    const orderByValue = req.query.orderByValue
    const userId=req.auth.credentials.userData.User.id
    let startDate = Moment().utc().format('YYYY-MM-DD HH:mm:ss')
    let endDate = Moment().add(30,'d').utc().format('YYYY-MM-DD HH:mm:ss')
    if(req.query.startDate && req.query.endDate) {
      if(Moment(req.query.endDate) < Moment(req.query.startDate)) {
        return h.response({success:false,message:req.i18n.__('END_DATE_CANNOT_BE_LESS_THAN_START_DATE'),responseData:{}}).code(400)
      }
      if(Moment(req.query.startDate) < Moment()) {
        startDate = Moment(req.query.startDate).utc().format('YYYY-MM-DD HH:mm:ss')
      } else {
        startDate = Moment(req.query.startDate).utc().format('YYYY-MM-DD HH:mm:ss')
      }
      endDate = Moment(req.query.endDate).utc().format('YYYY-MM-DD HH:mm:ss')
    }




    console.log(startDate, " ========================= " ,endDate)


    let  inOp=`1`
    events_data = await sequelize.query(
      `WITH recursive Date_Ranges AS
      (select (:qstartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < (:qendDate))
      select e.id as event, dr.Date as Date, e.start_date as startDate FROM event_events as e
      LEFT JOIN event_groups as eg on eg.id = e.group_id
      LEFT JOIN event_participants as ep on (ep.event_id =e.id)
      LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
      LEFT JOIN event_frequencies as ef on ef.event_id = e.id
      INNER JOIN Date_Ranges as dr
      where eg.id=:qgroupid 
      and e.deleted_at is null
      and e.status in (${inOp}) and e.payment_status = 1
      and (if(isnull(:quserid),1,ep.user_id=:quserid))
      and ((e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null))
      and (
          ef.id is null and (DATE(e.start_date)<=dr.Date and (e.end_date is null or DATE(e.end_date)>=dr.Date))
          OR (ef.type=1 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null) and mod(Datediff(e.start_date,dr.Date),ef.frequency)=0))
          OR(ef.type=2 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null)) and WEEKDAY(dr.date) = ef.day and mod(Datediff(IF(WEEKDAY(e.start_date)<ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),IF(WEEKDAY(e.start_date)>ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),e.start_date)),dr.Date),ef.frequency*7)=0)
          OR (ef.type=3 and (
                  (ef.week is null and ef.day=day(dr.Date))
                  OR (ef.week>0 and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
                  OR (ef.week=0 and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
              )
          )
          OR (ef.type=4 and (
                  (ef.week is null and ef.day=day(dr.Date) and ef.month=month(dr.Date))
                  OR (ef.week>0 and ef.month=month(dr.Date) and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
                  OR (ef.week=0 and ef.month=month(dr.Date) and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
              )
          )
      )
      ORDER BY e.start_date ${orderByValue}
      LIMIT :qoffset,:qlimit
      `,
      {replacements:{
        qstartDate: Moment(startDate).format('YYYY-MM-DD HH:mm:ss') , 
        qendDate: Moment(endDate).format('YYYY-MM-DD HH:mm:ss') ,
        qlimit:limit,
        qoffset:offset,
        qgroupid:2,
        quserid:userId
    },type: QueryTypes.SELECT}
    );
    let totalRecord= await sequelize.query(
      `WITH recursive Date_Ranges AS
      (select (:qstartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < (:qendDate))
      select count(e.id) as count FROM event_events as e
      LEFT JOIN event_groups as eg on eg.id = e.group_id
      LEFT JOIN event_participants as ep on (ep.event_id =e.id)
      LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
      LEFT JOIN event_frequencies as ef on ef.event_id = e.id
      INNER JOIN Date_Ranges as dr
      where eg.id=:qgroupid 
      and e.deleted_at is null
      and e.status in (${inOp}) and e.payment_status = 1
      and (if(isnull(:quserid),1,ep.user_id=:quserid))
      and ((e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null))
      and (
          ef.id is null and (DATE(e.start_date)<=dr.Date and (e.end_date is null or DATE(e.end_date)>=dr.Date))
          OR (ef.type=1 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null) and mod(Datediff(e.start_date,dr.Date),ef.frequency)=0))
          OR(ef.type=2 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null)) and WEEKDAY(dr.date) = ef.day and mod(Datediff(IF(WEEKDAY(e.start_date)<ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),IF(WEEKDAY(e.start_date)>ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),e.start_date)),dr.Date),ef.frequency*7)=0)
          OR (ef.type=3 and (
                  (ef.week is null and ef.day=day(dr.Date))
                  OR (ef.week>0 and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
                  OR (ef.week=0 and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
              )
          )
          OR (ef.type=4 and (
                  (ef.week is null and ef.day=day(dr.Date) and ef.month=month(dr.Date))
                  OR (ef.week>0 and ef.month=month(dr.Date) and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
                  OR (ef.week=0 and ef.month=month(dr.Date) and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
              )
          )
      )
      ORDER BY dr.Date ${orderByValue}
      `,
      {replacements:{
        qstartDate: Moment(startDate).format('YYYY-MM-DD HH:mm:ss') , 
        qendDate: Moment(endDate).format('YYYY-MM-DD HH:mm:ss') ,
        qlimit:limit,
        qoffset:offset,
        qgroupid:2,
        quserid:userId
    },type: QueryTypes.SELECT}
    );
    let records=[]
    for (const i of events_data) {
      let eventId=i.event
     
      let data=await Models.Event.findOne({
        where:{id:eventId, paymentStatus: 1},
        //attributes:['id','groupId','slots','duration','allDay','startDate','endDate','title','description','rrString','status','zoomDuration'],
        include:[
          {model:Models.Timing,attributes:['start','end','startTime','endTime']},
          {model:Models.Frequency},
          {model:Models.Order},
          {model:Models.Participants,as:'Participants',attributes:['userId','role'],include:[
            {model:Models.User}
          ]}
        ]})
        if(data == null) continue;
        records.push(data)
    }
    // console.log('totalRecord',totalRecord)
    const totalPages        = await Common.getTotalPages(totalRecord[0].count, limit);
    return h.response({success: true,message: req.i18n.__("SUCCESSFUL"),responseData:{
      records,
      count:records.length,
      totalRecords:totalRecord[0].count,
      totalPages
    }}).code(200);
    
}
catch(error)
{
  console.log('Error in getting upcoming meeting',error);
  return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
}
}


exports.completedMeetings=async(req,h)=>{
  try{
    const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
    const offset = (req.query.pageNumber - 1) * limit
    const orderByValue = req.query.orderByValue
    const userId=req.auth.credentials.userData.User.id
    let utcOffset = 0;
    // let utcOffset = req.headers.utcoffset || 0;

    let startDate = Moment('2023-01-01 00:00:00').utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')
    let endDate = Moment().utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')
    let currentDate = Moment().utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')
    if(req.query.startDate && req.query.endDate) {
      if(Moment(req.query.endDate) < Moment(req.query.startDate)) {
        return h.response({success:false,message:req.i18n.__('END_DATE_CANNOT_BE_LESS_THAN_START_DATE'),responseData:{}}).code(400)
      }
      startDate = Moment(req.query.startDate).utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')
      endDate = Moment(req.query.endDate).utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')
    }

    const eventsList = await Models.Event.findAndCountAll({
      limit: limit,
      offset: offset,
      //subQuery: false,
      distinct: true,
      order: [["endDate", "DESC"]],
      col: "id",
      where: {
        groupId: 2,
        status: { [Op.ne]: 10 },
        [Op.or]: [
          { status: 100 }, 
          { status: 1, startDate: {[Op.lt]: currentDate} },
          { startDate: {[Op.gt]: startDate}, endDate: {[Op.lt]: endDate }}
        ]
      },
      include: [
        {
          model: Models.Participants, as: "owner",
          where: { userId: userId },
          required: true
        },
        {model: Models.UserFeedback, as: "feedback", attributes: ["token"], required: false, where: { tokenStatus: 1 }},
          {model:Models.Invoice, as: "pendingInvoice", where: {isPaid: 0}, required: false},
          {model:Models.Invoice, as: "invoices"},
          {model:Models.Participants,as:'Participants',attributes:['userId','role'],include:[
            {model:Models.User}
          ]}
      ]
    });

    const totalPages        = await Common.getTotalPages(eventsList.count, limit);
    return h.response({success: true,message: req.i18n.__("SUCCESSFUL"),responseData:{
      records: eventsList.rows,
      perPage:limit,
      totalRecords:eventsList.count,
      totalPages
    }}).code(200);
    
  }
  catch(error)
  {
    console.log('Error in Getting meeting',error)
    return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
  }
}

exports.completedMeetingsForAdmin=async(req,h)=>{
  try{
    const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
    const offset = (req.query.pageNumber - 1) * limit
    const orderByValue = req.query.orderByValue
    const userId=req.query.userId;
    let utcOffset = 0;
    const customer = req.query.customer
    const companion = req.query.companion
    // let utcOffset = req.headers.utcoffset || 0;

    let startDate = Moment('2023-01-01 00:00:00').utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')
    let endDate = Moment().utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')
    let currentDate = Moment().utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')
    if(req.query.startDate && req.query.endDate) {
      if(Moment(req.query.endDate) < Moment(req.query.startDate)) {
        return h.response({success:false,message:req.i18n.__('END_DATE_CANNOT_BE_LESS_THAN_START_DATE'),responseData:{}}).code(400)
      }
      startDate = Moment(req.query.startDate).utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')
      endDate = Moment(req.query.endDate).utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')
    }

    let includes = [
      {model: Models.UserFeedback, as: "feedback", attributes: ["token"], required: false, where: { tokenStatus: 1 }},
      {model:Models.Invoice, as: "pendingInvoice", where: {isPaid: 0}, required: false},
      {model:Models.Invoice, as: "invoices"},
      {model:Models.Participants,as:'Participants',attributes:['userId','role'],include:[{model:Models.User}]}
    ]

    // includes.push(participantUserObject)
 
    if(customer !== null) {
      includes.push({
        model: Models.Participants, as: "owner",
        where: {role: "owner"},
        required: true,
        include:[
          {model:Models.User, where: {[Op.or]: [
            Sequelize.where(
              Sequelize.literal('CONCAT(`owner->User`.`first_name`, " ", `owner->User`.`last_name`)'),
              { [Op.like]: `%${customer}%` }
            ),
            {firstName: customer}, 
            { lastName: customer }
          ]}
        }
        ],
        attributes: []
      })
    }
    if(companion !== null) {
      includes.push({
        model: Models.Participants, as: "companion",
        where: {role: "companion"},
        required: true,
        include:[
          {model:Models.User, where: {[Op.or]: [
            Sequelize.where(
              Sequelize.literal('CONCAT(`companion->User`.`first_name`, " ", `companion->User`.`last_name`)'),
              { [Op.like]: `%${companion}%` }
            ),
            {firstName: companion}, 
            { lastName: companion }
          ]}
        }
        ],
        attributes: []
      })
    }

    let where = {
        groupId: 2,
        status: { [Op.ne]: 10 },
        [Op.or]: [
          { status: 100 }, 
          { status: 1, startDate: {[Op.lt]: currentDate} },
          { startDate: {[Op.gt]: startDate}, endDate: {[Op.lt]: endDate }}
        ]
      
    };
    if(req.query.status !== null) {
      where = {...where, eventStatus: req.query.status}
    }

    const eventsList = await Models.Event.findAndCountAll({
      limit: limit,
      offset: offset,
      //subQuery: false,
      distinct: true,
      order: [["endDate", "DESC"]],
      col: "id",
      where: where,
      include: includes
    });

    const totalPages        = await Common.getTotalPages(eventsList.count, limit);
    return h.response({success: true,message: req.i18n.__("SUCCESSFUL"),responseData:{
      records: eventsList.rows,
      perPage:limit,
      totalRecords:eventsList.count,
      totalPages
    }}).code(200);
    
  }
  catch(error)
  {
    console.log('Error in Getting meeting',error)
    return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
  }
}

// exports.completedMeetings=async(req,h)=>{
//   try{
//     const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
//     const offset = (req.query.pageNumber - 1) * limit
//     const orderByValue = req.query.orderByValue
//     const userId=req.auth.credentials.userData.User.id


//     let utcOffset = req.headers.utcoffset || 0;

//     let startDate = Moment('2023-01-01 00:00:00').utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')
//     let endDate = Moment().utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')
//     let currentDate = Moment().utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')
//     if(req.query.startDate && req.query.endDate) {
//       if(Moment(req.query.endDate) < Moment(req.query.startDate)) {
//         return h.response({success:false,message:req.i18n.__('END_DATE_CANNOT_BE_LESS_THAN_START_DATE'),responseData:{}}).code(400)
//       }
//       startDate = Moment(req.query.startDate).utc().format('YYYY-MM-DD HH:mm:ss')
//       endDate = Moment(req.query.endDate).utc().format('YYYY-MM-DD HH:mm:ss')
//     }
//     // and ((e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null))
//     let  inOp=`100`
//     events_data = await sequelize.query(
//       `WITH recursive Date_Ranges AS
//       (select (:qstartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < (:qendDate))
//       select e.id as event, dr.Date as Date FROM event_events as e
//       LEFT JOIN event_groups as eg on eg.id = e.group_id
//       LEFT JOIN event_participants as ep on (ep.event_id =e.id)
//       LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
//       LEFT JOIN event_frequencies as ef on ef.event_id = e.id
//       INNER JOIN Date_Ranges as dr
//       where eg.id=:qgroupid 
//       and e.deleted_at is null
//       and ( e.status in (${inOp}) or (e.status = 1 and e.start_date < :currentDate) and ((e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null)) )
//       and (if(isnull(:quserid),1,ep.user_id=:quserid))
//       GROUP BY e.id
//       ORDER BY e.start_date ${orderByValue}
//       LIMIT ${offset},${limit}
//       `,
//       {replacements:{
//         qstartDate: Moment(startDate).format('YYYY-MM-DD HH:mm:ss') , 
//         qendDate: Moment(endDate).format('YYYY-MM-DD HH:mm:ss') ,
//         qgroupid:2,
//         quserid:userId,
//         currentDate
//     },type: QueryTypes.SELECT}
//     );
//     console.log("events_data == ", events_data, " == events_data")
//     let totalRecord= await sequelize.query(
//       `WITH recursive Date_Ranges AS
//       (select (:qstartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < (:qendDate))
//       select count(e.id) as count FROM event_events as e
//       LEFT JOIN event_groups as eg on eg.id = e.group_id
//       LEFT JOIN event_participants as ep on (ep.event_id =e.id)
//       LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
//       LEFT JOIN event_frequencies as ef on ef.event_id = e.id
//       INNER JOIN Date_Ranges as dr
//       where eg.id=:qgroupid 
//       and e.deleted_at is null
//       and ( e.status in (${inOp}) or (e.status = 1 and e.start_date < :currentDate) and ((e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null)) )
//       and (if(isnull(:quserid),1,ep.user_id=:quserid))
//       GROUP BY e.id
//       ORDER BY e.start_date ${orderByValue}
//       `,
//       {replacements:{
//         qstartDate: Moment(startDate).format('YYYY-MM-DD HH:mm:ss') , 
//         qendDate: Moment(endDate).format('YYYY-MM-DD HH:mm:ss') ,
//         qgroupid:2,
//         quserid:userId,
//         currentDate
//     },type: QueryTypes.SELECT}
//     );
//     let records=[]
//     for (const i of events_data) {
//       let eventId=i.event
     
//       let data=await Models.Event.findOne({
//         where:{id:eventId},
//         // attributes:['id','groupId','slots','duration','allDay','startDate','endDate','title','description','rrString','status','zoomDuration'],
//         include:[
//           {model: Models.UserFeedback, as: "feedback", attributes: ["token"], required: false, where: { tokenStatus: 1 }},
//           {model:Models.Timing,attributes:['start','end','startTime','endTime']},
//           {model:Models.Frequency},
//           {model:Models.Invoice, as: "invoices"},
//           {model:Models.Participants,as:'Participants',attributes:['userId','role'],include:[
//             {model:Models.User}
//           ]}

//         ]})

//         let pendingInvoice = await Models.Invoice.findOne({where:{eventId: eventId,isPaid:0}});
//         let pendingInvoiceLink = pendingInvoice?.paymentLink ? pendingInvoice?.paymentLink : null;
//         data["paymentLink"] = pendingInvoiceLink
//         records.push(data)
//     }
//     let lengthCount = 0
//     if(totalRecord?.length > 0) {
//       lengthCount = totalRecord[0].count
//     }
//     // console.log(totalRecord, " ======================== totalRecord")
//     // console.log(totalRecord[0], " ======================== totalRecord[0]")
//     // console.log(totalRecord[0].count, " ======================== totalRecord[0].count")

//     const totalPages        = await Common.getTotalPages(lengthCount, limit);
//     return h.response({success: true,message: req.i18n.__("SUCCESSFUL"),responseData:{
//       records,
//       count:records.length,
//       totalRecord:lengthCount,
//       totalPages
//     }}).code(200);
    
//   }
//   catch(error)
//   {
//     console.log('Error in Getting meeting',error)
//     return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
//   }
// }

exports.listMeetingsForCustomerHistory=async(req,h)=>{
  try{
    const userId = req.query.userId;
    let utcOffset = req.headers.utcoffset || 0;

    let startDate = Moment('2023-01-01 00:00:00').utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')
    let endDate = Moment().utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')
    let currentDate = Moment().utc().add(utcOffset,"minutes").format('YYYY-MM-DD HH:mm:ss')

    let  inOp=`100`
    events_data = await sequelize.query(
      `WITH recursive Date_Ranges AS
      (select (:qstartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < (:qendDate))
      select e.id as event, dr.Date as Date FROM event_events as e
      LEFT JOIN event_groups as eg on eg.id = e.group_id
      LEFT JOIN event_participants as ep on (ep.event_id =e.id)
      LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
      LEFT JOIN event_frequencies as ef on ef.event_id = e.id
      INNER JOIN Date_Ranges as dr
      where eg.id=:qgroupid 
      and e.deleted_at is null
      and ( e.status in (${inOp}) or (e.status = 1 and e.start_date < :currentDate) and ((e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null)) )
      and (if(isnull(:quserid),1,ep.user_id=:quserid))
      GROUP BY e.id
      ORDER BY e.start_date DESC
      `,
      {replacements:{
        qstartDate: Moment(startDate).format('YYYY-MM-DD HH:mm:ss') , 
        qendDate: Moment(endDate).format('YYYY-MM-DD HH:mm:ss') ,
        qgroupid:2,
        quserid:userId,
        currentDate
    },type: QueryTypes.SELECT}
    );
    
    let totalRecord= await sequelize.query(
      `WITH recursive Date_Ranges AS
      (select (:qstartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < (:qendDate))
      select count(e.id) as count FROM event_events as e
      LEFT JOIN event_groups as eg on eg.id = e.group_id
      LEFT JOIN event_participants as ep on (ep.event_id =e.id)
      LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
      LEFT JOIN event_frequencies as ef on ef.event_id = e.id
      INNER JOIN Date_Ranges as dr
      where eg.id=:qgroupid 
      and e.deleted_at is null
      and ( e.status in (${inOp}) or (e.status = 1 and e.start_date < :currentDate) and ((e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null)) )
      and (if(isnull(:quserid),1,ep.user_id=:quserid))
      GROUP BY e.id
      ORDER BY e.start_date DESC
      `,
      {replacements:{
        qstartDate: Moment(startDate).format('YYYY-MM-DD HH:mm:ss') , 
        qendDate: Moment(endDate).format('YYYY-MM-DD HH:mm:ss') ,
        qgroupid:2,
        quserid:userId,
        currentDate
    },type: QueryTypes.SELECT}
    );
    let records=[]
    for (const i of events_data) {
      let eventId=i.event
     
      let data=await Models.Event.findOne({
        where:{id:eventId},
        attributes: ["id","groupId","startDate","endDate","status","title","description","createdBy","updatedBy","amount","totalAmount","eventStatus","currency","createdAt","updatedAt"],
        include:[
          {model:Models.Participants, where: { role: "companion" }, as:'Participants',attributes:['userId','role'],include:[
            {model:Models.User, attributes: ["id","userId","firstName","lastName","title","profilePhotoUrl","profilePhotoId","languages","reason","rating","gender","email","vita","experience"]}
          ]}

        ]})
        records.push(data)
    }
    let lengthCount = 0
    if(totalRecord?.length > 0) {
      lengthCount = totalRecord[0].count
    }

    return h.response({success: true,message: req.i18n.__("SUCCESSFUL"),responseData:{
      data: records,
      totalRecord:records.length
    }}).code(200);
    
  }
  catch(error)
  {
    console.log('Error in Getting meeting',error)
    return h.response({success:false,message:req.i18n.__('SOMETHING_WENT_WRONG'),responseData:{}}).code(500)
  }
}

const claculateInvoice = async(userId) => {
  try {

    let calculatedAmount= await sequelize.query(
    `     SELECT
     SUM(ROUND(CAST(JSON_EXTRACT(ei.data, '$.earned_amount') AS DECIMAL(10,2)) * 1.5, 2)) AS total_earned_amount
FROM event_events AS e
    INNER JOIN event_participants AS ep ON ep.event_id = e.id
    INNER JOIN event_invoices AS ei ON ei.event_id = e.id
WHERE ep.role = 'companion' 
    AND e.deleted_at IS NULL
    AND e.event_status = 6
    AND ei.deleted_at IS NULL
    AND ei.is_paid = 1 
    AND ep.user_id = ${userId};
    `,
      {type: QueryTypes.SELECT}
    );
    // let calculatedAmount= await sequelize.query(
    // ` 
    //   SELECT 
    //     SUM((CAST(JSON_EXTRACT(ei.data, '$.amount') AS DECIMAL (10 , 2 )) - CAST(JSON_EXTRACT(ei.data, '$.vat_amount') AS DECIMAL (10 , 2 ))) * 0.921) AS total_earned_amount
    //   FROM event_events AS e
    //     INNER JOIN event_participants AS ep ON ep.event_id = e.id
    //     INNER JOIN event_invoices AS ei ON ei.event_id = e.id
    //   WHERE ep.role = 'companion' AND e.deleted_at IS NULL AND ei.is_paid = 1 AND ep.user_id = ${userId}
    //   ORDER BY e.id DESC;
    // `,
    //   {type: QueryTypes.SELECT}
    // );

    let amount = calculatedAmount[0].total_earned_amount ? calculatedAmount[0].total_earned_amount : 0;

    const data = await Models.User.update({ totalEarning: amount }, { where: { userId: userId } });
    console.log(data, "====================== earned data ======================", amount)
    return true;
  } catch (error) {
    console.log(error);
    return false;
  }
}



exports.updatePaymentStatus=async(req,h)=>{
  const transaction = await Models.sequelize.transaction();
  try{
    const language = req.headers.language;
    let {data} = req.payload;
    let order = await decodeData(data)
    let eventId = order.custom;
    eventId = eventId.split(",");
    eventId = parseInt(eventId[0]);

    let toCheckOrderId = order.order_id;

    let orderExists = await sequelize.query(
      `SELECT id FROM event_invoices where JSON_UNQUOTE(JSON_EXTRACT(data, '$.order_id')) = "${toCheckOrderId}" and is_paid = 1;`,
        {type: QueryTypes.SELECT}
      );

      if(orderExists && orderExists.length > 0) {
        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_ORDER_SAVED"),responseData:{}}).code(200);
      }


    // const userId  =   req.auth.credentials.userData.User.id
    const offset = req.headers.utcoffset ? req.headers.utcoffset : 0;
    if(!eventId) {
      await transaction.rollback();
      return h.response({success: false,message: req.i18n.__("MEETING_RECORD_NOT_FOUND"),responseData:{}}).code(400);
    }
    let event = await Models.Event.findOne({where:{id:eventId}})
    if(!event) {
      await transaction.rollback()
      return h.response({success: false,message: req.i18n.__("MEETING_RECORD_NOT_FOUND"),responseData:{}}).code(400);
    }

    const userId = event.dataValues.createdBy
    let orderDetails = await Common.DgStoreRequest(`getPurchase/?purchase_id=${order.order_id}`)

    let save = await Models.Order.create({eventId,amount:order.amount.toString(),data:order,userId,orderDetails},{transaction})

    // await sequelize.query(
    //   `UPDATE user_validations SET meeting_count = meeting_count + 1 WHERE user_id = ${userId};`,
    //   { type: QueryTypes.UPDATE }
    // );

    
    
    let participents=await Models.Participants.findAll({raw:true,nest:true,where:{eventId:eventId}})
    let companionEmail=null;
    let companionId = null;
    let earning = 0;
    for (const iterator of participents) {
      if(iterator.role=='companion') {
        let user=await Models.User.findOne({where:{userId:iterator.userId}})
        companionEmail        = user.dataValues.email
        companionId = user.dataValues.userId
        earning = order?.earned_amount * 0.6;
        const companionEarining = user.totalEarning;
        const totalEarning = companionEarining + earning;
        // await claculateInvoice(companionId);
        // await user.update({totalEarning: totalEarning},{transaction});
        await save.update({companionId: companionId}, {transaction})
      }
     // await WebhookServices.sendNotification(notificationData,transaction);
    }

    // sending zoom meeting
    let zoommeetingLink = await Zoom.create(event.dataValues.id,companionEmail,transaction);
    
    const checkExistingEvent = await Models.Event.findOne({
      where: {startDate: event.startDate, eventStatus: {[Op.or]: [1,2]}},
      include: [
        {
          model: Models.Participants,
          as: "Participants",
          where: { role: "companion", userId: companionId }
        }
      ]
    })

    if(zoommeetingLink === false) {
      await transaction.rollback();

      console.log("Refunding Payment")
      let data =  await Common.DgStoreRequest(`https://www.digistore24.com/api/call/refundPurchase/?purchase_id=${order.order_id}`)

      console.log(data, " =================== data dump")

      console.log({eventId:eventId,userId:userId,amount:order.amount,orderId:save.id,data:data.data}, " =========== order log")

      let refund= await Models.Refund.create({eventId:eventId,userId:userId,amount:order.amount,orderId:save.id,data:data.data})

      return h.response({success: false,message: req.i18n.__("ERROR_WHILE_CREATING_LINK"),responseData:{}}).code(400);
    } 

    if(checkExistingEvent) {
      await transaction.rollback();

      console.log("checkExistingEvent ================ ")
      let data =  await Common.DgStoreRequest(`https://www.digistore24.com/api/call/refundPurchase/?purchase_id=${order.order_id}`)

      console.log(data, " =================== data dump in checkExistingEvent")

      let refund= await Models.Refund.create({eventId:eventId,userId:userId,amount:order.amount,orderId:save.id,data:data.data})

      return h.response({success: false,message: req.i18n.__("SLOT_NOT_AVAILABLE"),responseData:{}}).code(400);
    } 

    const userInfo = await Models.User.findOne({where: {userId: userId}});
    const companionInfo = await Models.User.findOne({where: {userId: companionId}});

    await updateStudentLimit(userId, companionId, transaction);
    //send notifications
    await Notification.send(event.dataValues.id,'MEETING_SCHEDULED',1,transaction, offset, language)
    // update zoom app
    let eventUpdateObj = {}
    if(event.dataValues.eventStatus === 0 || event.eventStatus === 0) {
      eventUpdateObj["eventStatus"] = Constants.EVENT_STATUS.BOOKED;
      eventUpdateObj["companionProfit"] = earning;

    } else if(event.dataValues.eventStatus === 7 || event.eventStatus === 7) {
      eventUpdateObj["eventStatus"] = Constants.EVENT_STATUS.COMPLETED_WITH_COMPLETED_PAYMENT;
    }
    eventUpdateObj["paymentStatus"] = 1;
    if(event.dataValues.currency === null) {
      let currency = order.currency;
      eventUpdateObj["currency"] = currency;
    }
    await event.update(eventUpdateObj,{transaction})
    await Models.Invoice.update({ isPaid: 1, data: order }, { where: { eventId: eventId }, transaction })



    let earningHistoryObj = {
      meetingId: eventId, data: order, amount: order?.amount,
      amountReceived: order?.earned_amount, userId: userId,
      companionId: companionId, userObject: userInfo,
      companionObject: companionInfo, meetingObject: event
    }

    let responseData=await Axios({
      method: 'post', url: `${process.env.USER_ONBOARDING_DOMAIN}/earning-history`, headers:{}, data: earningHistoryObj
    });

    let topicTitle = "";
    if(req.headers.language == "en") {
      topicTitle = `One-to-one meeting with ${userInfo.firstName} ${userInfo.lastName}`;
    } else {
      topicTitle = `Einzel-Gespräch mit ${userInfo.firstName} ${userInfo.lastName}`
    }

    const calendarData = {
      meetingId: eventId, userId: userId,
      companionId: companionId, summary: topicTitle,
      description: event.dataValues.description, startDate: event.dataValues.startDate,
      endDate: event.dataValues.endDate, joinLink: zoommeetingLink.zoomLink || "",
      startLink: zoommeetingLink.startUrl || ""
    }

    Axios({ method: 'post', url: `${process.env.ZOOM_DOMAIN}/calendar/event`, headers:{}, data: calendarData });

    await transaction.commit();

    await claculateInvoice(companionId);

    // let notificationdata = {
    //   replacements: { user_name: projectInfo.title, start_date: "companionName" },
    //   typeId: "meeting-scheduled", userId: userId,
    //   data: { eventId: saveRecord.dataValues.id },
    // };
    // createNotification(notificationdata)

    return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_ORDER_SAVED"),responseData:{
      order:save,
      event:event,
      companionInfo
    }}).code(200);
  }
  catch(error){
    console.log("error", error);
    await transaction.rollback();
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
}

exports.updateInvoice=async(req,h)=>{
  const transaction = await Models.sequelize.transaction();
  try{
    let{data}     =   req.payload;
    let   order   =   await decodeData(data)
    let   eventId =  order.custom
    eventId = eventId.split(",");
    eventId = parseInt(eventId[0]);
    if(!eventId)
    {
      await transaction.rollback()
      return h.response({success: false,message: req.i18n.__("NO_MEETING_FOUND"),responseData:{}}).code(400);
    }
    let   invoice =   await Models.Invoice.findOne({where:{eventId:eventId, isPaid: 0}, order: [["id", "desc"]]});
    if(!invoice)
    {
      await transaction.rollback()
    return h.response({success: false,message: req.i18n.__("NO_INVOICE_FOUND"),responseData:{}}).code(400);
    }
    await Models.Invoice.update({data:order,isPaid:1},{where: { eventId: eventId, isPaid: 0 }, transaction})
    let orderData     = await Models.Order.findOne({where:{eventId}})
    let Orderamount   = parseFloat(orderData.dataValues.amount)
    let invoiceAmount = parseFloat(invoice.dataValues.amount)
    let totalAmount   = Orderamount+invoiceAmount

    let event= await Models.Event.findOne({where:{id:eventId}})
    if(event)
    {
    let data=await Models.Setting.findOne({where:{key:'COMPANION_COMMISON'},attributes:['value'],raw:true,nest:true})
    if(!data)
    {
      await transaction.rollback()
      return h.response({success: false,message: req.i18n.__("SOMETING_WRONG_IN_ADMIN_CONFIGRATION"),responseData:{}}).code(400);
    }
    let dataValues=await Models.Setting.findOne({where:{key:'COMPANION_COMMISON'},attributes:['value'],raw:true,nest:true})
    if(!dataValues)
    {
      await transaction.rollback()
      return h.response({success: false,message: req.i18n.__("SOMETING_WRONG_IN_ADMIN_CONFIGRATION"),responseData:{}}).code(400);
    }
    let ADMIN_COMMISON=dataValues.value
    let COMPANION_COMMISON=data.value

      let companionProfit = (totalAmount*parseFloat(COMPANION_COMMISON))/100
      let adminProfit     = (totalAmount*parseFloat(ADMIN_COMMISON))/100
      await event.update({totalAmount:totalAmount.toString(),companionProfit:companionProfit.toString(),adminProfit:adminProfit.toString(), eventStatus: Constants.EVENT_STATUS.COMPLETED_WITH_COMPLETED_PAYMENT},{transaction})
    }

    const companionParticipant = await Models.Participants.findOne({ where: { eventId, role: "companion" } });
    const companionId = companionParticipant.userId;

    // await claculateInvoice(companionId);

    const userInfo = await Models.User.findOne({where: {userId: event.createdBy}});
    const companionInfo = await Models.User.findOne({where: {userId: companionId}});

    let earningHistoryObj = {
      meetingId: eventId, data: order, amount: order?.amount,
      amountReceived: order?.earned_amount, userId: event.createdBy,
      companionId: companionId, userObject: userInfo,
      companionObject: companionInfo, meetingObject: event
    }

    let responseData=await Axios({
      method: 'post', url: `${process.env.USER_ONBOARDING_DOMAIN}/earning-history`, headers:{}, data: earningHistoryObj
    });


    await transaction.commit()

    await claculateInvoice(companionId);


    return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_SAVED"),responseData:invoice}).code(200);
  }
  catch(error){
    console.log("error", error);
    await transaction.rollback();
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
}

exports.getInvoice=async(req,h)=>{
  try{
    const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
    const offset = (req.query.pageNumber - 1) * limit
    const orderByValue = req.query.orderByValue
    const orderByParameter = req.query.orderByParameter
    let where ={}
        if(req.query.userId!==null)
        {
            where= {...where,userId:req.query.userId}
        }

let options = {
    where,
    order       : [
            [orderByParameter, orderByValue]
        ],
    distinct:true,
    //subQuery    : false,
    include     : [{model:Models.User},{model:Models.Event,include:[{model:Models.Participants ,as :'Participants',include:[{model:Models.User}]}]}]
};
if (req.query.pageNumber !== null) 
    options = {
        ... options,
        limit,
        offset
    };
const cartProducts         = await Models.Invoice.findAndCountAll(options);
const totalPages        = await Common.getTotalPages(cartProducts.count, limit);
console.log('limit',limit,'offset',offset)
const responseData      = {
    totalPages,
    perPage: limit,
    records: cartProducts.rows,
    totalRecords: cartProducts.count,
    //cartValue:totalValue
};
return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData}).code(200);

}catch(error){
    console.error('Error in getting Attributes',error)
    return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
}
}


exports.test=async(req,h)=>{
  try{
    const companions = req.payload.companions;

    for(let item of companions) {
      await claculateInvoice(item)
    }

    return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_SAVED"),responseData:{}}).code(200);
  }
  catch(error){
    console.log("error", error);
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
}


const generateRsting=async(start,end,type,frequency,day,week,month,occurance)=>{
  try{
    start=Moment.utc(start)
    if(end!==null)end=Moment.utc(end)
    start =start.toDate();
    if(end!==null)  end=  end.toDate();
    let object = {}
    switch (type) {
      case Constants.TYPE.DAY: 
          object = { freq: RRule.DAILY, interval: frequency[0], dtstart: start, until: end,count:occurance}
        break;
      case Constants.TYPE.WeekDay: 
      if(!day || day.length < 1) return {success: false, message: "INVALID_DAY_FORMAT"}
      let verifyDay = day.find(dayData => dayData > 6)
      if(verifyDay) return {success: false, message: "INVALID_DAY_FORMAT"}
          object = { freq: RRule.WEEKLY, interval: frequency [0], dtstart: start, until: end, byweekday: day,count:occurance }
        break;
      case Constants.TYPE.Month: 
      if(!day || day.length < 1) return {success: false, message: "INVALID_DAY_FORMAT"}
      if(week && week.length > 0) {
        let verifyDay = day.find(dayData => dayData > 6 )
        if(verifyDay) return {success: false, message: "INVALID_DAY_FORMAT"}
        object = { freq: RRule.MONTHLY, interval: frequency[0], dtstart: start, until: end, byweekday: day, bysetpos: week ,count:occurance}
      } else {
        object = { freq: RRule.MONTHLY, interval: frequency[0], dtstart: start, until: end, bymonthday: day ,count:occurance}
      }
        break;
      case Constants.TYPE.Year: 
      if(!day || day.length < 1) return {success: false, message: "INVALID_DAY_FORMAT"}
      if(!month || month.length < 1) return {success: false, message: "INVALID_MONTH_FORMAT"}
      if(week && week.length > 0) {
        let verifyDay = day.find(dayData => dayData > 6)
        if(verifyDay) return {success: false, message: "INVALID_DAY_FORMAT"}
        object = { freq: RRule.YEARLY, interval: frequency[0], dtstart: start, until: end, bymonth: month, byweekday: day, bysetpos: week ,count:occurance}
      } else {
        object = { freq: RRule.YEARLY, interval: frequency[0], dtstart: start, until: end, bymonth: month, bymonthday: day,count:occurance }
      }
        break;
      default:
        break;
    }
    const rule = new RRule(object);
    return {data: rule.toString(), success: true, message: "RRULE_SUCCESSFULLY_CREATED"}
  }
  catch (e)
  {
    console.log('Error IN R String',e);
    return {success: false, message: 'ERROR_IN_CREATING_RRULE'}
  }
}



const createFrequncies=async(eventId,type,frequency,day,week,month,occurance,transaction)=>{
  try{
  switch (type) {
    case Constants.TYPE.DAY:
      {
        for (const iterator of frequency) {
          await Models.Frequency.create({type:type,frequency:iterator,eventId:eventId,occurance},{transaction});
        }
      }    

      break;
    case Constants.TYPE.WeekDay:
      {
        if(day===null)return {status:false,message:"DAY_IS_REQUIRED"}
        for (const f of frequency) {
          for (const d of day) {
            await Models.Frequency.create({type:type,day:d,frequency:f,eventId:eventId,occurance},{transaction})
          }
        }
      }    
      break;
      case Constants.TYPE.Month:
        {
        //day case 
        if(day===null) return {status:false,message:"DAY_IS_REQUIRED"};
        if(week===null){
          for (const f of frequency) {
            for (const d of day) {
              await Models.Frequency.create({type:type,day:d,frequency:f,eventId:eventId,occurance},{transaction})
            }
          }
        }
        //week case 

        if(week!==null && day!==null)
        for (const f of frequency) {
          for (const w of week) {
            for (const d of day) {
              await Models.Frequency.create({type:type,week:w,day:d,frequency:f,eventId:eventId,occurance},{transaction})
            }
          }
        }
        }    
        break;
      case Constants.TYPE.Year:
        {
          if(month==null) {
            return {tatus:false,message:"MONTH_CAN_NOT_BE_NULL"}
          }
          if(day==null){
            return {tatus:false,message:"DAY_CAN_NOT_BE_NULL"}
          }
    //month with day 
    if(month!==null && day!==null && week==null)
    {
    for (const f of frequency) {
      for (const m of month) {
        for (const d of day) {
          await Models.Frequency.create({type:type,month:m,day:d,frequency:f,eventId:eventId,occurance},{transaction})
        }
      }
    }
  }
    // month with week and day
    if(week!==null &&month!==null && day!==null)
    {
    for (const f of frequency) {
      for (const m of month) {
        for (const w of week) {
        for (const d of day) {
          await Models.Frequency.create({type:type,month:m,day:d,week:w,frequency:f,eventId:eventId,occurance},{transaction})
        }
      }
      }
    }
    //rstring= {freq: RRule.YEARLY, dtstart: startDate, until: endDate, interval: frequency[0],bymonth:month,byweekday:day,byweekno:week}
  }
        }    
        break;
      default:
        break;
  }
  return {status:true,message:""}
}
catch(e)
{
  return {status:false,message:"ERROR_IN_CREATION_OF_FREQUNCY"}
}
}


const validate=async(payload)=>{
  let type=payload.type;
  let frequency=payload.frequency;
  let day=payload.day;
  let week=payload.week;
  let month=payload.month;
  let groupId=payload.groupId;
  if(type===null )
  {
    if(frequency!==null)return {status:false,data:'TYPE_CAN_NOT_BE_NULL'}
  }
  else if(type!==null)
  {
    if(frequency===null)
      return {status:false,data:'INVALID_FREQUENCY_FIELD'}
    else if(frequency.length===0)
      return {status:false,data:'FREQUENCY_CANNOT_BE_EMPTY'}
    else if(frequency.includes(0))
      return {status:false,data:'FREQUENCY_CANNOT_BE_INCLUDE_ZERO'}
  }

  if(type===Constants.TYPE.DAY)
  {
    if(day!==null)
    return {status:false,data:'INVALID_DAY_FIELD'}

    if(week!==null)
    return {status:false,data:'INVALID_WEEK_FIELD'}

    if(month!==null)
    return {status:false,data:'INVALID_MONTH_FIELD'}
   
  }
  if(type===Constants.TYPE.WeekDay)
  {
    if(frequency===null)
    return {status:false,data:'INVALID_FREQUENCY_FIELD'}
    else if(frequency.length===0)
      return {status:false,data:'FREQUENCY_CANNOT_BE_EMPTY'}
    else if(frequency.includes(0))
      return {status:false,data:'FREQUENCY_CANNOT_BE_INCLUDE_ZERO'}
      
    if(day === null)
    return {status:false,data:'INVALID_DAY_FIELD'}
    else if(day.length<=0 )
    return {status:false,data:'DAY_CAN_BE_EMPTY'}
    else if(!(day.every(value => { return (value < 7 && value>=0) })))
    return {status:false,data:'DAY_CAN_BE_GRATER_THEN_SEVEN'}

    if(week!==null)
    return {status:false,data:'INVALID_WEEK_FIELD'}
    if(month!==null)
    return {status:false,data:'INVALID_MONTH_FIELD'}


  }
  if(type===Constants.TYPE.Month)
  {
    if(frequency===null)
    return {status:false,data:'INVALID_FREQUENCY_FIELD'}
    else if(frequency.length===0)
      return {status:false,data:'FREQUENCY_CANNOT_BE_EMPTY'}
    else if(frequency.includes(0))
      return {status:false,data:'FREQUENCY_CANNOT_BE_INCLUDE_ZERO'}

    if(month!==null)
      return {status:false,data:'INVALID_MONTH_FIELD'}

    if(day===null)
      return {status:false,data:'INVALID_DAY_FIELD'}
    
    if(week!==null)
     {
      if(!(week.every(value => { return (value < 4 && value>=0) })))
        return {status:false,data:'WEEK_CAN_BE_ZERO_TO_FOUR'}
      if(!(day.every(value => { return (value < 7 && value>=0) })))
        return {status:false,data:'DAY_CAN_BE_BETWEEN_ZERO_TO_SEVEN'}
     }
     if(week===null)
     {
      if(!(day.every(value => { return (value <=31 && value>0) })))
       return {status:false,data:'DAY_CAN_BE_ONE_TO_THERTYONE'}
     }
  }
  if(type===Constants.TYPE.Year)
  {
    if(frequency===null)
    return {status:false,data:'INVALID_FREQUENCY_FIELD'}
    else if(frequency.length===0)
      return {status:false,data:'FREQUENCY_CANNOT_BE_EMPTY'}
    else if(frequency.includes(0))
      return {status:false,data:'FREQUENCY_CANNOT_BE_INCLUDE_ZERO'}
      if(day===null)
      return {status:false,data:'INVALID_DAY_FIELD'}
      if(month===null)
      return {status:false,data:'INVALID_MONTH_FIELD'}
    if(!(month.every(value => { return (value <=12 && value>=1) })))
      return {status:false,data:'MONTH_CAN_BE_ONE_TO_TWALE'}
    if(week!==null)
    {
      if(!(day.every(value => { return (value < 7 && value>=0) })))
        return {status:false,data:'DAY_CAN_BE_ZERO_TO_SEVEN'}
    }
    else{
      if(!(day.every(value => { return (value <=31 && value>=0) })))
        return {status:false,data:'DAY_CAN_BE_ONE_TO_THERTYONE'}
    }
  }
  return {status:true,data:true}
}

exports.companionDashboardCount = async(req, h) => {
  try {
    const companionId = req.auth.credentials.userData.User.id;

    const totalUsersConnected= await sequelize.query(
      `
        SELECT distinct oe.user_id owner FROM event_participants ce INNER JOIN
        event_participants oe ON ce.event_id = oe.event_id
        AND oe.role = 'owner'
        INNER JOIN event_events e ON e.id = oe.event_id
        WHERE ce.role = 'companion' AND ce.user_id = ${companionId} AND e.payment_status = 1;
      `,
      {type: QueryTypes.SELECT}
    );

    let users = [];

    for(let user of totalUsersConnected) {
      users.push(user.owner)
    }

    const totalMeetings= await sequelize.query(
      `
      SELECT count(ep.event_id) as eventCount FROM event_participants as ep INNER JOIN event_events as e on e.id = ep.event_id where ep.user_id = ${companionId} and ep.role = 'companion' and e.status != 0 and e.status != 10 AND e.payment_status = 1;
      `,
      {type: QueryTypes.SELECT}
    );

    const userInfo = await Models.User.findOne({ attributes: ["id","userId", "totalEarning"], where: {userId: companionId} });

    let responseData = {users: users, meetingCount: totalMeetings[0].eventCount, earning: userInfo.totalEarning}

    return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
  } catch (error) {
    console.log(error)
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
}

exports.getOrders = async(req,h) =>{
  try{
      const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
      const offset = (req.query.pageNumber - 1) * limit
      const orderByValue = req.query.orderByValue
      const orderByParameter = req.query.orderByParameter

      // let categoryIds = req.query.categoryId; 
      let where ={}
      // if((req.auth.credentials.userData.Role.includes('admin')))
      // {
         
      //     if(req.query.userId!==null)
      //     {
      //         where= {...where,userId:req.query.userId}
      //     }
      // }
      // else{
      //     where= {userId:req.auth.credentials.userData.User.id}
      // }

      if(req.auth.credentials.userData.Role.includes('companion')) {
        where = {companionId: req.query.userId}
      } else if(req.auth.credentials.userData.Role.includes('costumer')) {
        where = {userId: req.query.userId}
      } else {
          if(req.query.userId!==null)
          {
            where= {...where,userId:req.query.userId}
          }
      }
     
      // if(categoryIds !== null) {
      //     where = {...where, [Op.and] : [Models.sequelize.literal("JSON_EXTRACT(`Order`.`custom`, '$[0].product.category.id') in ("+ categoryIds + ")")]}
      // }



      if(req.query.startDate && req.query.endDate) {
          if(Moment(req.query.endDate) < Moment(req.query.startDate)) {
            return h.response({success:false,message:req.i18n.__('END_DATE_CANNOT_BE_LESS_THAN_START_DATE'),responseData:{}}).code(400)
          }
          let startDate =  Moment(req.query.startDate).utc().format('YYYY-MM-DD HH:mm:ss');
          let endDate =  Moment(req.query.endDate).utc().format('YYYY-MM-DD HH:mm:ss');
          where = {...where, createdAt: {[Op.between]: [startDate, endDate]}}
      } else if(req.query.startDate) {
          let startDate =  Moment(req.query.startDate).utc().format('YYYY-MM-DD HH:mm:ss');
          where = {...where, createdAt: {[Op.gte]: startDate}}
      } else if(req.query.endDate) {
          let endDate =  Moment(req.query.endDate).utc().format('YYYY-MM-DD HH:mm:ss');
          where = {...where, createdAt: {[Op.lte]: endDate}}
      }

  let options = {
      //attributes:['productId','count'],
      where,
      include: [{
        model: Models.User
      }, {
        model: Models.User, as: "companion"
      }],
      order       : [
              [orderByParameter, orderByValue]
          ],
      subQuery    : false,
      // include     : [{model:Models.User}]
  };
  if (req.query.pageNumber !== null) 
      options = {
          ... options,
          limit,
          offset
      };
    
    

  const cartProducts         = await Models.Order.findAndCountAll(options);
  const totalPages        = await Common.getTotalPages(cartProducts.count, limit);
  const responseData      = {
      totalPages,
      perPage: limit,
      records: cartProducts.rows,
      totalRecords: cartProducts.count,
      //cartValue:totalValue
  };
  return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData}).code(200);

  }catch(error){
      console.error('Error in getting Attributes',error)
      return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
  }
}

exports.getOrderById = async(req,h) =>{
  try{
      let where ={}

  let options = {
      //attributes:['productId','count'],
      where: { id: req.query.orderId },
      include: [{
        model: Models.User
      }, {
        model: Models.User, as: "companion"
      }],
      subQuery    : false,
      // include     : [{model:Models.User}]
  };
    
    

  const cartProducts         = await Models.Order.findOne(options)

  return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: cartProducts}).code(200);

  }catch(error){
      console.error('Error in getting Attributes',error)
      return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
  }
}

exports.getEventSummary = async(req, h) => {
  try {
      const userId = req.query.userId;

      const userInfo = await Models.User.findOne({ where: { userId } });

      let eventInfo = await Models.Event.findAll({ 
          where: { createdBy: userId, eventStatus: {[Op.or]: [6,7]} },
          include: [
            {
              model: Models.Participants, as: "Participants",
              where: { role: "companion" },
              include: [{
                model: Models.User
              }]
            }
          ]
      });

      if(eventInfo) eventInfo = JSON.parse(JSON.stringify(eventInfo));

      const responseData = {
          userInfo: {
              userId: userInfo?.userId, 
              email: userInfo?.email, 
              firstName: userInfo?.firstName,
              lastName: userInfo?.lastName,
              title: userInfo?.title,
              userObject: userInfo?.userObject
          },
          eventInfo: eventInfo
      }

      return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
  } catch (error) {
      console.log(error)
      return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
}

exports.legacyMeetings = async(req, h) => {
  try {
      let userId = req.query.userId;
      if(userId === null) {
        userId = req.auth.credentials.userData.User.id;
      }
      const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT;
      const offset = (req.query.pageNumber - 1) * limit;

      let totalRecords = await sequelize.query(
        `SELECT count(id) as count FROM event_meeting_history where user_id = ${userId} or companion_id = ${userId};`,
        {type: QueryTypes.SELECT}
      );

      totalRecords = totalRecords[0].count;

      const records = await sequelize.query(
        `SELECT emh.id as id, emh.user_id as userId, emh.companion_id as companionId, emh.meeting_details as meetingDetails, 
          ec.user_object as companion, eu.user_object as user FROM event_meeting_history emh 
          LEFT JOIN event_users eu on emh.user_id = eu.user_id 
          LEFT JOIN event_users ec on emh.companion_id = ec.user_id 
          where emh.user_id = ${userId} or companion_id = ${userId}
          limit ${limit} offset ${offset};`,
        {type: QueryTypes.SELECT}
      );

      const totalPages = await Common.getTotalPages(totalRecords, limit);
      const responseData = {
        totalPages,
        perPage: limit,
        records: records,
        totalRecords: totalRecords
      };

      return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
  } catch (error) {
      console.log(error)
      return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
}

exports.removeSlots = async(req, h) => {
  try {
    const isCustomSlot = req.query.isCustomSlot;
    const userId = req.auth.credentials.userData.User.id;
    await Models.Event.destroy({ where: { createdBy: userId, isCustomSlot: isCustomSlot } });
    return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: {}}).code(200);
  } catch (error) {
    console.log(error)
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
}

exports.generateSlotReminder = async(req, h) => {
  const transaction = await Models.sequelize.transaction();
  try {

    const userId = req.auth.credentials.userData.User.id;
    const companionId = req.payload.companionId;

    const reminderExists = await Models.SlotReminder.findOne({ where: { userId, companionId } });
    if(!reminderExists) {
      await Models.SlotReminder.create({ userId, companionId });
      const companionInfo = await Models.User.findOne({ where: { userId: companionId } });
      const companionName = companionInfo?.firstName ?? "Companion";

      const userInfo = await Models.User.findOne({ where: { userId: userId } });
      const userName = (userInfo?.firstName + " " + userInfo?.lastName) ?? "User";

      const replacements = { companion: companionName,name: userName }
      const code = "WAITLIST_REQUEST";
      const recipients = [companionInfo.email];
      const language = req.headers.language;
      const sentEmail = await Notification.sendEmail(replacements,code,recipients,transaction, language)
      
    }

    // const userId = req.payload.userId;

    // const companionId = req.payload.companionId;
    // const status = req.payload.status;

    // const reminderExists = await Models.SlotReminder.findOne({ where: { userId, companionId } });

    // if(status === 1 && !reminderExists) {
    //   await Models.SlotReminder.create({ userId, companionId });
    // } else if(status === 0 && reminderExists) {
    //   await reminderExists.destroy({});
    // } else {
    //   return h.response({success:false,message:req.i18n.__('INVALID_STATUS'),responseData:{}}).code(400)
    // }

    // send companion the email



    await transaction.commit();
    return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: {}}).code(200);
  } catch (error) {
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
}

exports.slotReminderUserList = async(req, h) => {
  try {
    const companionId = req.auth.credentials.userData.User.id;
    const users = await Models.SlotReminder.findAll({ where: { companionId } });
    const usersArray = [];
    const createdAtArray = [];
    for(let item of users) {
      usersArray.push(item.userId);
      createdAtArray.push({userId: item.userId, createdAt: item.createdAt});
    }
    
    let responseData = [];

    if(usersArray.length > 0) {
      responseData = await Models.User.findAll({ where: { userId: usersArray } });
      responseData = JSON.parse(JSON.stringify(responseData));
      //Code to have add createdAt date from slot reminder table into the waitlist records
      for(let [index, item] of responseData.entries()){
        console.log(item.id, createdAtArray, 'mmmmmmmmmmm' );
        const matchingRecord = createdAtArray.find(record => record.userId == item.userId);
        console.log(matchingRecord, 'matchingRecord')
        if(matchingRecord){
          console.log(matchingRecord, 'matchingRecord2')
          responseData[index] = Object.assign(responseData[index], {createdAt: matchingRecord.createdAt});
        }
      }
    }

    return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
  } catch (error) {
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
}

exports.slotReminderCheck = async(req, h) => {
  try {
    const userId = req.auth.credentials.userData.User.id;
    const companionId = req.query.companionId;

    const check = await Models.SlotReminder.findOne({ where: { userId, companionId } });

    return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: check ? true : false}).code(200);
  } catch (error) {
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
}


// exports.getAllCompanionSlots = async (req, h) => {
//   try {
//     function matchMeetingsToSlots(slots, meetings) {
//       return slots.map((slot) => {
//         let updatedSubSlots = slot.slots.map((subSlot) => {
//           const isMeetingMatched = meetings.some(
//             (meeting) =>
//               meeting.companionId === slot.createdBy &&
//               Moment(meeting.startDate).isBetween(subSlot.startTime, subSlot.endTime, null, "[]")
//           );

//           return {
//             ...subSlot,
//             isBooked: isMeetingMatched
//           };
//         });

//         // Collect meetings that match any sub-slot within the slot
//         const matchingMeetings = meetings.filter(
//           (meeting) =>
//             meeting.companionId === slot.createdBy &&
//             updatedSubSlots.some((subSlot) =>
//               Moment(meeting.startDate).isBetween(subSlot.startTime, subSlot.endTime, null, "[]")
//             )
//         );

//         return {
//           ...slot,
//           slots: updatedSubSlots,
//           meetings: matchingMeetings.map((meeting) => ({
//             id: meeting.id,
//             startDate: meeting.startDate,
//             endDate: meeting.endDate
//           }))
//         };
//       });
//     }

//     let conversionInverse = Common.minutesToHoursInverse(utcOffset);

//     let slots = await sequelize.query(
//       `
//         SELECT 
//           e.id as id,
//           e.duration as duration,
//           e.start_date as startDate,
//           e.end_date as endDate,
//           e.title as title,
//           e.created_by as createdBy,
//           e.event_status as eventStatus,
//           e.status as status,
//           et.start_time as startTime,
//           et.end_time as endTime,
//           et.offset as timeZone,
//           eu.first_name as firstName,
//           eu.last_name as lastName,
//           eu.profile_photo_url as profileImage
//         FROM event_events e 
//         LEFT JOIN event_frequencies as ef on ef.event_id = e.id
//         LEFT JOIN event_timings as et on et.event_id = e.id
//         LEFT JOIN event_users as eu on eu.user_id = e.created_by
// 	      WHERE e.deleted_at is null
// 		      and e.status=1 
//           and ef.type = 1
// 		      and e.group_id=1
//           and e.is_custom_slot=1
//           and CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=:qendDate 
//           and CONVERT_TZ(e.end_date,'+00:00',:conversionInverse)>=:qstartDate;
//       `,
//       {
//         replacements: {
//           qstartDate: req.query.startDate,
//           qendDate: req.query.endDate,
//           conversionInverse: conversionInverse
//         },
//         type: QueryTypes.SELECT
//       }
//     );

//     let meetings = await sequelize.query(
//       `
//         SELECT
//           e.id as id,
//           e.start_date as startDate,
//           e.end_date as endDate,
//           e.created_by as createdBy,
//           e.event_status as eventStatus,
//           e.status as status,
//           ep.user_id as companionId
//         FROM event_events e 
//         INNER JOIN event_participants as ep on ep.event_id = e.id and ep.role="companion"
// 	      WHERE e.deleted_at is null 
// 		      and e.event_status in (1,2) 
// 		      and e.group_id=2
//           and e.start_date<=:qendDate 
//           and e.end_date>=:qstartDate;
//       `,
//       {
//         replacements: {
//           qstartDate: req.query.startDate,
//           qendDate: req.query.endDate,
//           conversionInverse: conversionInverse
//         },
//         type: QueryTypes.SELECT
//       }
//     );

//     const slotsData = [];

//     for (let event of slots) {
//       let allSlots = [];
//       let startTime = Number(event.startTime);
//       let duration = Number(event.duration);
//       let endTime = Number(event.endTime);
//       while (startTime + duration <= endTime) {
//         allSlots.push({
//           startTime: Moment(event.startDate).add(startTime, "minutes"),
//           endTime: Moment(event.startDate).add(startTime + duration, "minutes"),
//           isBooked: false
//         });
//         startTime = startTime + duration;
//       }

//       let object = { ...event, slots: allSlots };
//       // let dataObj = convertEventToLocal(object)

//       slotsData.push(object);
//     }

//     const data = matchMeetingsToSlots(slotsData, meetings);

//     console.log(JSON.stringify(data));

//     let responseData = [];

//     for (let item_1 of data) {
//       let object = { ...item_1 };
//       delete object["slots"];
//       delete object["meetings"];
//       for (let item_2 of item_1.slots) {
//         if (item_2.isBooked === false) {
//           responseData.push({
//             ...object,
//             s_startTime: item_2.startTime,
//             s_endTime: item_2.endTime
//           });
//         }
//       }
//     }

//     responseData.sort((a, b) => new Date(a.s_startTime) - new Date(b.s_startTime));

//     let finalResult = {};
//     for (let item of responseData) {
//       console.log(item);
//       const date = moment
//         .utc(item.s_startTime)
//         .tz(item.timeZone)
//         .startOf("day")
//         .format("YYYY-MM-DD");
//       if (!finalResult[date]) {
//         finalResult[date] = [];
//       }

//       finalResult[date].push(item);
//     }

//     return h
//       .response({ success: true, message: req.i18n.__("SUCCESSFUL"), responseData: finalResult })
//       .code(200);
//   } catch (err) {
//     console.log("error", err);
//     return h
//       .response({ success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {} })
//       .code(500);
//   }
// };


const timeZoneConversion = (inputDate, timeZone) => {
    // Input: UTC time string
  //const inputDate = "2025-01-20T18:30:00.000Z";
  
  // Step 1: Create a Date object from the UTC input
  const dateInUTC = new Date(inputDate);
  
  // Step 2: Convert the date to Asia/Kolkata timezone by adjusting the time
  const kolkataTime = dateInUTC.toLocaleString('en-US', {
    timeZone: timeZone,
    hour12: false
  });
  
  // Step 3: Extract the components from the formatted date string (DD/MM/YYYY, HH:MM:SS)
  const [datePart, timePart] = kolkataTime.split(', ');
  
  // Split the date part (DD/MM/YYYY)
  const [month, day, year] = datePart.split('/');
  
  // Split the time part (HH:MM:SS)
  const [hours, minutes, seconds] = timePart.split(':');
  
  // Step 4: Set the time to midnight (00:00:00) in Asia/Kolkata
  const kolkataMidnight = new Date(Date.UTC(year, month - 1, day, 0, 0, 0));
  
  // Step 5: Convert the midnight date back to UTC
  console.log(kolkataMidnight.toISOString()); // This should output: 2025-01-21T00:00:00.000Z

  return kolkataMidnight.toISOString();
}

exports.getAllCompanionSlots = async (req, h) => {
  try {
    let tt = req.headers.timezone;
    let timezoneArray = tt.split("|")
    let ssDate = timeZoneConversion(req.query.startDate, timezoneArray[0]) ;
    let eeDate = timeZoneConversion(req.query.endDate, timezoneArray[0]) ;

    const language = req.headers.language;

    const languageInfo = await sequelize.query(
      `
        SELECT id FROM userobd_languages where code = '${language}';
      `,
      {
        type: QueryTypes.SELECT
      }
    )

    if(languageInfo.length < 1) {
      return h.response({success: true, message: req.i18n.__("LANGUAGE_NOT_FOUND"), responseData: {}}).code(200);
    }

    const languageId = languageInfo[0].id;

    const matchMeetingsToSlots = (slots, meetings) => {
      return slots.map((slot) => {
        const updatedSubSlots = slot.slots.map((subSlot) => {
          let isMeetingMatched = meetings.some(
            (meeting) =>
              { 


                return ( meeting.companionId === slot.createdBy && 
                (moment(subSlot.startTime).isSameOrBefore(meeting.startDate) && moment(subSlot.endTime).isAfter(meeting.startDate)))
              }
          );

          if(moment().add(slot.scheduleTime, "hours") > moment(subSlot.startTime)) {
            isMeetingMatched = true
          }

          console.log(isMeetingMatched, " ================= isMeetingMatched")

          return {
            ...subSlot,
            isBooked: isMeetingMatched
          };
        });

        const matchingMeetings = meetings.filter(
          (meeting) =>
            meeting.companionId === slot.createdBy &&
            updatedSubSlots.some((subSlot) =>
              Moment(meeting.startDate).isBetween(subSlot.startTime, subSlot.endTime, null, true)
            )
        );

        return {
          ...slot,
          slots: updatedSubSlots,
          meetings: matchingMeetings.map((meeting) => ({
            id: meeting.id,
            startDate: meeting.startDate,
            endDate: meeting.endDate
          }))
        };
      });
    };

    const conversionInverse = Common.minutesToHoursInverse(utcOffset);

    // and CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=:qendDate 
    //         and CONVERT_TZ(e.end_date,'+00:00',:conversionInverse)>=:qstartDate

    const [slots, meetings] = await Promise.all([
      sequelize.query(
        `
          SELECT 
            e.id as id,
            e.duration as duration,
            e.start_date as startDate,
            e.end_date as endDate,
            e.title as title,
            e.created_by as createdBy,
            e.event_status as eventStatus,
            e.status as status,
            et.start_time as startTime,
            et.end_time as endTime,
            et.offset as timeZone,
            eu.user_id as companionId,
            eu.first_name as firstName,
            eu.last_name as lastName,
            eu.profile_photo_url as profileImage,
            (SELECT meeting_price from userobd_profiles where user_id = e.created_by) as meetingPrice,
            (SELECT schedule_time from userobd_profiles where user_id = e.created_by) as scheduleTime,
            (SELECT name FROM userobd_roles as r INNER JOIN userobd_user_roles as ur on r.id = ur.role_id where user_id = e.created_by) as role
          FROM event_events e 
          LEFT JOIN event_frequencies as ef on ef.event_id = e.id
          LEFT JOIN event_timings as et on et.event_id = e.id
          LEFT JOIN event_users as eu on eu.user_id = e.created_by
          LEFT JOIN userobd_user_languages ul on ul.user_id = e.created_by
          LEFT JOIN userobd_user_roles uur on uur.user_id = e.created_by
          LEFT JOIN userobd_users usersonb on usersonb.id = e.created_by
          WHERE e.deleted_at is null
            and usersonb.status = 1
            and e.status=1 
            and uur.role_id in (3,4)
            and ef.type = 1
            and e.group_id=1
            and e.is_custom_slot=1
            and e.start_date<=:qendDate 
            and e.end_date>=:qstartDate
            and ul.language_id = :languageId;
        `,
        {
          replacements: {
            qstartDate: ssDate,
            qendDate: eeDate,
            conversionInverse: conversionInverse,
            languageId: languageId
          },
          type: QueryTypes.SELECT
        }
      ),
      sequelize.query(
        `
          SELECT
            e.id as id,
            e.start_date as startDate,
            e.end_date as endDate,
            e.created_by as createdBy,
            e.event_status as eventStatus,
            e.status as status,
            ep.user_id as companionId
          FROM event_events e 
          INNER JOIN event_participants as ep on ep.event_id = e.id and ep.role="companion"
          WHERE e.deleted_at is null 
            and e.event_status in (1,2) 
            and e.group_id=2
            
            and e.start_date<=:qendDate 
            and e.end_date>=:qstartDate;
        `,
        {
          replacements: {
            qstartDate: ssDate,
            qendDate: eeDate,
            // qstartDate: req.query.startDate,
            // qendDate: req.query.endDate,
            conversionInverse: conversionInverse
          },
          type: QueryTypes.SELECT
        }
      )
    ]);

    const slotsData = slots.map((event) => {
      const allSlots = [];
      let startTime = Number(event.startTime);
      const duration = Number(event.duration);
      const endTime = Number(event.endTime);

      while (startTime + duration <= endTime) {
        allSlots.push({
          startTime: Moment(timeZoneConversion(event.startDate, event.timeZone)).add(startTime, "minutes"),
          
          dummystartTime: timeZoneConversion(event.startDate, event.timeZone),
          endTime: Moment(timeZoneConversion(event.startDate, event.timeZone)).add(startTime + duration, "minutes"),
          isBooked: false
        });
        startTime += duration;
      }

      return { ...event, slots: allSlots };
    });

    const data = matchMeetingsToSlots(slotsData, meetings);

    const responseData = data.flatMap((item) => {
      const { slots, meetings, ...rest } = item;
      return slots
        .filter((slot) => !slot.isBooked)
        .map((slot) => ({
          ...rest,
          s_startTime: slot.startTime,
          s_endTime: slot.endTime,
          dummystartTime: slot.dummystartTime
        }));
    }).sort((a, b) => new Date(a.s_startTime) - new Date(b.s_startTime));

    const finalResult = responseData.reduce((acc, item) => {
      const date = moment.utc(item.s_startTime).tz(timezoneArray[0]).startOf("day").format("YYYY-MM-DD");
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(item);
      return acc;
    }, {});

    return h.response({ success: true, message: req.i18n.__("SUCCESSFUL"), responseData: finalResult, meta: { slots,
      meetings, slotsData, data } }).code(200);
  } catch (err) {
    console.error("Error:", err);
    return h.response({ success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {} }).code(500);
  }
};


exports.getCopyForTest = async (req, h) => {
  try {
    let tt = req.headers.timezone;
    let timezoneArray = tt.split("|")
    let ssDate = timeZoneConversion(req.query.startDate, timezoneArray[0]) ;
    let eeDate = timeZoneConversion(req.query.endDate, timezoneArray[0]) ;
    let companionId = req.query.companionId;

    const language = req.headers.language;

    const languageInfo = await sequelize.query(
      `
        SELECT id FROM userobd_languages where code = '${language}';
      `,
      {
        type: QueryTypes.SELECT
      }
    )

    if(languageInfo.length < 1) {
      return h.response({success: true, message: req.i18n.__("LANGUAGE_NOT_FOUND"), responseData: {}}).code(200);
    }

    const languageId = languageInfo[0].id;

    const matchMeetingsToSlots = (slots, meetings) => {
      return slots.map((slot) => {
        const updatedSubSlots = slot.slots.map((subSlot) => {
          let isMeetingMatched = meetings.some(
            (meeting) =>
              { 


                console.log(" ======================= ")
                console.log(" ======================= ")
                console.log(" ======================= ")

                // if(moment().add(slot.scheduleTime, "hours") > moment(subSlot.startTime)) {

                //   console.log(moment().add(slot.scheduleTime, "hours"), " >>>>>> ", moment(subSlot.startTime))

                //   return true
                // }

                return ( meeting.companionId === slot.createdBy && 
                (moment(subSlot.startTime).isSameOrBefore(meeting.startDate) && moment(subSlot.endTime).isAfter(meeting.startDate)))
              }
          );

          if(moment().add(slot.scheduleTime, "hours") > moment(subSlot.startTime)) {

            console.log(moment().add(slot.scheduleTime, "hours"), " >>>>>> ", moment(subSlot.startTime))

            isMeetingMatched = true
          }

          console.log(isMeetingMatched, " ================= isMeetingMatched")

          return {
            ...subSlot,
            isBooked: isMeetingMatched
          };
        });

        const matchingMeetings = meetings.filter(
          (meeting) =>
            meeting.companionId === slot.createdBy &&
            updatedSubSlots.some((subSlot) =>
              Moment(meeting.startDate).isBetween(subSlot.startTime, subSlot.endTime, null, true)
            )
        );

        return {
          ...slot,
          slots: updatedSubSlots,
          meetings: matchingMeetings.map((meeting) => ({
            id: meeting.id,
            startDate: meeting.startDate,
            endDate: meeting.endDate
          }))
        };
      });
    };

    const conversionInverse = Common.minutesToHoursInverse(utcOffset);

    // and CONVERT_TZ(e.start_date,'+00:00',:conversionInverse)<=:qendDate 
    //         and CONVERT_TZ(e.end_date,'+00:00',:conversionInverse)>=:qstartDate

    const [slots, meetings] = await Promise.all([
      sequelize.query(
        `
          SELECT 
            e.id as id,
            e.duration as duration,
            e.start_date as startDate,
            e.end_date as endDate,
            e.title as title,
            e.created_by as createdBy,
            e.event_status as eventStatus,
            e.status as status,
            et.start_time as startTime,
            et.end_time as endTime,
            et.offset as timeZone,
            eu.user_id as companionId,
            eu.first_name as firstName,
            eu.last_name as lastName,
            eu.profile_photo_url as profileImage,
            (SELECT meeting_price from userobd_profiles where user_id = e.created_by) as meetingPrice,
            (SELECT schedule_time from userobd_profiles where user_id = e.created_by) as scheduleTime,
            (SELECT name FROM userobd_roles as r INNER JOIN userobd_user_roles as ur on r.id = ur.role_id where user_id = e.created_by) as role
          FROM event_events e 
          LEFT JOIN event_frequencies as ef on ef.event_id = e.id
          LEFT JOIN event_timings as et on et.event_id = e.id
          LEFT JOIN event_users as eu on eu.user_id = e.created_by
          LEFT JOIN userobd_user_languages ul on ul.user_id = e.created_by
          WHERE e.deleted_at is null
            and e.status=1 
            and e.created_by = :companionId
            and ef.type = 1
            and e.group_id=1
            and e.is_custom_slot=1
            and e.start_date<=:qendDate 
            and e.end_date>=:qstartDate
            and ul.language_id = :languageId;
        `,
        {
          replacements: {
            qstartDate: ssDate,
            qendDate: eeDate,
            conversionInverse: conversionInverse,
            languageId: languageId,
            companionId
          },
          type: QueryTypes.SELECT
        }
      ),
      sequelize.query(
        `
          SELECT
            e.id as id,
            e.start_date as startDate,
            e.end_date as endDate,
            e.created_by as createdBy,
            e.event_status as eventStatus,
            e.status as status,
            ep.user_id as companionId
          FROM event_events e 
          INNER JOIN event_participants as ep on ep.event_id = e.id and ep.role="companion"
          WHERE e.deleted_at is null 
            and e.event_status in (1,2) 
            and e.group_id=2
            and ep.user_id = :companionId
            and e.start_date<=:qendDate 
            and e.end_date>=:qstartDate;
        `,
        {
          replacements: {
            qstartDate: ssDate,
            qendDate: eeDate,
            // qstartDate: req.query.startDate,
            // qendDate: req.query.endDate,
            conversionInverse: conversionInverse,
            companionId
          },
          type: QueryTypes.SELECT
        }
      )
    ]);

    const slotsData = slots.map((event) => {
      const allSlots = [];
      let startTime = Number(event.startTime);
      const duration = Number(event.duration);
      const endTime = Number(event.endTime);

      while (startTime + duration <= endTime) {
        allSlots.push({
          startTime: Moment(timeZoneConversion(event.startDate, event.timeZone)).add(startTime, "minutes"),
          
          dummystartTime: timeZoneConversion(event.startDate, event.timeZone),
          endTime: Moment(timeZoneConversion(event.startDate, event.timeZone)).add(startTime + duration, "minutes"),
          isBooked: false
        });
        startTime += duration;
      }

      return { ...event, slots: allSlots };
    });

    const data = matchMeetingsToSlots(slotsData, meetings);

    const responseData = data.flatMap((item) => {
      const { slots, meetings, ...rest } = item;
      return slots
        .filter((slot) => !slot.isBooked)
        .map((slot) => ({
          ...rest,
          s_startTime: slot.startTime,
          s_endTime: slot.endTime,
          dummystartTime: slot.dummystartTime
        }));
    }).sort((a, b) => new Date(a.s_startTime) - new Date(b.s_startTime));

    const finalResult = responseData.reduce((acc, item) => {
      const date = moment.utc(item.s_startTime).tz(timezoneArray[0]).startOf("day").format("YYYY-MM-DD");
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(item);
      return acc;
    }, {});

    return h.response({ success: true, message: req.i18n.__("SUCCESSFUL"), responseData: finalResult, meta: { slots,
      meetings, slotsData, data } }).code(200);
  } catch (err) {
    console.error("Error:", err);
    return h.response({ success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {} }).code(500);
  }
};