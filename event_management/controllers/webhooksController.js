const Models = require("../models/index.js");
require('dotenv').config();
const Webhook=require('../services/webhook')
const Common=require('../common')
const MD5       = require('md5');
const {sendEmail} = require("../services/notification.js"); 
const constants = require("../constants.js");

const generateCode = (requestedlength) => {
    const char = "0123456789abcdefghijklmnopqrstuvwxyz!@#$%^&*()ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const length = typeof requestedlength !='undefined' ? requestedlength : 4;
    let randomvalue = '';
    for ( let i = 0; i < length; i++) {
      const value = Math.floor(Math.random() * char.length);
      randomvalue += char.substring(value, value + 1).toUpperCase();
    }
    return randomvalue;
  }


module.exports =
{
    upsertUser : async(req, h) => {
        const transaction = await Models.sequelize.transaction();
        try {
            // console.log('languages',languages)
            console.log(req.payload, " ================= event user data")
            await Models.User.upsert({
                ...req.payload
            }, {transaction})
            await transaction.commit();
            return h.response({success:true,message:req.i18n.__("USER_CREATED_SUCCESSFULLY"),responseData:{}}).code(201);
        }
        catch (error) {
            console.error(error);
            await transaction.rollback();
            return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
        }
    },
    
    updateZoomAccountId : async(req, h) => {
        const transaction = await Models.sequelize.transaction();
        try {
            // console.log('languages',languages)
            const userId = req.payload.userId;
            const userInfo = await Models.User.findOne({ where: { userId: userId } });
            if(!userInfo) {
                await transaction.rollback()
                return h.response({success:false,message:req.i18n.__("USER_NOT_EXIST"),responseData:{}}).code(400);
            }
            await userInfo.update({ zoomAccount: req.payload.zoomStatus }, { transaction });

            await transaction.commit();
            return h.response({success:true,message:req.i18n.__("USER_CREATED_SUCCESSFULLY"),responseData:{}}).code(201);
        }
        catch (error) {
            console.error(error);
            await transaction.rollback();
            return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
        }
    },
    
    updateZoomDetails:async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try {
            let{zoomId,audioRecording,videoRecording,audioLink,videoLink,zoomDuration,audioPassword,videoPassword}=req.payload
            console.log('req.payload',req.payload)
            let event =await Models.Event.findOne({where:{zoomId:zoomId}, order: [["id", "desc"]]});
            let prefferedLanguage = event?.prefferedLanguage ?? "de";
            if(!event) {
                await transaction.rollback()
                return h.response({success:false,message:req.i18n.__("EVENT_NOT_EXIST"),responseData:{}}).code(400);
            }

            let updateDetails = {status:100}
            if(audioRecording!==null) updateDetails.audioRecording = audioRecording
            if(videoRecording!==null) updateDetails.videoRecording = videoRecording
            if(audioLink!==null) updateDetails.audioLink = audioLink
            if(videoLink!==null) updateDetails.videoLink = videoLink
            if(audioPassword!==null) updateDetails.audioPassword = audioPassword
            if(videoPassword!==null) updateDetails.videoPassword = videoPassword;

            if(zoomDuration !== null) {
                updateDetails.zoomDuration = zoomDuration
                if(zoomDuration > constants.ZOOM_PAYMENT_DURATION) {
                    updateDetails["eventStatus"] = constants.EVENT_STATUS.COMPLETED_WITH_PENDING_PAYMENT;
                } else {
                    updateDetails["eventStatus"] = constants.EVENT_STATUS.COMPLETED_WITH_COMPLETED_PAYMENT;
                }
            }

            // MEETING_FINISH_PRODUCT_ID
            await event.update(updateDetails,{transaction})
            
            if(zoomDuration!==null) {
                // find Companion Price
                let price=0
                let customerId = null;
                let customerEmail = null;
                let customerName = null;
                let companionId = null;
                let companionEmail = null;
                let companionName = null;
                let participents= await Models.Participants.findAll({where:{eventId:event.dataValues.id},raw:true,nest:true})
                let meetingProductId = null;
                for (const iterator of participents) {
                    let user= await Models.User.findOne({where:{userId:iterator.userId}})
                    if(iterator.role=='companion') {
                        if(user) {
                            price=user.meetingPrice,
                            companionId = iterator.userId;
                            meetingProductId = user.meetingProductId;
                            companionEmail = user.email;
                            companionName = user.firstName;
                        }
                    }
                    if(iterator.role=='owner') {
                        customerId = iterator.userId;
                        customerEmail = user.email;
                        customerName = user.firstName;
                    }
                }
                if(meetingProductId === null) {
                    await transaction.rollback();
                    return h.response({ success: false, message: req.i18n.__("CONPANION_NEED_TO_SETUP_THE_ACCOUNT"), responseData: {}}).code(400)
                  }
                let chargeAmount=parseFloat(zoomDuration*price).toFixed(2)
                let eventTotalAmount = chargeAmount;
                await Models.Event.update({ totalAmount: eventTotalAmount }, { where: { id: event.dataValues.id }, transaction: transaction })
                // Detuct last Amount
                let order= await Models.Order.findOne({where:{eventId:event.dataValues.id}})
                chargeAmount=parseFloat(chargeAmount - order.dataValues.amount).toFixed(2)
                // chargeAmount=chargeAmount-parseFloat(order.dataValues.amount)
                console.log('chargeAmount',chargeAmount)
                if(chargeAmount<0) {
                    chargeAmount=constants.MIN_PAYMENT_AMOUNT;
                }
                let keyvalue=await Models.Setting.findOne({where:{key:'ZOOM_MEETING_PRODUCT_ID'},attributes:['value'],raw:true,nest:true})
    
                if(!keyvalue) {
                    await transaction.rollback()
                    return h.response({success: false,message: req.i18n.__("SOMETING_WRONG_IN_ADMIN_CONFIGRATION"),responseData:{}}).code(400);
                }
                let MEETING_FINISH_PRODUCT_ID=keyvalue.value
                let list    =   await Common.DgStoreRequest(`https://www.digistore24.com/api/call/createBuyUrl/?product_id=${meetingProductId}&payment_plan[first_amount]=${chargeAmount}&valid_until=forever&tracking[custom]=${event.dataValues.id},complete-payment`)
                // let alreadyInvoice=await Models.Invoice.findOne({where:{eventId:event.dataValues.id}})
                // if(alreadyInvoice)
                // {
                //     await transaction.rollback()
                //     return h.response({success:true,message:req.i18n.__("INVOICE_ALREADY_GENERATED"),responseData:{}}).code(400);
                // }
                let isPaid = 0
                if(zoomDuration <= constants.ZOOM_PAYMENT_DURATION) {
                    isPaid = 1
                }

                await Models.Invoice.create({eventId:event.dataValues.id,paymentLink:list.data.url,userId:event.dataValues.createdBy,amount:chargeAmount.toString(), isPaid},{transaction})

                let rawToken = generateCode(32);
                rawToken = MD5(rawToken);
                await Models.UserFeedback.create({ meetingId: event.id, userId: customerId, companionId, token: rawToken }, {transaction});
                let replacements = {
                    name: customerName,
                    link: `${process.env.DOMAIN_URL}/feedback/${rawToken}`
                }
                let companionReplacements = {
                    companion: companionName,
                    meetingWith: customerName,
                    date: Math.ceil(zoomDuration)
                }
                await sendEmail(replacements,"MEETING_FEEDBACK",[customerEmail],transaction, prefferedLanguage);
                await sendEmail(companionReplacements,"MEETING_COMPLETED",[companionEmail],transaction, prefferedLanguage);
            }

            if(videoLink !== null) {
                let startDate = Moment(event.dataValues.startDate).toString();
                let conversion = Common.minutesToHours(0);
                startDate = startDate.split(" ");
                startDate.pop();
                startDate = startDate.join(" ")
                startDate = startDate + `(${conversion})`
                let participents= await Models.Participants.findAll({where:{eventId:event.dataValues.id},raw:true,nest:true});
                let users = [];
                let sendToDetails = {};
                let meetingWithDetails = {};
                for (const iterator of participents) {
                    let user= await Models.User.findOne({where:{userId:iterator.userId}});
                    if(!user) continue;
                    if(!user?.email) continue;
                    if(iterator.role === "owner") {
                        sendToDetails["name"] = user.firstName;
                        sendToDetails["email"] = user.email;
                    } else {
                        meetingWithDetails["name"] = user.firstName;
                        meetingWithDetails["email"] = user.email;
                    }
                    
                    //users.push({name: user.firstName, email: user.email})
                }
                
                let vidRecLink = `${process.env.DOMAIN_URL}/user/meeting-history?meetingId=${event.id}`

                let eventZoomDuration = await Models.Event.findOne({ where: { id: event.id }, transaction });
                eventZoomDuration = eventZoomDuration?.zoomDuration ? parseInt(eventZoomDuration?.zoomDuration) : 0;

                let pendingPaymentLink = await Models.Invoice.findOne({ where: { eventId: event.id, isPaid: 0 }, transaction });
                if(pendingPaymentLink) {
                    pendingPaymentLink = pendingPaymentLink?.paymentLink;
                } else {
                    pendingPaymentLink = `${process.env.DOMAIN_URL}/user/meeting-history?meetingId=${event.id}`
                }

                let dataArray = [
                    { 
                        sendTo: sendToDetails.name, meetingWith: meetingWithDetails.name, date: eventZoomDuration, link: vidRecLink, password: videoPassword, email: sendToDetails.email, paymentLink: pendingPaymentLink
                    },
                    // { sendTo: users[1].name, meetingWith: users[0].name, date: startDate, link: videoLink, password: videoPassword, email: users[1].email }
                ];

                for(let replacements of dataArray) {
                    let email = replacements.email;
                    delete replacements["email"];
                    await sendEmail(replacements,"VIDEO_RECORDING",[email],transaction,prefferedLanguage)
                }
            }
            await transaction.commit();
            return h.response({success:true,message:req.i18n.__("USER_CREATED_SUCCESSFULLY"),responseData:{}}).code(200);
        }
        catch (error) {
            console.error(error);
            await transaction.rollback();
            return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
        }
    },
    createSettings:async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try{
            let {data}=req.payload
            let setting=await Models.Setting.bulkCreate(data, {updateOnDuplicate: ["key","value","type","label"] ,transaction:transaction});
            await transaction.commit();
            return h.response({success: true, message: req.i18n.__('SUCESSFULLY_DELETED'), responseData:setting}).code(200);
        }
        catch(error){
            console.log(error);
            await transaction.rollback();
            return h.response({success: false, message: req.i18n.__('SOMETHING_WENT_WRONG'), responseData: {}}).code(500);
        }
    },


}