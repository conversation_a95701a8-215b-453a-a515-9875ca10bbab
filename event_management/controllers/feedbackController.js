const { Op,QueryTypes, where } = require("sequelize");
exports.defaultQuestions = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const questions = [
          {
            "waitage": 5,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I have realized that I cannot improve everything in me at once, but I have to decide what to improve, heal or solve right now."
              },
              {
                "languageCode": "de",
                "question": "Ich habe erkannt, dass ich nicht alles auf einmal bei mir verbessern kann, sondern mich entscheiden muss, was ich jetzt verbessern, heilen oder lösen möchte."
              }
            ]
          },
          {
            "waitage": 0,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I do not know how to continue from this point."
              },
              {
                "languageCode": "de",
                "question": "Ich weiß nicht, wie ich nun weitermachen soll."
              }
            ]
          },
          {
            "waitage": 1,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I do not know what is meant by pain picture."
              },
              {
                "languageCode": "de",
                "question": "Ich weiß nicht, was mit Schmerzbild gemeint ist."
              }
            ]
          },
          {
            "waitage": 4,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I noticed that I have to put the focus on a certain person from my intimate network."
              },
              {
                "languageCode": "de",
                "question": "Ich habe gespürt, dass ich mich mit einer bestimmten Person aus meinem engeren Beziehungsgeflecht sehr viel mehr beschäftigen muss."
              }
            ]
          },
          {
            "waitage": 3,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "But I cannot let out my anger towards my torturer in this way!"
              },
              {
                "languageCode": "de",
                "question": "Ich kann doch nicht meinem Peiniger gegenüber meine Wut so direkt herauslassen."
              }
            ]
          },
          {
            "waitage": 2,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I do not understand why I am supposed to write down how everything happened, now that I have already told it."
              },
              {
                "languageCode": "de",
                "question": "Ich verstehe nicht, weshalb ich jetzt noch aufschreiben soll, wie das alles passiert ist, wenn ich es doch schon erzählt habe."
              }
            ]
          },
          {
            "waitage": 1,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I have so far not understood the connection between my physical symptoms and emotional suffering."
              },
              {
                "languageCode": "de",
                "question": "Der Zusammenhang meines körperlichen Symptoms mit einem seelischen Leiden ist mir nicht klar geworden."
              }
            ]
          },
          {
            "waitage": 6,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I have never seen my problem like this, so far the cause for my problem has played no role in my consciousness."
              },
              {
                "languageCode": "de",
                "question": "So habe ich mein Problem noch nie gesehen, bisher hat die Ursache für mein Problem in meinem Bewusstsein noch nie eine Rolle gespielt."
              }
            ]
          },
          {
            "waitage": 5,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I have already learnt many therapy methods but the KUBYmethod is something different."
              },
              {
                "languageCode": "de",
                "question": "Ich habe schon viele Therapiemethoden kennengelernt, aber die KUBYmethode ist doch etwas ganz anderes."
              }
            ]
          },
          {
            "waitage": 0,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "In this conversation I felt too patronized, I had the impression that I could not do anything right."
              },
              {
                "languageCode": "de",
                "question": "Ich fühlte mich in diesem Gespräch zu sehr bevormundet, ich hatte das Gefühl, nichts richtig zu machen."
              }
            ]
          },
          {
            "waitage": 8,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "In this conversation I felt very well understood and the companion gave me valuable impulses."
              },
              {
                "languageCode": "de",
                "question": "Ich fühle mich in diesem Gespräch sehr gut verstanden und die Begleitung gab mir wertvolle Impulse."
              }
            ]
          },
          {
            "waitage": 5,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I am supposed to imagine how to create a harmonic relationship with my torturer. But how is he expected to change? He has always been like that."
              },
              {
                "languageCode": "de",
                "question": "Ich soll mir jetzt ausdenken, wie es ganz harmonisch mit meinem Peiniger wird. Aber wie soll der sich denn ändern, der war doch so."
              }
            ]
          },
          {
            "waitage": 9,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I am so grateful. I have indeed a completely new feeling for this person who has so far always made my life difficult. It is like a wonder."
              },
              {
                "languageCode": "de",
                "question": "Ich bin so dankbar. Ich habe tatsächlich ein völlig neues Gefühl für die Person, mit der ich bisher immer Probleme hatte. Es ist wie ein Wunder."
              }
            ]
          },
          {
            "waitage": 7,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I am very agitated about the pain that came up inside me. I feel that there is more repressed stuff deep down."
              },
              {
                "languageCode": "de",
                "question": "Ich bin ziemlich aufgewühlt davon, was bei mir an Schmerz zutage kommt. Ich fühle, dass es mehr gibt, was bei mir noch verdrängt ist."
              }
            ]
          },
          {
            "waitage": 5,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I have realized now that my illness/ my problem already arose in my childhood."
              },
              {
                "languageCode": "de",
                "question": "Ich habe erkannt, dass meine Krankheit / mein Problem schon in meiner Kindheit entstanden ist."
              }
            ]
          },
          {
            "waitage": 2,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I do not understand how anyone from my childhood can help me, someone who never existed."
              },
              {
                "languageCode": "de",
                "question": "Ich verstehe nicht, wie mir jemand in meiner Kindheit helfen soll, den es nie gab."
              }
            ]
          },
          {
            "waitage": 0,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I do not know what the companion actually wanted me to do."
              },
              {
                "languageCode": "de",
                "question": "Ich weiß nicht, was die Begleitung von mir eigentlich wollte."
              }
            ]
          },
          {
            "waitage": 7,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I have recognized a cause for my project."
              },
              {
                "languageCode": "de",
                "question": "Ich habe eine Ursache für mein Projekt erkannt."
              }
            ]
          },
          {
            "waitage": 5,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "For the first time ever, I have felt great anger, but I sense that it is still deep down inside me."
              },
              {
                "languageCode": "de",
                "question": "Ich habe zum ersten Mal heftige Wut empfunden, aber ich spüre, dass sie noch immer in mir drin sitzt."
              }
            ]
          },
          {
            "waitage": 5,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I believe that I have to dive deeper to get at the cause of my disease."
              },
              {
                "languageCode": "de",
                "question": "Ich glaube, dass es bei mir noch tiefer gehen muss, um an die Ursache meines Leidens zu kommen."
              }
            ]
          },
          {
            "waitage": 5,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I cannot imagine that the so-called rewriting has an effect on me."
              },
              {
                "languageCode": "de",
                "question": "Ich kann mir nicht vorstellen, dass die sogenannte Umschreibung bei mir wirkt."
              }
            ]
          },
          {
            "waitage": 8,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I now want to go on working on my project with the KUBYmethod."
              },
              {
                "languageCode": "de",
                "question": "Jetzt möchte ich mit der KUBYmethode an meinem Projekt weiterarbeiten."
              }
            ]
          },
          {
            "waitage": 8,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I am surprised that thanks to the soul letter in present tense, my feeling about an event in the past has come up so strongly as I have never experienced it before."
              },
              {
                "languageCode": "de",
                "question": "Ich bin erstaunt, dass mein Gefühl auf ein Geschehen in der Vergangenheit durch die Gegenwartsform so stark hervorgekommen ist, wie ich das noch nie erlebt habe."
              }
            ]
          },
          {
            "waitage": 1,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I do not know the cause of my problem (project)."
              },
              {
                "languageCode": "de",
                "question": "Ich weiß nicht, wo die Ursache meines Problems (Projektes) liegt."
              }
            ]
          },
          {
            "waitage": 3,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I was not touched emotionally by my story."
              },
              {
                "languageCode": "de",
                "question": "Mich hat meine Geschichte emotional nicht berührt."
              }
            ]
          },
          {
            "waitage": 5,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "It is very hard for me to simply imagine (rewrite) a new reality."
              },
              {
                "languageCode": "de",
                "question": "Eine neue Wirklichkeit mir einfach auszudenken (umzuschreiben), fällt mir sehr schwer."
              }
            ]
          },
          {
            "waitage": 4,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "My rewriting does not convince me."
              },
              {
                "languageCode": "de",
                "question": "Meine Umschreibung überzeugt mich nicht."
              }
            ]
          },
          {
            "waitage": 7,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I feel very much understood by the way the conversation was led."
              },
              {
                "languageCode": "de",
                "question": "Ich fühlte mich durch die Art der Gesprächsführung sehr gut verstanden."
              }
            ]
          },
          {
            "waitage": 6,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "Now I know that my physical disease has an emotional cause."
              },
              {
                "languageCode": "de",
                "question": "Jetzt weiß ich, dass meine körperliche Krankheit eine seelische Ursache hat."
              }
            ]
          },
          {
            "waitage": 5,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I see now that this behavior pattern has lasted for several generations."
              },
              {
                "languageCode": "de",
                "question": "Ich sehe jetzt, dass das Verhaltensmuster schon mehrere Generationen besteht."
              }
            ]
          },
          {
            "waitage": 2,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I have found several ain pictures and do not know with which to proceed now."
              },
              {
                "languageCode": "de",
                "question": "Ich bin auf mehrere Schmerzbilder gekommen und weiß jetzt nicht, mit welchem ich weiterarbeiten soll."
              }
            ]
          },
          {
            "waitage": 3,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "The companion hardly let me say anything."
              },
              {
                "languageCode": "de",
                "question": "Die Begleitung hat mich wenig zu Wort kommen lassen."
              }
            ]
          },
          {
            "waitage": 7,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "The pack of lies I was told in my socialization has surfaced in this conversation."
              },
              {
                "languageCode": "de",
                "question": "Die Lügen in meiner Sozialisation sind im Gespräch ans Licht gekommen."
              }
            ]
          },
          {
            "waitage": 7,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I became very aware of the big difference between the past tense and the present tense when being in the scene."
              },
              {
                "languageCode": "de",
                "question": "Mir ist der große Unterschied zwischen Gegenwarts- und Vergangenheitsform im Sprechen sehr bewusst geworden."
              }
            ]
          },
          {
            "waitage": 0,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "For me, thethe conversation was time wasted."
              },
              {
                "languageCode": "de",
                "question": "Das Gespräch war für mich vergeudete Zeit."
              }
            ]
          },
          {
            "waitage": 10,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I write down now the pain picture I became aware of in script style."
              },
              {
                "languageCode": "de",
                "question": "Ich schreibe jetzt das bewusst gewordene Schmerzbild im Drehbuchstil auf."
              }
            ]
          },
          {
            "waitage": 10,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "Before I start with the rewriting, I write down the pain picture and determine at what moment my rewriting begins."
              },
              {
                "languageCode": "de",
                "question": "Bevor ich mit der Umschreibung beginne, schreibe ich das Schmerzbild auf und lege fest, ab welchem Moment meine Umschreibung beginnt."
              }
            ]
          },
          {
            "waitage": 8,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I have understood why I have to look for the spiritual-emotional cause if I want to reach a lasting healing or solve my problem."
              },
              {
                "languageCode": "de",
                "question": "Ich habe verstanden, weshalb ich nach der geistig-seelischen Ursache suchen muss, wenn ich mich nachhaltig heilen oder mein Problem lösen möchte."
              }
            ]
          },
          {
            "waitage": 9,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I understand now the structural terms like project, pain picture, link and rewriting."
              },
              {
                "languageCode": "de",
                "question": "Ich verstehe jetzt die strukturierenden Begriffe, wie Projekt, Schmerzbild, Brücke und Umschreibung."
              }
            ]
          },
          {
            "waitage": 7,
            "category": 1,
            "content": [
              {
                "languageCode": "en",
                "question": "I have realized which unsolved conflict is the cause of my symptoms."
              },
              {
                "languageCode": "de",
                "question": "Mir ist bewusst geworden, welcher ungelöster Konflikt die Ursache meines Symptoms ist."
              }
            ]
          },
          {
            "waitage": 4,
            "category": 2,
            "content": [
              {
                "languageCode": "en",
                "question": "Only now have I found my project and understand why it is essential to stay with one scene."
              },
              {
                "languageCode": "de",
                "question": "Jetzt erst habe ich mein Projekt gefunden und verstehe, weshalb es wichtig ist, bei einer Szene zu bleiben."
              }
            ]
          },
          {
            "waitage": 0,
            "category": 2,
            "content": [
              {
                "languageCode": "en",
                "question": "Even after this conversation, I ignore how to proceed with my problem."
              },
              {
                "languageCode": "de",
                "question": "Ich weiß auch nach diesem Gespräch nicht, wie ich mit meinem Problem weiterkomme."
              }
            ]
          },
          {
            "waitage": 0,
            "category": 2,
            "content": [
              {
                "languageCode": "en",
                "question": "What do you call pain picture, and what is a project?"
              },
              {
                "languageCode": "de",
                "question": "Was nennt man Schmerzbild und was Projekt?"
              }
            ]
          },
          {
            "waitage": 3,
            "category": 2,
            "content": [
              {
                "languageCode": "en",
                "question": "I realize now that I have to put the focus on my father / my mother."
              },
              {
                "languageCode": "de",
                "question": "Mir ist jetzt bewusst geworden, dass ich mich mit meinem Vater / mit meiner Mutter intensiv beschäftigen muss."
              }
            ]
          },
          {
            "waitage": 3,
            "category": 2,
            "content": [
              {
                "languageCode": "en",
                "question": "But I cannot let out my anger towards my torturer in such a direct way!"
              },
              {
                "languageCode": "de",
                "question": "Ich kann doch nicht meinem Peiniger gegenüber meine Wut so direkt herauslassen."
              }
            ]
          },
          {
            "waitage": 1,
            "category": 2,
            "content": [
              {
                "languageCode": "en",
                "question": "I could not write anything after the first conversation."
              },
              {
                "languageCode": "de",
                "question": "Ich konnte nach dem ersten Gespräch nichts schreiben."
              }
            ]
          },
          {
            "waitage": 0,
            "category": 2,
            "content": [
              {
                "languageCode": "en",
                "question": "The connection between my physical symptoms and my emotional disease is not yet obvious to me."
              },
              {
                "languageCode": "de",
                "question": "Der Zusammenhang meines körperlichen Symptoms mit einem seelischen Leiden ist mir noch nicht klar geworden."
              }
            ]
          },
          {
            "waitage": 7,
            "category": 2,
            "content": [
              {
                "languageCode": "en",
                "question": "Now I got it! I see clearly why I have my problem / my illness."
              },
              {
                "languageCode": "de",
                "question": "Jetzt hat es bei mir klick gemacht. Es ist mir jetzt ganz klar, weshalb ich mein Problem / meine Krankheit habe."
              }
            ]
          },
          {
            "waitage": 4,
            "category": 2,
            "content": [
              {
                "languageCode": "en",
                "question": "I believe I have understood the KUBYmethod now."
              },
              {
                "languageCode": "de",
                "question": "Ich glaube, ich habe jetzt die KUBYmethode verstanden."
              }
            ]
          },
          {
            "waitage": 7,
            "category": 2,
            "content": [
              {
                "languageCode": "en",
                "question": "In this conversation I felt very well understood, and I have received valuable impulses."
              },
              {
                "languageCode": "de",
                "question": "In diesem Gespräch fühlte ich mich sehr gut verstanden, und ich habe sehr wertvolle Impulse mitgenommen."
              }
            ]
            },
            {
              "waitage": 6,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have found a way to rewrite my torturer."
                },
                {
                  "languageCode": "de",
                  "question": "Ich habe einen Weg gefunden, wie ich meinen Peiniger umschreiben kann."
                }
              ]
            },
            {
              "waitage": 9,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I am so grateful. I have indeed a completely new feeling for this person who always gave me problems. It is like a wonder."
                },
                {
                  "languageCode": "de",
                  "question": "Ich bin so dankbar. Ich habe tatsächlich ein völlig neues Gefühl für die Person, mit der ich bisher immer Probleme hatte. Es ist wie ein Wunder."
                }
              ]
            },
            {
              "waitage": 7,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I am very agitated about the pain that came up inside me."
                },
                {
                  "languageCode": "de",
                  "question": "Ich bin ziemlich aufgewühlt davon, was bei mir an Schmerz alles zutage kommt."
                }
              ]
            },
            {
              "waitage": 4,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have realized now that my illness/ my problem already arose in my childhood."
                },
                {
                  "languageCode": "de",
                  "question": "Ich habe jetzt erst erkannt, dass meine Krankheit / mein Problem schon in meiner Kindheit entstanden ist."
                }
              ]
            },
            {
              "waitage": 2,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have understood the spiritual helper that he will solve my problem for me, because I am so helpless."
                },
                {
                  "languageCode": "de",
                  "question": "Ich habe den geistigen Helfer so verstanden, dass er für mich mein Problem löst, weil ich so hilflos bin."
                }
              ]
            },
            {
              "waitage": 0,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I ignore what the companion actually wanted me to do."
                },
                {
                  "languageCode": "de",
                  "question": "Ich weiß nicht, was die Begleitung von mir eigentlich wollte."
                }
              ]
            },
            {
              "waitage": 6,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have recognized one cause of my project."
                },
                {
                  "languageCode": "de",
                  "question": "Ich habe eine Ursache für mein Projekt erkannt."
                }
              ]
            },
            {
              "waitage": 6,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "For the first time I have the courage to let out my anger."
                },
                {
                  "languageCode": "de",
                  "question": "Ich habe zum ersten Mal den Mut, meine Wut heraus zu lassen."
                }
              ]
            },
            {
              "waitage": 3,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have the feeling that I still cannot reach the cause of my illness."
                },
                {
                  "languageCode": "de",
                  "question": "Ich habe das Gefühl, noch immer nicht an die Ursache meines Leidens zu kommen."
                }
              ]
            },
            {
              "waitage": 7,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I am confident that the rewriting will work."
                },
                {
                  "languageCode": "de",
                  "question": "Ich habe das Vertrauen, dass die Umschreibung bei mir wirkt."
                }
              ]
            },
            {
              "waitage": 8,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I now want to continue with my project by applying the KUBYmethod."
                },
                {
                  "languageCode": "de",
                  "question": "Jetzt möchte ich mit der KUBYmethode an meinem Projekt weiterarbeiten."
                }
              ]
            },
            {
              "waitage": 8,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I am surprised that the script style can cause such a strong feeling for something that happened in the past."
                },
                {
                  "languageCode": "de",
                  "question": "Ich bin erstaunt, dass der Drehbuchstil ein so starkes Gefühl hervorruft, für etwas, das weit in der Vergangenheit passiert ist."
                }
              ]
            },
            {
              "waitage": 1,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I still do not know the cause of my problem (project)."
                },
                {
                  "languageCode": "de",
                  "question": "Ich weiß noch immer nicht, wo die Ursache meines Problems (Projektes) liegt."
                }
              ]
            },
            {
              "waitage": 3,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "My story did not touch me emotionally."
                },
                {
                  "languageCode": "de",
                  "question": "Mich hat meine Geschichte emotional nicht berührt."
                }
              ]
            },
            {
              "waitage": 5,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "It was very difficult for me to simply create a new reality rewriting my story."
                },
                {
                  "languageCode": "de",
                  "question": "Eine neue Wirklichkeit mir einfach auszudenken (umzuschreiben), fällt mir sehr schwer."
                }
              ]
            },
            {
              "waitage": 4,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "My rewriting does not convince me."
                },
                {
                  "languageCode": "de",
                  "question": "Meine Umschreibung überzeugt mich nicht."
                }
              ]
            },
            {
              "waitage": 7,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I felt very well understood thanks to the way the companion led the conversation."
                },
                {
                  "languageCode": "de",
                  "question": "Ich fühlte mich durch die Art der Gesprächsführung sehr gut verstanden."
                }
              ]
            },
            {
              "waitage": 6,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "Now I know that my physical illness has an emotional cause in the past."
                },
                {
                  "languageCode": "de",
                  "question": "Jetzt weiß ich, dass meine körperliche Krankheit eine seelische Ursache in der Vergangenheit hat."
                }
              ]
            },
            {
              "waitage": 5,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I can see now that this sickening behavior pattern arose a long time ago."
                },
                {
                  "languageCode": "de",
                  "question": "Ich sehe jetzt, dass das kranke Verhaltensmuster schon mehrere Generationen besteht."
                }
              ]
            },
            {
              "waitage": 2,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have found several pain pictures and do not know now with which to continue."
                },
                {
                  "languageCode": "de",
                  "question": "Ich bin auf mehrere Schmerzbilder gekommen und weiß jetzt nicht, mit welchem ich weiterarbeiten soll."
                }
              ]
            },
            {
              "waitage": 3,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "The companion hardly let me talk."
                },
                {
                  "languageCode": "de",
                  "question": "Die Begleitung hat mich wenig zu Wort kommen lassen."
                }
              ]
            },
            {
              "waitage": 7,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "The pack of lies I was told in my socialization has surfaced during the conversation."
                },
                {
                  "languageCode": "de",
                  "question": "Die Lügen in meiner Sozialisation sind im Gespräch ans Licht gekommen."
                }
              ]
            },
            {
              "waitage": 7,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I am now aware of the great difference between writing and talking in the past tense and writing and talking in the present tense."
                },
                {
                  "languageCode": "de",
                  "question": "Mir ist der große Unterschied zwischen Gegenwarts- und Vergangenheitsform im Schreiben und Sprechen sehr bewusst geworden."
                }
              ]
            },
            {
              "waitage": 0,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "The conversation has not helped me at all."
                },
                {
                  "languageCode": "de",
                  "question": "Das Gespräch hat mich nicht weitergebracht."
                }
              ]
            },
            {
              "waitage": 8,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I will now write down the pain picture in script style."
                },
                {
                  "languageCode": "de",
                  "question": "Ich schreibe jetzt das bewusst gewordene Schmerzbild im Drehbuchstil auf."
                }
              ]
            },
            {
              "waitage": 8,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "In my pain picture I have realized now where to draw the red line in order to create a new reality. (This is called rewriting)"
                },
                {
                  "languageCode": "de",
                  "question": "In meinem Schmerzbild habe ich nun erkannt, wo ich den roten Strich mache, um ab dort die neue Wirklichkeit zu erschaffen. (Das ist die „Umschreibung“.)"
                }
              ]
            },
            {
              "waitage": 4,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have now understood why I have to look for the spiritual-emotional cause if I want to be healthy again or solve my problem lastingly."
                },
                {
                  "languageCode": "de",
                  "question": "Ich habe jetzt verstanden, weshalb ich nach der geistig-seelischen Ursache suchen muss, wenn ich mich nachhaltig heilen oder mein Problem lösen möchte."
                }
              ]
            },
            {
              "waitage": 5,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I understand now the structural terms like project, pain picture, link and rewriting."
                },
                {
                  "languageCode": "de",
                  "question": "Ich verstehe jetzt die strukturierenden Begriffe, wie Projekt, Schmerzbild, Brücke und Umschreibung."
                }
              ]
            },
            {
              "waitage": 7,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I think I have hit the mark with my pain picture. This is the real cause of my project."
                },
                {
                  "languageCode": "de",
                  "question": "Mit meinem Schmerzbild habe ich, glaube ich, ins Schwarze getroffen. Da sitzt die Ursache für mein Projekt."
                }
              ]
            },
            {
              "waitage": 10,
              "category": 2,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I already feel much better than I ever thought possible."
                },
                {
                  "languageCode": "de",
                  "question": "Ich fühle mich jetzt schon viel besser, als ich es je für möglich gehalten habe."
                }
              ]
            },
            {
              "waitage": 10,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have made good progress with my project. The problem in our relationship has been solved. I am in peace with this person."
                },
                {
                  "languageCode": "de",
                  "question": "Ich habe großen Fortschritt bei meinem Projekt erzielt. Das Beziehungsproblem besteht nicht mehr. Ich bin mit dieser Person im Frieden."
                }
              ]
            },
            {
              "waitage": 2,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I still do not know what to do. My project, i.e. my symptom, has even got worse."
                },
                {
                  "languageCode": "de",
                  "question": "Ich komme noch immer nicht zurecht. Mein Projekt, bzw. mein Symptom hat sich sogar verschlimmert."
                }
              ]
            },
            {
              "waitage": 0,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I do not where how to continue. I am completely blocked."
                },
                {
                  "languageCode": "de",
                  "question": "Ich komme nicht weiter. Ich bin völlig blockiert."
                }
              ]
            },
            {
              "waitage": 2,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have finally recognized that I have to put the focus on my father / my mother."
                },
                {
                  "languageCode": "de",
                  "question": "Mir ist jetzt (endlich) bewusst geworden, dass ich mich mit meinem Vater / mit meiner Mutter intensiv beschäftigen muss."
                }
              ]
            },
            {
              "waitage": 1,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I still cannot see the conflict person in a different way than I have always seen him/her."
                },
                {
                  "languageCode": "de",
                  "question": "Ich kann die Konfliktperson immer noch nicht anders sehen, als ich sie immer gesehen habe."
                }
              ]
            },
            {
              "waitage": 8,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "My symptoms have got 80% better."
                },
                {
                  "languageCode": "de",
                  "question": "Mein Symptom hat sich über 80% gebessert."
                }
              ]
            },
            {
              "waitage": 10,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "My symptoms have got 100% better. It is gone."
                },
                {
                  "languageCode": "de",
                  "question": "Mein Symptom hat sich zu 100% gebessert. Es ist weg."
                }
              ]
            },
            {
              "waitage": 8,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "My project still exists but another problem of mine has been solved."
                },
                {
                  "languageCode": "de",
                  "question": "Mein Projekt gibt es noch, aber ein anderes Problem von mir hat sich in Luft aufgelöst."
                }
              ]
            },
            {
              "waitage": 9,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I believe that I can now solve my problems without a companion."
                },
                {
                  "languageCode": "de",
                  "question": "Ich denke, dass ich nun ohne Begleiter meine Probleme lösen kann."
                }
              ]
            },
            {
              "waitage": 8,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I can now start a new project and I am so grateful for it."
                },
                {
                  "languageCode": "de",
                  "question": "Ich konnte jetzt mit einem ganz neuen Projekt beginnen. Wofür ich sehr dankbar bin."
                }
              ]
            },
            {
              "waitage": 7,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "My project has hardly become better, but I see my life now on a completely different level than before my project. This is quite a progress for me."
                },
                {
                  "languageCode": "de",
                  "question": "Mein Projekt ist kaum besser geworden, aber ich sehe das Leben jetzt in ganz anderen Bezügen, als vor meinem Projekt. Das ist auch schon ein großer Fortschritt für mich."
                }
              ]
            },
            {
              "waitage": 5,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have completely deviated from the path and stopped writing a long time ago. I will now put myself to work on it again."
                },
                {
                  "languageCode": "de",
                  "question": "Ich bin völlig vom Weg abgekommen und habe schon lange nichts mehr geschrieben. Ich werde mich jetzt wieder dransetzen."
                }
              ]
            },
            {
              "waitage": 6,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I am quite agitated about the pain that surfaced in me."
                },
                {
                  "languageCode": "de",
                  "question": "Ich bin ziemlich aufgewühlt davon, was bei mir an Schmerz noch zutage kommt."
                }
              ]
            },
            {
              "waitage": 3,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "Only now have I recognized that my illness / my problem already arose in my childhood."
                },
                {
                  "languageCode": "de",
                  "question": "Ich habe jetzt erst erkannt, dass meine Krankheit / mein Problem schon in meiner Kindheit entstanden ist."
                }
              ]
            },
            {
              "waitage": 7,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I was surprised that I reached the effective and healing rewriting when I introduced a spiritual helper."
                },
                {
                  "languageCode": "de",
                  "question": "Ich war überrascht, dass ich die wirksame und heilende Umschreibung erreicht habe, als ich einen geistigen Helfer einbezogen habe."
                }
              ]
            },
            {
              "waitage": 0,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I do not know if I want to continue with the KUBYmethod."
                },
                {
                  "languageCode": "de",
                  "question": "Ich weiß nicht, ob ich noch weiter mit der KUBYmethode arbeiten soll."
                }
              ]
            },
            {
              "waitage": 5,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I also work with other methods, but the one problem / illness I have is still not gone."
                },
                {
                  "languageCode": "de",
                  "question": "Ich arbeite auch noch mit anderen Methoden, aber das eine Problem / Krankheit von mir gibt es noch immer."
                }
              ]
            },
            {
              "waitage": 8,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I believe I am a new person. The people who surround me are amazed, even my children / friends / workmates."
                },
                {
                  "languageCode": "de",
                  "question": "Ich glaube, ich bin ein neuer Mensch. Mein Umfeld ist positiv überrascht, sogar meine Kinder / Freunde / Kollegen."
                }
              ]
            },
            {
              "waitage": 3,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "This is working far too slowly. Although I am writing and working, somehow things do not move forward."
                },
                {
                  "languageCode": "de",
                  "question": "Mir geht das alles viel zu langsam. Ich schreibe und tu, aber irgendwie bewegt sich nichts."
                }
              ]
            },
            {
              "waitage": 8,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have experienced that the rewriting works."
                },
                {
                  "languageCode": "de",
                  "question": "Ich habe erfahren, dass die Umschreibung bei mir wirkt."
                }
              ]
            },
            {
              "waitage": 6,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I think I will participate in a seminar again / for the first time."
                },
                {
                  "languageCode": "de",
                  "question": "Ich glaube, dass ich jetzt nochmal / zum ersten Mal ein Seminar mitmache."
                }
              ]
            },
            {
              "waitage": 7,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I am surprised that the script style causes such a strong feeling for something I had totally repressed."
                },
                {
                  "languageCode": "de",
                  "question": "Ich bin erstaunt, dass der Drehbuchstil bei mir ein so starkes Gefühl hervorruft, für etwas, was ich völlig verdrängt hatte."
                }
              ]
            },
            {
              "waitage": 2,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I am not sure I have already found the true cause of my project."
                },
                {
                  "languageCode": "de",
                  "question": "Ich bin mir nicht sicher, ob ich überhaupt die wahre Ursache für mein Projekt gefunden habe."
                }
              ]
            },
            {
              "waitage": 2,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "My stories do not touch me. I gather I only imagine them. I do not see the link to my project."
                },
                {
                  "languageCode": "de",
                  "question": "Mich berühren meine Geschichten nicht. Ich glaube, die denke ich mir nur so aus. Die Brücke zu meinem Projekt sehe ich nicht."
                }
              ]
            },
            {
              "waitage": 3,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I find it very difficult to create (rewrite) a new reality that is supposed to have an effect."
                },
                {
                  "languageCode": "de",
                  "question": "Eine neue Wirklichkeit mir einfach auszudenken (umzuschreiben), die dann wirken soll, fällt mir sehr schwer."
                }
              ]
            },
            {
              "waitage": 8,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have reached an insight in my former life which is so real in front of my inner eye as if it was happening now."
                },
                {
                  "languageCode": "de",
                  "question": "Ich bin in ein Vorleben gekommen, das so real vor meinem inneren Auge ablief, als wäre es jetzt."
                }
              ]
            },
            {
              "waitage": 8,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "The rewriting of my former life indeed had an effect on my present life."
                },
                {
                  "languageCode": "de",
                  "question": "Die Umschreibung meines früheren Lebens, hat sich tatsächlich auf mein heutiges Leben ausgewirkt."
                }
              ]
            },
            {
              "waitage": 7,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "All that manifests in my body has a spiritual cause. I would never have thought so before."
                },
                {
                  "languageCode": "de",
                  "question": "Alles, was sich bei mir körperlich manifestiert, hat eine geistige Ursache. Das hätte ich früher nie so gesehen."
                }
              ]
            },
            {
              "waitage": 7,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have not changed much, but my father / mother / partner / workmate has totally changed. This has a great impact on me."
                },
                {
                  "languageCode": "de",
                  "question": "Bei mir hast sich noch nicht sehr viel geändert, aber mein Vater / Mutter / Partner / Kollege hat sich völlig verändert. Das beeindruckt mich sehr."
                }
              ]
            },
            {
              "waitage": 3,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have several projects. I do not know yet with which to continue."
                },
                {
                  "languageCode": "de",
                  "question": "Ich habe mehrere Projekte. Ich weiß gar nicht, mit welchem ich jetzt weitermachen soll."
                }
              ]
            },
            {
              "waitage": 4,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "The companion hardly let me talk."
                },
                {
                  "languageCode": "de",
                  "question": "Die Begleitung hat mich wenig zu Wort kommen lassen."
                }
              ]
            },
            {
              "waitage": 8,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "Some lies I never thought possible have come to light now."
                },
                {
                  "languageCode": "de",
                  "question": "Lügen, die ich nie für möglich gehalten habe, sind jetzt ans Licht gekommen."
                }
              ]
            },
            {
              "waitage": 9,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "The script style is fantastic. I apply it to every project I am working with."
                },
                {
                  "languageCode": "de",
                  "question": "Der Drehbuchstil ist genial. Ich arbeite nur noch, bei jedem Projekt damit."
                }
              ]
            },
            {
              "waitage": 1,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have done so many rewritings but somehow I do not know how to continue."
                },
                {
                  "languageCode": "de",
                  "question": "Ich habe so viele Umschreibungen gemacht, aber ich stecke irgendwie fest."
                }
              ]
            },
            {
              "waitage": 6,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have now understood that I can also prewrite things in the future with the KUBYmethod."
                },
                {
                  "languageCode": "de",
                  "question": "Dass ich mit der KUBYmethode auch voraus schreiben kann, bzw muss, habe ich jetzt verstanden."
                }
              ]
            },
            {
              "waitage": 10,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "Putting into practice the prewriting for the future in my daily life worked really fine. I am grateful for the good instruction."
                },
                {
                  "languageCode": "de",
                  "question": "Die Umsetzung meines Seelenschreibens in den Alltag durch Vorausschreiben hat super funktioniert. Ich danke für die gute Anleitung."
                }
              ]
            },
            {
              "waitage": 9,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "Practising KUBY® is now part of my weekly work and it maintains me healthy, something completely new to me compared with the past."
                },
                {
                  "languageCode": "de",
                  "question": "KUBY® zu praktizieren gehört nun zu meiner wöchentlichen Arbeit und die hält mich weitestgehend gesund. Völlig anders als in früheren Jahren."
                }
              ]
            },
            {
              "waitage": 9,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "The structure of the KUBYmethod with a project, pain picture, link, rewriting, affirmation, and prewriting is now totally normal for me."
                },
                {
                  "languageCode": "de",
                  "question": "Die Struktur der KUBYmethode mit Projekt, Schmerzbild, Brücke, Umschreibung, Affirmation, Vorausschreiben, ist mir jetzt selbstverständlich."
                }
              ]
            },
            {
              "waitage": 9,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I have recognized that the reincarnation seminar was absolutely necessary for the expansion of my consciousness."
                },
                {
                  "languageCode": "de",
                  "question": "Ich habe erkannt, dass das Reinkarnations-Seminar für meine Bewusstseinserweiterung absolut notwendig ist."
                }
              ]
            },
            {
              "waitage": 10,
              "category": 3,
              "content": [
                {
                  "languageCode": "en",
                  "question": "I will now write down the pain picture in script style."
                },
                {
                  "languageCode": "de",
                  "question": "Ich schreibe jetzt das bewusst gewordene Schmerzbild im Drehbuchstil auf."
                }
              ]
            }
            ]

        const createQuestions = await Models.FeedbackQuestion.bulkCreate(questions, {transaction, include: [{ model: Models.FeedbackQuestionContent, as: "content" }]});

        await transaction.commit()
        return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_SAVED"),responseData:createQuestions}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
} 

exports.listQuestions = async(req, h) => {
    try {
        const token = req.query.token;
        const languageCode = req.headers.language;

        let userFeedback = await Models.UserFeedback.findOne({ where: {token: token} });
        if(!userFeedback) {
          await transaction.rollback();
          return h.response({success: false,message: req.i18n.__("INVALID_FEEDBACK_TOKEN_PROVIDED"),responseData:{}}).code(400); 
        }
        if(userFeedback.tokenStatus === 0) {
          await transaction.rollback();
          return h.response({success: false,message: req.i18n.__("FEEDBACK_ALREADY_PROVIDED"),responseData:{}}).code(400); 
        }
        const meetingId = userFeedback.meetingId;

        const participants = await Models.Participants.findAll({ where: { eventId: meetingId } });
        let companionId = null;
        let customerId = null;
        for(let item of participants) {
          if(item.role === "owner") {
            customerId = item.userId;
          } else {
            companionId = item.userId;
          }
        }
        let category = 1;

        if(customerId && companionId) {
          const events = await Models.Event.findAll({ 
            where: {  },
            include: [
              {
                required: true, model: Models.Participants,
                as: "owner", where: { userId: customerId, role: "owner" }
              },
              {
                required: true, model: Models.Participants,
                as: "companion", where: { userId: companionId, role: "companion" }
              }
            ] 
          });

          const count = events.length;
          if(count === 2) category = 2;
          if(count > 2) category = 3;
        }



        let attributes = ["id", "waitage", "category", [Sequelize.literal('(case when `content`.`question` is not null then `content`.`question` else `defaultContent`.`question` END)'), 'question']]

        const listQuestions = await Models.FeedbackQuestion.findAll({ 
            attributes: attributes,
            where: { category: category },
            include: [
              {model: Models.FeedbackQuestionContent, required: false, as:"content", attributes: [], where: { languageCode: languageCode } },
              {model: Models.FeedbackQuestionContent, required: false, as:"defaultContent", attributes: [],  where: { languageCode: "en" }}
            ] 
        });

        return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_SAVED"),responseData:listQuestions}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

const checkForStudentLimit = async (userId, companionId) => {
  const role = await sequelize.query(
    `SELECT id, user_id, rating FROM kuby_development.userobd_profiles where user_id = 122;`,
    { type: QueryTypes.SELECT }
  );
  return true;
};

exports.catchUserRating = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const token = req.payload.token;
        const feedback = req.payload.feedback;

        let userFeedback = await Models.UserFeedback.findOne({ where: {token: token} });
        if(!userFeedback) {
          await transaction.rollback();
          return h.response({success: false,message: req.i18n.__("INVALID_FEEDBACK_TOKEN_PROVIDED"),responseData:{}}).code(400); 
        }
        if(userFeedback.tokenStatus === 0) {
          await transaction.rollback();
          return h.response({success: false,message: req.i18n.__("FEEDBACK_ALREADY_PROVIDED"),responseData:{}}).code(400); 
        }
        const meetingId = userFeedback.meetingId;

        const eventInfo = await Models.Event.findOne({ where: { id: meetingId } });
        if(!eventInfo) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_MEETING_ID_PROVIDED"),responseData:{}}).code(400);
        }
        const eventParticipants = await Models.Participants.findAll({ where: { eventId: meetingId } });
        let userId = null;
        let companionId = null;
        for(let participant of eventParticipants) {
            if(participant.role === "owner")  userId = participant.userId;
            if(participant.role === "companion") companionId = participant.userId;
        }
        let waitageSum = 0;
        let feedbackArray = []
        for(let question of feedback) {
            let questionInfo = await Models.FeedbackQuestion.findOne({ 
                where: { id: question }, 
                attributes: ["id", "waitage","category"],
                include: [{model: Models.FeedbackQuestionContent, as: "content", attributes:["id","questionId","question","languageCode"]}] 
            });
            waitageSum += questionInfo.waitage;
            feedbackArray.push(questionInfo)
        }

        let rating = waitageSum / feedback.length
        const createUserFeedback = await userFeedback.update(
          { feedback: feedbackArray, tokenStatus: 0, rating: rating }, { transaction }
        );

        let ratingsInfo = await sequelize.query(
          `SELECT id, user_id, rating FROM userobd_profiles where user_id = ${companionId};`,
          { type: QueryTypes.SELECT }
        );

        if(ratingsInfo && ratingsInfo.length > 0) {
          ratingsInfo = ratingsInfo[0];
          let ratingCount = ratingsInfo.rating === null ? rating : (rating + ratingsInfo.rating) / 2;
          await sequelize.query(
            `UPDATE userobd_profiles SET rating = ${ratingCount} WHERE user_id = ${companionId};`,
            { type: QueryTypes.UPDATE, transaction }
          );
        }

        await transaction.commit();

        return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_SAVED"),responseData:createUserFeedback}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.showUserRating = async(req, h) => {
  try {
      const meetingId = req.params.meetingId;

      let userFeedback = await Models.UserFeedback.findOne({ where: {meetingId: meetingId} });

      return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_SAVED"),responseData:userFeedback}).code(200);
  } catch (error) {
      console.log("error", error);
      return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
}

exports.validateToken = async(req, h) => {
  try {
    const token = req.params.token;
    let validateToken = await Models.UserFeedback.findOne({ where: {token: token} });

    console.log(validateToken, " =================== feedback token")


    if(!validateToken) {
      return h.response({success: false,message: req.i18n.__("INVALID_FEEDBACK_TOKEN_PROVIDED"),responseData:{}}).code(400); 
    }
    if(validateToken.tokenStatus === 0) {
      return h.response({success: false,message: req.i18n.__("FEEDBACK_ALREADY_PROVIDED"),responseData:{}}).code(400); 
    }
    const userInfo = await Models.User.findOne({ where: { userId: validateToken.userId } })
    const companionInfo = await Models.User.findOne({ where: { userId: validateToken.companionId } })
    return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_VALIDATED"),responseData:{userInfo, companionInfo}}).code(200);
  } catch (error) {
    console.log("error", error);
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
}

exports.fetchFeedbackList = async(req, h) => {
  try {

    const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT;
    const offset = (req.query.pageNumber - 1) * limit;


    const userId = req.params.companionId;

    const eventInfo = await Models.Event.findAndCountAll({
      include: [
        {
          model: Models.UserFeedback, 
          as: "feedback", 
          required: true, 
          where: { tokenStatus: 0, companionId: userId }
        },
        {
          model: Models.Participants, 
          as: "owner",
          include: [{
            model: Models.User
          }],
          where: { role: "owner" }
        },
        {
          model: Models.Participants, 
          as: "companion",
          include: [{
            model: Models.User
          }],
          where: { role: "companion" }
        }
      ],
      subQuery    : false,
      limit,
      offset
    });

    const totalPages        = await Common.getTotalPages(eventInfo.count, limit);
    const responseData      = {
        totalPages,
        perPage: limit,
        records: eventInfo.rows,
        totalRecords: eventInfo.count,
        //cartValue:totalValue
    };

    return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_VALIDATED"),responseData:responseData}).code(200);
  } catch (error) {
    console.log(error)
    return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
  }
}