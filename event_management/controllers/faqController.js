exports.categoryList = async(req, h) => {
    try {

        const categoryList = await Models.Category.findAll({});

        return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_PROCESSED"),responseData:categoryList}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.categoryById = async(req, h) => {
    try {
        const { id } = req.params;

        const categoryExist = await Models.Category.findOne({ where: {id: id} });

        return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_PROCESSED"),responseData:categoryExist}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.createCategory = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const { name } = req.payload;
        const categoryExist = await Models.Category.findOne({ where: {name: name} });
        if(categoryExist) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("CATEGORY_ALREADY_EXISTS"),responseData:{}}).code(400);
        }

        let createcategory = await Models.Category.create({ name: name }, {transaction});
        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_SAVED"),responseData:createcategory}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.updateCategory = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const { name, id } = req.payload;

        let categoryExist = await Models.Category.findOne({ where: {id: id} });
        if(!categoryExist) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_CATEGORY_ID_PROVIDED"),responseData:{}}).code(400);
        }

        let updatecategory = await categoryExist.update({ name: name }, {transaction});
        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_SAVED"),responseData:updatecategory}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.deleteCategory = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const { id } = req.payload;

        let categoryExist = await Models.Category.findOne({ where: {id: id} });
        if(!categoryExist) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_CATEGORY_ID_PROVIDED"),responseData:{}}).code(400);
        }

        let deletecategory = await categoryExist.destroy({transaction});
        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_DELETED"),responseData:deletecategory}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.createFAQ = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const { title, description, categoryId } = req.payload;
        const language = req.headers.language || "en";
        const categoryExist = await Models.Category.findOne({ where: {id: categoryId} });
        if(!categoryExist) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_CATEGORY_ID_PROVIDED"),responseData:{}}).code(400);
        }

        let createFAQ = await Models.FAQ.create({ 
            categoryId,
            FAQContents: [{title, description, languageCode: language}]
        }, {
            include: [{model: Models.FAQContent}], transaction
        });

        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_SAVED"),responseData:createFAQ}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.updateFAQ = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const { id, title, description, categoryId } = req.payload;
        let language = req.headers.language || "en";
        let faqExists = await Models.FAQ.findOne({ where: {id: id} });
        if(!faqExists) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_FAQ_ID_PROVIDED"),responseData:{}}).code(400);
        }

        let updateObj = {}
        if(title !== null) updateObj["title"] = title;
        if(description !== null) updateObj["description"] = description;
        if(categoryId) {
            const categoryExist = await Models.Category.findOne({ where: {id: categoryId} });
            if(!categoryExist) {
                await transaction.rollback();
                return h.response({success: false,message: req.i18n.__("INVALID_CATEGORY_ID_PROVIDED"),responseData:{}}).code(400);
            }
            updateObj["categoryId"] = categoryId
        }

        let faqContent = await Models.FAQContent.findOne({ where: {faqId: id, languageCode: language} });
        let responseData = {}
        if(!faqContent) {
            let defaultFaq = await Models.FAQContent.findOne({ where: { faqId: id, languageCode: "en" }, raw: true });
            if(!defaultFaq) {
                await transaction.rollback();
                return h.response({success: false,message: req.i18n.__("FAQ_CONTENT_DOES_NOT_EXIST"),responseData:{}}).code(400);
            }
            delete defaultFaq["id"];
            defaultFaq["languageCode"] = language;
            if(updateObj?.title) defaultFaq["title"] = title;
            if(updateObj?.description) defaultFaq["description"] = description;
            responseData = await Models.FAQContent.create(defaultFaq, {transaction});
        } else {
            responseData = await faqContent.update(updateObj, {transaction});
        }


        // let upadteFAQ = await faqExists.create(updateObj, {transaction});
        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_SAVED"),responseData:responseData}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.deleteFAQ = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const { id } = req.params;
        let faqExists = await Models.FAQ.findOne({ where: {id: id} });
        if(!faqExists) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_FAQ_ID_PROVIDED"),responseData:{}}).code(400);
        }
        let deleteFAQ = await faqExists.destroy({ transaction });
        await Models.FAQContent.destroy({ where: { faqId: id }, transaction })
        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_SAVED"),responseData:deleteFAQ}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.listFAQ = async(req, h) => {
    try {
        const limit = (req.query.limit !== null )
        ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit
        : Constants.PAGINATION_LIMIT;
        const offset = (req.query.pageNumber-1) * limit;
        const orderByValue = req.query.orderByValue;
        const orderByParameter = req.query.orderByParameter;
        let language = req.headers.language || "en";

        let where = {}
        if(req.query.categoryId !== null) {
            where = {...where, categoryId: req.query.categoryId}
        }
        let attributes = ["id", "categoryId", [Sequelize.literal('(case when `mainContent`.title is not null then `mainContent`.title else `defaultContent`.title END)'), 'title'], [Sequelize.literal('(case when `mainContent`.description is not null then `mainContent`.description else `defaultContent`.description END)'), 'description'],
        [Sequelize.literal('`category`.name'), 'categoryName']
    ]
        let options = { where: where, attributes: attributes, order: [[orderByParameter,orderByValue]], 
            include: [
                {model: Models.FAQContent, as: "defaultContent", where: { languageCode: "en" }, attributes: [], required: false},
                {model: Models.FAQContent, as: "mainContent", where: { languageCode: language }, attributes: [], required: false},
                {model: Models.Category, as: "category", attributes: [], required: false}
            ] 
        };
        if(req.query.pageNumber !== null) options={...options,limit,offset};

        const faqList = await Models.FAQ.findAndCountAll(options);

        const totalPages = await Common.getTotalPages(faqList.count,limit);
        const responseData = {
            totalPages,
            perPage:limit,
            totalRecords: faqList.count,
            faqList: faqList.rows,
            baseUrl: process.env.NODE_SERVER_PUBLIC_API,
        }

        return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_SAVED"),responseData:responseData}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.detailFAQ = async(req, h) => {
    try {
        const {id} = req.params;
        let language = req.headers.language || "en";

        let attributes = ["id", "categoryId", [Sequelize.literal('(case when `mainContent`.title is not null then `mainContent`.title else `defaultContent`.title END)'), 'title'], [Sequelize.literal('(case when `mainContent`.description is not null then `mainContent`.description else `defaultContent`.description END)'), 'description'], [Sequelize.literal('`category`.name'), 'categoryName']]

        const faqList = await Models.FAQ.findOne({ 
            where: { id: id },
            include: [
                {model: Models.FAQContent, as: "defaultContent", where: { languageCode: "en" }, attributes: [], required: false},
                {model: Models.FAQContent, as: "mainContent", where: { languageCode: language }, attributes: [], required: false},
                {model: Models.Category, as: "category", attributes: [], required: false}
            ],
            attributes: attributes
        });

        return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_SAVED"),responseData:faqList}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

