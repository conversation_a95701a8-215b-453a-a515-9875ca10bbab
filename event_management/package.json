{"name": "kinn_backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"seeder": "NODE_ENV=development npx sequelize-cli db:seed:all", "start": "node server", "teststart": "NODE_ENV=test node server", "cypress:open": "cypress open", "cypress:run": "cypress run --record --key c1cdf2ad-f557-43cc-b280-bbbcfbc1333f", "inittest": "start-server-and-test start http://localhost:3018/ cypress:open", "devtest": "NODE_ENV=test start-server-and-test start http://**************:3018/ cypress:run", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "illuminz", "license": "ISC", "dependencies": {"@hapi/boom": "^10.0.0", "@hapi/hapi": "^21.1.0", "@hapi/inert": "^7.0.0", "@hapi/vision": "^7.0.0", "@joi/date": "^2.1.0", "axios": "^1.2.1", "bcrypt": "^5.1.0", "child_process": "^1.0.2", "colors": "^1.4.0", "crypto": "^1.0.1", "crypto-js": "^4.1.1", "cypress": "^12.1.0", "dotenv": "^16.0.3", "fs": "0.0.1-security", "handlebars": "^4.7.7", "hapi-auth-jwt2": "^10.2.0", "hapi-auto-route": "^3.0.4", "hapi-cron": "^1.1.0", "hapi-i18n": "^3.0.1", "hapi-swagger": "^15.0.0", "http": "^0.0.1-security", "joi": "^17.7.0", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.29.4", "mysql2": "^2.3.3", "ngrok": "^4.3.3", "node-jose": "^2.1.1", "node-rsa": "^1.1.1", "nonce": "^1.0.4", "path": "^0.12.7", "request-ip": "^3.3.0", "rrule": "^2.7.1", "sequelize": "^6.27.0", "sharp": "^0.31.2", "urlsafe-base64": "^1.0.0", "uuid": "^9.0.0"}, "devDependencies": {"@hapi/code": "^9.0.2", "@hapi/lab": "^25.0.1", "chai": "^4.3.7", "sequelize-cli": "^6.5.2", "start-server-and-test": "^1.15.2"}}