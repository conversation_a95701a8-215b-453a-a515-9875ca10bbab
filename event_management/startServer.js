const sequelize = require('sequelize');

let port= process.env.NODE_PORT;
if(process.env.NODE_ENV==='test')
  {
    port=process.env.NODE_PORT_TEST
  }
global.server = new Hapi.server({
  
    host: process.env.NODE_HOST,
    port,
    routes: {
      cors: {
        origin: ['*'],
        headers: ['accept', 'authorization', 'Content-Type', 'If-None-Match',"language","utcoffset","timezone"],
        additionalHeaders: ["Access-Control-Allow-Origin","Access-Control-Allow-Headers","Origin, X-Requested-With, Content-Type"]
      }
    }
  });
init=async()=>{
    const swaggerOptions = {
        info: {
          title: process.env.SITE_NAME,
          version:process.env.API_VERSION
        },
        securityDefinitions: {
          Bearer: {
            type: "apiKey",
            name: "Authorization",
            in: "header"
          }
        },
        grouping: "tags",
        sortEndpoints: "ordered",
        consumes: ["application/json"],
        produces: ["application/json"]
      };
      
      await server.register([auth_jwt],{
        once: true  //critical so that you don't re-init your plugins
      });

      await server.auth.strategy("jwt", "jwt", {
        complete: true,
        key: Common.privateKey, // secret key
        validate: Common.validateToken, // validate function defined in common function for timestamp check
        verifyOptions: { algorithms: ["HS256"] } // algorithm
      });
      server.auth.default("jwt");

      await server.register(
          [
              Inert,
              Vision,
              {plugin: HapiSwagger,options: swaggerOptions},
              {plugin: i18n,options: {locales: process.env.VALID_LANGUANGE_CODES.split(','),directory: __dirname + "/locales",languageHeaderField: "language",defaultLocale:process.env.DEFAULT_LANGUANGE_CODE}},
              {plugin: Routes,options:{routes_dir: Path.join(__dirname, 'routes')}},
              {plugin: Cron,options: { jobs: Constants.CRON }}
            ], {
              once: true  //critical so that you don't re-init your plugins
            }
      );
    
      await Models.sequelize.authenticate();
      if(process.env.NODE_ENV == "test" )
      {
          await Models.sequelize.query("SET FOREIGN_KEY_CHECKS = 0", { raw: true ,logging: false}).then(async () =>{
          await Models.sequelize.sync({force:true}).then(async () => {
            console.log('table Droped')
          await Models.sequelize.query("SET FOREIGN_KEY_CHECKS = 1", { raw: true , logging: false})
            await server.start();
            
           require('./systemInitlizer')
          });
      })
      }
      else{
        await Models.sequelize.sync().then(async () => {
        await server.start();
        console.log("SERVER STARTED AT :", server.info.uri)
      });
      }
      server.route({
        method: 'GET',
        path: '/',
        options:{
          auth:false
        },
        handler: function (request, h) {
          console.log('I am handler')
            return h.response({'Message':'Welcome User'}).code(200);
        }
    });
    
}



// console.log('err.name', err.name);
// console.log('err.message', err.message);
// console.log('err.errors', err.errors);
// err.errors.map(e => console.log(e.message))