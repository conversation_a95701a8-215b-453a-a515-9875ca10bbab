"use strict";
module.exports = (sequelize, DataTypes) => {
    let Participants = sequelize.define(
      "Participants",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        eventId: { type: DataTypes.INTEGER,defaultValue:null},
        userId: { type: DataTypes.INTEGER,defaultValue:null},
        type:{ type: DataTypes.INTEGER,defaultValue:null},
        status:{type: DataTypes.INTEGER,defaultValue:null},
        role:{type:DataTypes.STRING,defaultValue:null}
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_participants"
      }
    );
    Participants.associate = function(models) {
      Participants.belongsTo(models.Event, {foreignKey: "eventId"});
        Participants.belongsTo(models.User,{targetKey:'userId',foreignKey:"userId"})
  };
    return Participants;
};  
