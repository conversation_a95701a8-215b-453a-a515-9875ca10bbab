"use strict";
module.exports = (sequelize, DataTypes) => {
    let User = sequelize.define(
      "User",
      {
        userId            : {
          type          : DataTypes.INTEGER,
          allowNull     : false,
          unique        : true
      },
      firstName         : { 
          type          : DataTypes.STRING,
          defaultValue  : null
      },
      lastName          : {
          type          : DataTypes.STRING,
          defaultValue  : null
      },
      title             : {
          type          : DataTypes.STRING,
          defaultValue  : null
      },
      profilePhotoUrl   : {
          type          : DataTypes.STRING,
          allowNull     : true,
      },
      profilePhotoId    : {
          type          : DataTypes.STRING,
          allowNull     : true,
      },
      languages          : {
        type          : DataTypes.JSON,
        allowNull     : true,
      },
      meetingPrice      :{
        type          : DataTypes.STRING,
        allowNull     : true,
      },
      reason            :{
        type          : DataTypes.TEXT,
        allowNull     : true,
      },
      rating            :{
        type          : DataTypes.INTEGER,
        allowNull     : true
      },
      scheduleTime      : { 
        type          : DataTypes.INTEGER,
        allowNull     : true
      },
      reScheduleTime    : { 
        type          : DataTypes.INTEGER,
        allowNull     : true
      },
      cancelTime      :{
        type          : DataTypes.INTEGER,
        allowNull     : true
      },
      role            :{
        type          : DataTypes.JSON,
        allowNull     : true
      }, 
      gender            :{
        type          : DataTypes.INTEGER,
        allowNull     : true
      },
      email:{
        type          : DataTypes.STRING,
        allowNull     : true
      },
      vita:{
        type          : DataTypes.TEXT('long'),
        allowNull     : true
      },
      experience:{
        type          : DataTypes.DATE,
        allowNull     : true
      },
      totalEarning: {
        type: DataTypes.FLOAT, defaultValue: 0
      },
      soulwritingProductId: { type: DataTypes.STRING, defaultValue: null },
      meetingProductId: { type: DataTypes.STRING, defaultValue: null },
      zoomAccount: { type: DataTypes.INTEGER, defaultValue: 0 },
      userObject: { type: DataTypes.JSON, allowNull: true, defaultValue: null }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_users"
      }
    );
    User.associate = function(models) {
  };
    return User;
};  
