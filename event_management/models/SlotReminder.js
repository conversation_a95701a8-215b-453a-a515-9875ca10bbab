"use strict";
module.exports = (sequelize, DataTypes) => {
    let SlotReminder = sequelize.define(
      "SlotReminder",
      {
        id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false },
        companionId: { type: DataTypes.INTEGER, allowNull: false },
        userId: { type: DataTypes.INTEGER, allowNull: false }
      },
      {
        paranoid: false,
        underscored: true,
        tableName: "event_slot_reminder"
      }
    );
    
    return SlotReminder;
};  
