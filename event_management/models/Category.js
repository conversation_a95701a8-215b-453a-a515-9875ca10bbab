"use strict";
module.exports = (sequelize, DataTypes) => {
    let Category = sequelize.define(
      "Category",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        name: { type: DataTypes.STRING, allowNull: false},
        status: { type: DataTypes.INTEGER, defaultValue: 1 }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_category"
      }
    );
    Category.associate = function(models) {

  };
    return Category;
};  
