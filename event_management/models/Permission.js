"use strict";
module.exports = (sequelize, DataTypes) => {
    let Permission = sequelize.define(
      "Permission",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        permission_code: { type: DataTypes.STRING, allowNull:false,unique:'permission'},
        status: { type: DataTypes.INTEGER, defaultValue:null},
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_permissions"
      }
    );
    Permission.associate = function(models) {
        
    };
    return Permission;
};