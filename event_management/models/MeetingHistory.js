"use strict";
module.exports = (sequelize, DataTypes) => {
    let MeetingHistory = sequelize.define(
      "MeetingHistory",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        userId: { type: DataTypes.INTEGER, allowNull: false},
        companionId: { type: DataTypes.INTEGER, allowNull: true },
        meetingDetails: { type: DataTypes.JSON, allowNull: false },
        status: { type: DataTypes.INTEGER, defaultValue: 1 }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_meeting_history"
      }
    );
    MeetingHistory.associate = function(models) {
      // MeetingHistory.belongsTo(models.User, { foreignKey: "userId", as: "user" });
      // MeetingHistory.belongsTo(models.User, { foreignKey: "companionId", as: "companion" });
    };
    return MeetingHistory;
};  
