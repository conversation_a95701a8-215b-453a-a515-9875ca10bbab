"use strict";
module.exports = (sequelize, DataTypes) => {
    let Refund = sequelize.define(
      "Refund",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        eventId :   { type: DataTypes.INTEGER   ,   defaultValue:null},
        userId  :   { type: DataTypes.INTEGER   ,   defaultValue:null},
        data    :   { type: DataTypes.JSON      ,   defaultValue:null},
        amount  :   { type: DataTypes.STRING   ,   defaultValue:null},
        orderId  :   { type: DataTypes.INTEGER   ,   defaultValue:null},
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_refunds"
      }
    );
    Refund.associate = function(models) {
      //  Refund.belongsTo(models.Event, {foreignKey: "eventId"});
        Refund.belongsTo(models.User,{targetKey:'userId',foreignKey:"userId"})
  };
    return Refund;
};  
