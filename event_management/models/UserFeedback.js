"use strict";
module.exports = (sequelize, DataTypes) => {
    let UserFeedback = sequelize.define(
      "UserFeedback",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        userId: {type: DataTypes.INTEGER, allowNull: false },
        companionId: {type: DataTypes.INTEGER, allowNull: false },
        meetingId: {type: DataTypes.INTEGER, allowNull: false },
        token: {type: DataTypes.TEXT, allowNull: false},
        tokenStatus: {type: DataTypes.INTEGER, defaultValue: 1},
        occurance: {type: DataTypes.INTEGER, allowNull: true},
        feedback: {type: DataTypes.JSON, allowNull: true},
        rating: {type: DataTypes.STRING, allowNull: true},
        status: {type: DataTypes.INTEGER, defaultValue: 1}
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_user_feedback"
      }
    );
    UserFeedback.associate = function(models) {

    };
    return UserFeedback;
};  
