"use strict";
module.exports = (sequelize, DataTypes) => {
    let UrlDump = sequelize.define(
      "UrlDump",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        link: {type: DataTypes.TEXT, allowNull: true},
        data: {type: DataTypes.TEXT, allowNull: true},
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_url_dump"
      }
    );
    UrlDump.associate = function(models) {

    };
    return UrlDump;
};  
