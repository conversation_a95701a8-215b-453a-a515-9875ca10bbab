module.exports = (sequelize, DataTypes) => {
    let Group = sequelize.define(
      "Group",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        groupTypeId:{ type: DataTypes.INTEGER, allowNull: true,unique: "group"},
        parentId: { type: DataTypes.INTEGER, allowNull: true,unique: "group"},
        groupCode: { type: DataTypes.STRING, allowNull: false,unique: "group"},
        accountId: { type: DataTypes.INTEGER, allowNull: true,unique: "group"},
        userId: { type: DataTypes.INTEGER, allowNull: true},
        status: { type: DataTypes.INTEGER, allowNull: true}
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_groups"
      }
    );
    Group.associate = function(models) {
        Group.belongsTo(models.GroupType, { foreignKey: "groupTypeId" });
        Group.hasMany(models.Group, { foreignKey: "parentId",onDelete: 'cascade', hooks:true, as:"subGroups" });
        Group.hasMany(models.GroupContent, { foreignKey: "groupId",onDelete: 'cascade', hooks:true});
        Group.hasMany(models.GroupContent, { foreignKey: "groupId",onDelete: 'cascade', hooks:true, as:"mainContent"});
        Group.hasMany(models.GroupContent, { foreignKey: "groupId",onDelete: 'cascade', hooks:true,as:"defaultContent" });
        Group.hasMany(models.Permission, { foreignKey: "groupId",onDelete: 'cascade', hooks:true});
    };
    return Group;
  };  