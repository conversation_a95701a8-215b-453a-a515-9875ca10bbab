"use strict";
module.exports = (sequelize, DataTypes) => {
    let Event = sequelize.define(
      "Event",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
            zoomId        : {type:DataTypes.STRING,   defaultValue:null},
            zoomLink      : {type:DataTypes.STRING,   defaultValue:null},
            startLink      : {type:DataTypes.TEXT,   defaultValue:null},
            groupId       : {type:DataTypes.INTEGER,  defaultValue:null},
            slots         : {type:DataTypes.INTEGER,  defaultValue:null},
            duration      : {type:DataTypes.STRING,   defaultValue:null},
            allDay        : {type:DataTypes.INTEGER,  defaultValue:null},
            startDate     : {type:DataTypes.DATE,     defaultValue:null},
            endDate       : {type:DataTypes.DATE,     defaultValue:null},
            status        : {type:DataTypes.INTEGER,  defaultValue:1},
            title         : {type:DataTypes.STRING,   defaultValue:null},
            description   : {type:DataTypes.TEXT,     defaultValue:null},
            remark        : {type:DataTypes.TEXT,     defaultValue:null},
            link          : {type:DataTypes.STRING,   defaultValue:null},
            createdBy     : {type:DataTypes.INTEGER,  defaultValue:null},
            updatedBy     : {type:DataTypes.INTEGER,  defaultValue:null},
            data          : {type:DataTypes.JSON,     defaultValue:null},
            clinicId      : {type:DataTypes.INTEGER,  defaultValue:null},
            rrString      : {type:DataTypes.STRING,   defaultValue:null},
            returnData    : {type:DataTypes.JSON,     defaultValue:null},
            audioRecording: {type:DataTypes.JSON,     defaultValue:null},
            videoRecording: {type:DataTypes.JSON,     defaultValue:null},
            audioLink     : {type:DataTypes.STRING,   defaultValue:null},
            videoLink     : {type:DataTypes.STRING,   defaultValue:null},
            paymentLink   : {type:DataTypes.STRING,   defaultValue:null},
            paymentStatus : {type:DataTypes.INTEGER,  defaultValue:0},
            orderId       : {type:DataTypes.INTEGER,  defaultValue:null},
            expireDate    : {type:DataTypes.DATE,     defaultValue:null},
            amount        : {type:DataTypes.STRING,  defaultValue:null},
            zoomDuration  : {type:DataTypes.STRING,  defaultValue:0},
            audioPassword : {type:DataTypes.STRING,  defaultValue:null},
            videoPassword : {type:DataTypes.STRING,  defaultValue:null},
            totalAmount   : {type:DataTypes.STRING,  defaultValue:null},
            companionProfit: {type:DataTypes.STRING,  defaultValue:null},
            adminProfit    : {type:DataTypes.STRING,  defaultValue:null},
            isCustomSlot: { type: DataTypes.INTEGER, defaultValue: 0 },
            eventStatus: {type: DataTypes.INTEGER, defaultValue: 0},
            currency: { type: DataTypes.STRING, defaultValue: null },
            prefferedLanguage: { type: DataTypes.STRING, defaultValue: "en" },
            timezone: { type: DataTypes.STRING, defaultValue: null },
            eventReminder: { type: DataTypes.INTEGER, defaultValue: 0 },
            paymentReminder: { type: DataTypes.INTEGER, defaultValue: 0 }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_events"
      }
    );
    Event.associate = function(models) {
      Event.hasMany(models.Timing,{foreignKey:'eventId'});
      Event.hasMany(models.Frequency,{foreignKey:'eventId'});
      Event.hasOne(models.Invoice,{foreignKey:'eventId', as: "pendingInvoice"});
      Event.hasOne(models.Invoice,{foreignKey:'eventId'});
      Event.hasMany(models.Invoice,{foreignKey:'eventId', as: "invoices"});
      Event.hasOne(models.Order,{foreignKey:'eventId'});
      Event.belongsTo(models.Group,{foreignKey:'groupId'});
      Event.hasMany(models.Participants, { foreignKey: "eventId", as:'Participants'});
      Event.hasOne(models.Participants, { foreignKey: "eventId", as:'owner'});
      Event.hasOne(models.Participants, { foreignKey: "eventId", as:'companion'});
      Event.hasMany(models.EventLog,{foreignKey:'eventId'});
      Event.belongsTo(models.User,{targetKey:"userId",foreignKey:'createdBy',as:'Created_By'});
      Event.belongsTo(models.User,{targetKey:"userId",foreignKey:'updatedBy',as:'Updated_By'});
      Event.hasOne(models.UserFeedback,{foreignKey:'meetingId', as:'feedback'});
  };
    return Event;
};  
