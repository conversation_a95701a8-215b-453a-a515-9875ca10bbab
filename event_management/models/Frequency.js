"use strict";
module.exports = (sequelize, DataTypes) => {
    let Frequency = sequelize.define(
      "Frequency",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        frequency: { type: DataTypes.INTEGER,defaultValue:null},
        eventId: { type: DataTypes.INTEGER,defaultValue:null},
        type: { type: DataTypes.INTEGER,defaultValue:null},
        day:{type: DataTypes.INTEGER,defaultValue:null},
        week:{type: DataTypes.INTEGER,defaultValue:null},
        month:{type: DataTypes.INTEGER,defaultValue:null},
        occurance:{type:DataTypes.INTEGER,defaultValue:null}
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_frequencies"
      }
    );
    Frequency.associate = function(models) {
        Frequency.belongsTo(models.Event, { foreignKey: "eventId"});
  };
    return Frequency;
};  
