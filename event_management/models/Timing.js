"use strict";
module.exports = (sequelize, DataTypes) => {
    let Timing = sequelize.define(
      "Timing",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        eventId:{type:DataTypes.INTEGER,defaultValue:null},
        startTime:{type:DataTypes.INTEGER,defaultValue:null},
        endTime:{type:DataTypes.INTEGER,defaultValue:null},
        start:{type:DataTypes.DATE,defaultValue:null},
        end:{type:DataTypes.DATE,defaultValue:null},
        duration:{type:DataTypes.STRING,defaultValue:null},
        timingType:{
          type:DataTypes.INTEGER,defaultValue:null
        },
       rrString:{
        type:DataTypes.TEXT,defaultValue:null
       },
       offset: {type:DataTypes.STRING,defaultValue:null}
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_timings"
      }
    );
    Timing.associate = function(models) {
      Timing.belongsTo(models.Event,{foreignKey:'eventId'})
  };
    return Timing;
};  
