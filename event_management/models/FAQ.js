"use strict";
module.exports = (sequelize, DataTypes) => {
    let FAQ = sequelize.define(
      "FAQ",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        // title: { type: DataTypes.STRING, allowNull: false},
        // description: { type: DataTypes.TEXT, allowNull: false},
        categoryId: {type: DataTypes.INTEGER, allowNull: false},
        status: { type: DataTypes.INTEGER, defaultValue: 1 }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_faq"
      }
    );
    FAQ.associate = function(models) {
      FAQ.hasMany(models.FAQContent, {foreignKey: "faqId"})
      FAQ.hasOne(models.FAQContent, {foreignKey: "faqId", as: "mainContent"})
      FAQ.hasOne(models.FAQContent, {foreignKey: "faqId", as: "defaultContent"})
      FAQ.belongsTo(models.Category, {foreignKey: "categoryId", as: "category"})
    };
    return FAQ;
};  
