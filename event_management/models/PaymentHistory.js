"use strict";
module.exports = (sequelize, DataTypes) => {
    let Payment = sequelize.define(
      "Payment",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        eventId :   { type: DataTypes.INTEGER   ,   defaultValue:null},
        userId  :   { type: DataTypes.INTEGER   ,   defaultValue:null},
        data    :   { type: DataTypes.JSON      ,   defaultValue:null},
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_payment"
      }
    );
    Payment.associate = function(models) {
        Payment.belongsTo(models.Event, {foreignKey: "eventId"});
        Payment.belongsTo(models.User,{targetKey:'userId',foreignKey:"userId"})
  };
    return Payment;
};  
