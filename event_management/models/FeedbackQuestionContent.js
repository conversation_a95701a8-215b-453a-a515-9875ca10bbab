"use strict";
module.exports = (sequelize, DataTypes) => {
    let FeedbackQuestion = sequelize.define(
      "FeedbackQuestionContent",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        question: {type: DataTypes.TEXT, allowNull: false },
        languageCode: {type: DataTypes.STRING, allowNull: false },
        questionId: {type: DataTypes.INTEGER, allowNull: false},
        status: {type: DataTypes.INTEGER, defaultValue: 1}
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_feedback_question_content"
      }
    );
    FeedbackQuestion.associate = function(models) {

    };
    return FeedbackQuestion;
};  
