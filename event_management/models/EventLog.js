"use strict";
module.exports = (sequelize, DataTypes) => {
    let EventLog = sequelize.define(
      "EventLog",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        eventId:{type:DataTypes.INTEGER,defaultValue:null},
        eventDate:{type:DataTypes.DATE,defaultValue:null},
        timingId:{type:DataTypes.INTEGER,defaultValue:null},
        attachment:{type:DataTypes.JSON,defaultValue:null},
        createdBy:{type:DataTypes.INTEGER,defaultValue:null},
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_log"
      }
    );
    EventLog.associate = function(models) {
        EventLog.belongsTo(models.Event,{foreignKey:'eventId'});
        //EventLog.belongsTo(models.User,{foreignKey:'createdBy'});
        //EventLog.belongsTo(models.Timing,{foreignKey:'timingId'});
        // EventLog.belongsTo(mod)
  };
    return EventLog;
};  
