"use strict";
module.exports = (sequelize, DataTypes) => {
    let Order = sequelize.define(
      "Order",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        eventId: { type: DataTypes.INTEGER, defaultValue: null },
        orderId: { type: DataTypes.INTEGER, defaultValue: null },
        data: { type: DataTypes.JSON, defaultValue: null },
        amount: { type: DataTypes.STRING, defaultValue: null },
        userId: { type: DataTypes.INTEGER, defaultValue: null },
        companionId: { type: DataTypes.INTEGER, defaultValue: null },
        orderDetails: { type: DataTypes.JSON, allowNull: true }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_orders"
      }
    );
    Order.associate = function(models) {
        Order.belongsTo(models.Event,{foreignKey:'eventId'}),
        Order.belongsTo(models.User, {  targetKey:"userId",foreignKey: 'userId'});
        Order.belongsTo(models.User, {  targetKey:"userId",foreignKey: 'companionId', as: "companion"});
  };
    return Order;
};  
