"use strict";
module.exports = (sequelize, DataTypes) => {
    let Language = sequelize.define(
      "Language",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        code: { type: DataTypes.STRING,allowNull:false,unique:'language'},
        name: { type: DataTypes.STRING,allowNull:false,unique:'language'},
        isDefault: { type: DataTypes.INTEGER,defaultValue:null},
        status: { type: DataTypes.INTEGER,defaultValue:null},
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_languages"
      }
    );
    Language.associate = function(models) {
      //Language.hasMany(models.EmailTemplateContent, { foreignKey: "language_id"});
      Language.hasMany(models.GroupContent, { foreignKey: "languageId"});
  };
    return Language;
};  
