module.exports = (sequelize, DataTypes) => {
    let GroupType = sequelize.define(
      "GroupType",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        groupType: { type: DataTypes.STRING, allowNull: true,unique: "groupName"},
        groupTypeCode: { type: DataTypes.STRING, allowNull: true,unique: "groupName"},
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_group_types"
      }
    );
    GroupType.associate = function(models) {
        GroupType.hasMany(models.Group, { foreignKey: "groupTypeId",onDelete: 'cascade', hooks:true });
    };
    return GroupType;
  };  