"use strict";
module.exports = (sequelize, DataTypes) => {
    let FeedbackQuestion = sequelize.define(
      "FeedbackQuestion",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        waitage: {type: DataTypes.INTEGER, allowNull: false },
        category: {type: DataTypes.INTEGER, allowNull: false},
        status: {type: DataTypes.INTEGER, defaultValue: 1}
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_feedback_question"
      }
    );
    FeedbackQuestion.associate = function(models) {
      FeedbackQuestion.hasMany(models.FeedbackQuestionContent, { foreignKey: "questionId", as: "content" })
      FeedbackQuestion.hasMany(models.FeedbackQuestionContent, { foreignKey: "questionId", as: "defaultContent" })
    };
    return FeedbackQuestion;
};  
