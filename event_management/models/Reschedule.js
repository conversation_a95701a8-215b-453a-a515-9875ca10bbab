"use strict";
module.exports = (sequelize, DataTypes) => {
    let Reschedule = sequelize.define(
      "Reschedule",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        eventId: { type: DataTypes.INTEGER,defaultValue:null},
        clinicId: { type: DataTypes.INTEGER,defaultValue:null},
        startDate:{type:DataTypes.DATE,defaultValue:null},
        endDate:{type:DataTypes.DATE,defaultValue:null},
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_reschedule"
      }
    );
    Reschedule.associate = function(models) {
    Reschedule.belongsTo(models.Event,{foreignKey:'eventId'});
  };
    return Reschedule;
};  
