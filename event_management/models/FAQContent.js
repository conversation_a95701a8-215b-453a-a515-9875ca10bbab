"use strict";
module.exports = (sequelize, DataTypes) => {
    let FAQContent = sequelize.define(
      "FAQContent",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        faqId: {type: DataTypes.INTEGER, allowNull: false},
        title: { type: DataTypes.STRING, allowNull: false},
        description: { type: DataTypes.TEXT, allowNull: false},
        languageCode: { type: DataTypes.STRING, defaultValue: "en" }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "event_faq_content"
      }
    );
    FAQContent.associate = function(models) {

  };
    return FAQContent;
};  
