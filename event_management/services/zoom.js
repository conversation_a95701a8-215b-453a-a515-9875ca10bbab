const { URL } = require("../constants");
const Moment=require('moment')
module.exports = {

  // Create Zoom meeting 
  create: async (eventId,email, transaction) => {
    const url       =   URL.ZOOM_CREATE
    let event       = await Models.Event.findOne({where:{id:eventId},transaction})
    let startDate   = Moment(event.dataValues.startDate)
    let endDate     = Moment(event.dataValues.endDate)
    let duration    = endDate.diff(startDate,'minute')
    let topicTitle = "";
    try {
      const participants = await Models.Participants.findAll({ where: { eventId } });
      let companionName = "";
      let customerName = "";

      
      for(let item of participants) {
        const userInfo = await Models.User.findOne({ where: { userId: item.userId } });
        if(userInfo) {
          if(item.role == "owner") {
            customerName = userInfo.firstName + " " + userInfo.lastName;
          } else {
            companionName = userInfo.firstName + " " + userInfo.lastName;
          }
        }
      }

      topicTitle = "KUBY: " + companionName + " & " + customerName;
    } catch (error) {
      topicTitle = event.dataValues.title;
    }


    
    const data      =   {
        topic:topicTitle,
        // topic:event.dataValues.title,
        agenda:event.dataValues.description,
        duration:duration.toString(),
        start_time:event.dataValues.startDate,
        hostEmail:email
    }
    const axoisObj  =   createAxoisObj(url,'post',data)
    let res=await createRequest(axoisObj,transaction)
    if(res=={})
    {
        return false
    }

    if(Object.keys(res).length < 1) {
      return false
    }

    if(res?.responseData?.zoomId !== null && res?.responseData?.zoomId !== null)
    await event.update({zoomId:res.responseData.zoomId,zoomLink:res.responseData.joinLink, startLink: res.responseData.startLink},{transaction})
    return {
      zoomLink:res?.responseData?.joinLink ? res?.responseData?.joinLink : null ,
      startUrl: res.responseData.startLink
    }
  
  },
  // Update or reschedule Zoom meeting
  update: async (eventId,zoomId,zoomLink,startLink,email, transaction) => {
    if(zoomId==null)
    {
      return true
    }
    const url       =   URL.ZOOM_UPDATE
    let event       = await Models.Event.findOne({where:{id:eventId},transaction})
    let startDate   = Moment(event.dataValues.startDate)
    let endDate     = Moment(event.dataValues.endDate)
    let duration    = endDate.diff(startDate,'minute')

    try {
      const participants = await Models.Participants.findAll({ where: { eventId }, transaction });
      let companionName = "";
      let customerName = "";

      
      for(let item of participants) {
        const userInfo = await Models.User.findOne({ where: { userId: item.userId } });
        if(userInfo) {
          if(item.role == "owner") {
            customerName = userInfo.firstName + " " + userInfo.lastName;
          } else {
            companionName = userInfo.firstName + " " + userInfo.lastName;
          }
        }
      }

      topicTitle = "KUBY: " + companionName + " & " + customerName;
    } catch (error) {
      console.log(error)
      topicTitle = event.dataValues.title;
    }

    console.log(topicTitle, " ====================================== topicTitle")
    console.log(topicTitle, " ====================================== topicTitle")
    console.log(topicTitle, " ====================================== topicTitle")
    console.log(topicTitle, " ====================================== topicTitle")
    console.log(topicTitle, " ====================================== topicTitle")
    console.log(topicTitle, " ====================================== topicTitle")
    console.log(topicTitle, " ====================================== topicTitle")
    console.log(topicTitle, " ====================================== topicTitle")
    console.log(topicTitle, " ====================================== topicTitle")
    console.log(topicTitle, " ====================================== topicTitle")

    
    const data      =   {
      email,
      meetingId:zoomId,
        topic:topicTitle,
        agenda:event.dataValues.description,
        duration:duration.toString(),
        start_time:event.dataValues.startDate,
    }
    const axoisObj  =   createAxoisObj(url,'patch',data)
    let res=await createRequest(axoisObj,transaction)
    if(res=={})
    {
        return true
    }
    await event.update({zoomId,zoomLink,startLink},{transaction})
    return true
  
  },
  // Delete Zoom meeting
  delete: async (zoomId,email, transaction) => {
    if(zoomId==null)
    {
      return true
    }
    const url       =   URL.ZOOM_DELETE
    const data      =   {meetingId:zoomId,email}
    const axoisObj  =   createAxoisObj(url,'delete',data)
    let res=await createRequest(axoisObj,transaction)
    if(res=={})
    {
        return true
    }
    return true
  }

};



const createAxoisObj= (url,method,data,headers)=>{return {
    url,
    method,
    data,
    headers
}}


const createRequest=async (axiosObj,transaction)=>{
  console.log('axiosObj',axiosObj)
    let res=await Axios(axiosObj)
    .then(async(res)=>{
      console.log('hi Then ')
      console.log(res.data)
        return res.data
}
    )
    .catch(async (error) => {
      console.log('hi catch ',error)
      return {}
    });
  return res
}