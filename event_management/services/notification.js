const { URL } = require("../constants");
const Moment = require('moment-timezone');
require("moment/min/locales");
// console.log(Moment.utc().tz("UTC").locale("en").format('LLLL'))
// console.log(Moment.utc().tz("CET").locale("en").format('LLLL'))
const{sendNotification}=require('./webhook')
const { minutesToHours } = require("../common")


let timeZoneDisplay = process.env.TIME_ZONE_DISPLAY

module.exports = {
  // Create Zoom meeting
  send: async (eventId, code, typeId, transaction, offset = 0, language = "en") => {
    const url = URL.SENDEMAIL;
    let event = await Models.Event.findOne({ where: { id: eventId }, transaction });
    // let timezone = event.dataValues.timezone;
    let tz = "CET";

    let isTimezone = "CEST";
    let timeZoneValue = Moment.utc(event.dataValues.startDate).tz(tz)
    if (timeZoneValue.isDST()) {
      isTimezone = "CEST"
    } else {
      isTimezone = "CET"
    }

    let startDate = Moment.utc(event.dataValues.startDate).tz(tz).locale(language).format('LLLL');
    let startDateForCompanion = Moment.utc(event.dataValues.startDate).tz(tz).locale(language).format('LLLL');

    startDate = startDate + ` (${isTimezone})`
    startDateForCompanion = startDateForCompanion + ` (${isTimezone})`
    let paricipents = await Models.Participants.findAll({ where: { eventId: eventId }, transaction, raw: true, nest: true });
    let companionName = "companion";
    let userName = "user";

    let companionFirstName = "companion"
    let userFirstName = "user"

    let companionEmail = null;
    let userEmail = null;
    let companionId = null;
    let userUserId = null;
    for (const iterator of paricipents) {
      let userId = iterator.userId;
      let user = await Models.User.findOne({
        where: { userId: userId },
        transaction,
        raw: true,
        nest: true
      });
      if (user) {
        if (iterator.role == "companion") {
          companionFirstName = user.firstName;
          companionName = user.firstName;
          companionEmail = user.email;
          companionId = userId;
          if (user.lastName) {
            companionName = companionName + " " + user.lastName;
          }
        } else if (iterator.role == "owner") {
          userEmail = user.email;
          userName = user.firstName;
          userFirstName = user.firstName;
          userUserId = userId;
          if (user.lastName) {
            userName = userName + " " + user.lastName;
          }
        }
      }
    }

    // sending email to user
    // let eventStartDate = Moment(startDate).utc().toString()
    let userReplacements = {
      name: userFirstName,
      time: startDate,
      companion: companionName,
      reason: event.remark,
      zoomLink: `${process.env.PORTAL_DOMAIN_URL}user/upcoming-meeting`
      // zoomLink: event?.dataValues?.zoomLink
    };
    await sendEmail(userReplacements, code, [userEmail], transaction, language);
    // send push notification
    let notificationdata = {
      replacements: {
        start_date: startDate,
        user_name: companionName
      },
      typeId,
      data: { eventId: event.dataValues.id },
      userId: userUserId
    };
    await sendNotification(notificationdata, transaction);

    //sending email to companion
    let replacements = {
      name: companionFirstName,
      time: startDateForCompanion,
      companion: userName,
      zoomLink: `${process.env.PROFESSIONAL_DOMAIN_URL}user/upcoming-meeting`,
      comment: event.dataValues.description
      // zoomLink: event?.dataValues?.zoomLink
    };



    await sendEmail(replacements, code === "MEETING_CANCELLED_BY_COMPANION" ? "MEETING_CANCELLED" : code === "MEETING_SCHEDULED" ?"MEETING_SCHEDULED_COMPANION" : code, [companionEmail], transaction, language);
    notificationdata = {
      replacements: {
        start_date: startDate,
        user_name: userName
      },
      typeId,
      data: { eventId: event.dataValues.id },
      userId: companionId
    };

    await sendNotification(notificationdata, transaction);
    return true;
  },

  sendEmail:async(replacements,code,recipients,transaction, language)=> {
    try{
      data = { replacements, priority:'high', code, recipients }
      axoisObj  =   createAxoisObj(URL.SENDEMAIL,'post',data, {language})
      await createRequest(axoisObj,transaction)
    } catch(error) {
      console.log('Error in Sending email',error)
    }
  }
};


const sendEmail = async(replacements,code,recipients,transaction, language)=>{
  try{
    data = { replacements, priority:'high', code, recipients };
    headers = { language }
    axoisObj  =   createAxoisObj(URL.SENDEMAIL,'post',data, headers)
    console.log(axoisObj, " ================= axiosObj")
    await createRequest(axoisObj,transaction)
  } catch(error){
    console.log('Error in Sending email',error)
  }
}

const createAxoisObj= (url,method,data,headers)=>{return { url, method, data, headers }}

const createRequest=async (axiosObj,transaction)=>{
  let res=await Axios(axiosObj)
  .then(async(res)=>{ return res.data })
  .catch(async (error) => { return {} });
  return res
}