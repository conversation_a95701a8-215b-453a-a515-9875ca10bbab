const Constants=require('../constants')
module.exports = {
    sendNotification : async(data, transaction) => {
        const url = Constants.URL.SEND_NOTIFICATION
            const axiosObj = { url: url, method: "POST", data: data, headers:{ utcoffset:utcOffset }};

            await  Axios(axiosObj).then().catch(async () => {
                    console.log('Error_IN_SENDING_PUSH')
                    await Models.Request.create({
                        axiosObj : axiosObj, sucess   : 0
                    }, {transaction});
                });
        return true;
    },
    updateAttachmentService : async(reqPayload, transaction) => {
        const url = Constants.URL.ATTACHMENT_UPDATE;

        const axiosObj = {
            url    : url,
            method : "PATCH",
            data   : reqPayload
        };

        await 
            Axios(axiosObj)
            .then()
            .catch(async () => {
                await Models.Request.create({
                    axiosObj : axiosObj,
                    sucess   : 0
                }, {transaction});
            });
    },
}