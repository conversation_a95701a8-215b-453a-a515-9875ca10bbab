exports.privateKey = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456";
exports.algorithm = "aes-256-cbc";
exports.iv = "QWERTY1234567890";
const { Op,QueryTypes, where } = require("sequelize");
const Moment = require('moment-timezone');
require("moment/min/locales");

decrypt = (text) => {
    let decipher = crypto.createDecipheriv(this.algorithm, this.privateKey, this.iv);
    let decrypted = decipher.update(text, "hex", "utf8");
    decrypted = decrypted + decipher.final("utf8");
    return decrypted;
};

encrypt = (text) => {
    let cipher = crypto.createCipheriv(this.algorithm, this.privateKey, this.iv);
    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted = encrypted + cipher.final("hex");
    return encrypted;
};

readHTMLFile = (path, callback) => {
    Fs.readFile(path, {
        encoding: "utf-8"
    }, function (err, html) {
        if (err) {
            throw err;
            callback(err);
        } else {
            callback(null, html);
        }
    });
};

const getTokenStatus = async (userId) => {
    let data = await Models.User.findOne({
        where: {
            id: userId
        }
    });
    return data;
};

exports.prefunction = async (req, h) => {
    global.LanguageCodes = process.env.ALL_LANGUAGE_CODE.split(",");
    global.LanguageIds = process.env.ALL_LANGUAGE_ID.split(",").map(function (item) {
        return parseInt(item, 10);
    });
    global.utcOffset = req.headers.utcoffset;
    let Exist=await Models.Language.findOne({where:{code:req.headers.language}});
    if(!Exist) return false;
    req.headers.languageId=Exist.dataValues.id;
    // console.log('language',req.headers.languageId);
    
    return true;
};

exports.routeError = (errors, message) => {
    console.log(errors);
    errors.forEach((err) => {
        switch (err.code) {
            case "any.required":
                err.message = message;
                break;
        }
    });
    return errors;
};

exports.axiosCacheRequest = async (requestUrl, requestMethod, requestHeader, requestBody) => {
    let responseData;
    requestHeader = {
        ...requestHeader,
        "Api-Key": process.env.MYFOJO_API_KEY
    };
    try {
        let requestObject = {
            url: requestUrl,
            method: requestMethod.toLowerCase(),
            headers: requestHeader
        };
        requestObject = requestMethod.toLowerCase() === "get" ? {
            ... requestObject,
            params: requestBody
        } : {
            ... requestObject,
            data: requestBody
        };
        console.log(requestObject);

        let requestHash = encrypt(JSON.stringify(requestObject));
        const cacheExists = await Models.Cache.findOne({where: {
                requestHash
            }});
        if (cacheExists) {
            responseData = JSON.parse(cacheExists.response);
        } else {
            responseData = await Axios(requestObject);
            responseData = responseData.data;
            stringifiedResponse = JSON.stringify(responseData);
            await Models.Cache.create({
                requestUrl,
                requestMethod,
                requestHeader,
                requestBody,
                requestHash,
                response: stringifiedResponse
            });
        }
    } catch ({response}) {
        return response ? response.data : "AXIOS_REQUEST_FAILED";
    }
    return responseData;
};

exports.formatListingWithPagination = async (record, recordLimit) => {
    return {
        data: record.rows,
        perPage: recordLimit,
        totalRecords: record.count,
        baseUrl: process.env.NODE_SERVER_PUBLIC_API,
        totalPages: Math.ceil(record.count / recordLimit)
    };
};

exports.validateToken = async (token) => { // console.log('called',token)
    fetchtoken = JSON.parse(decrypt(token.data));
    //console.log('Role', fetchtoken.Role);

    var diff = Moment().diff(Moment(token.iat * 1000));
    if (diff > 0) {
        //console.log('fetchtoken',fetchtoken);
        let role=fetchtoken.Role
        let permissions=fetchtoken.Permissions;
        // console.log('role',role);
        // console.log('permissions',permissions)
        let scope=[...role,...permissions]
        // console.log('scope',scope);
        return {
            isValid: true,
            credentials: {
                userData: fetchtoken,
                scope:scope
            }
        };
    }
    return {isValid: false};
};

exports.convertToUTC = (date, offset) => {
    console.log(date);
    let utcDate = Moment(date).utcOffset(offset, true);
    console.log(utcDate);
    return utcDate;
};

exports.signToken = (tokenData) => {
    return Jwt.sign({
        data: encrypt(JSON.stringify(tokenData))
    }, this.privateKey);
};

exports.headers = (authorized) => {
    let Globalheaders = {
        language    :   Joi.string().optional().default(process.env.DEFAULT_LANGUANGE_CODE),
        utcoffset   :   Joi.string().optional().default(0),
        timezone    :   Joi.string().optional().default(null),
    };
    if (authorized) {
        _.assign(Globalheaders, {authorization: Joi.string().required().description("Token to identify user who is performing the action")});
    }
    return Globalheaders;
};

exports.sendOTP = async (phoneNumber) => {
    return {phoneNumber: phoneNumber, pinId: process.env.MASTER_OTP};
};

exports.sendEmail = async (to, from, cc, bcc, subject, content, replacements, attachments, language, template) => {
    let protocol = process.env.EMAIL_PROTOCOL;
    switch (protocol) {
        case "smtp":
            let transporter = nodemailer.createTransport({
                host: Constants.SMTP.host,
                port: Constants.SMTP.port,
                secure: Constants.SMTP.ssl,
                auth: {
                    user: Constants.SMTP.username,
                    pass: Constants.SMTP.password
                }
            });
            console.log(__dirname);
            readHTMLFile(__dirname + "/emails/" + language + "/" + template + ".html", async function (err, html) {
                let sendto = to.join(",");
                // let to='<EMAIL>';
                var template = handlebars.compile(html);
                var mergeContent = template({content: content});
                var templateToSend = handlebars.compile(mergeContent);
                var htmlToSend = templateToSend(replacements);
                // console.log('htmlToSend',htmlToSend)
                let mailOptions = {
                    from: from, // sender address
                    to: sendto, // list of receivers
                    cc: cc.join(","),
                    bcc: bcc.join(","),
                    subject: subject, // Subject line
                    text: striptags(htmlToSend), // plain text body
                    html: htmlToSend, // html body
                    attachments: attachments,
                    priority: "high"
                };
                let info = await transporter.sendMail(mailOptions);
                return info;
            });
    }
    return;
};

exports.generateCode = (requestedlength) => {
    const char = "1234567890"; // Random Generate Every Time From This Given Char
    const length = typeof requestedlength != "undefined" ? requestedlength : 4;
    let randomvalue = "";
    for (let i = 0; i < length; i++) {
        const value = Math.floor(Math.random() * char.length);
        randomvalue += char.substring(value, value + 1).toUpperCase();
    }
    return randomvalue;
};

exports.FailureError = (err, req) => {
    const updatedError = err;
    updatedError.output.payload.message = [];
    let customMessages = {};
    if (err.isJoi && Array.isArray(err.details) && err.details.length > 0) {
        err.details.forEach((error) => {
            customMessages[error.context.label] = req.i18n.__(error.message);
        });
    }
    delete updatedError.output.payload.validation;
    updatedError.output.payload.error = req.i18n.__("BAD_REQUEST");
    console.log("err.details.type", err.details);
    if (err.details[0].type === "string.email") {
        updatedError.output.payload.message = req.i18n.__("PLEASE_ENTER_A_VALID_EMAIL");
    } else {
        updatedError.output.payload.message = req.i18n.__("ERROR_WHILE_VALIDATING_REQUEST");
    } updatedError.output.payload.errors = customMessages;
    return updatedError;
};

exports.generateError = (req, type, message, err) => {
    switch (type) {
        case 500:
            error = Boom.badImplementation(message);
            error.output.payload.error = req.i18n.__("INTERNAL_SERVER_ERROR");
            error.output.payload.message = req.i18n.__(message);
            error.output.payload.errors = err;
            console.log(err);
            break;
        case 400:
            error = Boom.badRequest(message);
            error.output.payload.error = req.i18n.__("BAD_REQUEST");
            error.output.payload.message = req.i18n.__(message);
            error.output.payload.errors = err;
            break;
        case 401:
            error = Boom.unauthorized(message);
            error.output.payload.error = req.i18n.__("UNAUTHORIZED_REQUEST");
            error.output.payload.message = req.i18n.__(message);
            error.output.payload.errors = err;
            break;
        case 403:
            error = Boom.forbidden(message);
            error.output.payload.error = req.i18n.__("PERMISSION_DENIED");
            error.output.payload.message = req.i18n.__(message);
            error.output.payload.errors = err;
            break;
        default:
            error = Boom.badImplementation(message);
            error.output.payload.error = req.i18n.__("UNKNOWN_ERROR_MESSAGE");
            error.output.payload.message = req.i18n.__(message);
            error.output.payload.errors = err;
            break;
    }
    return error;
};

exports.getTotalPages = async (records, perpage) => {
    let totalPages = Math.ceil(records / perpage);
    return totalPages;
};



exports.verifyToken = async (token) => {
    let data = await Jwt.verify(token, this.privateKey);
    // console.log('data',data);

    return decrypt(data.data);
};


exports.decrypt = (text) => {
    let decipher = crypto.createDecipheriv(process.env.ALGORITHM, process.env.PRIVATE_KEY, process.env.IV);
    let decrypted = decipher.update(text, "hex", "utf8");
    decrypted = decrypted + decipher.final("utf8");
    return decrypted;
};

exports.encrypt = (text) => {
    let cipher = crypto.createCipheriv(process.env.ALGORITHM, process.env.PRIVATE_KEY, process.env.IV);
    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted = encrypted + cipher.final("hex");
    return encrypted;
};

exports.minutesToHours=(minutes)=>{
    if(minutes){
      let hours = parseInt(Math.abs(minutes)/60);
      let min = Math.abs(minutes)%60;
      if(minutes<0)
        return `-${hours}:${min}`;
      else
       return `+${hours}:${min}`;
  
    }else{
      return '+0:00';
    }
  }

  exports.minutesToHoursInverse=(minutes)=>{
    if(minutes){
      let hours = parseInt(Math.abs(minutes)/60);
      let min = Math.abs(minutes)%60;
      if(minutes<0)
        return `+${hours}:${min}`;
      else
       return `-${hours}:${min}`;
  
    }else{
      return '+0:00';
    }
  }



// exports.createRequest = async (url, method, payload,headers={}) => {
//     try {
// //console.log('url',url);
//         let saveReq = await Models.Request.create({url, method, payload,headers});
//         let result = await fatchData(url, method, payload,headers);
//         if (result) 
//             await saveReq.update({sucess: Constants.STATUS.ACTIVE});
        
//     } catch (error) {
//         console.error('error while Creating request', error)
//     }
// }

// const fatchData = async (url, method, payload,headers) => {
//     try {
//         let res = await Axios({method, url, data:payload,headers:headers})
//         console.log(res.status);
//         if (res.status === 200) {
//             return true
//         }
//     } catch (error) {
//         return false
//     }
// }


// exports.retryRequests = async () => {
//     try {
//         console.log('--------------------------------Executing Requests of database ------------------------------')
//         let requestData = await Models.Request.findAll({
//             where: {
//                 sucess: Constants.STATUS.INACTIVE
//             }
//         });
//         if (requestData.length > 0) { 
//             for (const iterator of requestData) {
//                 let result =await fatchData(iterator.url, iterator.method, iterator.payload);
//                 if (result) {
//                     await Models.Request.update({
//                         sucess: Constants.STATUS.ACTIVE
//                     }, {
//                         where: {
//                             id: iterator.id
//                         }
//                     });
//                 }
                
//             }
//         }
//     } catch (error) {
//         console.log('error while retrying Requests', error);
//     }
// }



  
//   exports.triggerNotifications=async(time_in_minutes)=>{
//     try{
//         // find all upcoming Events in five minutes ---
//         time_in_minutes=180
//         let start_date=Moment.utc();
//         let end_date=Moment.utc()
//         end_date=end_date.add(time_in_minutes,'m')
//         let localStartTime=Moment.utc().format("HH:mm")
//         let localEndTime=Moment.utc().add(time_in_minutes,'m')
//         localEndTime=localEndTime.format("HH:mm")
//         let events=await sequelize.query(
//             `WITH recursive Date_Ranges AS
//             (select Date(:qlocalStartDate) as Date union all select Date + interval 1 day from Date_Ranges where Date < Date(:qlocalEndDate))
//             select e.id,eg.id as groupId,e.start_date as startDate FROM kinn_event.event_events as e
//             LEFT JOIN event_groups as eg on eg.id = e.group_id
//             LEFT JOIN event_participants as ep on ep.event_id =e.id
//             LEFT JOIN event_group_contents as egc on eg.id = egc.group_id and egc.language_id=1
//             LEFT JOIN event_frequencies as ef on ef.event_id = e.id
//             LEFT JOIN event_event_pets as epets on epets.event_id=e.id
//             INNER JOIN Date_Ranges as dr
//             where 
//             eg.id NOT IN (5) and
//             (DATE(e.start_date)<=:qendDate and (((e.end_date)>=:qstartDate) or e.end_date is null))
//             and (
//                 ef.id is null and ((e.start_date)<=:qendDate and (e.end_date is null or (e.end_date)>=:qstartDate))
//                 OR (ef.type=1 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null) and mod(Datediff(e.start_date,dr.Date),ef.frequency)=0))
//                 OR(ef.type=2 and (DATE(e.start_date)<=dr.Date and (DATE(e.end_date)>=dr.Date or e.end_date is null)) and WEEKDAY(dr.date) = ef.day and mod(Datediff(IF(WEEKDAY(e.start_date)<ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),IF(WEEKDAY(e.start_date)>ef.day,DATE_ADD(e.start_date, INTERVAL (ef.day-WEEKDAY(e.start_date)) DAY),e.start_date)),dr.Date),ef.frequency*7)=0)
//                 OR (ef.type=3 and (
//                         (ef.week is null and ef.day=day(dr.Date))
//                         OR (ef.week>0 and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
//                         OR (ef.week=0 and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
//                     )
//                 )
//                 OR (ef.type=4 and (
//                         (ef.week is null and ef.day=day(dr.Date) and ef.month=month(dr.Date))
//                         OR (ef.week>0 and ef.month=month(dr.Date) and DATE(ADDDATE(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')),MOD(((9 + ef.day)-DAYOFWEEK(DATE(CONCAT(year(dr.date),'-',month(dr.date),'-01')))),7)+(if(ef.week>1,ef.week*7,0)))) = dr.date)
//                         OR (ef.week=0 and ef.month=month(dr.Date) and DATE(LAST_DAY(dr.Date) - ((7 + WEEKDAY(LAST_DAY(dr.Date)) - ef.day) % 7)) = dr.Date)
//                     )
//                 )
//             )`,
      
//             {replacements:{
//               qstartDate: start_date.format('YYYY-MM-DD HH:mm:ss') , 
//               qlocalStartDate:start_date.format('YYYY-MM-DD HH:mm:ss'),
//               qendDate: end_date.format('YYYY-MM-DD HH:mm:ss') ,
//               qlocalEndDate:end_date.format('YYYY-MM-DD HH:mm:ss')
//           },type: QueryTypes.SELECT});
//           const unique = [...new Map(events.map(item =>[item['id'], item])).values()];
//           const localStartMinutes= ConvertToMinutes(localStartTime)
//           const localEndMinutes= ConvertToMinutes(localEndTime)
//           for (const event of unique) {
//                 if((localStartMinutes- ConvertToMinutes(Moment.utc(event.startDate).format("HH:mm"))<0) && (localEndMinutes- ConvertToMinutes(Moment.utc(event.startDate).format("HH:mm"))>0))
//                 {
//                     console.log('------- Fire My Notifications -----')
//                     console.log('event.id',event.id)
//                     console.log('event.groupId',event.groupId)
//                     console.log('event.startDate',event.startDate)
//                     console.log('event.time',Moment.utc(event.startDate).format("HH:mm"))
//                     let eventData=await Models.Event.findOne({where: {id:event.id},include: [{
//                       model: Models.Attachment,
//                       attributes: ["attachment"],
//                     },
//                     {
//                       model:Models.Timing,
//                       attributes:['id',"start","end","duration",'start_time','end_time','timingType'],
//                     },
//                     {
//                       model: Models.Participants,
//                       attributes: ["userId","role"],
//                       as: "Participants",
//                       include:[{model:Models.User}]
//                     },
//                     {
//                         model: Models.Frequency,
//                         attributes: ["frequency", "type","day","week",'month','occurance']
//                       },
//                       {
//                         model:Models.EventPet,
//                         include:[{model:Models.Pet}]
//                       },
//                       {
//                         model:Models.Clinic,
//                         as:'Clinic_id'
//                       },
//                       {
//                         model:Models.EventLog,
//                         attributes:["eventDate","createdBy",'attachment']
//                       },
//                       {
//                         model:Models.EventClinic,
//                         attributes:['clinicName','clinicAddress','clinicContact','vetName'],
//                       }]
//                     });
                    
//                     console.log('eventData',eventData)
                    
//                     switch (event.groupId) {
//                         case 1:
//                             {
//                                 //medication
//                             }
//                             break;
//                         // case 2:
//                         //     {

//                         //     }
//                         //     break
//                         case 3:
//                             {
//                                 // task
//                             }
//                             break;
//                         // case 4:
//                         //     {

//                         //     }
//                         //     break;
//                          case 6:
//                             {
//                                 // appointment
//                             }  
//                             break;
//                          case 7 :
//                             {
//                                 //Preventive care

//                             }   
//                             break; 
//                         default:
//                             break;
//                     }
//                 }
//           }

//     }
//     catch(error)
//     {
//       console.error('Error Occurrs During Sending Notifications',error.toString())
//     }
//   }
  


// const ConvertToMinutes=(s)=>{
//     let a = s.split(':')
//     let minutes = (+a[0]) * 60 + (+a[1])
//     console.log('minutes',minutes)
//     return minutes
// }


exports.DgStoreRequest=async(url)=>{
    try{
        console.log('Dg Store data url',url)
        console.log(" ================= process.env.DG_STORE_API_KEY", process.env.DG_STORE_API_KEY)
        let res=await Axios({
            method: 'get',
            baseURL:process.env.DG_STORE_BASE_URL,
            url,
            headers:{
                'X-DS-API-KEY': process.env.DG_STORE_API_KEY
            }
        });
        console.log(res, " ===================== ")

        await Models.UrlDump.create({ link: url, data: JSON.stringify(res?.data?.data) })
        return {success:true,data:res?.data?.data}
    }
    catch(error)
    {
        console.log(error)
        return {success:false,error:error.toString()}
    }
}


exports.EveryMinuteCrone=async()=>{
    const transaction = await Models.sequelize.transaction();
    try{
        console.log('Executing deletion of unconfirmed payments....')
        
        let events= await Models.Event.findAll({where:{
            [Op.and]:[
                {
                    expireDate:{[Op.lt]:new Date()}
                },
                {
                    paymentStatus:0
                }
            ]
        },attributes:['id']})
        for (const iterator of events) {
            console.log('Deleting Event',iterator.dataValues.id)
            await iterator.destroy({transaction})
        }
        await transaction.commit()
        return  1
    }
    catch(error){
        console.log('Somthing Error Occurs in Events Crone',error)
        await transaction.rollback()
        return 0
    }
}

exports.notJoinedCallsToHistory=async()=>{
    const transaction = await Models.sequelize.transaction();
    try{
        console.log('notJoinedCallsToHistory....')
        
        let events= await Models.Event.findAll({where:{
            [Op.and]:[
                {
                    endDate:{[Op.lt]:new Date()}
                },
                {
                    eventStatus: {[Op.or]: [Constants.EVENT_STATUS.BOOKED, Constants.EVENT_STATUS.RESCHEDULED]}
                }
            ]
        },attributes:['id']})
        for (const iterator of events) {
            console.log('Changing Status Event',iterator.dataValues.id)
            await Models.Event.update({ eventStatus: Constants.EVENT_STATUS.NOT_JOINED }, { where: { id: iterator.dataValues.id }, transaction })
        }
        await transaction.commit()
        return  1
    }
    catch(error){
        console.log('Somthing Error Occurs in Events Crone',error)
        await transaction.rollback()
        return 0
    }
}

const sendEmail = async(recipients,replacements,code, language = "en")=>{
    try{
        const requestObj = {
            url : `${process.env.EMAIL_DOMAIN}/email/send`,
            data: { replacements, priority:'high', code, recipients },
            method: "post", headers: {language}
        }
        
        let res=await Axios(requestObj).then(async(res)=>{ return res.data }).catch(async (error) => { return {} });
        return res
    } catch(error) {
        console.log('Error in Sending email',error)
    }
}

exports.meetingReminder = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
      let startDate = Moment().add(60, "minutes").utc().format();
  
      let eventInfo = await Models.Event.findAll({
        transaction,
        where: { 
          eventStatus: { [Op.or] : [1, 2] },
          eventReminder: 0,
          groupId: 2,
          startDate:{[Op.lte]: startDate}
        },
        include: [
          { model: Models.Participants, as: "Participants" }
        ]
      })
  
      eventInfo = JSON.parse(JSON.stringify(eventInfo));
  
      for(let event of eventInfo) {
        await Models.Event.update({ eventReminder: 1 }, { transaction, where: { id: event.id } });
        let customerEmail = null;
        let companionEmail = null;
        // let timezone = event.timezone;
        // let tz = null;
        // let isTimezone = null;

        let tz = "CET";
        let isTimezone = "CEST";
        let timeZoneValue = Moment.utc(event.startDate).tz(tz)
        if (timeZoneValue.isDST()) {
          isTimezone = "CEST"
        } else {
          isTimezone = "CET"
        }

        // if(timezone === null) {
        //   tz = "UTC";   
        //   isTimezone = "UTC";
        // } else {
        //   timezone = timezone.split("|");
        //   tz = timezone[0];
        //   isTimezone = timezone[1];;
        // }
        //let startDate = Moment(event.dataValues.startDate).toString();
    
        let startDate = Moment.utc(event.startDate).tz(tz).locale(event.prefferedLanguage).format('LT');
        startDate = startDate + ` (${isTimezone})`


        let customerEmailObj = {link: event.zoomLink, date: startDate};
        let companionEmailObj = {link: event.startLink, date: startDate};
        console.log(event.Participants, " ===================== event.Participants")
        for(let participant of event.Participants) {
            let userInfo = await Models.User.findOne({ where: { userId: participant.userId } });

            if(participant.role === "owner") {
                console.log(" ============ owner", userInfo.firstName)
                // shoot email for customer
               customerEmail = userInfo.email;
               customerEmailObj["name"] = userInfo.firstName;
               companionEmailObj["meetingWith"] = userInfo.firstName + " " + userInfo.lastName;
            }
            if(participant.role == "companion") {
                // shoot email for companion
                console.log(" ============ companion", userInfo.firstName)
               companionEmail = userInfo.email;
               customerEmailObj["meetingWith"] = userInfo.firstName + " " + userInfo.lastName;
               companionEmailObj["name"] = userInfo.firstName;
            }
        }


        console.log("customerEmailObj ===== ", customerEmailObj)
        console.log("companionEmailObj ===== ", companionEmailObj)


        await sendEmail([customerEmail],customerEmailObj,"MEETING_REMINDER_CUSTOMER", event.prefferedLanguage);
        await sendEmail([companionEmail],companionEmailObj,"MEETING_REMINDER_COMPANION", event.prefferedLanguage);
      }
      console.log(eventInfo, " ============= eventInfo")
      await transaction.commit();
  
    } catch (error) {
      await transaction.rollback();
      console.log(error)
    }
  }

  exports.paymentReminder = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
      let startDate = Moment().subtract(7, "days").utc().format();
  
      let eventInfo = await Models.Event.findAll({
        transaction,
        where: { 
          eventStatus: 7,
          paymentReminder: 0,
          groupId: 2,
          startDate:{[Op.lte]: startDate}
        },
        include: [
          { model: Models.Participants, as: "Participants" }
        ]
      })
  
      eventInfo = JSON.parse(JSON.stringify(eventInfo));
  
      for(let event of eventInfo) {
        await Models.Event.update({ paymentReminder: 1 }, { transaction, where: { id: event.id } });
        let invoiceInfo = await Models.Invoice.findOne({ where: { eventId: event.id, isPaid: 0 } });
        if(!invoiceInfo) continue;
        let customerEmail = null;
        let timezone = event.timezone;
        let tz = null;
        let isTimezone = null;
        if(timezone === null) {
          tz = "UTC";
          isTimezone = "UTC";
        } else {
          timezone = timezone.split("|");
          tz = timezone[0];
          isTimezone = timezone[1];
        }
    
        let startDate = Moment.utc(event.startDate).tz(tz).locale(event.prefferedLanguage).format('LLLL');
        startDate = startDate + ` (${isTimezone})`

        let url = `${process.env.DOMAIN_URL}/user/meeting-history?meetingId=${event.id}`

        let customerEmailObj = {link: invoiceInfo.paymentLink, date: startDate, url};
        for(let participant of event.Participants) {
            let userInfo = await Models.User.findOne({ where: { userId: participant.userId } });

            if(participant.role === "owner") {
               customerEmail = userInfo.email;
               customerEmailObj["name"] = userInfo.firstName;
            }
            if(participant.role == "companion") {
               companionEmail = userInfo.email;
               customerEmailObj["meetingWith"] = userInfo.firstName + " " + userInfo.lastName;
            }
        }

        await sendEmail([customerEmail],customerEmailObj,"PAYMENT_REMINDER", event.prefferedLanguage);
      }
      console.log(eventInfo, " ============= eventInfo")
      await transaction.commit();
  
    } catch (error) {
        await transaction.rollback();
      console.log(error)
    }
  }