async function initlizeSystem(){
    const transaction = await Models.sequelize.transaction();
    try{
        console.log('************Initlizing System For Testing****************');
        //create User Details
        await Models.User.create({userId:1,data:{id: 1}},{transaction});
        await Models.User.create({userId:2,data:{id: 2}},{transaction});
        await Models.User.create({userId:3,data:{id: 3}},{transaction});
        await Models.User.create({userId:4,data:{id: 4}},{transaction});
        await Models.User.create({userId:5,data:{id: 5}},{transaction});


        //create Group Type:
        await Models.GroupType.bulkCreate([ {groupName:'Events',groupTypeCode:'events'}],{transaction});
             let t= await Models.Group.bulkCreate([
            {groupTypeId:1,groupCode:'medication',status:1,userId:1},
            {groupTypeId:1,groupCode:'to-do',status:1,userId:1},
        ],{transaction})
        await transaction.commit();

        console.log('****************SYSTEM_INITLIZED_FOR_TESTING*******************');
}

    catch(ex)
    {
        await transaction.rollback();
        console.log('error',ex);
        process.exit(1);
    }
}

initlizeSystem()