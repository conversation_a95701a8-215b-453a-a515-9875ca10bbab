"use strict";
const GroupController = require("../controllers/groups");
//"create_groupType","manage_groupType","create_group","view_groups","manage_groups","update_group",
module.exports = [ 
    {
		method : "POST",
		path : "/admin/groups/createGroupType",
		handler : GroupController.createGroupType,
		options: {
			tags: ["api", "Group"],
			notes: "Endpoint to define a new group type for portal",
			description:"Create group type",
			auth: {strategy: 'jwt',scope:["owner","admin","vet"]},
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
                    groupType : Joi.string().required().error(errors=>{return Common.routeError(errors,'GROUP_TYPE_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: <PERSON><PERSON>
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "POST",
		path : "/groups/createGroup",
		handler : GroupController.createGroup,
		options: {
			tags: ["api", "Group"],
			notes: "Endpoint to define a new group for portal",
			description:"Create group",
			auth:false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
                    groupTypeId : Joi.number().required().error(errors=>{return Common.routeError(errors,'GROUP_TYPE_IS_REQUIRED')}),
                    groupParentId: Joi.number().optional(),
                    groupName : Joi.string().required().error(errors=>{return Common.routeError(errors,'GROUP_NAME_TYPE_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "PATCH",
		path : "/groups/updateGroup",
		handler : GroupController.updateGroup,
		options: {
			tags: ["api", "Group"],
			notes: "Endpoint to edit a group for portal",
			description:"Update group",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					groupTypeId:Joi.number().required().error(errors=>{return Common.routeError(errors,'GROUP_TYPE_IS_REQUIRED')}),
                    groupId : Joi.number().required().error(errors=>{return Common.routeError(errors,'GROUP_ID_IS_REQUIRED')}),
                    groupParentId: Joi.number().optional(),
                    groupName : Joi.string().required().error(errors=>{return Common.routeError(errors,'GROUP_NAME_TYPE_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/groups/getGroup",
		handler : GroupController.getGroup,
		options: {
			tags: ["api", "Group"],
			notes: "Endpoint to get group, parent groups or all groups",
			description:"Fetch group",
			auth:false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
                    groupType: Joi.string().optional().default('events').valid('events'),
                    groupParentId: Joi.number().optional(),
                    groupId : Joi.number().optional(),
					completeTree:Joi.number().optional().default(0)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	}
]