"use strict";
const controller = require("../controllers/faqController");
module.exports = [
	{
    method : "POST",
    path : "/faq-category",
    handler : controller.createCategory,
    options: {
      tags: ["api", "FAQ Category"],
      notes: "Endpoint to allow admin to create FAQ category",
      description: "FAQs",
      auth:{strategy: "jwt", scope: ["admin","settings"]},
      validate: {
        headers: Joi.object(Common.headers(true)).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        payload: {
           name:Joi.string().example('category name').required().error(err=>{return Common.routeError(err,'NAME_IS_REQUIRED')})
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
	{
    method : "PATCH",
    path : "/faq-category",
    handler : controller.updateCategory,
    options: {
      tags: ["api", "FAQ Category"],
      notes: "Endpoint to allow admin to update FAQ category",
      description: "FAQs",
      auth:{strategy: "jwt", scope: ["admin","settings"]},
      validate: {
        headers: Joi.object(Common.headers(true)).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        payload: {
           id:Joi.number().integer().example(1).required().error(err=>{return Common.routeError(err,'ID_IS_REQUIRED')}),
           name:Joi.string().example('category name').optional().default(null)
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
	{
    method : "GET",
    path : "/faq-category",
    handler : controller.categoryList,
    options: {
      tags: ["api", "Feedback"],
      notes: "Endpoint to get faq category",
      description: "FAQs",
      auth:false,
      validate: {
        headers: Joi.object(Common.headers()).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        query: {},
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
  {
    method : "DELETE",
    path : "/faq-category",
    handler : controller.deleteCategory,
    options: {
      tags: ["api", "FAQ Category"],
      notes: "Endpoint to allow admin to delete FAQ category",
      description: "FAQs",
      auth:{strategy: "jwt", scope: ["admin","settings"]},
      validate: {
        headers: Joi.object(Common.headers(true)).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        payload: {
           id:Joi.number().integer().example(1).required().error(err=>{return Common.routeError(err,'ID_IS_REQUIRED')})
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
  {
    method : "POST",
    path : "/faq",
    handler : controller.createFAQ,
    options: {
      tags: ["api", "FAQ Category"],
      notes: "Endpoint to allow admin to create FAQ",
      description: "FAQs",
      auth:{strategy: "jwt", scope: ["admin","settings"]},
      validate: {
        headers: Joi.object(Common.headers(true)).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        payload: {
           title:Joi.string().example('title').required().error(err=>{return Common.routeError(err,'TITLE_IS_REQUIRED')}),
           description:Joi.string().example('description').required().error(err=>{return Common.routeError(err,'DESCRIPTION_IS_REQUIRED')}),
           categoryId:Joi.number().integer().example(1).required().error(err=>{return Common.routeError(err,'CATEGORY_ID_IS_REQUIRED')}),
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
  {
    method : "PATCH",
    path : "/faq",
    handler : controller.updateFAQ,
    options: {
      tags: ["api", "FAQ Category"],
      notes: "Endpoint to allow admin to update FAQ",
      description: "FAQs",
      auth:{strategy: "jwt", scope: ["admin","settings"]},
      validate: {
        headers: Joi.object(Common.headers(true)).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        payload: {
           title:Joi.string().example('title').optional().default(null),
           description:Joi.string().example('description').optional().default(null),
           categoryId:Joi.number().integer().example(1).optional().default(null),
           id:Joi.number().integer().example(1).required().error(err=>{return Common.routeError(err,'FAQ_ID_IS_REQUIRED')}),
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
  {
    method : "DELETE",
    path : "/faq/{id}",
    handler : controller.deleteFAQ,
    options: {
      tags: ["api", "FAQ Category"],
      notes: "Endpoint to allow admin to delete FAQ",
      description: "FAQs",
      auth:{strategy: "jwt", scope: ["admin","settings"]},
      validate: {
        headers: Joi.object(Common.headers(true)).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        params: {
           id:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'FAQ_ID_IS_REQUIRED')}),
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
  {
    method : "GET",
    path : "/faq",
    handler : controller.listFAQ,
    options: {
      tags: ["api", "FAQ Category"],
      notes: "Endpoint to list FAQ",
      description: "FAQs",
      auth:false,
      validate: {
        headers: Joi.object(Common.headers()).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        query: {
            limit: Joi.number().integer().optional().default(null),
            pageNumber : Joi.number().integer().min(1).optional().default(null),
            categoryId : Joi.number().integer().optional().default(null),
            orderByValue: Joi.string().allow('ASC', 'DESC').optional().default('DESC'),
            orderByParameter: Joi.string().allow('createdAt','id').optional().default('id'),
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
  {
    method : "GET",
    path : "/faq/{id}",
    handler : controller.detailFAQ,
    options: {
      tags: ["api", "FAQ Category"],
      notes: "Endpoint to list FAQ",
      description: "FAQs",
      auth:false,
      validate: {
        headers: Joi.object(Common.headers()).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        params: {
            id:Joi.number().integer().example(1).required().error(err=>{return Common.routeError(err,'FAQ_ID_IS_REQUIRED')}),
        },
        query: {},
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
];
