"use strict";
const Joi = require("joi").extend(require('@joi/date'));
const controller = require("../controllers/eventController");
module.exports = [
	{
    method : "POST",
    path : "/events",
    handler : controller.create,
    options: {
      tags: ["api", "Event"],
      notes: "Endpoint to allow add Event",
      description: "Create Event",
      auth:{strategy: 'jwt',scope:["admin","costumer","companion","student"]},
      validate: {
        headers: Joi.object(Common.headers(true)).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        payload: {
           title:Joi.string().example('This is my task1').required().error(err=>{return Common.routeError(err,'TITLE_IS_REQUIRED')}),
           groupId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')}),
           startDate:Joi.date().utc().example('2022-07-05T04:00:00Z').required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
           timings:Joi.array().items(Joi.object()).example([{start:"2022-07-05T04:00:00Z",end:"2022-07-05T04:00:00Z",timingType:1}]).optional().default(null),
           participant:Joi.array().items(Joi.object()).example([{userId:1,role:1}]).optional().default([]),
           allDay:Joi.number().example(0).required().error(err=>{return Common.routeError(err,'ALL_DAY_IS_REQUIRED')}),
           description:Joi.string().example('This is my task1 Description').optional().default(null),
           endDate:Joi.string().example('2022-07-05T04:00:00Z Optional').optional().default(null),
           day:Joi.array().example([1,2,3]).items(Joi.number()).optional().default(null),
           week:Joi.array().example([1,2,3]).items(Joi.number()).optional().default(null),
           month:Joi.array().example([1,2,3]).items(Joi.number()).optional().default(null),
           slots:Joi.number().example(10).optional().default(null),
           returnData:Joi.object().example({'id':'1'}).optional().default(null),
           type:Joi.number().example(1).valid(1,2,3,4).optional().default(null),
           duration:Joi.string().example('Duration').optional().default(null),
           remark:Joi.string().example('Remark').optional().default(null),
           link:Joi.string().example('ShareLink').optional().default(null),
           frequency:Joi.array().example([0]).optional().default(null),
           occurance:Joi.number().example(1).optional().default(null),
           owner:Joi.number().example(1).optional().default(null)
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },


// Get List 
  {
    method : "GET",
    path : "/events-slots",
    handler :controller.get,
    options: {
      tags: ["api", "Event"],
      notes: "Get Event Api",
      description: "Event List",
      auth:false,
      // auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
      validate: {
        headers: Joi.object(Common.headers()).options({
          allowUnknown: true
        }),
        query:{
                    companionId:Joi.number().integer().required().error(err=>{return Common.routeError(err,'COMPANION_ID_REQUIRED')}),
                    startDate:Joi.date().utc().example("2022-07-05T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
                    endDate:Joi.date().utc().example("2022-07-06T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'END_DATE_IS_REQUIRED')}),
                    //groupId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')})
        },
        options: {
          abortEarly: false
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
  {
    method : "GET",
    path : "/events-slots-copy-test",
    handler :controller.getCopyForTest,
    options: {
      tags: ["api", "Event"],
      notes: "Get Event Api",
      description: "Event List",
      auth:false,
      // auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
      validate: {
        headers: Joi.object(Common.headers()).options({
          allowUnknown: true
        }),
        query:{
                    companionId:Joi.number().integer().required().error(err=>{return Common.routeError(err,'COMPANION_ID_REQUIRED')}),
                    startDate:Joi.date().utc().example("2022-07-05T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
                    endDate:Joi.date().utc().example("2022-07-06T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'END_DATE_IS_REQUIRED')}),
                    //groupId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')})
        },
        options: {
          abortEarly: false
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
  {
    method : "GET",
    path : "/events-all-companion-slots",
    handler :controller.getAllCompanionSlots,
    options: {
      tags: ["api", "Event"],
      notes: "Get Event Api",
      description: "Event List",
      auth:false,
      validate: {
        headers: Joi.object(Common.headers()).options({
          allowUnknown: true
        }),
        query:{
          startDate:Joi.date().utc().example("2022-07-05T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
          endDate:Joi.date().utc().example("2022-07-06T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'END_DATE_IS_REQUIRED')})
        },
        options: {
          abortEarly: false
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },

  // Get List 
  {
    method : "GET",
    path : "/events-calander",
    handler :controller.getCalander,
    options: {
      tags: ["api", "Event"],
      notes: "Get Event Api",
      description: "Event List",
      auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
      validate: {
        headers: Joi.object(Common.headers(true)).options({
          allowUnknown: true
        }),
        query:{     
                    status:Joi.number().integer().default(null),
                    startDate:Joi.date().utc().example("2022-07-05T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
                    endDate:Joi.date().utc().example("2022-07-06T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'END_DATE_IS_REQUIRED')}),
                    flag: Joi.string().valid("companion", "user").allow(null).optional().default("user")
        },
        options: {
          abortEarly: false
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
 // get By Id 
  {
    method : "GET",
    path : "/events/id",
    handler : controller.getByid,
    options: {
      tags: ["api", "Event"],
      notes: "Endpoint to allow get event by id",
      description: "Get Event Details",
      auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
      validate: {
        headers: Joi.object(Common.headers(true)).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        query:{
            id:Joi.number().example(" 7 {Required}").integer().required().default(null).error(errors=>{return Common.routeError(errors,'SPECIES_ID_REQUIRED')}),
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },

//DELETE

  {
    method : "DELETE",
    path : "/events",
    handler : controller.delete,
    options: {
      tags: ["api", "Event"],
      notes: "Endpoint to Delete Event",
      description: "Delete Event ",
      auth:false,
      validate: {
        headers: Joi.object(Common.headers()).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        query:{
            id:Joi.number().example(1).integer().required().default(null).error(errors=>{return Common.routeError(errors,'EVENT_ID_REQUIRED')})
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
  {
    method : "DELETE",
    path : "/remove-slots",
    handler : controller.removeSlots,
    options: {
      tags: ["api", "Event"],
      notes: "Endpoint to Delete Event",
      description: "Delete Event ",
      auth:{strategy: "jwt"},
      validate: {
        headers: Joi.object(Common.headers(true)).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        query:{
            isCustomSlot:Joi.number().example(1).integer().required().valid(0,1).error(errors=>{return Common.routeError(errors,'EVENT_ID_REQUIRED')})
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },

// Mark as Complete a Event --------------------------
{
  method : "POST",
  path : "/events/complete",
  handler : controller.markComplete,
  options: {
    tags: ["api", "Event"],
    notes: "Endpoint to allow add Event",
    description: "Mark as complete",
    auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      options: {
        abortEarly: false
      },
      payload: {
         eventId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'EVENT_ID_IS_REQUIRED')}),
         timingId:Joi.number().example(1).optional().default(null),
         //userId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'USER_ID_IS_REQUIRED')}),
         attachment:Joi.array().example([{'id':1}]).optional().default(null),
         scheduleDate:Joi.date().example('2022-07-30T04:00:00Z').required().error(err=>{return Common.routeError(err,'EVENT_SCHEDULED_DATE_IS_REQUIRED')})    
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},



// Bulk Complete Event
{
  method : "POST",
  path : "/events/bulk-complete",
  handler : controller.bulkMarkComplete,
  options: {
    tags: ["api", "Event"],
    notes: "Endpoint to allow complete Event",
    description: "Bulk  mark as complete",
    auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      options: {
        abortEarly: false
      },
      payload: {
        data:Joi.array().items(
          Joi.object().keys(
            {
            eventId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'EVENT_ID_IS_REQUIRED')}),
            timingId:Joi.number().example(1).optional().default(null),
            attachment:Joi.array().example([{'id':1}]).optional().default(null),
            scheduleDate:Joi.date().example('2022-07-30T04:00:00Z').required().error(err=>{return Common.routeError(err,'EVENT_SCHEDULED_DATE_IS_REQUIRED')})
            }
          )
        ).min(1).required().error(err=>{return Common.routeError(err,'EVENT_ID_IS_REQUIRED')})     
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},


// rescheduleAppointment
{
  method : "POST",
  path : "/events/reschedule",
  handler : controller.rescheduleEvent,
  options: {
    tags: ["api", "Event"],
    notes: "Reschedule Event",
    description: "Reschedule Events",
    auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      payload:{
              eventId:Joi.number().integer().example(1).required().error(err=>{return Common.routeError(err,'EVENT_ID_IS_REQUIRED')}),
              title:Joi.string().example('This is my task1').required().error(err=>{return Common.routeError(err,'TITLE_IS_REQUIRED')}),
              groupId:Joi.number().example(1).valid(2).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')}),
              startDate:Joi.date().utc().example('2022-07-05T04:00:00Z').required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
              timings:Joi.array().items(Joi.object()).example([{start:"2022-07-05T04:00:00Z",end:"2022-07-05T04:00:00Z",timingType:1}]).optional().default(null),
              participant:Joi.array().items(Joi.object()).example([{userId:1,role:1}]).optional().default([]),
              allDay:Joi.number().example(0).required().error(err=>{return Common.routeError(err,'ALL_DAY_IS_REQUIRED')}),
              description:Joi.string().example('This is my task1 Description').optional().default(null),
              endDate:Joi.string().example('2022-07-05T04:00:00Z Optional').optional().default(null),
              returnData:Joi.object().example({'id':'1'}).optional().default(null),
              remark:Joi.string().example('Remark').optional().default(null),
              link:Joi.string().example('ShareLink').optional().default(null),
      },
      options: {
        abortEarly: false
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},

{
  method : "POST",
  path : "/events/action",
  handler : controller.confirmAppointmant,
  options: {
    tags: ["api", "Event"],
    notes: "Reschedule Event Action",
    description: "Reschedule Events Action",
    auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      payload:{
              eventId:Joi.number().integer().example(1).error(err=>{return Common.routeError(err,'EVENT_ID_IS_REQUIRED')}),
              notificationId:Joi.number().integer().example(1).error(err=>{return Common.routeError(err,'NOTIFICATION_ID_IS_REQUIRED')}),
              option:Joi.number().integer().valid(0,1).description('1 for accept & 0 for rejact').example(1).error(err=>{return Common.routeError(err,'EVENT_ID_IS_REQUIRED')}),
      },
      options: {
        abortEarly: false
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
// create Events in bulk
{
  method : "POST",
  path : "/events-bulk",
  handler : controller.bulkCreate,
  options: {
    tags: ["api", "Event"],
    notes: "Endpoint to allow add Events in Bulk",
    description: "Create Event",
    auth:{strategy: 'jwt',scope:["admin","costumer","companion","student"]},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      options: {
        abortEarly: false
      },
      payload: {
        data:Joi.array().items(
          Joi.object().min(1).keys(
            {
              title:Joi.string().example('This is my task1').required().error(err=>{return Common.routeError(err,'TITLE_IS_REQUIRED')}),
              groupId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')}),
              startDate:Joi.string().example('2022-07-05T04:00:00Z').required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
              timings:Joi.array().items(Joi.object()).example([{start:"2022-07-05T04:00:00Z",end:"2022-07-05T04:00:00Z",timingType:1}]).optional().default(null),
              participant:Joi.array().items(Joi.object()).example([{userId:1,role:1}]).optional().default([]),
              allDay:Joi.number().example(0).required().error(err=>{return Common.routeError(err,'ALL_DAY_IS_REQUIRED')}),
              description:Joi.string().example('This is my task1 Description ').optional().default(null),
              endDate:Joi.string().example('2022-07-05T04:00:00Z Optional').optional().default(null),
              day:Joi.array().example([1,2,3]).items(Joi.number()).optional().default(null),
              week:Joi.array().example([1,2,3]).items(Joi.number()).optional().default(null),
              month:Joi.array().example([1,2,3]).items(Joi.number()).optional().default(null),
              slots:Joi.number().example(10).optional().default(null),
              returnData:Joi.object().example({'id':'1'}).optional().default(null),
              type:Joi.number().example(1).valid(1,2,3,4).optional().default(null),
              duration:Joi.string().example('Duration').optional().default(null),
              remark:Joi.string().example('Remark').optional().default(null),
              link:Joi.string().example('ShareLink').optional().default(null),
              frequency:Joi.array().example([0]).optional().default(null),
              occurance:Joi.number().example(1).optional().default(null),
              isCustomSlot: Joi.number().integer().optional().default(0)
            }
          ).optional()
        ).required().error(err=>{return Common.routeError(err,'EVENT_DATA_IS_REQUIRED')}),
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
// Patch Events Bulk 
{
  method : "PATCH",
  path : "/events-bulk",
  handler :controller.bulkUpdate,
  options: {
    tags: ["api", "Event"],
    
    notes: "Endpoint to allow Update Bulk Event",
    description: "Update Event",
    auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      options: {
        abortEarly: false
      },
      payload: {
        data:Joi.array().items(
          Joi.object().min(1).keys(
            {
              title:Joi.string().example('This is my task1').required().error(err=>{return Common.routeError(err,'TITLE_IS_REQUIRED')}),
              groupId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')}),
              startDate:Joi.string().example('2022-07-05T04:00:00Z').required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
              timings:Joi.array().items(Joi.object()).example([{start:"2022-07-05T04:00:00Z",end:"2022-07-05T04:00:00Z",timingType:1}]).optional().default(null),
              participant:Joi.array().items(Joi.object()).example([{userId:1,role:1}]).optional().default([]),
              allDay:Joi.number().example(0).required().error(err=>{return Common.routeError(err,'ALL_DAY_IS_REQUIRED')}),
              description:Joi.string().example('This is my task1 Description ').optional().default(null),
              endDate:Joi.string().example('2022-07-05T04:00:00Z Optional').optional().default(null),
              day:Joi.array().example([1,2,3]).items(Joi.number()).optional().default(null),
              week:Joi.array().example([1,2,3]).items(Joi.number()).optional().default(null),
              month:Joi.array().example([1,2,3]).items(Joi.number()).optional().default(null),
              slots:Joi.number().example(10).optional().default(null),
              returnData:Joi.object().example({'id':'1'}).optional().default(null),
              type:Joi.number().example(1).valid(1,2,3,4).optional().default(null),
              duration:Joi.string().example('Duration').optional().default(null),
              remark:Joi.string().example('Remark').optional().default(null),
              link:Joi.string().example('ShareLink').optional().default(null),
              frequency:Joi.array().example([0]).optional().default(null),
              occurance:Joi.number().example(1).optional().default(null)
            }
          )
        ).min(1).required().error(err=>{return Common.routeError(err,'EVENT_DATA_IS_REQUIRED')}),
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
// Get Events For Patch

{
  method : "GET",
  path : "/events-bulk",
  handler :controller.bulkGet,
  options: {
    tags: ["api", "Event"],
    notes: "Get Event Api",
    description: "Event List",
    auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      query:{
          groupId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')}),
          isCustomSlot:Joi.number().example(1).valid(0,1).optional().default(0),
          startDate:Joi.date().optional().default(null)
      },
      options: {
        abortEarly: false
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},

// Cancel a Meeting 
{
  method : "DELETE",
  path : "/events-cancel",
  handler :controller.cancelMeeting,
  options: {
    tags: ["api", "Event"],
    notes: "CAncel Event Api",
    description: "Used For Cancel a Meeting of a user",
    auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      query:{
      id:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'MEETING_ID_IS_REQUIRED')}),
      reason:Joi.string().example('reason').required().error(err=>{return Common.routeError(err,'CANCEL_REASON_IS_REQUIRED')}),
      },
      options: {
        abortEarly: false
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "GET",
  path : "/upcoming-meetings",
  handler :controller.upcominMeetings,
  options: {
    tags: ["api", "Event"],
    notes: "Upcoming Meetings",
    description: "Upcoming Meetings in next 30 Days",
    auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      query:{
                    limit: Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(1),
                    //userId:Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC','DESC').optional().default('ASC'),
                    orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
                    startDate:Joi.string().example('2022-07-05T04:00:00Z').optional().default(null),
                    endDate:Joi.string().example('2022-07-05T04:00:00Z').optional().default(null),
      },
      options: {
        abortEarly: false
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "GET",
  path : "/test-digistore",
  handler :controller.testDgstore,
  options: {
    tags: ["api", "Event"],
    notes: "Upcoming Meetings",
    description: "Upcoming Meetings in next 30 Days",
    auth: false,
    validate: {
      headers: Joi.object(Common.headers()).options({
        allowUnknown: true
      }),
      options: {
        abortEarly: false
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "GET",
  path : "/completed-meetings",
  handler :controller.completedMeetings,
  options: {
    tags: ["api", "Event"],
    notes: "Completed Meetings",
    description: "Completed Meetings",
    auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      query:{
                    limit: Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(1),
                    //userId:Joi.number().integer().min(1).optional().,
                    orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
                    startDate:Joi.string().example('2022-07-05T04:00:00Z').optional().default(null),
                    endDate:Joi.string().example('2022-07-05T04:00:00Z').optional().default(null),
      },
      options: {
        abortEarly: false
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "GET",
  path : "/admin/completed-meetings",
  handler :controller.completedMeetingsForAdmin,
  options: {
    tags: ["api", "Event"],
    notes: "Completed Meetings",
    description: "Completed Meetings",
    auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      query:{
                    limit: Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(1),
                    userId:Joi.number().integer().optional().default(null),
                    status:Joi.number().integer().optional().default(null),
                    orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
                    companion: Joi.string().optional().default(null),
                    customer: Joi.string().optional().default(null),
                    orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
                    startDate:Joi.string().example('2022-07-05T04:00:00Z').optional().default(null),
                    endDate:Joi.string().example('2022-07-05T04:00:00Z').optional().default(null),
      },
      options: {
        abortEarly: false
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "GET",
  path : "/completed-meetings/customer",
  handler :controller.listMeetingsForCustomerHistory,
  options: {
    tags: ["api", "Event"],
    notes: "Completed Meetings",
    description: "Completed Meetings",
    auth: { strategy: "jwt" },
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      query:{
        userId:Joi.number().integer().required().error(err=>{return Common.routeError(err,'USER_ID_IS_REQUIRED')}),
      },
      options: {
        abortEarly: false
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},

{
  method : "POST",
  path : "/meeting-payment",
  handler : controller.updatePaymentStatus,
  options: {
    tags: ["api", "Meeting Payment"],
    notes: "Meeting Payment Confirmation",
    description: "Meeting payment confirmation",
    auth:false,
    validate: {
      headers: Joi.object(Common.headers()).options({
        allowUnknown: true
      }),
      payload:{
              data:Joi.string().example('data').error(err=>{return Common.routeError(err,'DATA_IS_REQUIRED')}),
            
      },
      options: {
        abortEarly: false
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "POST",
  path : "/complete-meeting-payment",
  handler : controller.updateInvoice,
  options: {
    tags: ["api", "Meeting Payment"],
    notes: "Meeting Payment Confirmation",
    description: "Meeting payment confirmation",
    auth: false,
    validate: {
      headers: Joi.object(Common.headers()).options({
        allowUnknown: true
      }),
      payload:{
              data:Joi.string().example('data').error(err=>{return Common.routeError(err,'DATA_IS_REQUIRED')}),
            
      },
      options: {
        abortEarly: false
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},

{
  method : "GET",
  path : "/invoices",
  handler :controller.getInvoice,
  options: {
    tags: ["api", "Event"],
    notes: "Completed Meetings",
    description: "Completed Meetings",
    auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      query:{
                    userId:Joi.number().integer().optional().default(null),
                    limit: Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(1),
                    orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt','id').optional().default('id')
      },
      options: {
        abortEarly: false
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "POST",
  path : "/test",
  handler :controller.test,
  options: {
    tags: ["api", "Event"],
    notes: "Completed Meetings",
    description: "Completed Meetings",
    auth: false,
    validate: {
      headers: Joi.object(Common.headers()).options({
        allowUnknown: true
      }),
      payload:{
        companions: Joi.array().required()     
      },
      options: {
        abortEarly: false
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "GET",
  path : "/events-all-slots",
  handler :controller.getAllSlots,
  options: {
    tags: ["api", "Event"],
    notes: "Get Event Api",
    description: "Event List",
    auth:false,
    // auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
    validate: {
      headers: Joi.object(Common.headers()).options({
        allowUnknown: true
      }),
      query:{
                  //companionId:Joi.number().integer().required().error(err=>{return Common.routeError(err,'COMPANION_ID_REQUIRED')}),
                  startDate:Joi.date().utc().example("2022-07-05T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
                  endDate:Joi.date().utc().example("2022-07-06T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'END_DATE_IS_REQUIRED')}),
                  //groupId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')})
      },
      options: {
        abortEarly: false
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "GET",
  path : "/companion-dashboard-count",
  handler :controller.companionDashboardCount,
  options: {
    tags: ["api", "Event"],
    notes: "Get Event user contacted count Api",
    description: "Event List",
    auth: {strategy: 'jwt'},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      query:{},
      options: {
        abortEarly: false
      },
      failAction: async (req, h, err) => {
        return Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "GET",
  path : "/order-meetings",
  handler : controller.getOrders,
  options : {
    tags: ["api", "Cart-Order"],
    notes: "GET Cart-Order",
    description: "GET Cart-Order",
    auth : {strategy: "jwt",scope : ["admin","costumer",'companion','student','order_management']},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      options : {
        abortEarly: false
      },
      query : {
                  limit: Joi.number().integer().optional().default(20),
        userId:Joi.number().integer().optional().default(null),
                  pageNumber: Joi.number().integer().min(1).optional().default(1),
                  orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                  orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id'),
                  // categoryId: Joi.string().allow(null).optional().default(null),
        startDate:Joi.date().example('2022-07-05T04:00:00Z').optional().default(null),
        endDate:Joi.date().example('2022-07-05T04:00:00Z').optional().default(null)
      },
      failAction: async(req,h, err) => {
        return  Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "GET",
  path : "/order-meeting",
  handler : controller.getOrderById,
  options : {
    tags: ["api", "Cart-Order"],
    notes: "GET Cart-Order",
    description: "GET Cart-Order",
    auth : {strategy: "jwt", scope : ["admin","costumer",'companion','student','order_management']},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      options : {
        abortEarly: false
      },
      query : {
        orderId:Joi.number().integer().required(),
      },
      failAction: async(req,h, err) => {
        return  Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "GET",
  path : "/event/user-summary",
  handler : controller.getEventSummary,
  options : {
    tags: ["api", "Summary"],
    notes: "GET Event summary",
    description: "GET Event summary",
    auth : {strategy: "jwt", scope : ["admin",'companion','student']},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      options : {
        abortEarly: false
      },
      query : {
        userId:Joi.number().integer().required(),
      },
      failAction: async(req,h, err) => {
        return  Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "GET",
  path : "/event/legacy",
  handler : controller.legacyMeetings,
  options : {
    tags: ["api", "Event"],
    notes: "GET Event summary from legacy list",
    description: "GET Event summary from legacy list",
    auth : {strategy: "jwt"},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      options : {
        abortEarly: false
      },
      query : {
        limit: Joi.number().integer().optional().default(20),
        pageNumber: Joi.number().integer().min(1).optional().default(1),
        userId:Joi.number().integer().optional().allow(null).default(null),
      },
      failAction: async(req,h, err) => {
        return  Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "POST",
  path : "/event/slot-reminder",
  handler : controller.generateSlotReminder,
  options : {
    tags: ["api", "Event"],
    notes: "GET Event summary from legacy list",
    description: "GET Event summary from legacy list",
    auth : {strategy: "jwt"},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      options : {
        abortEarly: false
      },
      payload : {
        companionId:Joi.number().integer().required()
      },
      failAction: async(req,h, err) => {
        return  Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "GET",
  path : "/event/slot-reminder-check",
  handler : controller.slotReminderCheck,
  options : {
    tags: ["api", "Event"],
    notes: "GET Event summary from legacy list",
    description: "GET Event summary from legacy list",
    auth : {strategy: "jwt"},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      options : {
        abortEarly: false
      },
      query : {
        companionId:Joi.number().integer().required()
      },
      failAction: async(req,h, err) => {
        return  Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
},
{
  method : "GET",
  path : "/event/slot-reminder-users",
  handler : controller.slotReminderUserList,
  options : {
    tags: ["api", "Event"],
    notes: "GET Event summary from legacy list",
    description: "GET Event summary from legacy list",
    auth : {strategy: "jwt"},
    validate: {
      headers: Joi.object(Common.headers(true)).options({
        allowUnknown: true
      }),
      options : {
        abortEarly: false
      },
      failAction: async(req,h, err) => {
        return  Common.FailureError(err, req);
      },
      validator: Joi
    },
    pre : [{method: Common.prefunction}]
  }
}
];
