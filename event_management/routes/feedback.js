"use strict";
const controller = require("../controllers/feedbackController");
module.exports = [
	{
    method : "POST",
    path : "/feedback",
    handler : controller.catchUserRating,
    options: {
      tags: ["api", "Feedback"],
      notes: "Endpoint to allow user to add feedback",
      description: "Feedback",
      auth:false,
      validate: {
        headers: Joi.object(Common.headers()).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        payload: {
           token:Joi.string().example('token').required().error(err=>{return Common.routeError(err,'TOKEN_IS_REQUIRED')}),
           feedback:Joi.array().items(Joi.number().integer()).min(1).required().error(err=>{return Common.routeError(err,'FEEDBACK_ARRAY_IS_REQUIRED')}),
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: <PERSON><PERSON>
      },
      pre : [{method: Common.prefunction}]
    }
  },
	{
    method : "GET",
    path : "/feedback-questions",
    handler : controller.listQuestions,
    options: {
      tags: ["api", "Feedback"],
      notes: "Endpoint to list feedback questions",
      description: "Feedback",
      auth:false,
      validate: {
        headers: Joi.object(Common.headers()).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        query: {
           token:Joi.string().example('token').required().error(err=>{return Common.routeError(err,'TOKEN_IS_REQUIRED')})
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
	{
    method : "GET",
    path : "/feedback/{meetingId}",
    handler : controller.showUserRating,
    options: {
      tags: ["api", "Feedback"],
      notes: "Endpoint to get feedback rating",
      description: "Feedback",
      auth:false,
      validate: {
        headers: Joi.object(Common.headers()).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        params: {
           meetingId:Joi.number().integer().example(1).required().error(err=>{return Common.routeError(err,'MEETING_IS_REQUIRED')})
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
	{
    method : "GET",
    path : "/default-feedback-questions",
    handler : controller.defaultQuestions,
    options: {
      tags: ["api", "Feedback"],
      notes: "Endpoint to list feedback questions",
      description: "Feedback",
      auth:false,
      validate: {
        headers: Joi.object(Common.headers()).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        query: {
           
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
  {
    method : "GET",
    path : "/validate-token/{token}",
    handler : controller.validateToken,
    options: {
      tags: ["api", "Feedback"],
      notes: "Endpoint to valiadte token",
      description: "Feedback",
      auth:false,
      validate: {
        headers: Joi.object(Common.headers()).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        params: {
          token: Joi.string().required().error(err=>{return Common.routeError(err,'TOKEN_IS_REQUIRED')})
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  },
  {
    method : "GET",
    path : "/{companionId}/feedbacks",
    handler : controller.fetchFeedbackList,
    options: {
      tags: ["api", "Feedback"],
      notes: "Endpoint to valiadte token",
      description: "Feedback",
      auth:{ strategy: "jwt", scope: ["admin"] },
      validate: {
        headers: Joi.object(Common.headers(true)).options({
          allowUnknown: true
        }),
        options: {
          abortEarly: false
        },
        query: {
          limit: Joi.number().integer().optional().default(20),
          pageNumber: Joi.number().integer().min(1).optional().default(1),
        },
        params: {
          companionId: Joi.number().required().error(err=>{return Common.routeError(err,'COMPANION_ID_IS_REQUIRED')})
        },
        failAction: async (req, h, err) => {
          return Common.FailureError(err, req);
        },
        validator: Joi
      },
      pre : [{method: Common.prefunction}]
    }
  }
];
