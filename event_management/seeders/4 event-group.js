'use strict';

module.exports = {
  async up (queryInterface, Sequelize) {
   await queryInterface.bulkInsert('event_groups',[
      {group_type_id:1,group_code:'working-hours',status:1,created_at:new Date(),updated_at:new Date()},    
      {group_type_id:1,group_code:'meeting',status:1,created_at:new Date(),updated_at:new Date()},    
      {group_type_id:1,group_code:'appointment',status:1,created_at:new Date(),updated_at:new Date()},
      {group_type_id:1,group_code:'seminar',status:1,created_at:new Date(),updated_at:new Date()},        
    ])
   
  },

  async down (queryInterface, Sequelize) {
  }
};

