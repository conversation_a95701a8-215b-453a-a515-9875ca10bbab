'use strict';

module.exports = {
  async up (queryInterface, Sequelize) {
   await  queryInterface.bulkInsert('event_permissions',[
      {permission_code:'event-create',status:1,created_at:new Date(),updated_at:new Date()},
      {permission_code:'event-manage',status:1,created_at:new Date(),updated_at:new Date()},
      {permission_code:'event-list',status:1,created_at:new Date(),updated_at:new Date()},
      {permission_code:'event-delete',status:1,created_at:new Date(),updated_at:new Date()}
    ])
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  }
};
