'use strict';

module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.bulkInsert('event_group_contents',[
        {group_id:1,language_id:1,group_name:'Working Hours',created_at:new Date(),updated_at:new Date()},
        {group_id:2,language_id:1,group_name:'Meeting',created_at:new Date(),updated_at:new Date()},
        {group_id:3,language_id:1,group_name:'Appointment',created_at:new Date(),updated_at:new Date()},
        {group_id:4,language_id:1,group_name:'Seminar',created_at:new Date(),updated_at:new Date()}
      ])
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  }
};
