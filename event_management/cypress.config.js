const { defineConfig } = require("cypress");
require('dotenv').config(); 

module.exports = defineConfig({
  e2e: {
    setupNodeEvents(on, config) {
      // implement node event listeners here
    },
    baseUrl:process.env.NODE_SERVER_PUBLIC_API_TEST,
  },
  projectId: "9kk6bt",
  video:false,
  env:{
    token:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiMDM3YTNiNzliZTE0YThmNmVmMmUyNDJhNDY2YzQ5Y2UzZTVjODcxOTJjOTU0MDA2YmNhZWIwZmExMTY1YmIwMWZhNjhjOWQzYzU4ZmRmM2ZlZDU2MDc3ZGEwYjAyYjA1NmRmMTJlYWVkODQ1OGFkZmI3MWJjNmYzMzNjMWRlOGFjMDQ5MjEwNzU0ODA5Y2U2NjJhM2NhMjMwYjUwZTJjMzgyY2Q5NmY5MDhhYjlmMGNiODMzYjYxYTkzYjJkNmQ5NDkzNzgxNjAyY2NjODNiNGI1MGVhM2QzOGM3NjQzYzNiMWUzZWRjMzIyNjdmODZhYTkzYWZjMDdjMmU4ODA1NjRmMDAwYmYzMWVmMWZhMmI2YWQyNTExYzgyNTY4MjM0MmM1MWMxNzMxZWVjMDExNWNlNmJlMjNjYjFhOTBlMzhlOGEzNmUyYmQ2ODQ5MWZjZmQ4NDg0NTY4ODcxZGQzODU1MTFjYjkzYzliMDAzODUwYjFkODNjNjllMGEwMGY3ODMzYmE2MjAxODg1YTExYzI4YmY0ODdmZDk5OGY0YzE2NDA5MWVjYWUzYjU2OTdkNjQ5ZGJkNzgzOTFlY2Y3YTMzZTEwYjc0ZThhNjMxODYxZTQzNWZmM2U1M2RkMDc1NGJhZDMzYjZmN2MwOGQyZWE3NjFiMzFjYTE1NmU0YjcwYmY4MTJmNGUwODkyMDQ3YjIyMWZhNGRiMDc0OTAxM2IzNDYzNzc5MmUxMjI3NWZhYzkzZDU1NDIxZTVmOWEyYTQyNzVlYTgxMGY2ZmE0Yzk4ODQ5M2Q4YTZiOGZkOWU3Mzg1ZmZhZjIwZjdiMjMzY2ZmNzg1ZjBlMTM0MTQ4MGFmMDA0ZDcxODQ1MjJjNmMzYWMyYzM3MDY1NzRlOGVhYmQ1MjQzMjJkZmNkMDA5NTEwNjc3YzFjNzlhZWRkN2NjNzYzNTBmNDI5M2IzYTU3MDgwYzg4NmEzMWE0OTI2MjExMTE0MWE0NTMyZjk2MDliNTNiODY0ZWQ1ZDQ4YWVlMWEwMjc4NjZiMDI2MGFiYTAzZmE5M2I1N2QwNjIyYzk1YjU0M2JiOTc1YmRiYTkxODk5ZGUxNDkyNDIwZjA2M2QxZTMzNGVmNjc3YzJiZTI1MjEwOGZkZWZlNjU4MTY4IiwiaWF0IjoxNjcwODE4ODc3fQ.lkRMOFyBCwStuPz45lxG2SGD5RzMwXozVG7euIHsQiQ"
  }
});
