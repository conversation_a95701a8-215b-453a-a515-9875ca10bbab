// Event On Every day  
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);
Cypress.dayjs = dayjs

describe("Testing for Event Model", () => {
    
    var token =Cypress.env("token")
    it("Initial  Event Test", () => {
        cy.request("/").then((res) => {
            expect(res.status).to.equal(200);
            let body = {
                            "title": "Daily Event",
                            "groupId": 1,
                            "startDate": Cypress.dayjs.utc(new Date()).add(1,'M'),
                            "endDate": Cypress.dayjs.utc(new Date()).add(2,'M'),
                            "timings": [{"start":Cypress.dayjs.utc(new Date()),"end":Cypress.dayjs.utc(new Date()).add(1,'hour')}],
                            "description": "Events Occurs Every Day For a Month",
                            "link": "Link",
                            "duration":"20",
                            "type":1,
                            "frequency":[1],
                            "allDay":0,
                            "remark": "Remark"
            };
            let injectionObject = {
                method: "POST",
                url: "/events",
                headers: {
                    authorization:token
                    },
                body: body,
            };
            cy.request(injectionObject).then((res) => {
                expect(res.body.message).to.equal("WORKING_HOURS_CREATED_SUCCESSFULLY");
            });
        });
    });
});
