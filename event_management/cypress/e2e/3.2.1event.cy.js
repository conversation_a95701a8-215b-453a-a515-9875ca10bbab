// event on every month on last Monday with 5 occurance
import dayjs from 'dayjs'; 
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);
Cypress.dayjs = dayjs
describe("Testing for Event Model", () => {
    it("Initial  Event Test", () => {
        var token =Cypress.env("token")
        cy.request("/").then((res) => {
            expect(res.status).to.equal(200);
            let body = {
                            "title": "Event Every Month on Last Monday 5 times",
                            "groupId": 1,
                            "startDate": Cypress.dayjs.utc(new Date()).add(1,'M'),
                            "timings": [{"start":Cypress.dayjs.utc(new Date()),"end":Cypress.dayjs.utc(new Date()).add(1,'hour')}],
                            "description": "Events Occurs On Every Month On last Monday 5 times",
                            "link": "Link",
                            "duration":"20",
                            "type":3,
                            "occurance":5,
                            "day":[0],
                            "week":[-1],
                            "frequency":[1],
                            "allDay":0,
                            "remark": "Remark"
            };
            let injectionObject = {
                method: "POST",
                url: "/events",
                headers: {
                    authorization:token
                    },
                body: body,
            };
            cy.request(injectionObject).then((res) => {
                expect(res.body.message).to.equal("WORKING_HOURS_CREATED_SUCCESSFULLY");
            });
        });
    });
});
