// event on every year on first sat of july
import dayjs from 'dayjs'; 
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);
Cypress.dayjs = dayjs
describe("Testing for Event Model", () => {
    var token =Cypress.env("token")
    it("Initial  Event Test", () => {
        cy.request("/").then((res) => {
            expect(res.status).to.equal(200);
            let body = {
                            "title": "Event Every Year On First Sat Of July",
                            "groupId": 1,
                            "startDate": Cypress.dayjs.utc(new Date()).add(1,'M'),
                            "timings": [{"start":Cypress.dayjs.utc(new Date()),"end":Cypress.dayjs.utc(new Date()).add(1,'hour')}],
                            "description": "Events Occurs On Every year on first sat of july",
                            "link": "Link",
                            "duration":"20",
                            "type":4,
                            "day":[5],
                            "week":[1],
                            "month":[7],
                            "frequency":[1],
                            "allDay":0,
                            "remark": "Remark"
            };
            let injectionObject = {
                method: "POST",
                url: "/events",
                headers: {
                    authorization:token
                    },
                body: body,
            };
            cy.request(injectionObject).then((res) => {
                expect(res.body.message).to.equal("WORKING_HOURS_CREATED_SUCCESSFULLY");
            });
        });
    });
});
