// event on every month first monday 
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);
Cypress.dayjs = dayjs
describe("Testing for Event Model", () => {
    it("Initial  Event Test", () => {
        cy.request("/").then((res) => {
            var token =Cypress.env("token")
            expect(res.status).to.equal(200);
            let body = {
                            "title": "Event Every Month on First Monday",
                            "groupId": 1,
                            "startDate": Cypress.dayjs.utc(new Date()).add(1,'M'),
                            "timings": [{"start":Cypress.dayjs.utc(new Date()),"end":Cypress.dayjs.utc(new Date()).add(1,'hour')}],
                            "description": "Events Occurs On Every Month On First Monday",
                            "link": "Link",
                            "duration":"20",
                            "type":3,
                            "day":[0],
                            "week":[1],
                            "frequency":[1],
                            "allDay":0,
                            "remark": "Remark"
            };
            let injectionObject = {
                method: "POST",
                url: "/events",
                headers: {
                    authorization:token
                    },
                body: body,
            };
            cy.request(injectionObject).then((res) => {
                expect(res.body.message).to.equal("WORKING_HOURS_CREATED_SUCCESSFULLY");
            });
        });
    });
});
