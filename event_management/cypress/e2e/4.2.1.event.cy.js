// event on every year on  2 Oct with 4 occurance 
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);
Cypress.dayjs = dayjs
describe("Testing for Event Model", () => {
    var token =Cypress.env("token")
    it("Initial  Event Test", () => {
        cy.request("/").then((res) => {
            expect(res.status).to.equal(200);
            let body = {
                            "title": "Event Every Year On 2 oct with 4 occu",
                            "groupId": 1,
                            "startDate": Cypress.dayjs.utc(new Date()).add(1,'M'),
                            "timings": [{"start":Cypress.dayjs.utc(new Date()),"end":Cypress.dayjs.utc(new Date()).add(1,'hour')}],
                            "description": "Event Every Year On 2 oct with 4 occu",
                            "link": "Link",
                            "duration":"20",
                            "type":4,
                            "day":[2],
                            "month":[10],
                            "frequency":[1],
                            "occurance":4,
                            "allDay":0,
                            "remark": "Remark"
            };
            let injectionObject = {
                method: "POST",
                url: "/events",
                headers: {
                    authorization:token
                    },
                body: body,
            };
            cy.request(injectionObject).then((res) => {
                expect(res.body.message).to.equal("WORKING_HOURS_CREATED_SUCCESSFULLY");
            });
        });
    });
});
