// event on every month on 10 with 2 occurance
import dayjs from 'dayjs'; 
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);
Cypress.dayjs = dayjs
describe("Testing for Event Model", () => {
    it("Initial  Event Test", () => {
        var token =Cypress.env("token")
        cy.request("/").then((res) => {
            expect(res.status).to.equal(200);
            let body = {
                            "title": "Event Every Month on 10",
                            "groupId": 1,
                            "startDate": Cypress.dayjs.utc(new Date()).add(1,'M'),
                            "timings": [{"start":Cypress.dayjs.utc(new Date()),"end":Cypress.dayjs.utc(new Date()).add(1,'hour')}],
                            "description": "Events Occurs On Every Month On 10 with 2 occurance",
                            "link": "Link",
                            "duration":"20",
                            "type":3,
                            "day":[10],
                            "frequency":[1],
                            "allDay":0,
                            "remark": "Remark",
                            "occurance":2
            };
            let injectionObject = {
                method: "POST",
                url: "/events",
                headers: {
                    authorization:token
                    },
                body: body
            }
            cy.request(injectionObject).then((res) => {
                expect(res.body.message).to.equal("WORKING_HOURS_CREATED_SUCCESSFULLY");
            });
        });
    });
});
