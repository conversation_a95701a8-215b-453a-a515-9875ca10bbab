'use strict';

module.exports = {
  async up (queryInterface, Sequelize) {
    return queryInterface.bulkInsert('soul_writing_category',[
      {id: 1,   name:'Reason',          created_at: new Date(),    status: 1,    updated_at: new Date()},
      {id: 2,   name:'Project',         created_at: new Date(),    status: 1,    updated_at: new Date()},
      {id: 3,   name:'Occasion',        created_at: new Date(),    status: 1,    updated_at: new Date()},
      {id: 4,   name:'<PERSON><PERSON>',         created_at: new Date(),    status: 1,    updated_at: new Date()},
      {id: 5,   name:'Pain Picture',    created_at: new Date(),    status: 1,    updated_at: new Date()},
      {id: 6,   name:'Protagonist',     created_at: new Date(),    status: 1,    updated_at: new Date()},
      {id: 7,   name:'Bridge',          created_at: new Date(),    status: 1,    updated_at: new Date()},
      {id: 8,   name:'<PERSON><PERSON>',         created_at: new Date(),    status: 1,    updated_at: new Date()},
      {id: 9,   name:'Rewrite',         created_at: new Date(),    status: 1,    updated_at: new Date()},
      {id: 10,  name:'Affirmation',     created_at: new Date(),    status: 1,    updated_at: new Date()},
      {id: 11,  name:'Projection',      created_at: new Date(),    status: 1,    updated_at: new Date()},
      {id: 12,  name:'Implementation',  created_at: new Date(),    status: 1,    updated_at: new Date()}
    ])
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  }
};
