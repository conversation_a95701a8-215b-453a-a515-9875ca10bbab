const characterController = require('../controllers/characterController');
module.exports = [
        {
        method : "POST",
        path : "/soul-writing/character",
        handler : characterController.createCharacter,
        options: {
            tags: ["api", "Soul Writing Character"],
            notes: "Endpoint to post new Character",
            description: "Add Character",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: { 
                    projectId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}), 
                    age: Joi.string().example("age").required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}), 
                    lifeStatus: Joi.number().integer().example(19).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                    passAwayDate: Joi.string().optional().default(null).allow(null), 
                    title: Joi.string().example('pane name').required().error(errors=>{return Common.routeError(errors,'TITLE_IS_REQUIRED')}),                      
                    name: Joi.string().example('pane name').required().error(errors=>{return Common.routeError(errors,'NAME_IS_REQUIRED')}),                      
                    degreeOfKinship: Joi.string().example('pane name (optional)').optional().default(null),                                            
                    Acronym: Joi.string().example('pane name (optional)').optional().default(null),
                    contact: Joi.string().example('pane name (optional)').optional().default(null),
                    noOfMeetings: Joi.string().example('pane name (optional)').optional().default(null),
                    sympethetic: Joi.string().example('pane name (optional)').optional().default(null),
                    distance: Joi.string().example('pane name (optional)').optional().default(null),
                    color: Joi.object().keys({
                        backgroundColor: Joi.string().example('pane name (optional)').optional().default(null),
                        fontColor: Joi.string().example('pane name (optional)').optional().default(null)
                    }).optional().default(null),
                    protogonistObject: Joi.object().optional().default(null),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/character",
        handler : characterController.listCharacter,
        options: {
            tags: ["api", "Soul Writing Character"],
            notes: "Endpoint to get all Character",
            description: "Get all Character",
            auth: {strategy: 'jwt', mode: "optional"},
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                query: {   
                    projectId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}), 
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/character/kinship",
        handler : characterController.getDegreeOfKinships,
        options: {
            tags: ["api", "Soul Writing Character"],
            notes: "Endpoint to get all Character",
            description: "Get all Character",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                query: {
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "PATCH",
        path : "/soul-writing/character",
        handler : characterController.updateCharacter,
        options: {
            tags: ["api", "Soul Writing Character"],
            notes: "Endpoint to update existing Character",
            description: "Update Character",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {       
                    id: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),  
                    projectId: Joi.number().integer().example(1).optional().default(null), 
                    age: Joi.string().example("age").optional().default(null), 
                    lifeStatus: Joi.number().integer().example(19).optional().default(null),
                    passAwayDate: Joi.string().optional().allow(null).default(null),
                    title: Joi.string().example('pane name').optional().default(null),                      
                    name: Joi.string().example('pane name').optional().default(null),                      
                    degreeOfKinship: Joi.string().example('pane name (optional)').optional().default(null),                   
                    Acronym: Joi.string().example('pane name (optional)').optional().default(null),
                    distance: Joi.string().example('pane name (optional)').optional().default(null),
                    contact: Joi.string().example('pane name (optional)').optional().default(null),
                    noOfMeetings: Joi.string().example('pane name (optional)').optional().default(null),
                    sympethetic: Joi.string().example('pane name (optional)').optional().default(null),
                    distance: Joi.string().example('pane name (optional)').optional().default(null),
                    color: Joi.object().keys({
                        backgroundColor: Joi.string().example('pane name (optional)').optional().default(null),
                        fontColor: Joi.string().example('pane name (optional)').optional().default(null)
                    }).optional().default(null),
                    protogonistObject: Joi.object().optional().default(null),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/character/{id}",
        handler : characterController.characterDetails,
        options: {
            tags: ["api", "Soul Writing Character"],
            notes: "Endpoint to get Character by id",
            description: "Get Character By Id",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {                        
                    id : Joi.string().example(1).required().error(errors=>{return Common.routeError(errors,'Character_ID_IS_REQUIRED')})
                },
                query: {
                    email : Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/character/questions",
        handler : characterController.questions,
        options: {
            tags: ["api", "Soul Writing Character"],
            notes: "Endpoint to get all questions for Character",
            description: "Get all questions for Character",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "DELETE",
        path : "/soul-writing/character",
        handler : characterController.deleteCharacter,
        options: {
            tags: ["api", "Soul Writing Character"],
            notes: "Endpoint to delete Character",
            description: "Delete Character",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                query: {                        
                    id : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'CHARACTER_ID_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },

]