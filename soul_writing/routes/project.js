const projectController = require('../controllers/projectController');
module.exports = [
        {
        method : "POST",
        path : "/soul-writing/project",
        handler : projectController.createProject,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to post new projects",
            description: "Add Project",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    title: Joi.string().example('pane name').required().error(errors=>{return Common.routeError(errors,'NAME_IS_REQUIRED')}),                      
                    description: Joi.string().example('pane name').optional().default("Dummy Description"),                      
                    reason: Joi.string().example('pane name').required().error(errors=>{return Common.routeError(errors,'REASON_IS_REQUIRED')}),                     
                    companionId: Joi.number().integer().example('1 (optional)').optional().default(null),  
                    // projectListId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PROJECT_LIST_ID_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/project/customer",
        handler : projectController.listProjectsForCustomerHistory,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to get all Project",
            description: "Get all Project",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers(false)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                query: {   
                    userId: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'USER_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/project",
        handler : projectController.listProjects,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to get all Project",
            description: "Get all Project",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                query: {   
                    companionStatus: Joi.number().integer().example(1).optional().default(null),
                    limit: Joi.number().integer().optional().default(null),
					pageNumber : Joi.number().integer().min(1).optional().default(null),
					historyDays : Joi.number().integer().min(1).optional().default(null),
					orderByValue: Joi.string().allow('ASC', 'DESC').optional().default('DESC'),
					orderByParameter: Joi.string().allow('createdAt','id','updatedAt').optional().default('updatedAt'),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "PATCH",
        path : "/soul-writing/project",
        handler : projectController.updateProject,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to update existing project",
            description: "Update Project",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    title: Joi.string().example('pane name').optional().default(null),                     
                    description: Joi.string().example('pane name').optional().default(null),                     
                    reason: Joi.string().example('pane name').optional().default(null),                     
                    id: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),  
                    companionId: Joi.number().integer().example('1 (optional)').optional().default(null), 
                    projectMeta: Joi.object().keys().optional().default(null), 
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/project/{id}",
        handler : projectController.projectById,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to get project by id",
            description: "Get project By Id",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {                        
                    id : Joi.string().example(1).required().error(errors=>{return Common.routeError(errors,'project_ID_IS_REQUIRED')})
                },
                query: {                        
                    version : Joi.number().example(1).optional().default(null)
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/project-pdf/{id}",
        handler : projectController.projectByIdForPdf,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to get project by id",
            description: "Get project By Id",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {                        
                    id : Joi.number().example(1).required().error(errors=>{return Common.routeError(errors,'project_ID_IS_REQUIRED')})
                },
                query: {                     
                    version : Joi.number().example(1).optional().default(null)
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "DELETE",
        path : "/soul-writing/project",
        handler : projectController.deleteProject,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to delete project",
            description: "Delete project",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {                        
                    id : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'project_ID_IS_REQUIRED')})
                },
                query: {
                    email : Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },

    {
        method : "POST",
        path : "/soul-writing/project-list",
        handler : projectController.createUserProjects,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to post new project list",
            description: "Add Project List",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    projectList: Joi.array().items(Joi.string()).min(1).required().error(errors=>{return Common.routeError(errors,'PROJECT_LIST_ARRAY_REQUIRED')}), 
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/project-list",
        handler : projectController.listUserProjects,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to post new project list",
            description: "Add Project List",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                query: {
                  
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/companion-dashboard-count",
        handler :projectController.companionDashboardCount,
        options: {
          tags: ["api", "Soul Writing Project"],
          notes: "Get Event user contacted count Api",
          description: "Event List",
          auth: {strategy: 'jwt'},
          validate: {
            headers: Joi.object(Common.headers(true)).options({
              allowUnknown: true
            }),
            query:{},
            options: {
              abortEarly: false
            },
            failAction: async (req, h, err) => {
              return Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      },
    {
        method : "POST",
        path : "/project/offline-check",
        handler :projectController.offlineCheck,
        options: {
          tags: ["api", "Soul Writing Project"],
          notes: "To check for offline version",
          description: "Offline Check",
          auth: {strategy: 'jwt'},
          validate: {
            headers: Joi.object(Common.headers(true)).options({
              allowUnknown: true
            }),
            payload:{
                version: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'VERSION_IS_REQUIRED')}),
                projectId: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                lastUpdatedAt: Joi.date().required().error(errors=>{return Common.routeError(errors,'DATE_IS_REQUIRED')})
            },
            options: {
              abortEarly: false
            },
            failAction: async (req, h, err) => {
              return Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      },
      {
        method : "GET",
        path : "/project/user-summary",
        handler : projectController.getSoulwritingSummary,
        options : {
          tags: ["api", "Summary"],
          notes: "GET Soulwriting summary",
          description: "GET Soulwriting summary",
          auth : {strategy: "jwt", scope : ["admin",'companion','student']},
          validate: {
            headers: Joi.object(Common.headers(true)).options({
              allowUnknown: true
            }),
            options : {
              abortEarly: false
            },
            query : {
              userId:Joi.number().integer().required(),
            },
            failAction: async(req,h, err) => {
              return  Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      },
      {
        method : "GET",
        path : "/project/legacy",
        handler : projectController.legacySoulwritings,
        options : {
          tags: ["api", "Soul Writing Project"],
          notes: "GET Event summary from legacy list",
          description: "GET Event summary from legacy list",
          auth : {strategy: "jwt"},
          validate: {
            headers: Joi.object(Common.headers(true)).options({
              allowUnknown: true
            }),
            options : {
              abortEarly: false
            },
            query : {
              limit: Joi.number().integer().optional().default(20),
              pageNumber: Joi.number().integer().min(1).optional().default(1),
              userId:Joi.number().integer().optional().allow(null).default(null),
              id:Joi.number().integer().optional().allow(null).default(null),
            },
            failAction: async(req,h, err) => {
              return  Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      }
]