const commentController = require('../controllers/CommentController');
module.exports = [
        {
        method : "POST",
        path : "/soul-writing/comment",
        handler : commentController.createComment,
        options: {
            tags: ["api", "Soul Writing Comment"],
            notes: "Endpoint to post new Comment",
            description: "Add Comment",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {
                    lineUuid: Joi.string().uuid().example("8c314e52-e602-483d-9a8f-4d4b3c8d8422").required().error(errors=>{return Common.routeError(errors,'LINE_UUID_IS_REQUIRED')}), 
                    comment: Joi.string().example('comment text').required().error(errors=>{return Common.routeError(errors,'COMMENT_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/comment",
        handler : commentController.listComments,
        options: {
            tags: ["api", "Soul Writing Comment"],
            notes: "Endpoint to get all Comment",
            description: "Get all Comment",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                query: {   
                    projectId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}), 
                    limit: Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt','id').optional().default('id')
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "PATCH",
        path : "/soul-writing/comment",
        handler : commentController.updateComment,
        options: {
            tags: ["api", "Soul Writing Comment"],
            notes: "Endpoint to update existing Comment",
            description: "Update Comment",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {       
                    id: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
                    comment: Joi.string().example('comment text').required().error(errors=>{return Common.routeError(errors,'COMMENT_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "DELETE",
        path : "/soul-writing/comment",
        handler : commentController.deleteComment,
        options: {
            tags: ["api", "Soul Writing Comment"],
            notes: "Endpoint to delete Comment",
            description: "Delete Comment",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {                        
                    id : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'COMMENT_ID_IS_REQUIRED')})
                },
                query: {
                    email : Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "PATCH",
        path : "/soul-writing/meta-comments",
        handler : commentController.metaComments,
        options: {
            tags: ["api", "Soul Writing Comment"],
            notes: "Endpoint to delete Comment",
            description: "Delete Comment",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {                        
                    projectId : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                    comments : Joi.any()
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },

]