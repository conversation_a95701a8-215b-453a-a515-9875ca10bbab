const categoryController = require('../controllers/categoryController');
module.exports = [
        {
        method : "POST",
        path : "/soul-writing/category",
        handler : categoryController.postCategory,
        options: {
            tags: ["api", "Soul Writing Category"],
            notes: "Endpoint to post new category",
            description: "Add Category",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    name: Joi.string().example('pane name').required().error(errors=>{return Common.routeError(errors,'NAME_IS_REQUIRED')}),                      
                    description: Joi.string().example('pane name').optional().allow(null).default(null),                      
                    type: Joi.number().integer().example('1 (optional)').optional().default(null),  
                    videoLink: Joi.string().example("video link (optional)").optional().default(null),
                    languageCode: Joi.string().example("en").required().error(errors=>{return Common.routeError(errors,'LANGUAGE_CODE_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/category",
        handler : categoryController.listCategory,
        options: {
            tags: ["api", "Soul Writing Category"],
            notes: "Endpoint to get all category",
            description: "Get all Category",
            auth:false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                query: {   

                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "PATCH",
        path : "/soul-writing/category",
        handler : categoryController.updateCategory,
        options: {
            tags: ["api", "Soul Writing Category"],
            notes: "Endpoint to update existing category",
            description: "Update Category",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    name: Joi.string().example('pane name').allow(null).optional().default(null),       
                    description: Joi.string().example('pane name').optional().allow(null).default(null),              
                    id: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),  
                    videoLink: Joi.string().example("video link (optional)").allow(null).optional().default(null),
                    // languageCode: Joi.string().example("en").required().error(errors=>{return Common.routeError(errors,'LANGUAGE_CODE_IS_REQUIRED')}) 
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/category/{id}",
        handler : categoryController.categoryDetails,
        options: {
            tags: ["api", "Soul Writing Category"],
            notes: "Endpoint to get category by id",
            description: "Get Category By Id",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {                        
                    id : Joi.string().example(1).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "DELETE",
        path : "/soul-writing/category",
        handler : categoryController.deleteCategory,
        options: {
            tags: ["api", "Soul Writing Category"],
            notes: "Endpoint to delete category",
            description: "Delete Category",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {                        
                    id : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')})
                },
                query: {
                    email : Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },

]