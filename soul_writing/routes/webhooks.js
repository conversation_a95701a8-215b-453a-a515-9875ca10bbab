const webhookController = require("../controllers/webhooksController")

module.exports = [
  {
    method: "POST",
    path: "/webhook/user",
    options: {
      handler: webhookController.upsertUser,
      description: "Add and update User",
      notes: "Add and update User",
      tags: ["api", "Webhook"],
      auth: false,
      validate: {
        headers: Joi.object(Common.headers()).options({
          allowUnknown: true
        }),
        payload: {
          userId: Joi.number().integer().optional().allow(null, "undefined"),
          firstName: Joi.string().trim().optional().allow(null, "undefined"),
          lastName: Joi.string().trim().optional().allow(null, "undefined"),
          title: Joi.string().trim().optional().allow(null, "undefined"),
          profilePhotoUrl: Joi.string().trim().optional().allow(null, "undefined"),
          profilePhotoId: Joi.number().integer().optional().allow(null, "undefined"),
          languages: Joi.array().optional().allow(null, "undefined"),
          meetingPrice: Joi.string().optional().allow(null, "undefined"),
          reason: Joi.string().optional().allow(null, "undefined"),
          rating: Joi.string().optional().allow(null, "undefined"),
          scheduleTime: Joi.number().optional().allow(null, "undefined"),
          reScheduleTime: Joi.number().optional().allow(null, "undefined"),
          cancelTime: Joi.number().optional().allow(null, "undefined"),
          roles: Joi.array().optional().allow(null, "undefined"),
          gender: Joi.number().optional().allow(null, "undefined"),
          email: Joi.string().email().optional().allow(null, "undefined"),
          vita: Joi.string().optional().allow(null, "undefined"),
          experience: Joi.string().optional().allow(null, "undefined"),
          meetingProductId: Joi.string().optional(),
						soulwritingProductId: Joi.string().optional(),
            userObject: Joi.object().optional().allow(null)
        },
        validator: Joi
      }
    }
  },
  {
    method : "POST",
    path : "/settings",
    handler :webhookController.createSettings,
    options: {
        tags: ["api", "Admin"],
        notes: "Endpoint to add User By Admin",
        description: "Add User By Admin",
        auth:false,
        validate: {
            headers: Joi.object(Common.headers()).options({
                allowUnknown: true
            }),
            options: {
                abortEarly: false
            },
            payload:{
                    data:Joi.array().example([{'key':'key','value':'value'}]).items(
                        Joi.object().example({'key':'key','value':'value'})
                    ).min(1).optional().default(null),
            },
            failAction: async (req, h, err) => {
                return Common.FailureError(err, req);
            },
            validator: Joi
        },
        pre : [{method: Common.prefunction}]
    }
}
];