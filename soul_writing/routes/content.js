const contentController = require('../controllers/contentController');
module.exports = [
    {
        method : "GET",
        path : "/soul-writing/content/{projectId}",
        handler : contentController.getContent,
        options: {
            tags: ["api", "Soul Writing Content"],
            notes: "Endpoint to get all lines",
            description: "Get all lines",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {   
                    projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')})
                },
                query: {   
                    version: Joi.number().integer().example(10).default(null)
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: <PERSON><PERSON>
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/content-pdf/{projectId}",
        handler : contentController.getContentforPdf,
        options: {
            tags: ["api", "Soul Writing Content"],
            notes: "Endpoint to get all lines",
            description: "Get all lines",
            auth:false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {   
                    projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')})
                },
                query: {   
                    version: Joi.number().integer().example(10).default(null)
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/content-word/{projectId}",
        handler : contentController.getContentforWord,
        options: {
            tags: ["api", "Soul Writing Content"],
            notes: "Endpoint to get all lines",
            description: "Get all lines",
            auth:false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {   
                    projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')})
                },
                query: {   
                    version: Joi.number().integer().example(10).default(null)
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/soul-writing/word-estimation",
        handler : contentController.wordCountEstimation,
        options: {
            tags: ["api", "Soul Writing Content"],
            notes: "Endpoint to create new lines",
            description: "Add line",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                    projectMeta: Joi.object().keys().optional().default({}),
                    isSaveAsDraft: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'SAVE_AS_DRAFT_IS_REQUIRED')}),
                    companionId:  Joi.number().integer().example('1 (optional)').optional().default(null),
                    activeStage: Joi.number().integer().example("3").optional().default(null),
                    contents: Joi.array().items({
                        categoryId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')}),
                        characterId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CHARACTER_ID_IS_REQUIRED')}),
                        lineNumber: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'LINE_NUMBER_IS_REQUIRED')}),
                        painpictureCollapse: Joi.number().integer().valid(0,1).example(1).optional().default(0),
                        content: Joi.string().example("line content").allow("").optional().default(""),
                        isRedLine: Joi.number().integer().valid(0, 1).optional().default(0),
                        highlightedText: Joi.array().items().allow(null).optional().default([]),
                        uuid: Joi.string().uuid().optional().allow(null).default(null)
                    }).optional().default([])
                    // .min(1).required().error(errors=>{return Common.routeError(errors,'CONTENTS_REQUIRED')}),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/soul-writing/word-estimation-test",
        handler : contentController.wordCountEstimationTest,
        options: {
            tags: ["api", "Soul Writing Content"],
            notes: "Endpoint to create new lines",
            description: "Add line",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                    projectMeta: Joi.object().keys().optional().default({}),
                    isSaveAsDraft: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'SAVE_AS_DRAFT_IS_REQUIRED')}),
                    companionId:  Joi.number().integer().example('1 (optional)').optional().default(null),
                    activeStage: Joi.number().integer().example("3").optional().default(null),
                    contents: Joi.array().items({
                        categoryId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')}),
                        characterId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CHARACTER_ID_IS_REQUIRED')}),
                        lineNumber: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'LINE_NUMBER_IS_REQUIRED')}),
                        painpictureCollapse: Joi.number().integer().valid(0,1).example(1).optional().default(0),
                        content: Joi.string().example("line content").allow("").optional().default(""),
                        isRedLine: Joi.number().integer().valid(0, 1).optional().default(0),
                        highlightedText: Joi.array().items().allow(null).optional().default([]),
                        uuid: Joi.string().uuid().optional().allow(null).default(null)
                    }).optional().default([])
                    // .min(1).required().error(errors=>{return Common.routeError(errors,'CONTENTS_REQUIRED')}),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/soul-writing/companion-submit",
        handler : contentController.companionSubmit,
        options: {
            tags: ["api", "Soul Writing Content"],
            notes: "Endpoint to submit comments",
            description: "Add Comments",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                    isSubmitToUser: Joi.number().integer().example(0).optional().default(0),
                    contents: Joi.array().items(Joi.object().keys({
                        id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'LINE_ID_IS_REQUIRED')}),
                        content: Joi.string().example("line content").allow("").optional().default(""),
                        // content:Joi.string().required().error(errors=>{return Common.routeError(errors,'CONTENT_IS_REQUIRED')})
                    })).min(1).required().error(errors=>{return Common.routeError(errors,'CONTENTS_ARE_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/soul-writing/companion-submit-content",
        handler : contentController.companionSubmitContent,
        options: {
            tags: ["api", "Soul Writing Content"],
            notes: "Endpoint to submit comments",
            description: "Add Comments",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                    isSubmitToUser: Joi.number().integer().example(0).optional().default(0),
                    contents: Joi.array().items({
                        categoryId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')}),
                        characterId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CHARACTER_ID_IS_REQUIRED')}),
                        lineNumber: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'LINE_NUMBER_IS_REQUIRED')}),
                        painpictureCollapse: Joi.number().integer().valid(0,1).example(1).optional().default(0),
                        content: Joi.string().example("line content").allow("").optional().default(""),
                        isRedLine: Joi.number().integer().valid(0, 1).optional().default(0),
                        highlightedText: Joi.array().items().allow(null).optional().default([]),
                        uuid: Joi.string().uuid().optional().allow(null).default(null),
                        id: Joi.number().optional().allow(null).default(null),
                        comment: Joi.string().optional().allow(null, "").default(null)
                    }).optional().default([])
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/soul-writing/companion-submit-content-with-comments",
        handler : contentController.companionSubmitContentWithCommentMeta,
        options: {
            tags: ["api", "Soul Writing Content"],
            notes: "Endpoint to submit comments",
            description: "Add Comments",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                    isSubmitToUser: Joi.number().integer().example(0).optional().default(0),
                    contents: Joi.array().items({
                        categoryId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')}),
                        characterId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CHARACTER_ID_IS_REQUIRED')}),
                        lineNumber: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'LINE_NUMBER_IS_REQUIRED')}),
                        painpictureCollapse: Joi.number().integer().valid(0,1).example(1).optional().default(0),
                        content: Joi.string().example("line content").allow("").optional().default(""),
                        isRedLine: Joi.number().integer().valid(0, 1).optional().default(0),
                        highlightedText: Joi.array().items().allow(null).optional().default([]),
                        uuid: Joi.string().uuid().optional().allow(null).default(null),
                        id: Joi.number().optional().allow(null).default(null),
                        comment: Joi.string().optional().allow(null, "").default(null)
                    }).optional().default([]),
                    comments: Joi.object().optional().default(null).allow(null)
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
		method : "POST",
		path : "/order-soul-writing",
		handler : contentController.confirmPayment,
		options : {
			tags: ["api", "Cart-Order"],
			notes: "Add Cart-Order",
			description: "Add Cart-Order",
			auth : false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options : {
					abortEarly: false
				},
				payload : {
                  data:Joi.string().required().error(errors=>{return Common.routeError(errors,'DATA_IS_REQUIRED')})
				},
				failAction: async(req,h, err) => {
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "GET",
		path : "/order-soul-writing",
		handler : contentController.getOrders,
		options : {
			tags: ["api", "Order List"],
			notes: "GET Cart-Order",
			description: "GET Cart-Order",
			auth : {strategy: "jwt", scope : ["admin","costumer",'companion','student','order_management']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly: false
				},
				query : {
                    limit: Joi.number().integer().optional().default(null),
					userId:Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id'),
                    // categoryId: Joi.string().allow(null).optional().default(null),
					startDate:Joi.date().example('2022-07-05T04:00:00Z').optional().default(null),
					endDate:Joi.date().example('2022-07-05T04:00:00Z').optional().default(null)
				},
				failAction: async(req,h, err) => {
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
        method : "GET",
        path : "/order-soul-writing-byid",
        handler : contentController.getOrderById,
        options : {
          tags: ["api", "Cart-Order"],
          notes: "GET Cart-Order",
          description: "GET Cart-Order",
          auth : {strategy: "jwt",scope : ["admin","costumer",'companion','student','order_management']},
          validate: {
            headers: Joi.object(Common.headers(true)).options({
              allowUnknown: true
            }),
            options : {
              abortEarly: false
            },
            query : {
              orderId:Joi.number().integer().required(),
            },
            failAction: async(req,h, err) => {
              return  Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
    },
    {
		method : "GET",
		path : "/payment-history",
		handler : contentController.paymentHistory,
		options: {
			tags: ["api", "Payment History"],
			notes: "Endpoint to list payment history",
			description:"List payment history",
			auth: {strategy: 'jwt'},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					limit: Joi.number().integer().optional().default(null),
					pageNumber : Joi.number().integer().min(1).optional().default(null),
					orderByValue: Joi.string().allow('ASC', 'DESC').optional().default('DESC'),
					orderByParameter: Joi.string().allow('createdAt','id').optional().default('id'),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
        method : "POST",
        path : "/soul-writing/test",
        handler :contentController.test,
        options: {
          tags: ["api", "Event"],
          notes: "Completed Meetings",
          description: "Completed Meetings",
          auth: false,
          validate: {
            headers: Joi.object(Common.headers()).options({
              allowUnknown: true
            }),
            payload:{
              companions: Joi.array().required()     
            },
            options: {
              abortEarly: false
            },
            failAction: async (req, h, err) => {
              return Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      },
]