var CryptoJS = require("crypto-js");
var runner = require("child_process").execSync;
// const  phpScriptPath = '/mnt/kuby/nodejs/kuby/kuby_backend/product_management/digistore24.php';
const  phpScriptPath = process.env.DIGISTORE_SCRIPT_PATH;

const key = process.env.DECRYPT_KEY
//const key = 'FFR5yY7oLYSM9tzMLJM6';

module.exports={
    decodeData:async(queryString)=>{
        //var queryString = 'order_id=S4JZA59D&order_item_id=ds242e909a8cae9f3ccd2c2d85fc6be7760d-Y1FPVUYwMmFTeE55YTJFL09JZjBudz09&order_language=ds249d29d7a4cbc0712322005cd49eaff458-Y0V2Z3h5UVhJeUxWaTUvMFlneDVDUT09&buyer_email=ds2427572cf878414da94a047f0dd35c31cb-eTAxaXJpaHN0a1p2RG9UcFBZenR5MDM3R3pKVlJRaGQwellqdHZjVSs1Zz0_e&payplan_id=ds2488791e43a82a7e437c458893dd4c478b-UFVEQkRRbG4wT1pkZDBUSDRkSlVsQT09&product_id=ds24890bedaa1ab7a7561a93dfcba3ed7df9-cjdIWTduUFhib2RkbXB3YWtESUkrUT09&product_name=ds24dfcb9c61428d9f474ea0cfcb522f3517-U3VyVVN0Um1qTEk2dTg5dnN4cWxoQT09&quantity=ds24785677e100c687ec3328dfc6be68f14f-a0JmWUFneVZlV1Y0ajVQZ1dzWS9oZz09&country=ds2471103e74e3e0f12ffc36399ca8fef4bd-WjFjeXEvb3ZrUC9oY0tnSmUweCtFdz09&buyer_language=ds24672cd6a88636621086b0a04fd77ae797-OVU0empPbHV2TUMzUkZqRnhYcmI5Zz09&buyer_id=ds24848b89d2097a46cc6805e46235020ce0-RlJmTlFGSTRvUWhlcnZnMlJLcThadz09&buyer_first_name=ds241d628e9cc02ca6183e39db4a2dd7bbe2-ZDgvZXp2M3ZTZjhjUys2bHdSQ0UzUT09&buyer_last_name=ds2464e51e413996f06164b5d38091a7cd8d-VVdlUkpxalhnOVNsYk5LUmJzRzZkZz09&billing_status=ds24feeed305197129b0ab770e46101d8103-MlpNaVB2UlYwd1pRUFlsaUsvUVBIQT09&tags=ds24590ce14a9cd71f63ae98b8c1b0ec4ec0-OHE2UXdMYzJJVWpxT1pQM3dDNlhiQT09&currency=ds2425f5eb8af3602571d2b141a28065de40-U2tqK1VJcWkrTUI3c28xL21aUi9iUT09&amount=ds246834c6fd45d2f835a2e0ac04896c47f5-Wmd1eVNLa3NzVHlpU1BMc1RNc1FkUT09&vat_amount=ds24af414969ef3c17ded6ca530306723511-czFWVVFwOENmY0RUNERtOGdSa3Irdz09&net_amount=ds242f253d53c6bb585c0db0ead3c1292c0a-WnJMZVNOdFM0U2wzelNWdFlFN3g3UT09&earned_amount=ds2454feb41acca88c797a874695e0b58070-a2dJQXM1QjlwVitYSjdmbmFCeXBwQT09&sha_sign=2988E1DDB245EF17D4C5D0A613D9CEA86066C4F5494DE2095B7E528472A0DD12A974FC39F5C030D2E799A9A3DE6A3C8B423859A71010802F2321AF6DB6EC3BC5';
        let argsString = queryString + ' | ' + key;
        const encodedWord = CryptoJS.enc.Utf8.parse(argsString);
        const encoded = CryptoJS.enc.Base64.stringify(encodedWord);
        
        let phpResponse = runner("php " + phpScriptPath + " " +encoded).toString();
        console.log(JSON.parse(phpResponse))
        return JSON.parse(phpResponse)
    }
}

