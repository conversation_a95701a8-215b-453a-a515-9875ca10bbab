require('dotenv').config(); 
module.exports = {
    development: {
      username: process.env.MYSQL_USERNAME_PRODUCTION,
      password: process.env.MYSQL_PASSWORD_PRODUCTION,
      database: process.env.MYSQL_DATABASE_PRODUCTION,
      host: process.env.MYSQL_HOST_PRODUCTION,
      port: process.env.MYSQL_PORT,
      dialect: 'mysql',
      seederStorage : "sequelize",
      seederStorageTableName : "sequelize_data"
    },
    test: {
        username: process.env.MYSQL_USERNAME_PRODUCTION,
        password: process.env.MYSQL_PASSWORD_PRODUCTION,
        database: process.env.TEST_MYSQL_DATABASE_PRODUCTION,
        host: process.env.MYSQL_HOST_PRODUCTION,
        port: process.env.MYSQL_PORT,
        dialect: 'mysql',
        seederStorage : "sequelize",
        seederStorageTableName : "sequelize_data"
    },
    production: {
        username: process.env.MYSQL_USERNAME_PRODUCTION,
        password: process.env.MYSQL_PASSWORD_PRODUCTION,
        database: process.env.TEST_MYSQL_DATABASE_PRODUCTION,
        host: process.env.MYSQL_HOST_PRODUCTION,
        port: process.env.MYSQL_PORT,
        dialect: 'mysql',
        seederStorage : "sequelize",
        seederStorageTableName : "sequelize_data"
    }
  };