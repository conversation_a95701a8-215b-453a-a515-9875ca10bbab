"use strict";
module.exports = (sequelize, DataTypes) => {
    let ProjectVersion = sequelize.define(
      "ProjectVersion",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        version: {type: DataTypes.INTEGER, allowNull: true },
        projectMeta: {type: DataTypes.JSON, alowNull: true},
        projectId: {type: DataTypes.INTEGER, defaultValue: null},
        companionId: {type: DataTypes.INTEGER, defaultValue: null},
        status: {type: DataTypes.INTEGER, defaultValue: 1 },
      },
      {
        paranoid: false,
        underscored: true,
        tableName: "soul_writing_project_version"
      }
    );

    ProjectVersion.associate = function(models) {
      
    }

    return ProjectVersion;
};