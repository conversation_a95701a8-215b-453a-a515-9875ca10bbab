"use strict";
var sequelize = new Sequelize(process.env.NODE_ENV == "test" ? process.env.MYSQL_TEST_DATABASE_NAME : process.env.MYSQL_DATABASE_PRODUCTION, process.env.MYSQL_USERNAME_PRODUCTION, process.env.MYSQL_PASSWORD_PRODUCTION, {
    // logging: false,
    define: {
        charset: "utf8",
        collate: "utf8_general_ci"
    },
    host: process.env.MYSQL_HOST_PRODUCTION,
    dialect: process.env.MYSQL_DIALECT,
    dialectOptions: {
        ssl: {
            rejectUnauthorized: false
        },
        // statement_timeout: 3000,
        // idle_in_transaction_session_timeout: 5000
      
       
    },
    // logging: false,
    port: process.env.MYSQL_PORT,
    operatorsAliases: process.env.OPERATORS_ALIASES,
    pool: {
        max: +process.env.DB_POOL_MAX,
        min: +process.env.DB_POOL_MIN,
        acquire: process.env.DB_POOL_ACQUIRE,
        idle: process.env.DB_POOL_IDLE
    }
});
var db = {};
Fs.readdirSync(__dirname).filter(function(file) {
    return file.indexOf(".") !== 0 && file !== "index.js";
}).forEach(function(file) {
    var model = require(Path.join(__dirname, file))(sequelize, Sequelize.DataTypes)
    db[model.name] = model;
});
Object.keys(db).forEach(function(modelName) {
    if ("associate" in db[modelName]) {
        db[modelName].associate(db);
    }
});
db.sequelize = sequelize;
global.sequelize=sequelize;
module.exports = db;