"use strict";
module.exports = (sequelize, DataTypes) => {
    let CategoryContent = sequelize.define(
      "CategoryContent",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        categoryId: {type: DataTypes.INTEGER, allowNull: false},
        languageCode: {type: DataTypes.STRING, allowNull:false },
        name: {type: DataTypes.STRING, allowNull: false},
        description: {type: DataTypes.TEXT, allowNull: false},
        videoLink: {type: DataTypes.STRING, allowNull: true},
        status: {type: DataTypes.TEXT, defaultValue:1 }
      },
      {
        paranoid: false,
        underscored: true,
        tableName: "soul_writing_category_content"
      }
    );

    // CategoryContent.associate = function(models) {
    //     CategoryContent.belongsTo(models.Category, { foreignKey: "categoryId", as: "mainContent" });
    //     CategoryContent.belongsTo(models.Category, { foreignKey: "categoryId", as: "defaultContent" });
    //     //Category.hasMany(models.CategoryContent, { foreignKey: "categoryId" });
    //   }
    return CategoryContent;
};