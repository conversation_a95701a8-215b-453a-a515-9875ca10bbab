"use strict";
module.exports = (sequelize, DataTypes) => {
    let Invoice = sequelize.define(
      "Invoice",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        projectId           :   {type:DataTypes.INTEGER,defaultValue:null},
        data                :   {type:DataTypes.JSON,defaultValue:null},
        amount              :   {type:DataTypes.STRING,defaultValue:null},
        amountReceived      :   {type:DataTypes.STRING,defaultValue:null},
        isPaid              :   {type:DataTypes.INTEGER,defaultValue:0},
        paymentLink         :   {type:DataTypes.STRING,defaultValue:null},  
        userId              :   {type:DataTypes.INTEGER,defaultValue:null},
        companionId         :   {type:DataTypes.INTEGER,defaultValue:null},
        feedback            :   {type:DataTypes.TEXT,defaultValue:null},
        isFeedback          :   {type:DataTypes.INTEGER,defaultValue:0},
        orderId             :   {type:DataTypes.INTEGER,allowNull: false},
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "soul_writing_invoices"
      }
    );
    Invoice.associate = function(models) {
        // Invoice.belongsTo(models.Event,{foreignKey:'eventId'})
  };
    return Invoice;
};  
