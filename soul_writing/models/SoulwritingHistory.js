"use strict";
module.exports = (sequelize, DataTypes) => {
    let SoulwritingHistory = sequelize.define(
      "SoulwritingHistory",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        userId: {type: DataTypes.INTEGER, allowNull: false},
        companionId: {type: DataTypes.INTEGER, allowNull: true},
        projectDetails: {type: DataTypes.JSON, allowNull: false },
        contentDetails: {type: DataTypes.JSON, allowNull: false }
      },
      {
        underscored: true,
        tableName: "soul_writing_history"
      }
    );

    SoulwritingHistory.associate = function(models) {
      // SoulwritingHistory.belongsTo(models.User, { foreignKey: "userId", as: "user" });
      // SoulwritingHistory.belongsTo(models.User, { foreignKey: "companionId", as: "companion" });
    }

    return SoulwritingHistory;
};