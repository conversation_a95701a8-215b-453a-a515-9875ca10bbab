"use strict";
module.exports = (sequelize, DataTypes) => {
  let User = sequelize.define(
    "User",
    {
      userId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        unique: true
      },
      firstName: { type: DataTypes.STRING, defaultValue: null },
      lastName: { type: DataTypes.STRING, defaultValue: null },
      title: { type: DataTypes.STRING, defaultValue: null },
      profilePhotoUrl: { type: DataTypes.STRING, allowNull: true },
      profilePhotoId: { type: DataTypes.STRING, allowNull: true },
      gender: { type: DataTypes.INTEGER, allowNull: true },
      email: {type: DataTypes.STRING, defaultValue: null},
      totalEarning: {type: DataTypes.FLOAT, defaultValue: 0},
      soulwritingProductId: { type: DataTypes.STRING, defaultValue: null },
      meetingProductId: { type: DataTypes.STRING, defaultValue: null },
      userObject: { type: DataTypes.JSON, allowNull: true, defaultValue: null }
    },
    {
      paranoid: true,
      underscored: true,
      tableName: "soul_writing_users"
    }
  );
  User.associate = function (models) {};
  return User;
};
