"use strict";
module.exports = (sequelize, DataTypes) => {
    let Character = sequelize.define(
      "Character",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        projectId: {type: DataTypes.INTEGER, allowNull: false},
        title: {type: DataTypes.STRING, allowNull: false},
        name: {type: DataTypes.STRING, allowNull: false},
        age: {type: DataTypes.STRING, allowNull: false},
        degreeOfKinship: {type: DataTypes.STRING, defaultValue:null },
        lifeStatus: { type: DataTypes.INTEGER, allowNull: true },
        passAwayDate: { type: DataTypes.STRING, allowNull: true },
        Acronym: { type: DataTypes.STRING, allowNull: true },
        contact: { type: DataTypes.STRING, allow: true },
        noOfMeetings: { type: DataTypes.STRING, allow: true },
        sympethetic: { type: DataTypes.STRING, allow: true },
        color: { type: DataTypes.JSON, allow: true },
        distance: { type: DataTypes.STRING, allow: true },
        protogonistObject: { type: DataTypes.JSON, allow: true },
        createdById: { type: DataTypes.INTEGER, allowNull: false },
        status: {type: DataTypes.TEXT, defaultValue:1 },
        isDefault: { type: DataTypes.INTEGER, defaultValue: 0 }
      },
      {
        paranoid: false,
        underscored: true,
        tableName: "soul_writing_character"
      }
    );

    return Character;
};