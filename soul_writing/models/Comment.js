"use strict";
module.exports = (sequelize, DataTypes) => {
    let Comment = sequelize.define(
      "Comment",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        projectId: {type: DataTypes.INTEGER, allowNull: false},
        comment: {type: DataTypes.TEXT, allowNull: false},
        isRead: {type: DataTypes.INTEGER, defaultValue:0 },
        createdById: { type: DataTypes.INTEGER, allowNull: false },
        versionId: {type: DataTypes.INTEGER, defaultValue: null},
        lineUuid: {type: DataTypes.UUID, defaultValue: null}
      },
      {
        paranoid: false,
        underscored: true,
        tableName: "soul_writing_comments"
      }
    );

   
    Comment.associate = function(models) {
      Comment.belongsTo(models.User, { foreignKey: "createdById", targetKey: "userId", as: "author" });
    }

    return Comment;
};