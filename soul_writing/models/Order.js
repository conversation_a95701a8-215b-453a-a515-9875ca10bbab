"use strict";

//const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let Order = sequelize.define(
      "Order",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        data: { type: DataTypes.JSON, allowNull: false },
        userId: { type: DataTypes.INTEGER, defaultValue:null},
        companionId: { type: DataTypes.INTEGER, defaultValue:null},
        projectId: { type: DataTypes.INTEGER, defaultValue:null},
        amount: { type: DataTypes.STRING, defaultValue:null},
        isPaid: { type: DataTypes.INTEGER, defaultValue:0},
        paymentLink: { type: DataTypes.STRING, defaultValue:null},
        orderDetails:{ type: DataTypes.JSON, allowNull: true },
        custom:{ type: DataTypes.JSON, defaultValue:null}
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "soul_writing_orders"
      }
    );

    Order.associate = (models) => {
      Order.belongsTo(models.User, {  targetKey:"userId",foreignKey: 'userId'});
      Order.belongsTo(models.User, {  targetKey:"userId",foreignKey: 'companionId', as: "companion"});
    };

    return Order;
  };  