"use strict";
module.exports = (sequelize, DataTypes) => {
    let ProjectList = sequelize.define(
      "ProjectList",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        title: {type: DataTypes.STRING, allowNull: false, unique: "user-project"},
        description: {type: DataTypes.TEXT, allowNull: true },
        createdById: {type: DataTypes.INTEGER, allowNull: false, unique: "user-project"},
        isDefault: {type: DataTypes.INTEGER, defaultValue: 0 },
        status: {type: DataTypes.INTEGER, defaultValue: 1 },
      },
      {
        paranoid: false,
        underscored: true,
        tableName: "soul_writing_project_list",
        index : [
          {
            unique: true,
            fields: ["title", "created_by_id"],
            name  : "user-project"
          }
        ]
      }
    );

    ProjectList.associate = function(models) {
    //   Project.belongsTo(models.User, { foreignKey: "companionId", targetKey: "userId", as: "companion" })
    //   Project.belongsTo(models.User, { foreignKey: "createdById", targetKey: "userId", as: "author" })
    }

    return ProjectList;
};