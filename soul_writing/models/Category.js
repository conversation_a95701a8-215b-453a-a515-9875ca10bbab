"use strict";
module.exports = (sequelize, DataTypes) => {
    let Category = sequelize.define(
      "Category",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        //name: {type: DataTypes.STRING, allowNull: false},
        type: {type: DataTypes.INTEGER, defaultValue:null },
        sortOrder: {type: DataTypes.INTEGER, defaultValue: null},
        status: {type: DataTypes.TEXT, defaultValue:1 }
      },
      {
        paranoid: false,
        underscored: true,
        tableName: "soul_writing_category"
      }
    );

    Category.associate = function(models) {
      Category.hasOne(models.CategoryContent, { foreignKey: "categoryId", as: "mainCategory" });
      Category.hasOne(models.CategoryContent, { foreignKey: "categoryId", as: "defaultCategory" });
      //Category.hasMany(models.CategoryContent, { foreignKey: "categoryId" });
    }

    return Category;
};