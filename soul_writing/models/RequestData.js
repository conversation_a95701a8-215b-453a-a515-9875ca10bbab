"use strict";
module.exports = (sequelize, DataTypes) => {
    let RequestData = sequelize.define(
      "RequestData",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        projectId: {type: DataTypes.INTEGER, allowNull: false},
        payload: {type: DataTypes.JSON, allowNull: false },
        userId: {type: DataTypes.INTEGER, allowNull: false }
      },
      {
        underscored: true,
        tableName: "soul_writing_request_data"
      }
    );

    RequestData.associate = function(models) {}

    return RequestData;
};