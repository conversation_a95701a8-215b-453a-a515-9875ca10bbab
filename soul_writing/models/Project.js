"use strict";
module.exports = (sequelize, DataTypes) => {
    let Project = sequelize.define(
      "Project",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        projectListId: {type: DataTypes.INTEGER, allowNull: true},
        title: {type: DataTypes.STRING, allowNull: false},
        description: {type: DataTypes.TEXT, allowNull: true },
        reason: {type: DataTypes.TEXT, allowNull: false },
        createdById: {type: DataTypes.INTEGER, allowNull: false},
        companionId: {type: DataTypes.INTEGER, defaultValue: null},
        projectStatus: {type: DataTypes.INTEGER, defaultValue: 1 },
        companionStatus: {type: DataTypes.INTEGER, defaultValue: null },
        customerStatus: {type: DataTypes.INTEGER, defaultValue: null },
        version: {type: DataTypes.INTEGER, allowNull: true },
        submittedVersion: {type: DataTypes.INTEGER, allowNull: true},
        companionStartVersion: { type: DataTypes.INTEGER, allowNull: true},
        stage: {type: DataTypes.INTEGER, allowNull: true },
        lastSubmittedStage: {type: DataTypes.INTEGER, allowNull: true },
        characters: {type: DataTypes.INTEGER, allowNull: true },
        submittedCharacters: {type: DataTypes.INTEGER, allowNull: true },
        recording: {type: DataTypes.STRING, allowNull: true},
        paymentLink: {type: DataTypes.TEXT, allowNull: true},
        projectMeta: {type: DataTypes.JSON, alowNull: true},
        status: {type: DataTypes.INTEGER, defaultValue: 1 },
        wordCount: {type: DataTypes.INTEGER, defaultValue: 0},
        paymentAmount: {type: DataTypes.FLOAT, defaultValue: 0 },
        isReviewed: {type: DataTypes.INTEGER, defaultValue: 0},
        projectId: {type: DataTypes.INTEGER, defaultValue: null},
        comments: {type: DataTypes.JSON, defaultValue: null}
      },
      {
        paranoid: false,
        underscored: true,
        tableName: "soul_writing_project"
      }
    );

    Project.associate = function(models) {
      Project.belongsTo(models.User, { foreignKey: "companionId", targetKey: "userId", as: "companion" })
      Project.belongsTo(models.User, { foreignKey: "createdById", targetKey: "userId", as: "author" })
    }

    return Project;
};