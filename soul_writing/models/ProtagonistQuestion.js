"use strict";
module.exports = (sequelize, DataTypes) => {
    let ProtagonistQuestion = sequelize.define(
      "ProtagonistQuestion",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        languageCode: {type: DataTypes.STRING, allowNull:false },
        questions: { type: DataTypes.JSON, allowNull: false },
        status: {type: DataTypes.TEXT, defaultValue:1 }
      },
      {
        paranoid: false,
        underscored: true,
        tableName: "soul_writing_protagonist_questions"
      }
    );

    return ProtagonistQuestion;
};