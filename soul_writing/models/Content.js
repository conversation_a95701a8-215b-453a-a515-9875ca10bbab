"use strict";
module.exports = (sequelize, DataTypes) => {
    let Content = sequelize.define(
      "Content",
      {
        id: {
          type: DataTypes.BIGINT,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        uuid: {type: DataTypes.UUID, defaultValue: null},
        projectId: {type: DataTypes.INTEGER, allowNull: false},
        characterId: {type: DataTypes.INTEGER, allowNull: false},
        categoryId: {type: DataTypes.INTEGER, allowNull: false},
        createdById: {type: DataTypes.INTEGER, allowNull: false},
        lineNumber: {type: DataTypes.INTEGER, allowNull: false},
        content: {type: DataTypes.TEXT, allowNull: false},
        companionContent: {type: DataTypes.TEXT, defaultValue: null},
        plainContent: {type: DataTypes.TEXT, allowNull: false},
        isReviewed: {type: DataTypes.INTEGER, defaultValue: 0},
        versionId: {type: DataTypes.INTEGER, defaultValue: 1},
        isRedLine: {type: DataTypes.INTEGER, defaultValue: 0},
        diffContent: {type: DataTypes.TEXT, defaultValue: null},
        status: {type: DataTypes.INTEGER, defaultValue:1},
        highlightedText: {type: DataTypes.JSON, defaultValue: null},
        painpictureCollapse: {type: DataTypes.INTEGER, defaultValue: 0},
        isHighlighted: {type: DataTypes.INTEGER, defaultValue:0},
        addedByCompanion: { type: DataTypes.INTEGER, defaultValue: 0 },
        updatedByCompanion: { type: DataTypes.INTEGER, defaultValue: 0 }
      },
      {
        paranoid: false,
        underscored: true,
        tableName: "soul_writing_content"
      }
    );

    Content.associate = function(models) {
      Content.belongsTo(models.Character, { foreignKey: "characterId", as: "character" })
    }

    return Content;
};

// "use strict";
// module.exports = (sequelize, DataTypes) => {
//     let Content = sequelize.define(
//       "Content",
//       {
//         id: {
//           type: DataTypes.INTEGER,
//           primaryKey: true,
//           autoIncrement: true,
//           allowNull: false
//         },
//         projectId: {type: DataTypes.INTEGER, allowNull: false},
//         characterId: {type: DataTypes.INTEGER, allowNull: false},
//         categoryId: {type: DataTypes.INTEGER, allowNull: false},
//         createdById: {type: DataTypes.INTEGER, allowNull: false},
//         lineNumber: {type: DataTypes.INTEGER, allowNull: false},
//         content: {type: DataTypes.TEXT, allowNull: false},
//         isReviewed: {type: DataTypes.INTEGER, defaultValue: 0},
//         versionId: {type: DataTypes.INTEGER, defaultValue: 1},
//         isRedLine: {type: DataTypes.INTEGER, defaultValue: 0},
//         status: {type: DataTypes.INTEGER, defaultValue:1}
//       },
//       {
//         paranoid: false,
//         underscored: true,
//         tableName: "soul_writing_content"
//       }
//     );

//     return Content;
// };