exports.listComments = async(req, h) => {
    try {
        const projectId = req.query.projectId;

        const limit = (req.query.limit !== null )
            ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit
            : Constants.PAGINATION_LIMIT;
        const offset = (req.query.pageNumber-1) * limit;

        const orderByValue = req.query.orderByValue;
        const orderByParameter = req.query.orderByParameter;

        let where = { projectId: projectId }

        let options = {where,order:[[orderByParameter,orderByValue]],distinct:true,subQuery:false};
        if(req.query.pageNumber !== null) options={...options,limit,offset};

        const commentList = await Models.Comment.findAndCountAll(options);

        const totalPages    = await Common.getTotalPages(commentList.count, limit);
        const responseData  = {
            totalPages,
            perPage: limit,
            users: commentList.rows,
            totalRecords: commentList.count
        };

        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULL"),responseData: responseData}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.createComment = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const {comment, lineUuid} = req.payload;
        const createdById = req.auth.credentials.userData.User.id;

        const userAttributes = Constants.MODEL_FIELDS.USER;
        const userInfo = await Models.User.findOne({ where: { userId: createdById }, attributes: userAttributes });
        if(!userInfo) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_AUTHOR"),responseData: {}}).code(400)
        }
        const lineInfo = await Models.Content.findOne({ where: { uuid: lineUuid, isReviewed: 1 }, order: [['versionId', 'DESC']] });
        if(!lineInfo) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_LINE_UUID_PROVIDED"),responseData: {}}).code(400)
        }
        const projectId = lineInfo.projectId;
        const versionId = lineInfo.versionId;

        const projectInfo = await Models.Project.findOne( {where: { id: projectId }} );
        if(!projectInfo) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_PROJECT_ID_PROVIDED"),responseData: {}}).code(400)
        }

        if(createdById === projectInfo.companionId && projectInfo.companionStatus === 2) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("ALREADY_SUBMITTED_THE_WRITING"),responseData: {}}).code(400)
        }
        
        if(createdById === projectInfo.createdById && projectInfo.customerStatus === 2) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("ALREADY_SUBMITTED_THE_WRITING"),responseData: {}}).code(400)
        }

        let commentCreated = await Models.Comment.create({ comment, projectId, createdById, versionId, lineUuid, isRead: 0 }, { transaction });

        commentCreated.dataValues["author"] = userInfo;

        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("COMMENT_CREATED_SUCCESSFULLY"),responseData: commentCreated}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.updateComment = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const {comment, id} = req.payload;
        const createdById = req.auth.credentials.userData.User.id;

        const commentInfo = await Models.Comment.findOne({ where: { id } });
        if(!commentInfo) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_COMMENT_ID_PROVIDED"),responseData: {}}).code(400)
        }

        if(commentInfo.createdById !== createdById) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("COMMENT_OWNER_CAN_CHANGE_THE_COMMENT"),responseData: {}}).code(400)
        }

        const updatedComment = await commentInfo.update({ comment }, { transaction })

        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("COMMENT_UPDATED_SUCCESSFULLY"),responseData: updatedComment}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.deleteComment = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const {id} = req.payload;
        const createdById = req.auth.credentials.userData.User.id;

        const commentInfo = await Models.Comment.findOne({ where: { id } });
        if(!commentInfo) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_COMMENT_ID_PROVIDED"),responseData: {}}).code(400)
        }

        if(commentInfo.createdById !== createdById) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("COMMENT_OWNER_CAN_CHANGE_THE_COMMENT"),responseData: {}}).code(400)
        }

        const deletedComment = await commentInfo.destroy({ transaction })

        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("COMMENT_DELETED_SUCCESSFULLY"),responseData: deletedComment}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.metaComments = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const {projectId, comments} = req.payload;

        const projectInfo = await Models.Project.findOne({ where: { id: projectId } });
        if(!projectInfo) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_PROJECT_ID_PROVIDED"),responseData: {}}).code(400)
        }

        // if(commentInfo.createdById !== createdById) {
        //     await transaction.rollback();
        //     return h.response({success: false,message: req.i18n.__("COMMENT_OWNER_CAN_CHANGE_THE_COMMENT"),responseData: {}}).code(400)
        // }

        // const deletedComment = await commentInfo.destroy({ transaction })
        const updatedComment = await projectInfo.update({ comments }, { transaction });
        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("COMMENT_DELETED_SUCCESSFULLY"),responseData: updatedComment}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}