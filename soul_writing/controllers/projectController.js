const { Op,QueryTypes } = require("sequelize");

exports.createProject = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const {title, description, reason, companionId} = req.payload;
        // const {title, description, reason, companionId, projectListId} = req.payload;
        const createdById = req.auth.credentials.userData.User.id;

        // const existingProject = await Models.Project.findOne({ where: {createdById: createdById, projectListId, projectListId} });
        // if(existingProject) {
        //     await transaction.rollback();
        //     return h.response({success: false,message: req.i18n.__("PROJECT_ALREADY_EXIST"),responseData: {}}).code(400)
        // }

        // const projectListInfo = await Models.ProjectList.findOne({ where: { id:  projectListId} });
        // if(!projectListInfo) {
        //     await transaction.rollback();
        //     return h.response({success: false,message: req.i18n.__("INVALID_PROJECT_SELECTED"),responseData: {}}).code(400)
        // }
        // const title = projectListInfo.title;
        if(companionId !== null) {
            const validateCompanion = await Models.User.findOne( {where: {userId: companionId}} );
            if(!validateCompanion) {
                await transaction.rollback();
                return h.response({success: false,message: req.i18n.__("PROJECT_OWNER_CAN_UPDATE_PROJECT"),responseData: {}}).code(400)
            }
        }

        const createProject = await Models.Project.create({title: title, description, reason, createdById, companionId, stage: 3, customerStatus: 1, version: 1}, {transaction});

        await Models.Project.update({ projectId: createProject.id }, { where: { id: createProject.id }, transaction });
        await Models.Character.create({projectId: createProject.id, title: "1", name: "I", Acronym: "I", age: 25, createdById: createdById, lifeStatus: 1, isDefault: 1}, {transaction})

        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("PROJECT_CREATED_SUCCESSFULLY"),responseData: createProject}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.updateProject = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const {title, description, reason, id, companionId, projectMeta} = req.payload;
        const createdById = req.auth.credentials.userData.User.id;

        let isProjectExist = await Models.Project.findOne( { where: {id: id} } );
        if(!isProjectExist) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_PROJECT_ID_PROVIDED"),responseData: {}}).code(400)
        }
        console.log(" ======================================== ")
        console.log(" ======================================== ")
        console.log(" ======================================== ")
        console.log(" ======================================== ")
        console.log(isProjectExist.createdById)
        console.log(createdById)
        console.log(isProjectExist)
        
        console.log(" ======================================== ")
        console.log(" ======================================== ")
        console.log(" ======================================== ")
        console.log(" ======================================== ")
        if(isProjectExist.createdById !== createdById) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("PROJECT_OWNER_CAN_UPDATE_PROJECT"),responseData: {}}).code(400)
        }

        let updateObj = {};
        if(projectMeta !== null) updateObj["projectMeta"] = projectMeta;
        if(title !== null) updateObj["title"] = title;
        if(description !== null) updateObj["description"] = description;
        if(reason !== null) updateObj["reason"] = reason;
        if(companionId !== null) {
            const validateCompanion = await Models.User.findOne( {where: {userId: companionId}} );
            if(!validateCompanion) {
                await transaction.rollback();
                return h.response({success: false,message: req.i18n.__("PROJECT_OWNER_CAN_UPDATE_PROJECT"),responseData: {}}).code(400)
            }
            updateObj["companionId"] = companionId;
        }

        await isProjectExist.update(updateObj, {transaction});

        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("PROJECT_CREATED_SUCCESSFULLY"),responseData: isProjectExist}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.deleteProject = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const {id} = req.payload;
        const createdById = req.auth.credentials.userData.User.id;
        let isProjectExist = await Models.Project.findOne( { id: id } );
        if(!isProjectExist) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_PROJECT_ID_PROVIDED"),responseData: {}}).code(400)
        }

        if(isProjectExist.createdById !== createdById) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("PROJECT_OWNER_CAN_DELETE_PROJECT"),responseData: {}}).code(400)
        }

        await isProjectExist.destroy({transaction});

        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("PROJECT_CREATED_SUCCESSFULLY"),responseData: isProjectExist}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.listProjects = async(req, h) => {
    try {
        const createdById = req.auth.credentials.userData.User.id;
        const companionStatus = req.query.companionStatus;
        console.log(req.auth.credentials.userData.Role[0], " req.auth.credentials.userData.Role[0] ")
        const limit = (req.query.limit !== null )
        ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit
        : Constants.PAGINATION_LIMIT;
        const offset = (req.query.pageNumber-1) * limit;
        const orderByValue = req.query.orderByValue;
        const orderByParameter = req.query.orderByParameter;
        
        let attributes = Constants.MODEL_FIELDS.PROJECT;
        attributes = [...attributes, [Sequelize.literal('(SELECT created_at FROM soul_writing_project_version where project_id = `Project`.`id` order by id desc limit 1)'), 'lastSubmittedDate']]
        const userAttributes = Constants.MODEL_FIELDS.USER;
        // let where =  { [Op.or]: [{createdById: createdById}, {companionId: createdById}] }
        let where = { status: 1 }
        if(req.auth.credentials.userData.Role[0] === "companion" || req.auth.credentials.userData.Role[0] === "student") {
            where =  {
                ...where, companionId: createdById, 
                companionStatus: { [Op.ne]: null }
                //[Op.or]: [{version: 1, projectStatus: 2}, {version: 1, projectStatus: 3}, {version: {[Op.gt]: 1}}]
            }
        } else if(req.auth.credentials.userData.Role[0] === "costumer") {
            where =  {...where, createdById: createdById}
        } else {
            //where = {...where, com}
        }

        if(req.query.historyDays !== null) {
            let date = Moment().add(-req.query.historyDays,'d').utc().format('YYYY-MM-DD HH:mm:ss');
            where = {...where, updatedAt: { [Op.lte]:  date}}
        }

        if(companionStatus !== null) where = {...where, companionStatus: companionStatus}

        let options = {
            where: where, order: [[orderByParameter,orderByValue]],attributes: attributes, include: [
                {  
                    model: Models.User,
                    as: "companion",
                    attributes: userAttributes 
                },
                {  
                    model: Models.User,
                    as: "author",
                    attributes: userAttributes 
                }
            ],
        }
        if(req.query.pageNumber !== null) options={...options,limit,offset};
        const projectList = await Models.Project.findAndCountAll(options);

        const totalPages = await Common.getTotalPages(projectList.count,limit);
        const responseData = {
            totalPages,
            perPage:limit,
            totalRecords: projectList.count,
            projectList: projectList.rows,
            baseUrl: process.env.NODE_SERVER_PUBLIC_API,
        }

        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULLY"),responseData: responseData}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.listProjectsForCustomerHistory = async(req, h) => {
    try {
        const createdById = req.query.userId;
        const attributes = Constants.MODEL_FIELDS.PROJECT;
        const userAttributes = Constants.MODEL_FIELDS.USER;

        let where = { status: 1 }
        where = {...where, createdById: createdById}
        where = {...where, submittedVersion: {[Op.ne]: null}}

        let options = {
            where: where,attributes: attributes, include: [
                {  
                    model: Models.User,
                    as: "companion",
                    attributes: userAttributes 
                }
            ],
        }
        const projectList = await Models.Project.findAndCountAll(options);
        const responseData = { totalRecords: projectList.count, data: projectList.rows }

        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULLY"),responseData: responseData}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.projectById = async(req, h) => {
    try {
        const projectId = req.params.id;
        let attributes = Constants.MODEL_FIELDS.PROJECT;
        const version = req.query.version;
        let where = {id: projectId};
        const authId = req.auth.credentials.userData.User.id;
        // else {
        //     where = {...where, id: projectId}
        // }
        const userAttributes = Constants.MODEL_FIELDS.USER;
        let projectInfo = await Models.Project.findOne({
          attributes: attributes,
          where: where,
          include: [
            {
              model: Models.User,
              as: "companion",
              attributes: userAttributes
            },
            {  
                model: Models.User,
                as: "author",
                attributes: userAttributes 
            }
          ]
        });

        if(authId !== projectInfo.createdById && authId !== projectInfo.companionId) {
            return h.response({success: false,message: req.i18n.__("NOT_ALLOWED_TO_ACCESS_THE_SOULWRITING"),responseData: {}}).code(400) 
        }


        console.log( projectInfo, " ================ projectInfo 1111111111")
        projectInfo = JSON.parse(JSON.stringify(projectInfo));
        
        if(version) {
            let versionWhere = {}
            versionWhere = {...versionWhere, version, projectId: projectId}
            let projectVersion = await Models.ProjectVersion.findOne({ where: versionWhere });
            if(projectVersion) { projectInfo["projectMeta"] = projectVersion.projectMeta }
        } 
        
        console.log( projectInfo, " ================ projectInfo 2222222222")
        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULLY"),responseData: projectInfo}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.projectByIdForPdf = async(req, h) => {
    try {
        const projectId = req.params.id;
        let attributes = Constants.MODEL_FIELDS.PROJECT;
        const version = req.query.version;
        let where = {id: projectId};
       
        const userAttributes = Constants.MODEL_FIELDS.USER;
        let projectInfo = await Models.Project.findOne({
          attributes: attributes,
          where: where,
          include: [
            {
              model: Models.User,
              as: "companion",
              attributes: userAttributes
            },
            {  
                model: Models.User,
                as: "author",
                attributes: userAttributes 
            }
          ]
        });

        projectInfo = JSON.parse(JSON.stringify(projectInfo));
        
        if(version) {
            let versionWhere = {}
            versionWhere = {...versionWhere, version, projectId: projectId}
            let projectVersion = await Models.ProjectVersion.findOne({ where: versionWhere });
            if(projectVersion) { projectInfo["projectMeta"] = projectVersion.projectMeta }
        } 
        
        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULLY"),responseData: projectInfo}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.createUserProjects = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const createdById = req.auth.credentials.userData.User.id;
        const projectList = req.payload.projectList;
        
        let data = [];
        for(let project of projectList) {
            data.push({title: project, createdById: createdById})
        }

        await Models.ProjectList.bulkCreate(data, {
            updateOnDuplicate: ["description", "status"] ,transaction:transaction
        });

        const projectLists = await Models.ProjectList.findAll({ where: {createdById: createdById}, order:[["createdAt", "DESC"]], transaction })
        
        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("PROJECT_LIST_SUCCESSFULLY_CREATED"),responseData: projectLists}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.listUserProjects = async(req, h) => {
    try {
        const createdById = req.auth.credentials.userData.User.id;

        const projectList = await Models.ProjectList.findAll({ where: {createdById}, order:[["createdAt", "DESC"]] });

        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFUL"),responseData: projectList}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.companionDashboardCount = async(req, h) => {
    try {
        const companionId = req.auth.credentials.userData.User.id;
  
        const soulWritingCount = await Models.Project.count({ where: {companionId: companionId, companionStatus: {[Op.ne]: null}} });

        const totalUsersConnected= await Models.sequelize.query(
            `
            SELECT distinct created_by_id user FROM soul_writing_project where companion_id = ${companionId} and companion_status is not null;
            `,
            {type: QueryTypes.SELECT}
          );

          let users = [];
          for(let user of totalUsersConnected) {
            users.push(user.user)
          }

        const companionEarning = await Models.User.findOne({  attributes: ["id", "userId", "totalEarning"], where: { userId: companionId } });
        

      let responseData = {users: users, soulWritingCount: soulWritingCount, earning: companionEarning.totalEarning}
  
      return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
    } catch (error) {
      console.log(error)
      return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
  }

exports.offlineCheck = async(req, h) => {
    try {
        const version = req.payload.version;
        const lastUpdatedAt = req.payload.lastUpdatedAt;
        const projectId = req.payload.projectId;
        let responseData = {}


        console.log(version, " ============== version")
        console.log(lastUpdatedAt, " ============== lastUpdatedAt")


        const projectInfo = await Models.Project.findOne({ where: { id: projectId } });
        if(!projectInfo) {
            return h.response({success: false,message: req.i18n.__("INVALID_PROJECT_ID_PROVIDED"),responseData: {}}).code(400)
        }

        console.log(projectInfo, " ===== ")
        console.log(Moment(projectInfo?.updatedAt), " Moment(projectInfo?.updatedAt)")

        if(projectInfo?.version > version) {
            responseData = { type: 1, value: "You are Behind The latest Version" }
            console.log(responseData, " ============ first check")
            return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
        }
        
        // if(projectInfo?.version === version && projectInfo?.customerStatus === 2 && Moment(lastUpdatedAt) < Moment(projectInfo?.updatedAt)) {
        if(projectInfo?.version === version && projectInfo?.customerStatus === 2) {
            // responseData = { type: 1, value: "You are Behind The latest Version and is submitted to companion" }
            responseData = { type: 1, value: "You can't take any action, already submitted to companion" }
            console.log(responseData, " ============ second check")
            return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
        }
        
        if(Moment(lastUpdatedAt) < Moment(projectInfo?.updatedAt)) {
            responseData = { type: 2, value: "There are recent changes to be merged" }
            console.log(responseData, " ============ third check")
            return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
        }
        
        if(Moment(lastUpdatedAt) > Moment(projectInfo?.updatedAt)) {
            responseData = { type: 3, value: "You can merge the changes" }
            console.log(responseData, " ============ fourth check")
            return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
        }
        
        console.log(responseData, " ============ final check")
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: {}}).code(200);
    } catch (error) {
        console.log(error)
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
  }

  exports.getSoulwritingSummary = async(req, h) => {
    try {
        const userId = req.query.userId;

        const userInfo = await Models.User.findOne({ where: { userId } });

        let soulwritingInfo = await Models.Project.findAll({ 
            where: { createdById: userId, submittedVersion: {[Op.gte]: 1} },
            include: [
                {
                    model: Models.User, as: "companion"
                }
            ]
        });

        if(soulwritingInfo) soulwritingInfo = JSON.parse(JSON.stringify(soulwritingInfo));

        const responseData = {
            userInfo: {
                userId: userInfo?.userId, 
                email: userInfo?.email, 
                firstName: userInfo?.firstName,
                lastName: userInfo?.lastName,
                title: userInfo?.title
            },
            soulwritingInfo: soulwritingInfo
        }

        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
    } catch (error) {
        console.log(error)
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
  }

  exports.legacySoulwritings = async(req, h) => {
    try {
        let userId = req.query.userId;
        if(userId === null) {
          userId = req.auth.credentials.userData.User.id;
        }

        const id = req.query.id;

        let where = "";
        let countWhere = "";
        if(id !== null) {
            where = `and swh.id = ${id}`;
            countWhere = `and id = ${id}`;
        }

        const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT;
        const offset = (req.query.pageNumber - 1) * limit;
  
        let totalRecords = await sequelize.query(
          `SELECT count(id) as count FROM soul_writing_history where (user_id = ${userId} or companion_id = ${userId}) ${countWhere};`,
          {type: QueryTypes.SELECT}
        );
  
        totalRecords = totalRecords[0].count;
  
        const records = await sequelize.query(
          `SELECT swh.id as id, swh.user_id as userId, swh.companion_id as companionId, swh.project_details as projectDetails, 
          swh.content_details as contentDetails, ec.user_object as companion, eu.user_object as user FROM soul_writing_history swh
          LEFT JOIN event_users eu on swh.user_id = eu.user_id 
          LEFT JOIN event_users ec on swh.companion_id = ec.user_id 
          where (swh.user_id = ${userId} or companion_id = ${userId}) ${where}
          limit ${limit} offset ${offset};`,
          {type: QueryTypes.SELECT}
        );
  
        const totalPages = await Common.getTotalPages(totalRecords, limit);
        const responseData = {
          totalPages,
          perPage: limit,
          records: records,
          totalRecords: totalRecords
        };
  
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
    } catch (error) {
        console.log(error)
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
  }