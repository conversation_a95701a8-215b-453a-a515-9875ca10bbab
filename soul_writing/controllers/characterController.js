exports.createCharacter = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const createdById = req.auth.credentials.userData.User.id;

        let {projectId, title, name, age, degreeOfKinship, lifeStatus, passAwayDate, Acronym, contact, noOfMeetings, sympethetic, color, protogonistObject, distance} = req.payload;
        const isCharacterExists = await Models.Character.findOne({ where: { projectId: projectId, name: name } });
        if(isCharacterExists) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("CHARACTER_ALREADY_EXISTS_FOR_THIS_PROJECT"),responseData: {}}).code(400)
        }

        const createCharacter = await Models.Character.create({ projectId: projectId, title: title, name: name, age: age, degreeOfKinship: degreeOfKinship, lifeStatus: lifeStatus, passAwayDate: passAwayDate, Acronym: Acronym, createdById: createdById, contact, noOfMeetings, sympethetic, color, protogonistObject, distance }, {transaction});
        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("CHARACTER_CREATED_SUCCESSFULLY"),responseData: createCharacter}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.updateCharacter = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const createdById = req.auth.credentials.userData.User.id;
        let {projectId, title, name, age, degreeOfKinship, lifeStatus, passAwayDate, Acronym, id,  contact, noOfMeetings, sympethetic, color, protogonistObject, distance} = req.payload;

        const isCharacterExists = await Models.Character.findOne({ where: { id: id } });
        if(!isCharacterExists) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_CHARACTER_ID_PROVIDED"),responseData: {}}).code(400)
        }

        if(isCharacterExists.isDefault === 1) {
            const characterAttributes = ["name","Acronym","title","age","projectId","id","color"];
            for(let item in req.payload) {
                if(!characterAttributes.includes(item)) {
                    delete req.payload[item];
                }
            }
            for(let item in req.payload) {
                if(!characterAttributes.includes(item)) {
                    await transaction.rollback();
                    return h.response({success: false,message: req.i18n.__("COMMON_ATTRIBUTES_ALLOWED_IN_DEFAULT_CHARACTER"),responseData: {}}).code(400)
                }
            }
        }

        if(isCharacterExists.createdById !== createdById) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("OWNER_CAN_CHANGE_CHARACTER"),responseData: {}}).code(400)
        }

        let updateObj = {};
        if(projectId !== null) updateObj["projectId"] = projectId;
        if(title !== null) updateObj["title"] = title;
        if(name !== null) updateObj["name"] = name;
        if(age !== null) updateObj["age"] = age;
        if(lifeStatus !== null) updateObj["lifeStatus"] = lifeStatus;
        if(passAwayDate !== null) updateObj["passAwayDate"] = passAwayDate;
        if(Acronym !== null) updateObj["Acronym"] = Acronym;
        if(contact !== null) updateObj["contact"] = contact;
        if(degreeOfKinship !== null) updateObj["degreeOfKinship"] = degreeOfKinship;
        if(noOfMeetings !== null) updateObj["noOfMeetings"] = noOfMeetings;
        if(sympethetic !== null) updateObj["sympethetic"] = sympethetic;
        if(color !== null) updateObj["color"] = color;
        if(protogonistObject !== null) updateObj["protogonistObject"] = protogonistObject;
        if(distance !== null) updateObj["distance"] = distance;

        await isCharacterExists.update(updateObj, {transaction});
        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("CHARACTER_UPDATED_SUCCESSFULLY"),responseData: isCharacterExists}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.getDegreeOfKinships = async(req, h) => {
    try {
        const createdById = req.auth.credentials.userData.User.id;

        const kinshipList = await Models.Kinship.findAll({ where: { userId: createdById } });

        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULL"),responseData: {kinshipList}}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.deleteCharacter = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const {id} = req.query;
        const createdById = req.auth.credentials.userData.User.id;
        const isCharacterExists = await Models.Character.findOne({ where: {id: id} });
        if(!isCharacterExists) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_CHARACTER_ID_PROVIDED"),responseData: {}}).code(400)
        }

        if(isCharacterExists.createdById !== createdById) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("OWNER_CAN_DELETE_CHARACTER"),responseData: {}}).code(400)
        }

        const projectId = isCharacterExists.projectId;
        let versionNumber = await Models.Content.max('versionId', {where: { projectId }});
        const isCharacterUsed = await Models.Content.findOne({ where: { characterId: id, versionId: versionNumber } });
        if(isCharacterUsed) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("CHARACTER_IS_ALREADY_IN_USE"),responseData: {}}).code(400)
        }

        await isCharacterExists.destroy({transaction});
        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("CHARACTER_DELETED_SUCCESSFULLY"),responseData: isCharacterExists}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.listCharacter = async(req, h) => {
    try {
        const projectId = req.query.projectId;
        const attributes = Constants.MODEL_FIELDS.CHARACTER;

        let where = {projectId: projectId}
        const characterList = await Models.Character.findAll({
             attributes: attributes,
             where: where 
            });

        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULL"),responseData: {characterList}}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.characterDetails = async(req, h) => {
    try {
        const characterId = req.params.id;
        const attributes = Constants.MODEL_FIELDS.CHARACTER;

        const characterList = await Models.Character.findOne({ attributes: attributes, where: { id: characterId } });

        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULL"),responseData: {characterList}}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.questions = async(req, h) => {
    try {
        const requestedLang = req.headers.language || "en";

        let questionList = await Models.ProtagonistQuestion.findOne({ 
            where: { languageCode: requestedLang },
            attributes: ["id","languageCode","questions"]
        });

        if(!questionList) {
            questionList = await Models.ProtagonistQuestion.findOne({ 
                where: { languageCode: "en" },
                attributes: ["id","languageCode","questions"]
            });
        }
        if(questionList) {
            questionList = JSON.parse(JSON.stringify(questionList))
        }
        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULL"),responseData: {questionList}}).code(200);
    } catch (error) {
        console.log(error)
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);   
    }
}