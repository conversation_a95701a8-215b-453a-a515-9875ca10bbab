const {decodeData} = require("../digistore24");
const { v4: uuidv4, version } = require('uuid');
const { convert }  = require('html-to-text');
const {patienceDiffPlus} = require("../helper");
const { Op,QueryTypes } = require("sequelize");

const createNotification = async(data) => {
    try {
        const requestObj = {
            url : `${process.env.NOTIFICATION_DOMAIN}/notification`,
            data: data, method: "post", headers: {}
        }
        
        await Axios(requestObj).then(async(response)=>{ return response.data }).catch(async (error) => { return {} });
        return true
    } catch (error) {
        console.log(error)
        return
    }
}

const soulWritingPrice = async (type, wordCount) => {
    let where = {};
    if(type === "student") {
        where = {...where, key: "SOUL_WRITING_PRICE_STUDENT"}
    } else {
        where = {...where, key: "SOUL_WRITING_PRICE_PROFESSIONAL"}
    }
    const settings = await Models.Setting.findOne({ where: where });
    const price = settings?.value ? settings?.value : 0.01;
    let totalPrice = price * wordCount;
    if(totalPrice < 1) totalPrice = 1;
    return totalPrice;
}

const diffStr = (oldContent, newContent) => {
    const oldHtml = oldContent.replaceAll("<p>&nbsp;</p>","^").replaceAll("&nbsp;","~").replaceAll(/<span\s+style="[^"]*background-color:\s*rgb\s*\(\s*195\s*,\s*241\s*,\s*204\s*\)\s*"[^>]*>(.*?)<\/span>/g, '$1');
    const newHtml = newContent.replaceAll("<p>&nbsp;</p>","^").replaceAll("&nbsp;","~").replaceAll(/<span\s+style="[^"]*background-color:\s*rgb\s*\(\s*195\s*,\s*241\s*,\s*204\s*\)\s*"[^>]*>(.*?)<\/span>/g, '$1');
    const diffHtml = patienceDiffPlus(oldHtml.split(""), newHtml.split(""));
    let string = diffHtml.html.replaceAll("^","<p>&nbsp;</p>").replaceAll("~","&nbsp;");
    let changeCount = diffHtml.count;
    let totalCount = diffHtml.totalCount;
    let diffString = diffHtml.diffHtml.replaceAll("^","<p>&nbsp;</p>").replaceAll("~","&nbsp;");
    return {string, diffString, changeCount, totalCount};
}

const uniqueArray = (array) => {
    const hashTable = {};
    const uniqueArray = [];
  
    for (let i = 0; i < array.length; i++) {
      const element = array[i];
      if (!hashTable[element]) {
        hashTable[element] = true;
        uniqueArray.push(element);
      }
    }
    return uniqueArray;
}

const validateContent = async(payload, createdById) => {
    try {
        const projectId = payload.projectId;
        const contents = payload.contents;
        const isSaveAsDraft = payload.isSaveAsDraft;
        const companionId = payload.companionId;

        const projectInfo = await Models.Project.findOne({where: {id: projectId}});
        if(!projectInfo) {
            return {success: false, message: "PROJECT_DOES_NOT_EXISTS"}
        }

        if(createdById !== projectInfo.createdById) {
            return {success: false, message: "ONLY_PROJECT_OWNER_CAN_ADD_LINES"}
        }

        if(isSaveAsDraft === 0 && projectInfo.companionId === null && companionId === null) {
            return {success: false, message: "COMPANION_IS_REQUIRED"}
        }

        if(projectInfo.companionStatus === 3) {
            return {success: false, message: "READ_ONLY_CONTENT"}
        }
        
        if(projectInfo.customerStatus === 3 && projectInfo.paymentLink !== null) {
            return {success: false, message: "PLEASE_COMPLETE_PAYMENT"}
        }

        let allCharacters = [];
        let allCategory = [];
        for(let content of contents) {
            allCharacters.push(content.characterId);
            allCategory.push(content.categoryId);
        }

        const Characters = uniqueArray(allCharacters);
        const categories = uniqueArray(allCategory);
        const checkCategoryLength = await Models.Category.findAll({ where: {id: categories} });
        const checkCharacterLength = await Models.Character.findAll({ where: {id: Characters} });
        if(checkCategoryLength.length !== categories.length) {
            return {success: false, message: "INVALID_CATEGORIES_PROVIDED"}
        }
        if(checkCharacterLength.length !== Characters.length) {
            return {success: false, message: "INVALID_CHARACTER_PROVIDED"}
        }   
        return {success: true, message: "LINE_VALIDATED_SUCCESSFULLY"}
    } catch (error) {
        console.log(error);
        return {success: false, message: "SOMETHING_WENT_WRONG_WITH_VALIDATIONS"}
    }
}

const digiStorePayment = async(projectId, companionId, userId, wordCount, transaction) => {
    let orderInfo = await Models.Order.findOne({ where: {projectId, isPaid: 0} });
    if(orderInfo) { await orderInfo.destroy({ transaction }) }
    const companionInfo = await Models.User.findOne({ where: { userId: companionId } });
    const role = await sequelize.query(
        `select u.id, ur.role_id roleId from userobd_users u INNER JOIN userobd_user_roles ur on ur.user_id = u.id where u.id = ${companionId};`,
        { type: QueryTypes.SELECT }
    );
    let userRole=(role && role[0].roleId==4)?'professional':'student';
    console.log("userRole",userRole);
    //let amount = await soulWritingPrice('professional', wordCount);
    let amount = await soulWritingPrice(userRole, wordCount);
    let soulwritingProductId = null;
    if(companionInfo) {
        if(companionInfo.soulwritingProductId) {
            soulwritingProductId = companionInfo.soulwritingProductId;
        }
    }
    if(soulwritingProductId === null) {
        return {success: false, message: "DIGISTORE_PRODUCT_ID_NOT_FOUND"} 
    }

    console.log(`https://www.digistore24.com/api/call/createBuyUrl/?product_id=${companionInfo.soulwritingProductId}&payment_plan[first_amount]=${amount}&tracking[custom]=${projectId}`)
    let data =  await Common.DgStoreRequest(`https://www.digistore24.com/api/call/createBuyUrl/?product_id=${companionInfo.soulwritingProductId}&payment_plan[first_amount]=${amount}&tracking[custom]=${projectId}`);
    console.log(data, " =========== data")
    if(!data.success) { return {success: false, message: "PAYMENT_LINK_FAILED"} }
    let createOrder = await Models.Order.create({projectId, data: {}, userId, companionId, amount: amount.toString(), paymentLink: data.data.data.url}, {transaction})    
    return { success: true, data: data.data.data.url, orderId: createOrder.id }
}

const sendEmail = async(data,code, language = "en")=>{
    try{
        let replacements = {}
        let recipients = []
        if(code === "SOUL_WRITING_SUBMITTED") {
            if(data?.customerId && data?.companionId) {
                const companionInfo = await Models.User.findOne({ where: { userId: data?.companionId } });
                const customerInfo = await Models.User.findOne({ where: { userId: data?.customerId } });
                replacements = {...replacements, customer: customerInfo?.firstName + " " + customerInfo?.lastName, companion: companionInfo?.firstName + " " + companionInfo?.lastName}
                recipients.push(companionInfo?.email)
            }
        } else if(code === "SOUL_WRITING_FEEDBACK") {
            const companionInfo = await Models.User.findOne({ where: { userId: data?.companionId } });
            const customerInfo = await Models.User.findOne({ where: { userId: data?.customerId } });
            replacements = {...replacements, customer: customerInfo?.firstName + " " + customerInfo?.lastName, companion: companionInfo?.firstName + " " + companionInfo?.lastName};
            recipients.push(customerInfo?.email)
        } else {
            return
        }

        const requestObj = {
            url : `${process.env.EMAIL_DOMAIN}/email/send`,
            data: { replacements, priority:'high', code, recipients },
            method: "post", headers: {language}
        }
        
        let res=await Axios(requestObj).then(async(res)=>{ return res.data }).catch(async (error) => { return {} });
        return res
    } catch(error) {
        console.log('Error in Sending email',error)
    }
}

exports.wordCountEstimationTest = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        // let payment = null;
        const createdById = req.auth.credentials.userData.User.id;
        const userId = createdById;
        const projectId = req.payload.projectId;
        const contents = req.payload.contents;
        const isSaveAsDraft = req.payload.isSaveAsDraft;
        const projectMeta = req.payload.projectMeta;
        const companionId = req.payload.companionId;
        const projectStage = req.payload.activeStage;
        let updateProjectObj = {};
        let createSubmitEntry = 0;
        let hasCompanionChanged = 0

        const validations = await validateContent(req.payload, createdById);
        if(!validations.success) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__(validations.message),responseData: {}}).code(400)
        }

        let projectInfo = await Models.Project.findOne( {where: {id: projectId}} );
        let initialCompanion = projectInfo.companionId;
        if(initialCompanion != companionId) {
            hasCompanionChanged = 1;
        }

        let lineArray = [];
        let versionNumber = projectInfo.submittedVersion;
        let projectVersion = 0;
        let characterCount = 0;


        let wordCount = 0;
        for(let content of contents) {
            let newContent = content.content;
            let oldContent = "";
            if(hasCompanionChanged === 0) {
                if(content.uuid) {
                    let compareVesrionContent = await Models.Content.findOne({ raw: true, where: { uuid: content.uuid, versionId: versionNumber } });
                    oldContent = compareVesrionContent.content;
                }
            }
            let diff = diffStr(oldContent, newContent);
            characterCount += diff.totalCount;
            wordCount += diff.changeCount;
            let plainContent = convert(content.content, { wordwrap: false });
            lineArray.push({...content, projectId, createdById, versionId: versionNumber + 1, plainContent: plainContent});
        }

        projectVersion = versionNumber + 1;
        updateProjectObj['projectStatus'] = 1;
        updateProjectObj['customerStatus'] = 1;

        let amount = 0;
        let paymentLink = null;
        if(isSaveAsDraft === 0) {

            let studentValidation = await checkForStudentLimit(userId, companionId);
            if(studentValidation === false) {
              await transaction.rollback();
              return h.response({success: false,message: req.i18n.__("STUDENT_MEETING_LIMIT_REACHED"),responseData: {}}).code(400)
            }

            let payment = await digiStorePayment(projectId, companionId, userId, wordCount, transaction);
            if(!payment.success) {
                await transaction.rollback();
                return h.response({success: false,message: req.i18n.__(payment.message),responseData: {}}).code(400)
            }
            paymentLink = payment.data;
            const role = await sequelize.query(
                `select u.id, ur.role_id roleId from userobd_users u INNER JOIN userobd_user_roles ur on ur.user_id = u.id where u.id = ${companionId};`,
                { type: QueryTypes.SELECT }
            );
            console.log("===========",role);
            let userRole=(role && role[0].roleId==4)?'professional':'student';
            console.log("userRole",userRole);
            //let amount = await soulWritingPrice('professional', wordCount);
            amount = await soulWritingPrice(userRole, wordCount);
            await Models.Invoice.create({ projectId, amount, paymentLink: payment.data, userId, companionId, orderId: payment.orderId }, {transaction})
            await projectInfo.update({paymentLink: payment.data},{transaction})
        }

        await Models.Content.destroy({ where: {projectId: projectId, versionId: versionNumber + 1}, transaction: transaction });
        await Models.Content.bulkCreate(lineArray, {transaction: transaction});

        updateProjectObj['version'] = projectVersion;
        if(isSaveAsDraft === 1) {
            updateProjectObj['stage'] = projectStage;
        }
        updateProjectObj['characters'] = characterCount;
        updateProjectObj['projectMeta'] = projectMeta;

        const projectDetails = await projectInfo.update(updateProjectObj, {transaction});
        const updatedAt = projectDetails.updatedAt;
        const responseData = {
            projectId: projectId, characterCount: characterCount,
            changeCharacterCount: wordCount, updatedAt,
            amount: amount, paymentLink: paymentLink
        }

        if(createSubmitEntry) {
           let test = await createContent(createdById, req.payload, transaction, req.headers.language);
           if(!test.success) {
               await transaction.rollback();
               return h.response({success: false,message: req.i18n.__(test.message),responseData: {}}).code(400)
           }
        } else {
            const payloadData = req.payload;
            let requestData = await Models.RequestData.findOne({ where: { projectId: projectId }, transaction });
            if(requestData) {
                await requestData.destroy({transaction});
            }
            await Models.RequestData.create({ projectId: projectId, payload: payloadData, userId: userId }, { transaction })
        }

        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("RECORDS_CREATED_SUCCESSFULLY"),responseData: responseData}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.wordCountEstimation = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        // let payment = null;
        const createdById = req.auth.credentials.userData.User.id;
        const userId = createdById;
        const projectId = req.payload.projectId;
        const contents = req.payload.contents;
        const isSaveAsDraft = req.payload.isSaveAsDraft;
        const projectMeta = req.payload.projectMeta;
        const companionId = req.payload.companionId;
        const projectStage = req.payload.activeStage;
        let updateProjectObj = {};
        let createSubmitEntry = 0;
        let hasCompanionChanged = 0

        const validations = await validateContent(req.payload, createdById);
        if(!validations.success) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__(validations.message),responseData: {}}).code(400)
        }

        let projectInfo = await Models.Project.findOne( {where: {id: projectId}} );
        let initialCompanion = projectInfo.companionId;
        if(initialCompanion != companionId) {
            hasCompanionChanged = 1;
        }

        let lineArray = [];
        let versionNumber = projectInfo.submittedVersion;
        let projectVersion = 0;
        let characterCount = 0;


        // findAll lines at once from database

        let findAllContent = await Models.Content.findAll({ where: { versionId: versionNumber, projectId } });
        findAllContent = JSON.parse(JSON.stringify(findAllContent));


        let wordCount = 0;
        for(let content of contents) {
            let newContent = content.content;
            let oldContent = "";
            if(hasCompanionChanged === 0) {
                if(content.uuid) {
                    // let compareVesrionContent = await Models.Content.findOne({ raw: true, where: { uuid: content.uuid, versionId: versionNumber } });
                    let compareVesrionContent = findAllContent.find(item => item["uuid"] === content.uuid);
                    oldContent = compareVesrionContent.content;
                }
            }
            let diff = diffStr(oldContent, newContent);
            characterCount += diff.totalCount;
            wordCount += diff.changeCount;
            let plainContent = convert(content.content, { wordwrap: false });
            lineArray.push({...content, projectId, createdById, versionId: versionNumber + 1, plainContent: plainContent});
        }

        projectVersion = versionNumber + 1;
        updateProjectObj['projectStatus'] = 1;
        updateProjectObj['customerStatus'] = 1;

        let amount = 0;
        let paymentLink = null;
        if(isSaveAsDraft === 0) {

            let studentValidation = await checkForStudentLimit(userId, companionId);
            if(studentValidation === false) {
              await transaction.rollback();
              return h.response({success: false,message: req.i18n.__("STUDENT_MEETING_LIMIT_REACHED"),responseData: {}}).code(400)
            }

            let payment = await digiStorePayment(projectId, companionId, userId, wordCount, transaction);
            if(!payment.success) {
                await transaction.rollback();
                return h.response({success: false,message: req.i18n.__(payment.message),responseData: {}}).code(400)
            }
            paymentLink = payment.data;
            const role = await sequelize.query(
                `select u.id, ur.role_id roleId from userobd_users u INNER JOIN userobd_user_roles ur on ur.user_id = u.id where u.id = ${companionId};`,
                { type: QueryTypes.SELECT }
            );
            console.log("===========",role);
            let userRole=(role && role[0].roleId==4)?'professional':'student';
            console.log("userRole",userRole);
            //let amount = await soulWritingPrice('professional', wordCount);
            amount = await soulWritingPrice(userRole, wordCount);
            await Models.Invoice.create({ projectId, amount, paymentLink: payment.data, userId, companionId, orderId: payment.orderId }, {transaction})
            await projectInfo.update({paymentLink: payment.data},{transaction})
        }

        await Models.Content.destroy({ where: {projectId: projectId, versionId: versionNumber + 1}, transaction: transaction });
        await Models.Content.bulkCreate(lineArray, {transaction: transaction});

        updateProjectObj['version'] = projectVersion;
        if(isSaveAsDraft === 1) {
            updateProjectObj['stage'] = projectStage;
        }
        updateProjectObj['characters'] = characterCount;
        updateProjectObj['projectMeta'] = projectMeta;

        const projectDetails = await projectInfo.update(updateProjectObj, {transaction});
        const updatedAt = projectDetails.updatedAt;
        const responseData = {
            projectId: projectId, characterCount: characterCount,
            changeCharacterCount: wordCount, updatedAt,
            amount: amount, paymentLink: paymentLink
        }

        if(createSubmitEntry) {
           let test = await createContent(createdById, req.payload, transaction, req.headers.language);
           if(!test.success) {
               await transaction.rollback();
               return h.response({success: false,message: req.i18n.__(test.message),responseData: {}}).code(400)
           }
        } else {
            const payloadData = req.payload;
            let requestData = await Models.RequestData.findOne({ where: { projectId: projectId }, transaction });
            if(requestData) {
                await requestData.destroy({transaction});
            }
            await Models.RequestData.create({ projectId: projectId, payload: payloadData, userId: userId }, { transaction })
        }

        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("RECORDS_CREATED_SUCCESSFULLY"),responseData: responseData}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

const createContent = async(createdById, payload, transaction, language = "en") => {
    try {
        const projectId = payload.projectId;
        const contents = payload.contents;
        const isSaveAsDraft = payload.isSaveAsDraft;
        const projectMeta = payload.projectMeta;
        const companionId = payload.companionId;
        const projectStage = payload.activeStage;
        let updateProjectObj = {};
        let sendEmailNotification = false;
        let hasCompanionChanged = 0

        const validations = await validateContent(payload, createdById);
        if(!validations.success) {
            return { success: false, message: validations.message }
        }

        let projectInfo = await Models.Project.findOne( {where: {id: projectId}} );
        let initialCompanion = projectInfo.companionId;
        const projectStageId = projectInfo.lastSubmittedStage ? projectInfo.lastSubmittedStage : 0 ;

        if(initialCompanion != companionId) {
            hasCompanionChanged = 1;
        }
        
        let lineArray = [];
        let versionNumber = projectInfo.submittedVersion;
        let projectVersion = 0;
        let characterCount = 0;

        let findAllContent = await Models.Content.findAll({ where: { versionId: versionNumber, projectId } });
        findAllContent = JSON.parse(JSON.stringify(findAllContent));

        let wordCount = 0;
        for(let content of contents) {
            let newContent = content.content;
            let oldContent = "";
            if(content.uuid) {
                // let compareVesrionContent = await Models.Content.findOne({ raw: true, where: { uuid: content.uuid, versionId: versionNumber } });
                let compareVesrionContent = findAllContent.find(item => item["uuid"] === content.uuid);
                oldContent = compareVesrionContent.content;
            }
            let diff = diffStr(oldContent, newContent);
            characterCount += diff.totalCount;
            if(isSaveAsDraft === 1) {
                let plainContent = convert(content.content, { wordwrap: false });
                lineArray.push({...content, projectId, createdById, versionId: versionNumber + 1, plainContent: plainContent});
            }
            
            if(isSaveAsDraft === 0) {
                let plainContent = convert(content.content, { wordwrap: false });
                if(oldContent !== newContent && versionNumber > 0) {
                    if(hasCompanionChanged) {
                        let contentForNewCompanion = content.content.replaceAll(/<span\s+style="[^"]*background-color:\s*rgb\s*\(\s*195\s*,\s*241\s*,\s*204\s*\)\s*"[^>]*>(.*?)<\/span>/g, '$1');
                        content["content"] = contentForNewCompanion;
                    } else {
                        console.log(" inner block ")
                        if(projectStageId !== null && projectStageId >= content.categoryId ) {
                            content["content"] = diff.string;
                            content["diffContent"] = diff.diffString;
                        }
                    }
                }
                wordCount += diff.changeCount;
                if(content.uuid == null) {
                    content["uuid"] = uuidv4();
                }
                content["companionContent"] = content["content"];
                lineArray.push({...content, projectId, createdById, isReviewed: 1, versionId: versionNumber + 1, plainContent: plainContent, diffContent: diff ? diff.diffString : null});
            }
        }

        if(isSaveAsDraft === 1) {
            projectVersion = versionNumber + 1;
            updateProjectObj['projectStatus'] = 1;
            updateProjectObj['customerStatus'] = 1;
        }

        if(isSaveAsDraft === 0) {
            const role = await sequelize.query(
                `select u.id, ur.role_id roleId from userobd_users u INNER JOIN userobd_user_roles ur on ur.user_id = u.id where u.id = ${companionId};`,
                { type: QueryTypes.SELECT }
            );
            let userRole=(role && role[0].roleId==4)?'professional':'student';
            console.log("userRole",userRole);
            //let amount = await soulWritingPrice('professional', wordCount);
            let amount = await soulWritingPrice(userRole, wordCount);
            projectVersion = versionNumber + 1;
            updateProjectObj['projectStatus'] = 2;
            updateProjectObj['lastSubmittedStage'] = projectStage;
            updateProjectObj['customerStatus'] = 2;
            updateProjectObj['companionStatus'] = 3;
            updateProjectObj['submittedVersion'] = versionNumber + 1;
            updateProjectObj['submittedCharacters'] = characterCount;
            updateProjectObj['wordCount'] = wordCount;
            updateProjectObj['paymentAmount'] = amount;
            await Models.Comment.update({isRead: 1}, { where: {projectId: projectId, createdById: createdById}, transaction });
            let rawProjectInfo = JSON.parse(JSON.stringify(projectInfo))
            let meta = rawProjectInfo.projectMeta ? rawProjectInfo.projectMeta : {}
            await Models.ProjectVersion.create({projectId, projectMeta: meta, version: rawProjectInfo.version, companionId: initialCompanion},{transaction})
            if(hasCompanionChanged) updateProjectObj['companionStartVersion'] = versionNumber + 1;
            sendEmailNotification = true;
        }

        await Models.Content.destroy({ where: {projectId: projectId, versionId: versionNumber + 1}, transaction: transaction });
        const createLine = await Models.Content.bulkCreate(lineArray, {transaction: transaction});
        updateProjectObj['version'] = projectVersion;
        updateProjectObj['stage'] = projectStage;
        updateProjectObj['characters'] = characterCount;
        updateProjectObj['projectMeta'] = projectMeta;
        if(companionId !== null) updateProjectObj['companionId'] = companionId;

        const projectDetails = await projectInfo.update(updateProjectObj, {transaction});

        if(sendEmailNotification) {
            let data = { customerId: createdById, companionId: companionId }
            let code = "SOUL_WRITING_SUBMITTED"
            sendEmail(data,code, language)
        }
        return { success: true, message: "RECORDS_CREATED_SUCCESSFULLY", responseData: {projectDetails, createLine, projectMeta}}
    } catch (error) {
        console.log("error", error);
        return {success: false, message: "SOMETHING_WENT_WRONG"}
    }
}

exports.companionSubmit = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const createdById = req.auth.credentials.userData.User.id;
        const projectId = req.payload.projectId;
        let payload = req.payload.contents;
        let isSubmitToUser = req.payload.isSubmitToUser;
        let sendEmailNotification = false;
        let sendMobileNotification = false;
        let responseData = [];

        const projectInfo = await Models.Project.findOne({ where: {id: projectId} });
        if(!projectInfo) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_PROJECT_ID_PROVIDED"),responseData: {}}).code(400)
        }
        let userId = projectInfo.createdById;

        const companionId = projectInfo.companionId;
        if(companionId !== createdById) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_COMPANION_ID_PROVIDED"),responseData: {}}).code(400)
        }

        if(projectInfo.companionStatus !== 3 && projectInfo.customerStatus !== 2) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("NOT_AUTHORIZED_TO_SUBMIT_COMMENTS"),responseData: {}}).code(400)
        }

        for(let item of payload) {
            let lineId = item.id;
            let content = item.content;
            const contentInfo = await Models.Content.findOne({ where: { id: lineId } });
            if(!contentInfo) {
                await transaction.rollback();
                return h.response({success: false,message: req.i18n.__("INVALID_CONTENT_ID_PROVIDED"),responseData: {}}).code(400)
            }
            const updatedContent = await contentInfo.update({ companionContent: content }, {transaction});
            responseData.push(updatedContent);
        }

        if(isSubmitToUser === 1) { 
            // if companionStatus != 3 and customerStatus != 2 throw error
            let projectUpdates = {companionStatus: 2, customerStatus: 3};
            await Models.Comment.update({ isRead: 1 }, {where: {projectId: projectId, createdById: createdById}, transaction});
            await projectInfo.update(projectUpdates, { transaction });
            let versionNumber = projectInfo.submittedVersion;
            const contentInfo = await Models.Content.findAll({ where: { projectId: projectId, versionId: versionNumber }, transaction });
            for(let item of contentInfo) {
                let companionContent = item.companionContent;
                console.log(companionContent)
                let result = await Models.Content.update({ content: companionContent }, { where: { id: item.id }, transaction });
                console.log(result)
            }
            sendEmailNotification = true;
            sendMobileNotification = true;
        }

        await transaction.commit();
        if(sendEmailNotification) {
            let data = { customerId: userId, companionId: companionId  }
            let code = "SOUL_WRITING_FEEDBACK"
            sendEmail(data,code, req.headers.language)
        }
        if(sendMobileNotification) {
            let notificationdata = {
                replacements: { title: projectInfo.title, companion: "companionName" },
                typeId: "soulwriting-companion-submit", userId: userId,
                data: { projectId: projectInfo.id },
            };
            createNotification(notificationdata)
        }
        return h.response({success: true,message: req.i18n.__("RECORDS_UPDATED_SUCCESSFULLY"),responseData: responseData}).code(200);
    } catch (error) {
        console.log(error);
        await transaction.rollback();
        return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
    }
}

// created new function from companion submit which creates new lines
exports.companionSubmitContent = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const createdById = req.auth.credentials.userData.User.id;
        const projectId = req.payload.projectId;
        let payload = req.payload.contents;
        let isSubmitToUser = req.payload.isSubmitToUser;
        let sendEmailNotification = false;
        let sendMobileNotification = false;
        let responseData = [];

        const projectInfo = await Models.Project.findOne({ where: {id: projectId} });
        if(!projectInfo) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_PROJECT_ID_PROVIDED"),responseData: {}}).code(400)
        }
        let userId = projectInfo.createdById;

        const companionId = projectInfo.companionId;
        if(companionId !== createdById) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_COMPANION_ID_PROVIDED"),responseData: {}}).code(400)
        }

        if(projectInfo.companionStatus !== 3 && projectInfo.customerStatus !== 2) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("NOT_AUTHORIZED_TO_SUBMIT_COMMENTS"),responseData: {}}).code(400)
        }

        for(let item of payload) {
            let content = item.content;
            let updatedContent = null;
            let comment = item.comment;
            let plainContent = convert(content, { wordwrap: false });
            delete item.content;
            delete item.comment;

            let lineUuid = null;

            if(item?.id) {
                let lineId = item.id;
                const contentInfo = await Models.Content.findOne({ where: { id: lineId } });
                if(!contentInfo) {
                    await transaction.rollback();
                    return h.response({success: false,message: req.i18n.__("INVALID_CONTENT_ID_PROVIDED"),responseData: {}}).code(400)
                }
                updatedContent = await contentInfo.update({ ...item, plainContent: plainContent, updatedByCompanion: 1, companionContent: content }, {transaction});

                lineUuid = contentInfo.uuid;
            } else {
                // sdjnfkjfkje xamnx jsjkf
                item.content = " ";
                lineUuid = uuidv4();
                let versionNumber = projectInfo.submittedVersion;
                updatedContent = await Models.Content.create({ 
                    ...item, uuid: lineUuid, plainContent: plainContent, addedByCompanion: 1, companionContent: content, projectId, createdById: userId, versionId: versionNumber
                }, {transaction});
                //lineArray.push({...content, projectId, createdById: userId, versionId: versionNumber + 1, plainContent: plainContent});
            }

            // let commentCreated = await Models.Comment.create({ comment, projectId, createdById, versionId, lineUuid, isRead: 0 }, { transaction });
            if(comment) {
                const checkComment = await Models.Comment.findOne({ 
                    where: { lineUuid, versionId: projectInfo.submittedVersion },
                    transaction
                });
                if(checkComment) {
                    await checkComment.update({ comment: comment }, { transaction })
                } else {
                    await Models.Comment.create({ 
                        comment: comment, projectId: projectId, createdById: createdById, versionId: projectInfo.submittedVersion, lineUuid, isRead: 0 
                    }, { transaction });
                }
            }

            responseData.push(updatedContent);
        }

        if(isSubmitToUser === 1) { 
            // if companionStatus != 3 and customerStatus != 2 throw error
            let projectUpdates = {companionStatus: 2, customerStatus: 3};
            await Models.Comment.update({ isRead: 1 }, {where: {projectId: projectId, createdById: createdById}, transaction});
            await projectInfo.update(projectUpdates, { transaction });
            let versionNumber = projectInfo.submittedVersion;
            const contentInfo = await Models.Content.findAll({ where: { projectId: projectId, versionId: versionNumber }, transaction });
            for(let item of contentInfo) {
                let companionContent = item.companionContent;
                console.log(companionContent)
                let result = await Models.Content.update({ content: companionContent }, { where: { id: item.id }, transaction });
                console.log(result)
            }
            sendEmailNotification = true;
            sendMobileNotification = true;
        }

        await transaction.commit();
        if(sendEmailNotification) {
            let data = { customerId: userId, companionId: companionId  }
            let code = "SOUL_WRITING_FEEDBACK"
            sendEmail(data,code, req.headers.language)
        }
        if(sendMobileNotification) {
            let notificationdata = {
                replacements: { title: projectInfo.title, companion: "companionName" },
                typeId: "soulwriting-companion-submit", userId: userId,
                data: { projectId: projectInfo.id },
            };
            createNotification(notificationdata)
        }
        return h.response({success: true,message: req.i18n.__("RECORDS_UPDATED_SUCCESSFULLY"),responseData: responseData}).code(200);
    } catch (error) {
        console.log(error);
        await transaction.rollback();
        return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
    }
}

// exports.companionSubmitContentWithCommentMeta = async (req, h) => {
//     const transaction = await Models.sequelize.transaction();
//     try {
//         const createdById = req.auth.credentials.userData.User.id;
//         const { projectId, contents: payload, isSubmitToUser, comments: metaComments } = req.payload;
//         let sendEmailNotification = false;
//         let sendMobileNotification = false;
//         let responseData = [];

//         // Fetch project info
//         const projectInfo = await Models.Project.findOne({ where: { id: projectId } });
//         if (!projectInfo) {
//             await transaction.rollback();
//             return h.response({ success: false, message: req.i18n.__("INVALID_PROJECT_ID_PROVIDED"), responseData: {} }).code(400);
//         }

//         const userId = projectInfo.createdById;
//         const companionId = projectInfo.companionId;

//         // Validate companion and statuses
//         if (companionId !== createdById) {
//             await transaction.rollback();
//             return h.response({ success: false, message: req.i18n.__("INVALID_COMPANION_ID_PROVIDED"), responseData: {} }).code(400);
//         }
//         if (projectInfo.companionStatus !== 3 && projectInfo.customerStatus !== 2) {
//             await transaction.rollback();
//             return h.response({ success: false, message: req.i18n.__("NOT_AUTHORIZED_TO_SUBMIT_COMMENTS"), responseData: {} }).code(400);
//         }

//         // Update meta comments if provided
//         if (metaComments) {
//             await projectInfo.update({ comments: metaComments }, { transaction });
//         }

//         // Process each content item
//         for (let item of payload) {

//             console.log(item, " ==================== item")

//             const content = item.content;
//             const comment = item.comment;
//             const plainContent = convert(content, { wordwrap: false });

//             delete item.content;
//             delete item.comment;

//             let lineUuid;

//             // Update or create content
//             if (item?.id) {
//                 const contentInfo = await Models.Content.findOne({ where: { id: item.id } });
//                 if (!contentInfo) {
//                     await transaction.rollback();
//                     return h.response({ success: false, message: req.i18n.__("INVALID_CONTENT_ID_PROVIDED"), responseData: {} }).code(400);
//                 }

//                 await contentInfo.update({ ...item, plainContent, updatedByCompanion: 1, companionContent: content }, { transaction });
//                 lineUuid = contentInfo.uuid;
//             } else {
//                 lineUuid = uuidv4();
//                 const versionNumber = projectInfo.submittedVersion;
//                 await Models.Content.create({
//                     ...item,
//                     uuid: lineUuid,
//                     plainContent,
//                     addedByCompanion: 1,
//                     companionContent: content,
//                     projectId,
//                     createdById: userId,
//                     versionId: versionNumber
//                 }, { transaction });
//             }

//             // Handle comments
//             if (comment) {
//                 const checkComment = await Models.Comment.findOne({
//                     where: { lineUuid, versionId: projectInfo.submittedVersion },
//                     transaction
//                 });
//                 if (checkComment) {
//                     await checkComment.update({ comment }, { transaction });
//                 } else {
//                     await Models.Comment.create({
//                         comment,
//                         projectId,
//                         createdById,
//                         versionId: projectInfo.submittedVersion,
//                         lineUuid,
//                         isRead: 0
//                     }, { transaction });
//                 }
//             }

//             responseData.push({ ...item, companionContent: content });
//         }

//         // Handle submit to user
//         if (isSubmitToUser === 1) {
//             await Models.Comment.update({ isRead: 1 }, { where: { projectId, createdById }, transaction });
//             await projectInfo.update({ companionStatus: 2, customerStatus: 3 }, { transaction });

//             const contentInfo = await Models.Content.findAll({ where: { projectId, versionId: projectInfo.submittedVersion }, transaction });
//             for (let item of contentInfo) {
//                 await Models.Content.update({ content: item.companionContent }, { where: { id: item.id }, transaction });
//             }

//             sendEmailNotification = true;
//             sendMobileNotification = true;
//         }

//         await transaction.commit();

//         // Notifications
//         if (sendEmailNotification) {
//             sendEmail({ customerId: userId, companionId }, "SOUL_WRITING_FEEDBACK", req.headers.language);
//         }
//         if (sendMobileNotification) {
//             createNotification({
//                 replacements: { title: projectInfo.title, companion: "companionName" },
//                 typeId: "soulwriting-companion-submit",
//                 userId,
//                 data: { projectId: projectInfo.id }
//             });
//         }

//         return h.response({ success: true, message: req.i18n.__("RECORDS_UPDATED_SUCCESSFULLY"), responseData }).code(200);

//     } catch (error) {
//         console.error(error);
//         await transaction.rollback();
//         return h.response({ success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {} }).code(500);
//     }
// };


exports.getContent = async(req, h) => {
    try {
        const userRole = req.auth.credentials.userData.Role;
        const projectId = req.params.projectId;
        const createdById = req.auth.credentials.userData.User.id;
        const authId = req.auth.credentials.userData.User.id;
        const userAttributes = Constants.MODEL_FIELDS.USER;

        let versionNumber = 0;

        if(req.query.version) {
            versionNumber = req.query.version;
        } else {
            if(userRole.includes('companion') || userRole.includes('student')) {
                versionNumber = await Models.Content.max('versionId', {where: { projectId, isReviewed: 1 }});
            } else {
                versionNumber = await Models.Content.max('versionId', {where: { projectId }});
            }
        }

        let where = { projectId: projectId, versionId: versionNumber };

        const attributes = Constants.MODEL_FIELDS.CONTENT;
        const characterAttributes = Constants.MODEL_FIELDS.CHARACTER;

        const projectDetails = await Models.Project.findOne({where: {id: projectId}, include: [
            {
              model: Models.User,
              as: "companion",
              attributes: userAttributes
            },
            {  
                model: Models.User,
                as: "author",
                attributes: userAttributes 
            }
          ]});

          if(authId !== projectDetails.createdById && authId !== projectDetails.companionId) {
            return h.response({success: false,message: req.i18n.__("NOT_ALLOWED_TO_ACCESS_THE_SOULWRITING"),responseData: {}}).code(400) 
        }

        const contents = await Models.Content.findAll({
          where: where, attributes: attributes, raw: true, nest: true,
          include: [
            { model: Models.Character, as: "character", attributes: characterAttributes }
          ],
          order: [['lineNumber', 'ASC'],]
        });

        let responseData = []
        for(let content of contents) {
            let comments = [];
            let newContent = {...content}
            if(content.uuid) {
                let companionComments = await Models.Comment.findAll({ 
                    raw: true,  nest: true, limit: 1, order: [["createdAt","DESC"]],
                    where: {
                        lineUuid: content.uuid, createdById: projectDetails.companionId,
                        [Op.or]: [{createdById, isRead: 0}, {isRead: 1}]
                    },
                    include: [{ model: Models.User, as: "author", attributes: userAttributes }]
                });
                let userComments = await Models.Comment.findAll({ 
                    raw: true, nest: true, limit: 1, order: [["createdAt","DESC"]],
                    where: {
                        lineUuid: content.uuid, createdById: projectDetails.createdById,
                        [Op.or]: [{createdById, isRead: 0}, {isRead: 1}]
                    },
                    include: [{ model: Models.User, as: "author", attributes: userAttributes }]
                });

                if(companionComments?.length > 0) comments.push(companionComments[0])
                if(userComments?.length > 0) comments.push(userComments[0])
                newContent = {...newContent, comments}
            } else {
                newContent = {...newContent, comments: []}
            }
            responseData.push(newContent)
        }

        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULL"),responseData: {projectDetails, content: responseData}}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.getContentforPdf = async(req, h) => {
    try {
        const projectId = req.params.projectId;

        const userAttributes = Constants.MODEL_FIELDS.USER;

        let versionNumber = 0;

        if(req.query.version) {
            versionNumber = req.query.version;
        } else {
            versionNumber = await Models.Content.max('versionId', {where: { projectId }});
        }

        let where = { projectId: projectId, versionId: versionNumber };

        const attributes = Constants.MODEL_FIELDS.CONTENT;
        const characterAttributes = Constants.MODEL_FIELDS.CHARACTER;

        const projectDetails = await Models.Project.findOne({where: {id: projectId}, include: [
            {
              model: Models.User,
              as: "companion",
              attributes: userAttributes
            },
            {  
                model: Models.User,
                as: "author",
                attributes: userAttributes 
            }
          ]});

        const contents = await Models.Content.findAll({
          where: where, attributes: attributes, raw: true, nest: true,
          include: [
            { model: Models.Character, as: "character", attributes: characterAttributes }
          ],
          order: [['lineNumber', 'ASC'],]
        });

        let responseData = []
        // for(let content of contents) {
        //     let comments = [];
        //     let newContent = {...content}
        //     if(content.uuid) {
        //         // let companionComments = await Models.Comment.findAll({ 
        //         //     raw: true,  nest: true, limit: 1, order: [["createdAt","DESC"]],
        //         //     where: {
        //         //         lineUuid: content.uuid, createdById: projectDetails.companionId,
        //         //         [Op.or]: [{createdById, isRead: 0}, {isRead: 1}]
        //         //     },
        //         //     include: [{ model: Models.User, as: "author", attributes: userAttributes }]
        //         // });
        //         // let userComments = await Models.Comment.findAll({ 
        //         //     raw: true, nest: true, limit: 1, order: [["createdAt","DESC"]],
        //         //     where: {
        //         //         lineUuid: content.uuid, createdById: projectDetails.createdById,
        //         //         [Op.or]: [{createdById, isRead: 0}, {isRead: 1}]
        //         //     },
        //         //     include: [{ model: Models.User, as: "author", attributes: userAttributes }]
        //         // });

        //         // if(companionComments?.length > 0) comments.push(companionComments[0])
        //         // if(userComments?.length > 0) comments.push(userComments[0])
        //         // newContent = {...newContent, comments}
        //     } else {
        //         // newContent = {...newContent, comments: []}
        //     }
        //     responseData.push(newContent)
        // }

        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULL"),responseData: {projectDetails, content: contents}}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.getContentforWord = async(req, h) => {
    try {
        const projectId = req.params.projectId;
        const language = req.headers.language;
        const userAttributes = Constants.MODEL_FIELDS.USER;
        const attributes = ["id", "uuid", "categoryId", "lineNumber", "content"];
        const characterAttributes = ["id", "title", "name", "age","protogonistObject"];
        // const characterAttributes = Constants.MODEL_FIELDS.CHARACTER;

        let versionNumber = 0;
        if(req.query.version) {
            versionNumber = req.query.version;
        } else {
            versionNumber = await Models.Content.max('versionId', {where: { projectId }});
        }

        let where = { projectId: projectId, versionId: versionNumber };

        const protogonistList = await Models.Character.findAll({ attributes: characterAttributes, where: { projectId: projectId } })

        const categoryList = await Models.Category.findAll({
            attributes: ["id", ["name","type"], [Sequelize.literal('(case when `mainCategory`.name is not null then `mainCategory`.name else `defaultCategory`.name END)'), 'name']],
            order:[["sortOrder", "ASC"]], 
            include: [
                {model: Models.CategoryContent, as: "mainCategory", where:{languageCode: language}, required: false, attributes: []},
                {model: Models.CategoryContent, as: "defaultCategory", where: {languageCode: 'en'}, required: false, attributes: []}
            ]
        });

        const projectDetails = await Models.Project.findOne({
            attributes: ["id", "title", "reason", "projectMeta", "comments"],
            where: {id: projectId}, 
            include: [
                {
                    model: Models.User,
                    as: "companion",
                    attributes: userAttributes
                },
                {  
                    model: Models.User,
                    as: "author",
                    attributes: userAttributes 
                }
            ]
        });

        const contents = await Models.Content.findAll({
          where: where, attributes: attributes, raw: true, nest: true,
          include: [
            { model: Models.Character, as: "character", attributes: characterAttributes }
          ],
          order: [['lineNumber', 'ASC'],]
        });


        let responseData = {
            categoryList, projectDetails, contents, protogonistList
        }

        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULL"),responseData: responseData}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}


const checkForStudentLimit = async (userId, companionId) => {
    const role = await sequelize.query(
      `select u.id, ur.role_id roleId from userobd_users u INNER JOIN userobd_user_roles ur on ur.user_id = u.id where u.id = ${companionId};`,
      { type: QueryTypes.SELECT }
    );
  
    console.log(role, " ======================================== role")
    console.log(role, " ======================================== role")
    console.log(role, " ======================================== role")
    console.log(role, " ======================================== role")
  
  
    if (role && role.length > 0) {
      if(role[0].roleId == 3) {
  
        // let definedCount = await Models.Setting.findOne({ where: { key: "STUDENT_MEETING_COUNT" } });
        let definedCount = await sequelize.query(
            "select * from userobd_settings where `key` = 'STUDENT_MEETING_COUNT';",
            { type: QueryTypes.SELECT }
          );

        if(definedCount && definedCount.length > 0) {
          definedCount = definedCount[0].value;
        } else {
          definedCount = null;
        }
        if(definedCount !== null) {
          const userValidationInfo = await sequelize.query(
            `select student_meeting_count from user_validations where user_id = ${userId};`,
            { type: QueryTypes.SELECT }
          );
    
          if(userValidationInfo && userValidationInfo.length > 0) {
            const studentMeetingCount = userValidationInfo[0].student_meeting_count;
            if(studentMeetingCount >= definedCount) return false;
          }
        }
      }
    }
  
    return true;
  };
  
  
  const updateStudentLimit = async (userId, companionId, transaction) => {
      const role = await sequelize.query(
          `select u.id, ur.role_id roleId from userobd_users u INNER JOIN userobd_user_roles ur on ur.user_id = u.id where u.id = ${companionId};`,
          { type: QueryTypes.SELECT }
        );
        
        if (role && role.length > 0) {
            if(role[0].roleId == 3) {
                await sequelize.query(
                    `UPDATE user_validations SET student_meeting_count = student_meeting_count + 1 WHERE user_id = ${userId};`,
                    { type: QueryTypes.UPDATE, transaction }
                );
            }
            if(role[0].roleId == 4) {
                await sequelize.query(
                    `UPDATE user_validations SET companion_meeting_count = companion_meeting_count + 1 WHERE user_id = ${userId};`,
                    { type: QueryTypes.UPDATE, transaction }
                );
            }
        }
        
        return true;
    };
    
    const calculateInvoice = async (companionId) => {
        const calculatedAmount = await sequelize.query(
          ` SELECT 
    SUM((CAST(JSON_EXTRACT(data, '$.amount') AS DECIMAL (10 , 2 )) - CAST(JSON_EXTRACT(data, '$.vat_amount') AS DECIMAL (10 , 2 ))) * 0.921) AS total_earned_amount
FROM
    soul_writing_invoices
WHERE
    deleted_at IS NULL AND is_paid = 1
        AND companion_id = ${companionId};`,
          { type: QueryTypes.SELECT }
        );
      
       
        let amount = calculatedAmount[0].total_earned_amount ? calculatedAmount[0].total_earned_amount : 0;
        await Models.User.update({ totalEarning: amount }, { where: { userId: companionId } });
        return true;
      };

      exports.test=async(req,h)=>{
        try{
          const companions = req.payload.companions;
      
          for(let item of companions) {
            await calculateInvoice(item)
          }
      
          return h.response({success: true,message: req.i18n.__("SUCCESSFULLY_SAVED"),responseData:{}}).code(200);
        }
        catch(error){
          console.log("error", error);
          return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
        }
      }
    
    
    exports.confirmPayment = async(req, h) =>{
        const transaction=await Models.sequelize.transaction()
        try{
            console.log(" ************************************************ ")
        console.log(" ************************************************ ")
        console.log(" ************************************************ ")
        console.log(" ************************************************ ")
        let {data}=req.payload
        let decryprData =   await decodeData(data);

        let projectId = parseInt(decryprData.custom);
        const earnings = decryprData?.earned_amount * 0.6
        let order= await Models.Order.findOne({where: { projectId: projectId, isPaid: 0 }});
        if(!order) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("PAYMENT_NOT_CONFIRMED"),responseData: {}}).code(400)   
        }
        const orderId = order.id;


        console.log(" 22222222222222222222222222222222222222222222222222 ")
        console.log(" 22222222222222222222222222222222222222222222222222 ")
        console.log(" 22222222222222222222222222222222222222222222222222 ")
        console.log(" 22222222222222222222222222222222222222222222222222 ")

        let projectInfo = await Models.Project.findOne({ where: { id: projectId } });
        if(!projectInfo) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("PROJECT_NOT_FOUND"),responseData: {}}).code(400)   
        }
        let companionId = projectInfo.companionId;
        const userId = projectInfo.createdById;
        let requestDataInfo = await Models.RequestData.findOne({ where: { projectId: projectId } });
        if(requestDataInfo) {
            companionId = requestDataInfo.payload.companionId;
        }

        console.log(" 33333333333333333333333333333333333333333333333333333 ")
        console.log(" 33333333333333333333333333333333333333333333333333333 ")
        console.log(" 33333333333333333333333333333333333333333333333333333 ")
        console.log(" 33333333333333333333333333333333333333333333333333333 ")

        let companionInfo = await Models.User.findOne({ where: {userId: companionId} });
        if(!companionInfo) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("COMPANION_NOT_FOUND"),responseData: {}}).code(400) 
        }

        const userInfo = await Models.User.findOne({ where: {userId: userId} })
        const invoiceInfo = await Models.Invoice.findOne({ where: { orderId: orderId } });
        await invoiceInfo.update({ isPaid: 1, amountReceived: earnings, data: decryprData }, { transaction });
        const companionEarning = companionInfo.totalEarning;
        const totalEarning = companionEarning + earnings;

        console.log(" 444444444444444444444444444444444444444444444444444444 ")
        console.log(" 444444444444444444444444444444444444444444444444444444 ")
        console.log(" 444444444444444444444444444444444444444444444444444444 ")
        console.log(" 444444444444444444444444444444444444444444444444444444 ")

        let orderDetails= await Common.DgStoreRequest(`getPurchase/?purchase_id=${decryprData.order_id}`)

        await order.update({data: decryprData, isPaid: 1, orderDetails}, {transaction});
        await projectInfo.update({ paymentLink: null, paymentAmount: 0, customerStatus: 1 }, {transaction});

        await calculateInvoice(companionId);

        // await companionInfo.update({totalEarning: totalEarning},{transaction});

        let earningHistoryObj = {
            projectId: projectId, data: decryprData, amount: decryprData?.amount,
            amountReceived: decryprData?.earned_amount, userId: userId,
            companionId: companionId, userObject: userInfo,
            companionObject: companionInfo, projectObject: projectInfo
        }

        await Axios({ method: 'post', url: `${process.env.USER_ONBOARDING_DOMAIN}/earning-history`, headers:{}, data: earningHistoryObj });
        console.log(" 555555555555555555555555555555555555555555555555555 ")
        console.log(" 555555555555555555555555555555555555555555555555555 ")
        console.log(" 555555555555555555555555555555555555555555555555555 ")
        console.log(" 555555555555555555555555555555555555555555555555555 ")
        if(requestDataInfo) {
            let payload = requestDataInfo.payload;
            let userId = requestDataInfo.userId;
            let createdContent = await createContent(userId, payload, transaction, req.headers.language);
            if(!createdContent?.success) {
                await transaction.rollback();
                return h.response({success: false,message: req.i18n.__(createdContent?.message),responseData: {}}).code(400) 
            }
            await requestDataInfo.destroy({transaction});

            const data = await updateStudentLimit(userId, companionId, transaction);
            console.log(data, " ============================ data limit update")
        }

        await transaction.commit();

        let notificationdata = {
            replacements: { title: projectInfo.title, customer: "customerName" },
            typeId: "soulwriting-customer-submit", userId: companionId,
            data: { projectId: projectInfo.id },
        };
        createNotification(notificationdata)

        return h.response({success: true,message: req.i18n.__("ATTRIBUTE_SUCCESSFULLY_CREATED"),responseData:order}).code(200)
    }catch(error){
        console.error('Error in Add orders',error)
        await transaction.rollback();
        return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
    }
}

exports.getOrders = async(req,h) =>{
    try{
        const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT
        const offset = (req.query.pageNumber - 1) * limit
        const orderByValue = req.query.orderByValue
        const orderByParameter = req.query.orderByParameter
        let where ={}

        if(req.auth.credentials.userData.Role.includes('companion')) {
            where = {companionId: req.query.userId}
        } else if(req.auth.credentials.userData.Role.includes('costumer')) {
            where = {userId: req.query.userId}
        } else {
            if(req.query.userId!==null)
            {
                where= {...where,userId:req.query.userId}
            }
        }

        if(req.query.startDate && req.query.endDate) {
            if(Moment(req.query.endDate) < Moment(req.query.startDate)) {
              return h.response({success:false,message:req.i18n.__('END_DATE_CANNOT_BE_LESS_THAN_START_DATE'),responseData:{}}).code(400)
            }
            let startDate =  Moment(req.query.startDate).utc().format('YYYY-MM-DD HH:mm:ss');
            let endDate =  Moment(req.query.endDate).utc().format('YYYY-MM-DD HH:mm:ss');
            where = {...where, createdAt: {[Op.between]: [startDate, endDate]}}
        } else if(req.query.startDate) {
            let startDate =  Moment(req.query.startDate).utc().format('YYYY-MM-DD HH:mm:ss');
            where = {...where, createdAt: {[Op.gte]: startDate}}
        } else if(req.query.endDate) {
            let endDate =  Moment(req.query.endDate).utc().format('YYYY-MM-DD HH:mm:ss');
            where = {...where, createdAt: {[Op.lte]: endDate}}
        }

        let options = {
            include: [
                { model: Models.User }, 
                { model: Models.User, as: "companion" }
            ],
            order: [ [orderByParameter, orderByValue] ],
            subQuery: false, where
        };
        if (req.query.pageNumber !== null)  options = { ... options, limit, offset };

        const cartProducts = await Models.Order.findAndCountAll(options);
        const totalPages = await Common.getTotalPages(cartProducts.count, limit);
        const responseData = { totalPages, perPage: limit, records: cartProducts.rows, totalRecords: cartProducts.count };

        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData}).code(200);
    }catch(error){
        console.error('Error in getting Attributes',error)
        return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
    }
}

exports.getOrderById = async(req,h) =>{
    try{
        let where = {}
  
        let options = {
            where: { id: req.query.orderId }, subQuery    : false,
            include: [{ model: Models.User }, { model: Models.User, as: "companion" }]
        };

        const cartProducts = await Models.Order.findOne(options)
  
        return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: cartProducts}).code(200);
    }catch(error){
        console.error('Error in getting Attributes',error)
        return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
    }
}

exports.paymentHistory = async(req, h) => {
    try {
        let userId=req.auth.credentials.userData.User.id;

        const limit = (req.query.limit !== null )
            ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit
            : Constants.PAGINATION_LIMIT;
        const offset = (req.query.pageNumber-1) * limit;
        const orderByValue = req.query.orderByValue;
        const orderByParameter = req.query.orderByParameter;

        let where = {companionId: userId, isPaid: 1};
        let options = { where, order: [ [ orderByParameter,orderByValue ] ], distinct:true };
        if(req.query.pageNumber !== null) options={ ...options,limit,offset };

        const paymentHistoryList = await Models.Invoice.findAndCountAll(options);
        const totalPages = await Common.getTotalPages(paymentHistoryList.count,limit);

        const responseData = {
            totalPages, totalRecords: paymentHistoryList.count,
            perPage:limit, paymentHistory: paymentHistoryList.rows,
            baseUrl: process.env.NODE_SERVER_PUBLIC_API,
        }

        return h.response({success:true,message:req.i18n.__('REQUEST_SUCCESSFUL'),responseData:responseData}).code(200);
    } catch (error) {
        console.error(error)
        await transaction.rollback();
        return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
    }
}

// created new function from companion submit which creates new lines
exports.companionSubmitContentWithCommentMeta = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const createdById = req.auth.credentials.userData.User.id;
        const projectId = req.payload.projectId;
        let payload = req.payload.contents;
        let isSubmitToUser = req.payload.isSubmitToUser;
        let sendEmailNotification = false;
        let sendMobileNotification = false;
        let responseData = [];
        let metaComments = req.payload.comments;

        const projectInfo = await Models.Project.findOne({ where: {id: projectId} });
        if(!projectInfo) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_PROJECT_ID_PROVIDED"),responseData: {}}).code(400)
        }
        let userId = projectInfo.createdById;

        const companionId = projectInfo.companionId;
        if(companionId !== createdById) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_COMPANION_ID_PROVIDED"),responseData: {}}).code(400)
        }

        if(projectInfo.companionStatus !== 3 && projectInfo.customerStatus !== 2) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("NOT_AUTHORIZED_TO_SUBMIT_COMMENTS"),responseData: {}}).code(400)
        }

        if (metaComments) {
            await projectInfo.update({ comments: metaComments }, { transaction });
        }

        for(let item of payload) {
            let content = item.content;
            let updatedContent = null;
            let comment = item.comment;
            let plainContent = convert(content, { wordwrap: false });
            delete item.content;
            delete item.comment;

            let lineUuid = null;

            if(item?.id) {
                let lineId = item.id;
                const contentInfo = await Models.Content.findOne({ where: { id: lineId } });
                if(!contentInfo) {
                    await transaction.rollback();
                    return h.response({success: false,message: req.i18n.__("INVALID_CONTENT_ID_PROVIDED"),responseData: {}}).code(400)
                }
                updatedContent = await contentInfo.update({ ...item, plainContent: plainContent, updatedByCompanion: 1, companionContent: content }, {transaction});

                lineUuid = contentInfo.uuid;
            } else {
                // sdjnfkjfkje xamnx jsjkf
                item.content = " ";
                lineUuid = uuidv4();
                let versionNumber = projectInfo.submittedVersion;
                updatedContent = await Models.Content.create({ 
                    ...item, uuid: lineUuid, plainContent: plainContent, addedByCompanion: 1, companionContent: content, projectId, createdById: userId, versionId: versionNumber
                }, {transaction});
                //lineArray.push({...content, projectId, createdById: userId, versionId: versionNumber + 1, plainContent: plainContent});
            }

            // let commentCreated = await Models.Comment.create({ comment, projectId, createdById, versionId, lineUuid, isRead: 0 }, { transaction });
            if(comment) {
                const checkComment = await Models.Comment.findOne({ 
                    where: { lineUuid, versionId: projectInfo.submittedVersion },
                    order: [["id", "desc"]],
                    transaction
                });
                if(checkComment) {
                    await checkComment.update({ comment: comment }, { transaction })
                } else {
                    await Models.Comment.create({ 
                        comment: comment, projectId: projectId, createdById: createdById, versionId: projectInfo.submittedVersion, lineUuid, isRead: 0 
                    }, { transaction });
                }
            }

            responseData.push(updatedContent);
        }

        if(isSubmitToUser === 1) { 
            // if companionStatus != 3 and customerStatus != 2 throw error
            let projectUpdates = {companionStatus: 2, customerStatus: 3};
            await Models.Comment.update({ isRead: 1 }, {where: {projectId: projectId, createdById: createdById}, transaction});
            await projectInfo.update(projectUpdates, { transaction });
            let versionNumber = projectInfo.submittedVersion;
            const contentInfo = await Models.Content.findAll({ where: { projectId: projectId, versionId: versionNumber }, transaction });
            for(let item of contentInfo) {
                let companionContent = item.companionContent;
                console.log(companionContent)
                let result = await Models.Content.update({ content: companionContent }, { where: { id: item.id }, transaction });
                console.log(result)
            }
            sendEmailNotification = true;
            sendMobileNotification = true;
        }

        await transaction.commit();
        if(sendEmailNotification) {
            let data = { customerId: userId, companionId: companionId  }
            let code = "SOUL_WRITING_FEEDBACK"
            sendEmail(data,code, req.headers.language)
        }
        if(sendMobileNotification) {
            let notificationdata = {
                replacements: { title: projectInfo.title, companion: "companionName" },
                typeId: "soulwriting-companion-submit", userId: userId,
                data: { projectId: projectInfo.id },
            };
            createNotification(notificationdata)
        }
        return h.response({success: true,message: req.i18n.__("RECORDS_UPDATED_SUCCESSFULLY"),responseData: responseData}).code(200);
    } catch (error) {
        console.log(error);
        await transaction.rollback();
        return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
    }
}
