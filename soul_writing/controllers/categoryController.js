exports.postCategory = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const {name, description, videoLink, type, languageCode} = req.payload;

        const isCategoryExists = await Models.Category.findOne({ where: {name: name} });
        if(isCategoryExists) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("CATEGORY_ALREADY_EXISTS"),responseData: {}}).code(400)
        }

        const createCategory = await Models.Category.create({ type: type }, {transaction});

        if(languageCode !== "en") {
            await Models.CategoryContent.create({ name, videoLink, languageCode: "en", description }, {transaction});
        }
        await Models.CategoryContent.create({ name, videoLink, languageCode, description }, {transaction});

        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("CATEGORY_CREATED_SUCCESSFULLY"),responseData: createCategory}).code(200);
    } catch (error) {
        console.log("error", err);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.updateCategory = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const {videoLink, id, name, description} = req.payload;
        const languageCode = req.headers.language;
        let updateObj = {};
        if(videoLink !== null) { updateObj["videoLink"] = videoLink }
        if(name !== null) { updateObj["name"] = name }
        if(description !== null) { updateObj["description"] = description }

        const isCategoryExists = await Models.Category.findOne({ where: {id: id} });
        if(!isCategoryExists) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_CATEGORY_ID_PROVIDED"),responseData: {}}).code(400)
        }

        const categoryContent = await Models.CategoryContent.findOne({ where: { categoryId: id, languageCode: languageCode } });
        let responseData = {}
        if(!categoryContent) {
            const defaultLanguage = "en"
            let findContent = await Models.CategoryContent.findOne({ where: {categoryId: id, languageCode: defaultLanguage}, raw: true });
            if(!findContent) {
                await transaction.rollback();
                return h.response({success: false,message: req.i18n.__("INVALID_CATEGORY_LANGUAGE_PROVIDED"),responseData: {}}).code(400)
            }
            console.log(findContent, " ============ findContent")
            // let newContentObj = JSON.stringify(JSON.parse(findContent));
            delete findContent["id"];
            if(updateObj?.videoLink) {
                findContent["videoLink"] = videoLink;

            }
            if(updateObj?.name) {
                findContent["name"] = name;
            }
            if(updateObj?.description) {
                findContent["description"] = description;
            }
            findContent["languageCode"] = languageCode;
            responseData = await Models.CategoryContent.create(findContent, {transaction});
        } else {
            responseData = await categoryContent.update(updateObj, {transaction});
        }
        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("CATEGORY_UPDATED_SUCCESSFULLY"),responseData: responseData}).code(200);
    } catch (error) {
        console.log("error", error);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.deleteCategory = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        const {id} = req.payload;

        const isCategoryExists = await Models.Category.findOne({ where: {id: id} });
        if(!isCategoryExists) {
            await transaction.rollback();
            return h.response({success: false,message: req.i18n.__("INVALID_CATEGORY_ID_PROVIDED"),responseData: {}}).code(400)
        }

        await isCategoryExists.destroy({transaction});
        await Models.CategoryContent.destroy({where: {categoryId: id},transaction});
        await transaction.commit();
        return h.response({success: true,message: req.i18n.__("CATEGORY_DELETED_SUCCESSFULLY"),responseData: isCategoryExists}).code(200);
    } catch (error) {
        console.log("error", err);
        await transaction.rollback();
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.listCategory = async(req, h) => {
    try {
        const requestedLang = req.headers.language || "en";
        let attributes = Constants.MODEL_FIELDS.CATEGORY;
        attributes = [...attributes, [Sequelize.literal('(case when `mainCategory`.name is not null then `mainCategory`.name else `defaultCategory`.name END)'), 'name'], [Sequelize.literal('(case when `mainCategory`.video_link is not null then `mainCategory`.video_link else `defaultCategory`.video_link END)'), 'videoLink'],[Sequelize.literal('(case when `mainCategory`.description is not null then `mainCategory`.description else `defaultCategory`.description END)'), 'description']]

        const options = {
            attributes: attributes,
            order:[["sortOrder", "ASC"]], 
            include: [
                {model: Models.CategoryContent, as: "mainCategory", where:{languageCode: requestedLang}, required: false, attributes: []},
                {model: Models.CategoryContent, as: "defaultCategory", where: {languageCode: 'en'}, required: false, attributes: []}
            ]
        }
        const categoryList = await Models.Category.findAll( options );

        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULL"),responseData: {categoryList}}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}

exports.categoryDetails = async(req, h) => {
    try {
        const requestedLang = req.headers.language || "en";
        const categoryId = req.params.id;
        let attributes = Constants.MODEL_FIELDS.CATEGORY;
        attributes = [...attributes, [Sequelize.literal('(case when `mainCategory`.name is not null then `mainCategory`.name else `defaultCategory`.name END)'), 'name'], [Sequelize.literal('(case when `mainCategory`.video_link is not null then `mainCategory`.video_link else `defaultCategory`.video_link END)'), 'videoLink'], [Sequelize.literal('(case when `mainCategory`.description is not null then `mainCategory`.description else `defaultCategory`.description END)'), 'description']];

        const options = {
            where: { id: categoryId },
            attributes: attributes, 
            include: [
                {model: Models.CategoryContent, as: "mainCategory", where:{languageCode: requestedLang}, required: false, attributes: []},
                {model: Models.CategoryContent, as: "defaultCategory", where: {languageCode: 'en'}, required: false, attributes: []}
            ]
        }
        const categoryList = await Models.Category.findOne( options );

        return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULL"),responseData: {categoryList}}).code(200);
    } catch (error) {
        console.log("error", error);
        return h.response({success: false,message: req.i18n.__("SOMETHING_WENT_WRONG"),responseData: {}}).code(500);
    }
}