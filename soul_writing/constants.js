module.exports = {
    PAGINATION_LIMIT: 20,
    MAX_PAGINATION_LIMIT: 50,
    STATUS: {
        ACTIVE: 1,
        INACTIVE: 0,
    },
    MODEL_FIELDS: {
        USER: [ "id", "userId", "firstName", "lastName", "title", "profilePhotoUrl", "profilePhotoId", "gender", "email"],
        CONTENT: ["id", "uuid", "projectId", "characterId", "categoryId", "createdById", "lineNumber", "content", "isRedLine", "status", "isReviewed", "versionId", "diffContent","highlightedText", "painpictureCollapse", "isHighlighted","companionContent"],
        CHARACTER: ["id", "projectId", "title", "name", "age", "degreeOfKinship", "lifeStatus", "passAwayDate", "Acronym", "createdById","contact", "noOfMeetings", "sympethetic", "color", "protogonistObject","distance","isDefault"],
        CATEGORY: ["id", ["name","type"]],
        PROJECT: ["id", "title", "description", "reason", "createdById", "companionId", "projectStatus", "companionStatus", "customerStatus", "status", "createdAt", "updatedAt", "recording", "characters", "stage", "version", "projectMeta", "submittedCharacters", "submittedVersion", "wordCount", "paymentLink", "paymentAmount","projectListId","companionStartVersion","lastSubmittedStage","comments"]
    },
    PROJECT_STATUS: {
        DRAFT: 1,
        SUBMITTED: 2,
        RECEIVED: 3,
        COMPLETED: 4
    }

}