exports.privateKey = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456";
exports.algorithm = "aes-256-cbc";
exports.iv = "QWERTY1234567890";
const Constants=require('./constants');
const crypto= require('crypto')
decrypt = (text) => {
    let decipher = crypto.createDecipheriv(this.algorithm, this.privateKey, this.iv);
    let decrypted = decipher.update(text, "hex", "utf8");
    decrypted = decrypted + decipher.final("utf8");
    return decrypted;
};

encrypt = (text) => {
    let cipher = crypto.createCipheriv(this.algorithm, this.privateKey, this.iv);
    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted = encrypted + cipher.final("hex");
    return encrypted;
};

readHTMLFile = (path, callback) => {
    Fs.readFile(path, {
        encoding: "utf-8"
    }, function (err, html) {
        if (err) {
            throw err;
            callback(err);
        } else {
            callback(null, html);
        }
    });
};


exports.prefunction =async (req, h) => {
    global.LanguageCodes = process.env.ALL_LANGUAGE_CODE.split(",");
    global.LanguageIds = process.env.ALL_LANGUAGE_ID.split(",").map(function (item) {
        return parseInt(item, 10);
    });
    global.utcOffset = req.headers.utcoffset;
    return true;
};

exports.routeError = (errors, message) => {
    console.log(errors);
    errors.forEach((err) => {
        switch (err.code) {
            case "any.required":
                err.message = message;
                break;
        }
    });
    return errors;
};

exports.sendEmail =   async(email,code,replacements)=>{
    let reqPayload={
            replacements,
            priority:'high',
            code,
            recipients:[email]
    }
    console.log('reqPayload',reqPayload);

    const axiosObj = {
        url     : Constants.URL.SEND_EMAIL,
        method  : "POST",
        data    : reqPayload
    }

    await Axios(axiosObj).then( (res) => {return true} ).catch((err) => {return false});
}




exports.validateToken = async (token) => { // console.log('called',token)
    fetchtoken = JSON.parse(decrypt(token.data));
    console.log(fetchtoken.User.id)
    let user=await Models.User.findOne({where:{id:fetchtoken.User.id}});
    user = user ? user : fetchtoken.User.id;
    if(!user)
    {
        return {
            isValid: false,
            message:'INVALID_TOKEN'
        }; 
    }
    console.log('Role', fetchtoken.Role);
    var diff = Moment().diff(Moment(token.iat * 1000));
    if (diff > 0) {
        return {
            isValid: true,
            credentials: {
                userData: fetchtoken,
                // scope: fetchtoken.Role
                scope: [...fetchtoken.Role,...fetchtoken.Permissions]
            }
        };
    }
    return {isValid: false};
};

exports.convertToUTC = (date, offset) => {
    console.log(date);
    let utcDate = Moment(date).utcOffset(offset, true);
    console.log(utcDate);
    return utcDate;
};

exports.signToken = (tokenData) => {
    return Jwt.sign({
        data: encrypt(JSON.stringify(tokenData))
    }, this.privateKey);
};

exports.headers = (authorized) => {
    let Globalheaders = {
        language    : Joi.string().optional().default(process.env.DEFAULT_LANGUANGE_CODE),
        utcoffset   : Joi.string().optional().default(0),
        timezone    :  Joi.string().optional().default(null)
    };
    if (authorized) {
        _.assign(Globalheaders, {authorization: Joi.string().required().description("Token to identify user who is performing the action")});
    }
    return Globalheaders;
};

exports.sendOTP = async (phoneNumber) => {
    return {phoneNumber: phoneNumber, pinId: process.env.MASTER_OTP};
};

exports.generateCode = (requestedlength) => {
    const char = "1234567890"; // Random Generate Every Time From This Given Char
    const length = typeof requestedlength != "undefined" ? requestedlength : 4;
    let randomvalue = "";
    for (let i = 0; i < length; i++) {
        const value = Math.floor(Math.random() * char.length);
        randomvalue += char.substring(value, value + 1).toUpperCase();
    }
    return randomvalue;
};

exports.FailureError = (err, req) => {
    const updatedError = err;
    updatedError.output.payload.message = [];
    let customMessages = {};
    if (err.isJoi && Array.isArray(err.details) && err.details.length > 0) {
        err.details.forEach((error) => {
            customMessages[error.context.label] = req.i18n.__(error.message);
        });
    }
    delete updatedError.output.payload.validation;
    updatedError.output.payload.error = req.i18n.__("BAD_REQUEST");
    console.log("err.details.type", err.details);
    if (err.details[0].type === "string.email") {
        updatedError.output.payload.message = req.i18n.__("PLEASE_ENTER_A_VALID_EMAIL");
    } else {
        updatedError.output.payload.message = req.i18n.__("ERROR_WHILE_VALIDATING_REQUEST");
    } updatedError.output.payload.errors = customMessages;
    return updatedError;
};

exports.generateError = (req, type, message, err) => {
    switch (type) {
        case 500:
            error = Boom.badImplementation(message);
            error.output.payload.error = req.i18n.__("INTERNAL_SERVER_ERROR");
            error.output.payload.message = req.i18n.__(message);
            error.output.payload.errors = err;
            console.log(err);
            break;
        case 400:
            error = Boom.badRequest(message);
            error.output.payload.error = req.i18n.__("BAD_REQUEST");
            error.output.payload.message = req.i18n.__(message);
            error.output.payload.errors = err;
            break;
        case 401:
            error = Boom.unauthorized(message);
            error.output.payload.error = req.i18n.__("UNAUTHORIZED_REQUEST");
            error.output.payload.message = req.i18n.__(message);
            error.output.payload.errors = err;
            break;
        case 403:
            error = Boom.forbidden(message);
            error.output.payload.error = req.i18n.__("PERMISSION_DENIED");
            error.output.payload.message = req.i18n.__(message);
            error.output.payload.errors = err;
            break;
        default:
            error = Boom.badImplementation(message);
            error.output.payload.error = req.i18n.__("UNKNOWN_ERROR_MESSAGE");
            error.output.payload.message = req.i18n.__(message);
            error.output.payload.errors = err;
            break;
    }
    return error;
};

exports.getTotalPages = async (records, perpage) => {
    let totalPages = Math.ceil(records / perpage);
    return totalPages;
};


exports.verifyToken = async (token) => {
    let data = await Jwt.verify(token, this.privateKey);
    return decrypt(data.data);
};

exports.isFirstLogin = async (userId) => { // console.log('userId',userId)
    const profileExist = await Models.UserProfile.findOne({where: {
            userId
        }});
    if (profileExist) 
        return false;
     else 
        return true;
    

};

exports.decrypt = (text) => {
    let decipher = crypto.createDecipheriv(process.env.ALGORITHM, process.env.PRIVATE_KEY, process.env.IV);
    let decrypted = decipher.update(text, "hex", "utf8");
    decrypted = decrypted + decipher.final("utf8");
    return decrypted;
};

exports.encrypt = (text) => {
    let cipher = crypto.createCipheriv(process.env.ALGORITHM, process.env.PRIVATE_KEY, process.env.IV);
    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted = encrypted + cipher.final("hex");
    return encrypted;
};

exports.createRequest = async (url, method, payload,headers={}) => {
    try {
        if(process.env.NODE_ENV==='test')
        {
            return {}
        }
        let saveReq     =   await Models.Request.create({url, method, payload,headers});
        let result      =   await fatchData(url, method, payload,headers);
        if (result.status) 
        {
            await saveReq.update({sucess: Constants.STATUS.ACTIVE});
            return result.data
        }
        else{
            if(result.data.status===500||result.data.status===400)
            {
                await saveReq.destroy();
            }
        }

    } catch (error) {
        console.error('error while Creating request', error)
    }
}

const fatchData = async (url, method, payload,headers) => {
    try {
        let res = await Axios({method, url, data:payload,headers})
        if (res.status === 200) {
            return {status:true,data:res}
        }
        else{
            return {status:false,data:res}
        }
    } catch (error) 
    {
        console.error('Error While sending Request',error)
         return {status:false,data:error}
    }
}

exports.retryRequests = async () => {
    try {
        console.log('-------------------------------- Executing Requests of database ------------------------------')
        let requestData = await Models.Request.findAll({
            where: {
                sucess: Constants.STATUS.INACTIVE
            }
        });
        if (requestData.length > 0) { 
            for (const iterator of requestData) {
                let result = await fatchData(iterator.url, iterator.method, iterator.payload,iterator.headers);
                if (result.status) {
                    await iterator.update({
                        sucess: Constants.STATUS.ACTIVE
                    });
                }
            }
        }
    } catch (error) {
        console.log('error while retrying Requests', error);
    }
}

exports.updateAttachmentStatus=async(data, transaction)=>{
    if(data.length>0)
    {
        let payload={
            data:data
        }

        await Webhook.updateAttachmentService(payload, transaction);
    }
}

exports.DgStoreRequest=async(url)=>{
    try{
        console.log('Dg Store data url',url)
        let res=await Axios({
            method: 'get',
            baseURL:process.env.DG_STORE_BASE_URL,
            url,
            headers:{
                'X-DS-API-KEY': process.env.DG_STORE_API_KEY
            }});
            console.log(res?.data, "====== res digi")
        return {success:true,data:res?.data}
    }
    catch(error)
    {
        console.log(error, " -========== diddiwdidid")
        return {success:false,error:error.toString()}
    }
}