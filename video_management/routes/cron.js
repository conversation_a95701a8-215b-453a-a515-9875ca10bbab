const CronController = require("../controllers/cronController.js");
const Joi = require("joi");

module.exports = 
[
    {
        method  : "GET",
		path    : "/cron/fiveMinuteCron",
		options :
		{
			handler     : CronController.updateServices,
			description : "Five MIn Cron",
			notes       : "Five MIn Cron",
			tags        : ["api", "Cron"],
			auth        : false,
			validate    :
			{
				headers : Joi.object
				({
					"language"      : Joi.string().default("en")
				}).options
				({
					"allowUnknown"  : true
				}),
				validator : Jo<PERSON>
			}
		}
    },
	{
        method  : "GET",
		path    : "/cron/oneMinuteCron",
		options :
		{
			handler     : CronController.updateVimeoLink,
			description : "One MIn Cron",
			notes       : "One MIn Cron",
			tags        : ["api", "Cron"],
			auth        : false,
			validate    :
			{
				headers : Joi.object
				({
					"language"      : Joi.string().default("en")
				}).options
				({
					"allowUnknown"  : true
				}),
				validator : <PERSON><PERSON>
			}
		}
    }
]