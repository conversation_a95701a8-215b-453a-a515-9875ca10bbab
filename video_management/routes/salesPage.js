const Joi                 = require("joi").extend(require("@joi/date"));
const SalesPageController = require("../controllers/salesPageController.js");

const salesPageModel = Joi.object({
    id            : Joi.number().integer().required(),
    videoId       : Joi.number().min(0).integer().example("5").required(),
	videoLink     : Joi.string().trim().example("asdf.com").required(),
	title         : Joi.string().trim().example("Title").required(),
	subTitle      : Joi.string().trim().example("Sub Title").required(),
    description   : Joi.string().trim().example("Description").required(),
    staticContent : Joi.string().trim().example("<html>").required()
}).label("SalesPage");

module.exports = [
    {
        method  : "POST",
		path    : "/salesPage",
		options : {
			handler     : SalesPageController.create,
			description : "Add new Sales Page",
			notes       : "Create sales page for particular topic",
			tags        : ["api", "Sales"],
			auth        : {strategy:"jwt", scope : ["admin", "self_practice"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				payload : salesPageModel.keys({
					id  : Joi.number().integer().min(1).example(1).forbidden()
				}).options({ presence: 'required' }),
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
    },
    {
        method  : "GET",
		path    : "/salesPage",
		options : {
			handler     : SalesPageController.get,
			description : "Gets the Sales Page Listing",
			notes       : "Get sales page listing",
			tags        : ["api", "Sales"],
			auth        : false,
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query : {
					pageNumber       : Joi.number().integer().example(1).optional().default(1),
					limit            : Joi.number().integer().example("13 (optional)").default(10).optional(),
					orderByValue     : Joi.string().valid('ASC', 'DESC').optional().default('ASC'),
                    orderByParameter : Joi.string().valid('createdAt', 'videoId').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
    },
	{
        method  : "GET",
		path    : "/salesPage/getById",
		options : {
			handler     : SalesPageController.getById,
			description : "Gets the Sales Page Details",
			notes       : "Get sales page Details",
			tags        : ["api", "Sales"],
			auth        : {strategy:"jwt"},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query : {
					videoId : Joi.number().min(0).integer().example("5").required(),
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
    },
	{
        method  : "DELETE",
		path    : "/salesPage",
		options : {
			handler     : SalesPageController.delete,
			description : "Delete Sales Page",
			notes       : "Delete sales page for particular topic",
			tags        : ["api", "Sales"],
			auth        : {strategy:"jwt", scope : ["admin", "self_practice"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query : Joi.object().keys({
					id      : Joi.number().min(1).integer(),
					videoId : Joi.number().min(1).integer()
				}).xor("id", "videoId"),
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
    }
]