const Joi               = require("joi").extend(require("@joi/date"));
const webhookController = require("../controllers/webhookController.js");

const webhookUserModel = Joi.object({
    userId          : Joi.number().integer(),
    firstName       : Joi.string().trim(),
    lastName        : Joi.string().trim(),
    title           : Joi.string().trim(),
    profilePhotoUrl : Joi.string().trim(),
    profilePhotoId  : Joi.number().integer(),
	userObject: Joi.object().optional().allow(null)
}).label("User_Webhook");

module.exports = [
    {
		method  : "POST",
		path    : "/webhook/user",
		options : {
			handler     : webhookController.upsertUser,
			description : "Add and update User",
			notes       : "Add and update User",
			tags        : ["api", "Webhook"],
			auth        : false,
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				payload   : webhookUserModel.keys().options({ presence: 'required' }),
				validator : Joi
			}
		}
	},
    {
		method  : "POST",
		path    : "/webhook/digistore",
		options : {
			handler     : webhookController.digistoreWebhook,
			description : "Add and update User",
			notes       : "Add and update User",
			tags        : ["api", "Webhook"],
			auth        : false,
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				//payload   : webhookUserModel.keys().options({ presence: 'required' }),
				validator : Joi
			}
		}
	},

]