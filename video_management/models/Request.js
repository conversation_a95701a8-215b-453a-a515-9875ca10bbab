module.exports = (sequelize, DataTypes, Deferrable, Model) => 
{
	class Request extends Model { }

	Request.init(
	{
		axiosObj : { 
            type      : DataTypes.JSON, 
            allowNull : false
        },
        sucess   : {
            type         : DataTypes.INTEGER, 
            defaultValue : 0
        }
	},
	{
		sequelize,                  // We need to pass the connection instance
		modelName   : "Request",    // We need to choose the model name
		paranoid    : true,         // This means it will soft delete the record
		underscored : true,
		tableName   : 'vidMgt_requests'
	});

	return Request;
}