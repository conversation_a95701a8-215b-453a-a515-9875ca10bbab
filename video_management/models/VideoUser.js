module.exports = (sequelize, DataTypes, Deferrable, Model) => 
{
	class VideoUser extends Model { }

	VideoUser.init(
	{
		userId            : {
            type          : DataTypes.INTEGER,
			allowNull     : false,
        },
		videoId           : {
			type          : DataTypes.INTEGER,
			allowNull     : false,
            references    : {
				model     : "vidMgt_videos",
				key       : "id"
			}
		},
		amountWatched     : {
			type          : DataTypes.DECIMAL(10, 2),
			allowNull     : false,
		},
		totalLength       : {
			type          : DataTypes.DECIMAL(10, 2),
			allowNull     : false,
		},
		percentageWatched : {
			type          : DataTypes.VIRTUAL,
			get() {
				return (+this.amountWatched / +this.totalLength) * 100
			},
			set(value) {
				throw new Error("Do not try to set the `percentage_watched` value!!")
			}
		},
		rating            : {
			type          : DataTypes.INTEGER,
			allowNull     : true,
		},
		comment           : {
			type          : DataTypes.TEXT("long"),
			allowNull     : true,
			defaultValue  : null,
		}
	},
	{
		sequelize,                  // We need to pass the connection instance
		modelName   : "VideoUser",  // We need to choose the model name
		paranoid    : true,         // This means it will soft delete the record
		underscored : true,
		tableName   : 'vidMgt_video_users',
		indexes : [
			{
				fields : ["user_id", "video_id"],
				unique : true
			}
		]
	});

	VideoUser.associate = (Models) =>
	{
		Models["VideoUser"].belongsTo(Models["Video"] , { foreignKey: "videoId" });
		Models["VideoUser"].belongsTo(Models["User"]  , { foreignKey: "userId", targetKey: "userId"});
	}

	return VideoUser;
}