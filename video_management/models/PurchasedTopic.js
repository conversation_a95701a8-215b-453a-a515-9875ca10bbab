module.exports = (sequelize, DataTypes, Deferrable, Model) => 
{
	class PurchasedTopic extends Model { }

	PurchasedTopic.init(
	{
		userId            : {
            type          : DataTypes.INTEGER,
			allowNull     : false,
        },
		videoId           : {
			type          : DataTypes.INTEGER,
			allowNull     : false,
            references    : {
				model     : "vidMgt_videos",
				key       : "id"
			}
		},
		purchaseDate      : {
			type          : DataTypes.DATE,
            allowNull     : true
		},
		startingDate      : {
			type          : DataTypes.DATE,
            allowNull     : true
		},
		endDate      : {
			type          : DataTypes.DATE,
            allowNull     : true
		},
		hasPassed         : {
			type          : DataTypes.INTEGER,
			allowNull     : true,
            defaultValue  : 0
		},
		activeSubscription: { type: DataTypes.INTEGER, allowNull: true, defaultValue: null },
		status: { type: DataTypes.INTEGER, defaultValue: 0 },
		isShopProduct: { type: DataTypes.INTEGER, defaultValue: 0 },
		parentId: {type: DataTypes.INTEGER, allowNull: true, defaultValue: null}
	},
	{
		sequelize,                       // We need to pass the connection instance
		modelName   : "PurchasedTopic",  // We need to choose the model name
		paranoid    : true,              // This means it will soft delete the record
		underscored : true,
		tableName   : 'vidMgt_purchased_topics'
	});

	PurchasedTopic.associate = (Models) =>
	{
		Models["PurchasedTopic"].belongsTo(Models["Video"] , { foreignKey: "videoId" });
		Models["PurchasedTopic"].belongsTo(Models["User"]  , { foreignKey: "userId", targetKey: "userId"});
	}

	return PurchasedTopic;
}