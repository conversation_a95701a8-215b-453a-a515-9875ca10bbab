require('dotenv').config()

module.exports = (sequelize, DataTypes, Deferrable, Model) => 
{
	class User extends Model { }

	User.init(
	{
		userId            : {
			type          : DataTypes.INTEGER,
			allowNull     : false,
			unique        : true
		},
		firstName         : { 
			type          : DataTypes.STRING,
			defaultValue  : null
		},
		lastName          : {
			type          : DataTypes.STRING,
			defaultValue  : null
		},
		title             : {
			type          : DataTypes.STRING,
			defaultValue  : null
		},
		profilePhotoUrl   : {
			type          : DataTypes.STRING,
			allowNull     : true,
			get() {
				const rawValue = this.getDataValue("profilePhotoUrl");

				if(rawValue)
				{
					return process.env.IMAGE_BASE_URL + rawValue;
				}
				else
				{
					return null;
				}
			}
		},
		profilePhotoId    : {
			type          : DataTypes.STRING,
			allowNull     : true,
		},
		userObject: { type: DataTypes.JSON, allowNull: true, defaultValue: null }
	},
	{
		sequelize,            // We need to pass the connection instance
		modelName   : "User", // We need to choose the model name
		paranoid    : true,   // This means it will soft delete the record
		underscored : true,
		tableName   : "vidMgt_users"
	});

	User.associate = (Models) =>
	{
		Models["User"].hasMany(Models["VideoUser"]      , { foreignKey: "userId" , sourceKey: "userId"});
		Models["User"].hasMany(Models["Order"]          , { foreignKey: "userId" , sourceKey: "userId"});
		Models["User"].hasMany(Models["PurchasedTopic"] , { foreignKey: "userId" , sourceKey: "userId"});
		Models["User"].hasMany(Models["VideoContent"]   , { foreignKey: "userId" , sourceKey: "userId"});
		Models["User"].hasMany(Models["Attachment"]     , { foreignKey: "userId" , sourceKey: "userId"});
	}

	return User;
}