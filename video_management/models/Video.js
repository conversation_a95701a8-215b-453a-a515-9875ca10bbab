module.exports = (sequelize, DataTypes, Deferrable, Model) => 
{
	class Video extends Model { }

	Video.init(
	{
		isTopic           : {
			type          : DataTypes.INTEGER,
			allowNull     : false,
			defaultValue  : 0 
		},
		parentId          : {
			type          : DataTypes.INTEGER,
			allowNull     : true,
			references    : {
				model     : "vidMgt_videos",
				key       : "id"
			}
		},
		topicId           : {
			type          : DataTypes.INTEGER,
			allowNull     : true,
			references    : {
				model     : "vidMgt_videos",
				key       : "id"
			}
		},
		price             : {
			type          : DataTypes.INTEGER,
			allowNull     : true,
			defaultValue  : null
		},
        status            : {
            type          : DataTypes.INTEGER,
			allowNull     : false
        },
		order             : {
			type          : DataTypes.STRING,
			allowNull     : true,
		},
		isFree            : {
			type          : DataTypes.INTEGER,
			allowNull     : false,
			defaultValue  : 0
		},
		accessibleDate    : {
			type          : DataTypes.DATE,
			allowNull     : true
		},
		noOfDays          : {
			type          : DataTypes.INTEGER,
			allowNull     : true
		},
		topicType         : {
			type          : DataTypes.ENUM,
			allowNull     : true,
			defaultValue  : null,
			values        : ["Seminars", "KUBYstudy", "Trainings", "ShopProduct"]
		},
		semester          : {
			type          : DataTypes.INTEGER,
			allowNull     : true,
		},
		digistoreId       : {
			type          : DataTypes.INTEGER,
			allowNull     : true,
			defaultValue  : null
		},
		digistoreIdString       : {
			type          : DataTypes.STRING,
			allowNull     : true,
			defaultValue  : null
		},
		salesPageLink     : {
			type          : DataTypes.STRING,
			allowNull     : true,
		},
		isSubscriptionBased: {
			type: DataTypes.INTEGER,
			defaultValue: 0
		},
		sortOrder: { 
			type: DataTypes.DECIMAL(10,3),
			allowNull: true
		}
	},
	{
		sequelize,              // We need to pass the connection instance
		modelName   : "Video",  // We need to choose the model name
		paranoid    : true,     // This means it will soft delete the record
		underscored : true,
		tableName   : "vidMgt_videos"
	});


	Video.associate = (Models) =>
	{
		Models["Video"].belongsTo(Models["Video"]        , { foreignKey: "parentId" });
		Models["Video"].hasMany(Models["Video"]          , { foreignKey: "parentId" });
		Models["Video"].belongsTo(Models["Video"]        , { foreignKey: "topicId"  });
		Models["Video"].hasMany(Models["Video"]          , { foreignKey: "topicId"  });
		Models["Video"].hasMany(Models["VideoUser"]      , { foreignKey: "videoId"  });
		Models["Video"].belongsTo(Models["User"]         , { foreignKey: "userId"   });
		Models["Video"].hasMany(Models["VideoContent"]   , { foreignKey: "videoId"  });
		Models["Video"].hasOne(Models["Attachment"]      , { foreignKey: "videoId" , as : "posterImage"});
		Models["Video"].hasMany(Models["Attachment"]     , { foreignKey: "videoId" , as : "attachments"});
		Models["Video"].hasMany(Models["SalesPage"]      , { foreignKey: "videoId" });
		Models["Video"].hasMany(Models["PurchasedTopic"] , { foreignKey: "videoId" });
		Models["Video"].hasMany(Models["Order"]          , { foreignKey: "videoId" });
		Models["Video"].hasMany(Models["VideoLanguage"]          , { foreignKey: "videoId" });
	}

	return Video;
}