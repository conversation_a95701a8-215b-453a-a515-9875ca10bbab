module.exports = (sequelize, DataTypes, Defer<PERSON>el, Model) => {

    class Attachment extends Model { }

    Attachment.init({
        name           : {
            type       : DataTypes.STRING,
			allowNull  : false,
        },
        attachmentId   : {
            type       : DataTypes.STRING,
			allowNull  : false,
        },
        attachmentUrl  : {
            type       : DataTypes.STRING,
			allowNull  : false
        },
        userId         : {
            type       : DataTypes.INTEGER,
			allowNull  : false,
        },
        videoId        : {
            type       : DataTypes.INTEGER,
			allowNull  : false,
        },
        mimeType       : {
            type       : DataTypes.STRING,
			allowNull  : false,
        },
        isThumbnail    : {
            type       : DataTypes.INTEGER,
			allowNull  : false,
            defaultValue : 0
        },
        isPoster       : {
            type       : DataTypes.INTEGER,
			allowNull  : false,
            defaultValue : 0
        }
    }, {
		sequelize,                   // We need to pass the connection instance
		modelName   : "Attachment",  // We need to choose the model name
		paranoid    : true,          // This means it will soft delete the record
		underscored : true,
        tableName   : "vidMgt_attachments"
	});

    Attachment.associate = (Models) =>
	{
		Models["Attachment"].belongsTo(Models["User"] , { foreignKey: "userId" , targetKey: "userId"});
        Models["Attachment"].belongsTo(Models["Video"] , { foreignKey: "videoId" , as : "posterImage"});
        Models["Attachment"].belongsTo(Models["Video"] , { foreignKey: "videoId" , as : "attachments"});
	}

    return Attachment;
}