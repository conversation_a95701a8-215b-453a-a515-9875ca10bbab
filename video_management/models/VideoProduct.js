"use strict";
module.exports = (sequelize, DataTypes) => {
    let VideoProduct = sequelize.define(
      "VideoProduct",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        digistoreId: { type: DataTypes.INTEGER, defaultValue: null },
        videoId: { type: DataTypes.INTEGER, defaultValue: null },
        status: { type: DataTypes.INTEGER, defaultValue: 1 },
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "vidMgt_video_products"
      }
    );

    VideoProduct.associate = (models) => {
      // Language.belongsToMany(models.User, { through: 'prdmng_languages'})
    };


    return VideoProduct;
};  
