module.exports = (sequelize, DataTypes, Deferrable, Model) => 
{
	class VideoContent extends Model { }

	VideoContent.init(
	{
		name              : {
			type          : DataTypes.STRING,
			allowNull     : false,
		},
		userId            : {
            type          : DataTypes.INTEGER,
			allowNull     : false,
        },
		audioLink         : {
			type          : DataTypes.STRING,
			allowNull     : true
		},
        videoLink         : {
			type          : DataTypes.STRING,
			allowNull     : true
		},
		vimeoStatus       : {
			type          : DataTypes.ENUM,
			allowNull     : false,
			defaultValue  : "0",
			values        : ["0", "1", "2"]
		},
		languageCode      : {
			type          : DataTypes.STRING,
			allowNull     : false,
			defaultValue  : "en"
		},
		shortText         : {
			type          : DataTypes.STRING,
			allowNull     : true
		},
		description       : {
			type          : DataTypes.TEXT('long'),
			allowNull     : true
		},
		videoId           : {
			type          : DataTypes.INTEGER,
			allowNull     : true,
			references    : {
				model     : "vidMgt_videos",
				key       : "id"
			}
		},
		subTitle          : {
			type          : DataTypes.STRING,
			allowNull     : true
		},
		linkArray         : {
			type          : DataTypes.JSON,
			allowNull     : true
		}
	},
	{
		sequelize,                     // We need to pass the connection instance
		modelName   : "VideoContent",  // We need to choose the model name
		paranoid    : true,            // This means it will soft delete the record
		underscored : true,
		tableName   : "vidMgt_video_contents"
	});

	VideoContent.associate = (Models) =>
	{
		Models["VideoContent"].belongsTo(Models["User"]  , { foreignKey: "userId" , targetKey: "userId" });
		Models["VideoContent"].belongsTo(Models["Video"] , { foreignKey: "videoId" });
	}

	return VideoContent;
}