require('dotenv').config()

module.exports = (sequelize, DataTypes, Deferrable, Model) => 
{
	class SalesPage extends Model { }

	SalesPage.init(
	{
		videoId            : {
			type           : DataTypes.INTEGER,
			allowNull      : false
		},
		videoLink         : { 
			type          : DataTypes.STRING,
			defaultValue  : null
		},
		title             : {
			type          : DataTypes.STRING,
			defaultValue  : null
		},
		subTitle          : {
			type          : DataTypes.STRING,
			defaultValue  : null
		},
		description       : {
			type          : DataTypes.TEXT('long'),
			allowNull     : true
		},
        staticContent     : {
            type          : DataTypes.BLOB('long'),
			allowNull     : false
        },
		languageCode      : {
			type          : DataTypes.STRING,
			allowNull     : false,
			defaultValue  : "en"
		}
	},
	{
		sequelize,                 // We need to pass the connection instance
		modelName   : "SalesPage", // We need to choose the model name
		paranoid    : true,        // This means it will soft delete the record
		underscored : true,
		tableName   : "vidMgt_sales_pages"
	});

	SalesPage.associate = (Models) =>
	{
		Models["SalesPage"].belongsTo(Models["Video"] , { foreignKey: "videoId" });
	}

	return SalesPage;
}