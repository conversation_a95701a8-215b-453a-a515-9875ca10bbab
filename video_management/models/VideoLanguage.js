"use strict";

const Constants = require('../constants');

module.exports = (sequelize, DataTypes) => {
    let VideoLanguage = sequelize.define(
      "VideoLanguage",
      {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        status: { type: DataTypes.INTEGER, defaultValue: 1 },
        videoId: { type: DataTypes.INTEGER, defaultValue: null },
        languageCode: { type: DataTypes.STRING, allowNull: false }
      },
      {
        paranoid: true,
        underscored: true,
        tableName: "vidMgt_video_languages"
      }
    );

    VideoLanguage.associate = (models) => {
      // Language.belongsToMany(models.User, { through: 'prdmng_languages'})
    };


    return VideoLanguage;
};  
