module.exports = (sequelize, DataTypes, Deferrable, Model) => {
  class DigistoreWebhookDump extends Model {}

  DigistoreWebhookDump.init(
    {
        userId: { type: DataTypes.INTEGER, allowNull: true },
        orderId: { type: DataTypes.STRING, allowNull: true },
        json: { type: DataTypes.JSON, allowNull: false }
    },
    {
        sequelize, // We need to pass the connection instance
        modelName: "DigistoreWebhookDump", // We need to choose the model name
        paranoid: true, // This means it will soft delete the record
        underscored: true,
        tableName: "vidMgt_digistore_webhook_dump"
    }
  );

  DigistoreWebhookDump.associate = (Models) => {
       
  };

  return DigistoreWebhookDump;
};
