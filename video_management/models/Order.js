module.exports = (sequelize, DataTypes, Deferrable, Model) => 
{
	class Order extends Model { }

	Order.init(
	{
		userId            : {
            type          : DataTypes.INTEGER,
			allowNull     : false,
        },
		videoId           : {
			type          : DataTypes.INTEGER,
			allowNull     : false,
            references    : {
				model     : "vidMgt_videos",
				key       : "id"
			}
		},
		data              : {
			type          : DataTypes.JSON,
            allowNull     : true
		},
		mode: {
			type: DataTypes.STRING, allowNull: false, defaultValue: "thankyou-page"
		}
	},
	{
		sequelize,                       // We need to pass the connection instance
		modelName   : "Order",  // We need to choose the model name
		paranoid    : true,              // This means it will soft delete the record
		underscored : true,
		tableName   : 'vidMgt_orders'
	});

	Order.associate = (Models) =>
	{
		Models["Order"].belongsTo(Models["Video"] , { foreignKey: "videoId" });
		Models["Order"].belongsTo(Models["User"]  , { foreignKey: "userId", targetKey: "userId"});
	}

	return Order;
}