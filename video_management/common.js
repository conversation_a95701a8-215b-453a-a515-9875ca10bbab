const crypto = require("crypto");
const Moment = require("moment");
const Axios  = require("axios");

exports.privateKey='ABCDEFGHIJKLMNOPQRSTUVWXYZ123456';
exports.algorithm="aes-256-cbc";
exports.iv='QWERTY1234567890';

decrypt = (text) =>
{
    let decipher  = crypto.createDecipheriv(this.algorithm, this.privateKey, this.iv);
    let decrypted = decipher.update(text, "hex", "utf8");

    decrypted = decrypted + decipher.final("utf8");
    return decrypted;
}

exports.validateToken = async function(token)
{
    try
    {
        fetchtoken = JSON.parse(decrypt(token.data));
        let diff = Moment().diff(Moment(token.iat * 1000));

        if (diff > 0) {
            return {
                isValid     : true,
                credentials : {
                    userData : fetchtoken,
                    scope    : [...fetchtoken.Role,...fetchtoken.Permissions]
                }
            };
        }

        return {isValid: false};
    }
    catch(error)
    {
        return {isValid: false};
    }
}

exports.getTotalPages = async (records, perpage) => {
    let totalPages = Math.ceil(records / perpage);
    return totalPages;
};

exports.DgStoreRequest = async(url) => {
    try
    {
        let res = await Axios({
            method: 'get',
            baseURL:process.env.DG_STORE_BASE_URL,
            url,
            headers:{
                'X-DS-API-KEY': process.env.DG_STORE_API_KEY
            }});

        return {success:true,data:res?.data}
    }
    catch(error)
    {
        return {success:false,error:error.toString()}
    }
}