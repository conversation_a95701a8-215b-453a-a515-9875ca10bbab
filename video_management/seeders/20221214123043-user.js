'use strict';

module.exports = {
	async up (queryInterface, Sequelize) {
		/**
		 * Add seed commands here.
		 *
		 * Example:
		 * await queryInterface.bulkInsert('People', [{
		 *   name: '<PERSON>',
		 *   isBetaMember: false
		 * }], {});
		*/

		await queryInterface.bulkInsert('vidMgt_users', [{
			user_id           : 1,
			first_name        : "Admin",
			last_name         : null,
			created_at        : new Date(),
			updated_at        : new Date(),
			title             : null,
			profile_photo_url : null,
			profile_photo_id  : null 
		}], {});
	},

	async down (queryInterface, Sequelize) {
		/**
		 * Add commands to revert seed here.
		 *
		 * Example:
		 * await queryInterface.bulkDelete('People', null, {});
		 */
	}
};
