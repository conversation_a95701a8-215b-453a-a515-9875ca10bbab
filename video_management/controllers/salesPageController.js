const Models       = require("../models/index.js");
const { Op }       = require("sequelize");
const Common       = require("../common.js");
require('dotenv').config()

module.exports = {
    get     : async(req, h) => {
        try {
            const languageCode = req.headers.language;
            const {limit, orderByValue, orderByParameter, pageNumber} = req.query;

            const salesPage = await Models.SalesPage.findAndCountAll({
                attributes : [[Models.sequelize.literal(`
                JSON_OBJECTAGG(language_code,
                        JSON_OBJECT('id',
                                id,
                                'videoId',
                                video_id,
                                'videoLink',
                                video_link,
                                'title',
                                title,
                                'subTitle',
                                sub_title,
                                'description',
                                description,
                                'staticContent',
                                CONVERT(static_content USING utf8),
                                'languageCode',
                                language_code))
                `), "content"],"video_id",[Models.sequelize.literal(`
                    max(created_at)
                `), "createdAt"]],
                limit      : limit,
            	offset     : (pageNumber - 1) * limit,
				order      : [[orderByParameter, orderByValue]],
                group      : ["video_id"]
            });

            const salesPageContentArr = []

            for(const salesPageContent of salesPage.rows)
            {
                if(salesPageContent.dataValues.content.languageCode && salesPageContent.dataValues.content.languageCode == languageCode)
                {
                    salesPageContentArr.push(salesPageContent.dataValues.content.languageCode);
                }
                else
                {
                    salesPageContentArr.push(salesPageContent.dataValues.content.en);
                }
            }

            salesPage.count = salesPage.count.length;

            const totalPages = await Common.getTotalPages(salesPage.count, limit);

			const responseData = {
				totalPages,
				perPage       : limit,
				salesPageList : salesPageContentArr,
				totalRecords  : salesPage.count,
				baseUrl       : process.env.IMAGE_BASE_URL
			}

			return h.response({success:true,message:req.i18n.__("SALES_PAGE_FETCHED_SUCCESSFULLY"),responseData:responseData}).code(200);
        } catch (error) {
            console.error(error);
            return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
        }
    },
    getById : async(req, h) => {
        try {
            const languageCode = req.headers.language;
            const { videoId } = req.query;
            const userDetails      = req.auth.credentials.userData;

            const salesPage = await Models.SalesPage.findAndCountAll({
                where      : {
                    videoId : videoId
                },
                attributes : [[Models.sequelize.literal(`
                JSON_OBJECTAGG(language_code,
                        JSON_OBJECT('id',
                                SalesPage.id,
                                'videoId',
                                video_id,
                                'videoLink',
                                video_link,
                                'title',
                                title,
                                'subTitle',
                                sub_title,
                                'description',
                                description,
                                'staticContent',
                                CONVERT(static_content USING utf8),
                                'languageCode',
                                language_code))
                `), "content"],"video_id",[Models.sequelize.literal(`
                    max(SalesPage.created_at)
                `), "createdAt"]],
                include    : {
                    model      : Models.Video,
                    attributes : ["digistoreId", "price"]
                },
                group      : ["video_id"]
            });

            let customField = {
                videoId : videoId, 
                userId : userDetails.User.id
            };
            
            customField = JSON.stringify(customField);

            if(salesPage.rows.length < 0)
            {
                return h.response({success:false,message:req.i18n.__("NO_SALES_PAGE_FOUND"),responseData:{}}).code(404);
            }

            const salesPageContentArr = []

            for(const salesPageContent of salesPage.rows)
            {
                const dgStoreUrlObj = await Common.DgStoreRequest(`https://www.digistore24.com/api/call/createBuyUrl/?product_id=${salesPageContent.Video.dataValues.digistoreId}&payment_plan[first_amount]=${salesPageContent.Video.dataValues.price}&tracking[custom]=${customField}`)

                if(salesPageContent.dataValues.content.languageCode && salesPageContent.dataValues.content.languageCode == languageCode)
                {
                    salesPageContent.dataValues.content.languageCode["purchaseUrl"] = dgStoreUrlObj?.data?.data?.url
                    salesPageContentArr.push(salesPageContent.dataValues.content.languageCode);
                }
                else
                {
                    salesPageContent.dataValues.content.en["purchaseUrl"] = dgStoreUrlObj?.data?.data?.url
                    salesPageContentArr.push(salesPageContent.dataValues.content.en);
                }
            }

			return h.response({success:true,message:req.i18n.__("SALES_PAGE_FETCHED_SUCCESSFULLY"),responseData:salesPageContentArr[0]}).code(200);
        } catch (error) {
            console.error(error);
            return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
        }
    },
    create  : async(req, h) => {
        const transaction = await Models.sequelize.transaction();

        try
        {
            const languageCode = req.headers.language;
            const {videoId, videoLink, title, subTitle, description, staticContent} = req.payload;

            // CHECKING IF VIDEO EXISTS 
            const isVideoExists = await Models.Video.findOne({
                where : {
                    isTopic : 1,
                    id      : videoId
                }
            });

            // CHECKING IF SALES PAGE EXISTS 
            const isSalesPageExists = await Models.SalesPage.findOne({
                where : {
                    videoId,
                    languageCode
                }
            });

            if(!isVideoExists)
            {
                await transaction.rollback();
                return h.response({success:false,message:req.i18n.__("INVALID_PAYLOAD"),responseData:{}}).code(400);
            }

            /*
                IF SALES PAGE NOT EXISTS 
                    CHECK IF THE INCOMING LANG HEADER IS NOT EN 
                    THEN ADD SAME CONTENT FOR DEFAULT LANG EN
                ELSE
                    UPDATE THE SALES PAGE CONTENT FOR SPECIFIC LANG
            */
            if(!isSalesPageExists)
            {
                let bulkCreate = [];

                bulkCreate.push({videoLink, title, subTitle, description, staticContent, videoId, languageCode})

                if(languageCode !== "en")
                {
                    const isDefaultSalesPageExists = await Models.SalesPage.findOne({
                        where : {
                            videoId,
                            languageCode : "en"
                        }
                    });

                    if(!isDefaultSalesPageExists)
                    {
                        bulkCreate.push({videoLink, title, subTitle, description, staticContent, videoId, languageCode : "en"})
                    }
                }

                const salesPage = await Models.SalesPage.bulkCreate(bulkCreate, {transaction});

                await transaction.commit();
                return h.response({success:true,message:req.i18n.__("SALES_PAGE_CREATED_SUCCESSFULLY"),responseData:salesPage}).code(201);
            }
            else
            {
                await isSalesPageExists.update({
                    videoLink, title, subTitle, description, staticContent
                }, {transaction});

                await transaction.commit();
                return h.response({success:true,message:req.i18n.__("SALES_PAGE_UPDATED_SUCCESSFULLY"),responseData:{}}).code(204);
            }
        }
        catch (error)
        {
            console.error(error);

            await transaction.rollback();
            return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
        }
    },
    delete  : async(req, h) => {
        const transaction = await Models.sequelize.transaction();

        try
        {
            const {id, videoId} = req.query;

            if(id)
            {
                const isExists = await Models.SalesPage.findOne({
                    where : {
                        id
                    }
                });

                if(isExists)
                {
                    const languageCode = isExists.languageCode;

                    if(languageCode == "en")
                    {
                        const isOtherLangExists = await Models.SalesPage.findOne({
                            where : {
                                videoId      : isExists.videoId,
                                languageCode : {
                                    [Op.ne]  : languageCode
                                }
                            }
                        });

                        if(isOtherLangExists)
                        {
                            await transaction.rollback();
                            return h.response({success:false,message:req.i18n.__("OTHER_LANGUAGE_CONTENT_EXIXTS"),responseData:{}}).code(409);
                        }
                    }

                    await isExists.destroy({transaction});

                    await transaction.commit();
                    return h.response({success:true,message:req.i18n.__("SALES_PAGE_DELETED_SUCCESSFULLY"),responseData:{}}).code(204);
                }
                else
                {
                    await transaction.rollback();
                    return h.response({success:false,message:req.i18n.__("INVALID_PAYLOAD"),responseData:{}}).code(400);
                }
            }
            else
            {
                const isExists = await Models.SalesPage.findOne({
                    where : {
                        videoId
                    }
                });

                if(isExists)
                {
                    await Models.SalesPage.destroy({
                        where : {
                            videoId
                        },
                        transaction
                    });

                    await transaction.commit();
                    return h.response({success:true,message:req.i18n.__("SALES_PAGE_DELETED_SUCCESSFULLY"),responseData:{}}).code(204);
                }
                else
                {
                    await transaction.rollback();
                    return h.response({success:false,message:req.i18n.__("INVALID_PAYLOAD"),responseData:{}}).code(400);
                }
            }
        }
        catch (error)
        {
            console.error(error);

            await transaction.rollback();
            return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
        }
    }
}