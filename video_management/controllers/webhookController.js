const Models = require("../models/index.js");
require('dotenv').config();
const Axios        = require("axios");
const Moment = require("moment");

const confirmPaymentHelper = async(payload, language = "en", mode, transaction) => {
	try{

		const dgStoreOrderId = payload.order_id;
		const checkExistingOrder = await Models.Order.findOne({ where: { 'data.order_id': dgStoreOrderId } });
		if(checkExistingOrder) return { success: true, message: "ORDER_ALREADY_EXISTS", data: checkExistingOrder };

		const dgStoreProductId = payload.product_id;
		if(!dgStoreProductId) return { success: false, message: "INVALID_PRODUCT_ID_PROVIDED_BY_GATEWAY", data: {} };
		
        const videoIdExists = await Models.VideoProduct.findOne({ where: { digistoreId: dgStoreProductId } });
        if(!videoIdExists) {
            await transaction.commit();
            return h.response({success:true,message:req.i18n.__("NO_VIDEO"),responseData:{}}).code(200);
        }

		const topicInfo = await Models.Video.findOne({ where: { id: videoIdExists.videoId } });
        if(!topicInfo) {
            return { success: true, message: "INVALID_VIDEO", data: checkExistingOrder };
        }
		const videoId = topicInfo.id;
		const isSubscriptionBased = topicInfo.isSubscriptionBased;

		let userId = null;
		if(payload?.userId) userId = payload?.userId;
		

		if(userId === null) {
			const data = {
				email: payload.buyer_email, firstName: payload.buyer_first_name,
				lastName: payload.buyer_last_name, existingUserEmailSend: 1
			}
			let responseData = await Axios({
				method: "post", headers: {language: language}, data: data,
				url: `${process.env.USER_ONBOARDING_DOMAIN}/create-dg-user`
			});
			responseData = responseData.data.userId;
			if(responseData) userId = responseData;
		}
		
		let order = await Models.Order.create({ mode: mode, videoId: videoId, userId : userId, isPaid: 1, data: payload }, {transaction});
		if(!order) return { success: false, message: "PAYMENT_NOT_CONFIRMED", data: {} }
		
		let date= Moment(new Date());
        let endDate= Moment(new Date()).add(1, "month");

        const purchaseObject = {
			userId: userId,
			videoId: videoId,
			hasPassed: 0,
			purchase_date: date,
			starting_date: date,
            // endDate: isSubscriptionBased == 1 ? endDate: null,
			activeSubscription: isSubscriptionBased == 1 ? 1 : null,
            status: 1
		}

        if(isSubscriptionBased == 1) {
            purchaseObject["endDate"] = endDate;
        }

		const createdEntry = await Models.PurchasedTopic.create(purchaseObject,{transaction});

        const parentId = createdEntry.id;

		const vidInfo = await Models.Video.findOne({
			where : { id : videoId },
			include : {
				model : Models.VideoContent,
				attributes : ["name", "subTitle", "videoLink"]
			},
			attributes : ["topicType"]
		})

		const userObj = await Models.User.findOne({
			where : { userId : userId }
		});

		let earningHistoryObj = {
			topicId: videoId, data: payload, amount: payload?.amount,
			amountReceived: payload?.earned_amount, userId: userId,
			topicObject: vidInfo, userObject: userObj
		}

		await Axios({
			method: 'post',
			url: `${process.env.USER_ONBOARDING_DOMAIN}/earning-history`,
			headers: {},
			data: earningHistoryObj
		});

		return { success: true, message: "REQUEST_SUCCESSFULL", data: order,  meta: { parentId, userId } }
	}
	catch(error) {
		console.error('Error in Add orders',error)

		return { success: false, message: "ERROR", data: {} }
	}
}

const addonsHelper = async(payload, transaction) => {
	try{
		

        const videoIdExists = await Models.VideoProduct.findOne({ where: { digistoreId: payload.product_id } });
        if(!videoIdExists) {
            await transaction.commit();
            return h.response({success:true,message:req.i18n.__("NO_VIDEO"),responseData:{}}).code(200);
        }

		const topicInfo = await Models.Video.findOne({ where: { id: videoIdExists.videoId } });
		if(!topicInfo) return { success: false, message: "INVALID_PRODUCT_ID_PROVIDED_BY_GATEWAY", data: {} };
		const videoId = topicInfo.id;
		
		const alreadyExists = await Models.PurchasedTopic.findOne({ where: { userId: payload.userId, videoId: videoId } });
		if(alreadyExists) {
			return { success: true, message: "ALREADY_EXISTS", data: {} };
		}

		let date= Moment(new Date());
		let endDate= Moment(new Date()).add(1, "month");
		await Models.PurchasedTopic.create({
			userId: payload?.userId,
			videoId: videoId,
			hasPassed: 0,
			purchaseDate: date,
			startingDate: date,
			endDate: null,
			activeSubscription: null,
			status: 1,
			parentId: payload.parentId
		},{transaction});

		return { success: true, message: "REQUEST_SUCCESSFULL", data: {} }
	}
	catch(error) {
		console.error('Error in Add orders',error)
		return { success: false, message: "ERROR", data: {} }
	}
}

module.exports = 
{
    upsertUser : async(req, h) => {
        const transaction = await Models.sequelize.transaction();

        try {
            await Models.User.upsert({
                ...req.payload
            }, {transaction})

            await transaction.commit();
            return h.response({success:true,message:req.i18n.__("USER_CREATED_SUCCESSFULLY"),responseData:{}}).code(201);
        }
        catch (error) {
            console.error(error);

            await transaction.rollback();
            return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
        }
    },
    digistoreWebhook : async(req, h) => {
        const transaction = await Models.sequelize.transaction();
        try {
            console.log(req.payload, " ================== digistore payload")
            console.log(req.headers, " ================== digistore header");

            // const topicInfo = await Models.Video.findOne({ where: { digistoreId: dgStoreProductId } });
            const digistoreProductId = req.payload.product_id;
            const dgStoreOrderId = req.payload.order_id;
            let refVideoId = null;

            if(!dgStoreOrderId) {
                await transaction.commit();
                return h.response({success:true,message:req.i18n.__("WITHOUT_ORDER_ID"),responseData:{}}).code(200);
            }

			const checkExistingOrder = await Models.Order.findOne({ where: { 'data.order_id': dgStoreOrderId } });

            // if(digistoreProductId) {
            //     const videoInfo = await Models.Video.findOne({ where: { digistoreId: digistoreProductId } });
            //     if(videoInfo) {

            //     }
            // }

            console.log(checkExistingOrder, " =================== checkExistingOrder")
            console.log(checkExistingOrder?.userId, " =================== checkExistingOrder?.userId")
            console.log(checkExistingOrder?.videoId, " =================== checkExistingOrder?.videoId")

            const userId = checkExistingOrder?.userId ? checkExistingOrder?.userId : null;
            const videoId = checkExistingOrder?.videoId ? checkExistingOrder?.videoId : null;

            const purchaseInfo = await Models.PurchasedTopic.findOne({ where: { userId, videoId } });

            let updatedObject = {}
            if(req.payload.event === "on_payment") {

                const newObject = {};
                for (const key in req.payload) {
                    if (key.startsWith('product_id') && key !== "product_id") {
                        newObject[key] = req.payload[key];
                    }
                }

                const videoIdExists = await Models.VideoProduct.findOne({ where: { digistoreId: digistoreProductId } });
                if(!videoIdExists) {
                    await transaction.commit();
                    return h.response({success:true,message:req.i18n.__("NO_VIDEO"),responseData:{}}).code(200);
                }

                let language = "en";
                const videoInfo = await Models.Video.findOne({ where: { id: videoIdExists.videoId } });
                if(!videoInfo) {
                    await transaction.commit();
                    return h.response({success:true,message:req.i18n.__("NO_VIDEO"),responseData:{}}).code(200);
                }
                if(videoInfo.isSubscriptionBased) {
                    refVideoId = videoIdExists.videoId;
                }
                const videoLanguageInfo = await Models.VideoLanguage.findOne({ where: { videoId: videoInfo.id } });
                console.log(videoLanguageInfo, " ======================= videoLanguageInfo")
                if(videoLanguageInfo) language = videoLanguageInfo.languageCode;
                console.log(language, " ========================== language")
                const payload = {
                    order_id: req.payload?.order_id,
                    product_id: req.payload?.product_id,
                    buyer_email: req.payload?.buyer_email,
                    buyer_first_name: req.payload?.buyer_first_name,
                    buyer_last_name: req.payload?.buyer_last_name,
                    amount: req.payload?.amount,
                    earned_amount: req.payload?.amount,
                    userId: null
                }
                const controller = await confirmPaymentHelper(payload, language, "webhook", transaction);
                if(!controller?.success) {
                    await transaction.rollback();
                    return h.response({success: false,message: req.i18n.__(controller.message),responseData: {}}).code(400)
                }


                for(let item in newObject) {

                    const newPayload = {
                        userId: controller?.meta?.userId ? controller?.meta?.userId : null, 
                        parentId: controller?.meta?.parentId ? controller?.meta?.parentId : null, 
                        product_id: newObject[item]
                    }	
    
                    if(newPayload.userId) {
                        let helperController = await addonsHelper(newPayload, transaction);
                        if(!helperController?.success) {
                            // await transaction.rollback();
                            // return h.response({success: false,message: req.i18n.__(helperController.message),responseData: {}}).code(400)
                        }
                    }


                    // const newPayload = {
                    //     userId: controller.meta.userId, parentId: controller.meta.parentId, product_id: newObject[item]
                    // }	
    
                    // console.log(newPayload, " ========================= newPayload")
                    
                    // let helperController = await addonsHelper(newPayload, transaction);
                    // if(!helperController?.success) {
                    //     // await transaction.rollback();
                    //     // return h.response({success: false,message: req.i18n.__(helperController.message),responseData: {}}).code(400)
                    // }
                }



            } else if(userId && req.payload.event === "on_refund") {
                updatedObject["activeSubscription"] = 0;
                updatedObject["status"] = 2;
            } else if(userId && req.payload.event === "on_chargeback") {
                updatedObject["activeSubscription"] = 0;
                updatedObject["status"] = 3;
            } else if(userId && req.payload.event === "on_payment_missed") {
                updatedObject["activeSubscription"] = 0;
                updatedObject["status"] = 4;
            } else if(userId && req.payload.event === "payment_denial") {
                updatedObject["status"] = 5;
            } else if(userId && req.payload.event === "on_rebill_cancelled") {
                updatedObject["activeSubscription"] = 0;
                updatedObject["status"] = 6;
            } else if(userId && req.payload.event === "on_rebill_resumed") {
                updatedObject["activeSubscription"] = 1;
                if(purchaseInfo && purchaseInfo?.endDate) {
                    updatedObject["endDate"] = Moment(purchaseInfo.endDate).add(1, "month");
                }
                updatedObject["status"] = 7;
            } else if(userId && req.payload.event === "last_paid_day") {
                updatedObject["status"] = 1;
                updatedObject["endDate"] = null;
                // updatedObject["activeSubscription"] = 0;
            } else {
                updatedObject["status"] = 0;
            }



            console.log(req.payload.event, " ========== req.payload.event")
            console.log(refVideoId, " ========== refVideoId")
            console.log(req.payload.event === "on_payment", " ========== req.payload.event === on_payment")
            console.log(req.payload.event === "on_payment" && refVideoId, "req.payload.event === on_payment && refVideoId")

            if(req.payload.event === "on_payment" && refVideoId) {
                let endDate= Moment(new Date()).add(1, "month");
                await Models.PurchasedTopic.update({endDate, status: 1}, { where: { userId, videoId: refVideoId }, transaction });
            }

            if(req.payload.event !== "on_payment") {
                await Models.PurchasedTopic.update(updatedObject, { where: { userId, videoId }, transaction });
            }
            await Models.DigistoreWebhookDump.create({ userId, orderId: dgStoreOrderId, json: req.payload }, { transaction });

           await transaction.commit();

            return h.response({success:true,message:req.i18n.__("REQUEST_SUCCESSFULL"),responseData:{}}).code(200);
        }
        catch (error) {
            console.error(error);
            await transaction.rollback();
            return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
        }
    }
}