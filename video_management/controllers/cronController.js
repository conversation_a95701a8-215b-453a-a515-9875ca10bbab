const Models       = require("../models/index.js");
const VideoService = require("../services/video.js");
const Axios        = require("axios");

module.exports = {
    updateServices: async(req, h) => {
        const transaction = await Models.sequelize.transaction();

        try {
            const pendingWebhooks = await Models.Request.findAll({
                where :{
                    sucess : 0
                }
            });

            for(const webhookObj of pendingWebhooks)
            {
                try
                {
                    const axiosRes = await Axios(webhookObj.dataValues.axiosObj).then(async() => {
                        await Models.Request.update({
                            sucess : 1
                        }, {
                            where : {
                                id : webhookObj.dataValues.id
                            },
                            transaction
                        })
                    });
                }
                catch (error)
                {
                    console.log(error);
                }
            }

            await transaction.commit();
            return true;
        }
        catch (error) {
            console.log(error);

            await transaction.rollback();
            return false
        }
    },
    updateVimeoLink: async(req, h) => {
        try {
            const isSuccess = await VideoService.getAuthorizedVideo();

            return isSuccess;
        } catch (error) {
            return false
        }
    }
}