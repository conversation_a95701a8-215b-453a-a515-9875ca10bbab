const {decodeData} = require("../digistore24");
const Models       = require("../models/index.js");
const VideoService = require("../services/video.js");
const Webhook      = require("../services/webhook.js");
const { Op, Model }       = require("sequelize");
const Common       = require("../common.js");
const Axios        = require("axios");
const Moment = require("moment");
// const { addTopicFromExcelSubTopics } = require("../services/video")
require('dotenv').config()

const attachmentAttributes = ["id", ["name", "originalName"],["attachment_url", "path"],"attachmentId", "mimeType", "videoId", "isPoster", "isThumbnail", "createdAt","updatedAt"]


const sendEmail = async(data,code,language="en")=>{
	console.log("========", data, " ======== ")
    try{
        let replacements = {
          orderId: data.orderId, productId: data.productId, amount: data.amount,
          paymentMethod: data.paymentMethod, date: data.date, email: data.email,  
		  firstName: data.firstName, lastName: data.lastName
        };
        let recipients = [data.email]

        const requestObj = {
            url : `${process.env.EMAIL_SERVICE}/email/send`,
            method: "post",
            data: { replacements, priority:'high', code, recipients },
            headers: {language}
        }
        
        let res=await Axios(requestObj).then(async(res)=>{
            console.log('Sucessfully Send Email.......... ')
            console.log(res.data)
            return res.data
        }).catch(async (error) => {
            console.log('hi catch in Email .........',error)
            return {}
        });
        return res
    } catch(error) {
        console.log('Error in Sending email',error)
    }
}

module.exports =
{
	/**
	 * Creates the main heading for the chapters and videos
	 * @param {*} req 
	 * @param {*} h 
	 * @returns 
	 */
	createTitle : async(req, h) => {
		const transaction = await Models.sequelize.transaction();

		try {
			const {name, audioLink, videoLink, shortText, description, isFree, accessibleDate, subTitle, topicType, semester, attachments, price, posterImage, salesPageLink, isSubscriptionBased} = req.payload;
			let noOfDays = req.payload.noOfDays;
			if(noOfDays == "") noOfDays = 0;
			let videoLanguages = req.payload.videoLanguages;
			if(videoLanguages === null) videoLanguages = [];

			let digistoreId = req.payload.digistoreId;
			if(digistoreId) {
				digistoreId = digistoreId.replace(/[^0-9,]+/g, '')
				.replace(/,{2,}/g, ',')
				.replace(/(^,|,$)/g, '');
				console.log(digistoreId)
			}


			console.log(videoLanguages, "====== videoLanguages vs req.payload.videoLanguages =======", req.payload.videoLanguages )

			const languageCode = req.headers.language;
			const userDetails  = req.auth.credentials.userData;
			console.log(userDetails, " ================================== userDetails vid management")
			let formattedAttachmentObjArr = [];

			let data = [];

			let titleObj = {
        		topicId: null, parentId: null, isTopic: 1, status: 1, isSubscriptionBased: isSubscriptionBased || 0,
        		order: null, isFree: isFree, accessibleDate: accessibleDate,
        		noOfDays: noOfDays, topicType: topicType, price: price || null,
        		semester: topicType == "KUBYstudy" ? semester : null,
        		digistoreIdString: digistoreId ? digistoreId : null,
        		VideoContents: [{
					name: name, userId: userDetails.User.id, audioLink: audioLink,
					videoLink: videoLink, languageCode: languageCode, shortText: shortText,
					description: description, subTitle: subTitle ? subTitle : null
				}],
				attachments: [{
					...posterImage, isThumbnail: 0,
					isPoster: 1, userId: userDetails.User.id
				}],
        		salesPageLink
      		};

			data.push({ id: posterImage.attachmentId, status: 1 })

			if(attachments) {
				for(const attachmentDetails of attachments) {
					formattedAttachmentObjArr.push({
						name: attachmentDetails.originalName, attachmentId: attachmentDetails.id,
						attachmentUrl: attachmentDetails.path, userId: userDetails.User.id,
						mimeType: attachmentDetails.mimeType, isThumbnail: 0, isPoster: 0
					});

					data.push({ id: attachmentDetails.id, status: 1 })
				}

				titleObj.attachments = [...titleObj.attachments, ...formattedAttachmentObjArr];
			}

			let reqPayload = {data};
			await Webhook.updateAttachmentService(reqPayload, transaction);

			const addedTitleContent = await VideoService.createVideo([titleObj], transaction);
			console.log(addedTitleContent, " ----------- ")
			console.log(addedTitleContent[0].dataValues.id, " ----------- dataValues.id")
			console.log(addedTitleContent[0].id, " ----------- id")

			if(digistoreId && digistoreId !== "") {
				let ids = digistoreId.split(",");
				for(let item of ids) {
					await Models.VideoProduct.create({ videoId: addedTitleContent[0].dataValues.id, digistoreId: item });
				}
			}

			for(let language of videoLanguages) {
				console.log(language, " =============== language")
				// await Models.VideoLanguage.create( {videoId: id, languageCode: language}, { transaction } )
				await Models.VideoLanguage.create( {videoId: addedTitleContent[0].id, languageCode: language}, { transaction } )
			}

			await transaction.commit();
			return h.response({success:true,message:req.i18n.__("TITLE_CREATED_SUCCESSFULLY"),responseData:addedTitleContent}).code(201);
		}
		catch (error) {
			console.error(error);

			await transaction.rollback();
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	/**
	 * @desc Creates a new Container, Video and Sub Video
	 * @param {*} req 
	 * @param {*} h 
	 * @returns 
	 */
	createVideo: async(req, h) => {
		const transaction = await Models.sequelize.transaction();

		try {
			const userDetails  = req.auth.credentials.userData;
			const videoArray   = req.payload.lessons;
			// const languageCode = req.headers.language;
			const languageCode = "en";
			let data = [];

			for(const videoDetails of videoArray)
			{
				const {name, audioLink, videoLink, shortText, description, accessibleDate, subVideos, topicId, parentId, subTitle, attachments, posterImage, noOfDays} = videoDetails;
				const isValidTopic = await VideoService.validateTopic(topicId, transaction);
				let isValidVideo = false;
				let formattedAttachmentObjArr = [];

				if(parentId)
				{
					isValidVideo = await VideoService.validateVideo(topicId, parentId, transaction);
				}

				if(isValidTopic && ((parentId && isValidVideo) || (!parentId && !isValidVideo)))
				{
					let videoObj = {
						topicId        : topicId,
						parentId       : parentId ? parentId : null,
						isTopic        : 0,
						status         : 1,
						order          : null,
						isFree         : 0,
						accessibleDate : accessibleDate,
						noOfDays       : noOfDays,
						price          : null,
						VideoContents  : [{
							name         : name, 
							userId       : userDetails.User.id,
							audioLink    : audioLink ? audioLink : null,
							videoLink    : videoLink,
							languageCode : languageCode,
							shortText    : shortText,
							description  : description,
							subTitle     : subTitle ? subTitle : null
						}],
						attachments    : posterImage ? [{
							...posterImage,
							isThumbnail : 0,
							isPoster    : 1,
							userId      : userDetails.User.id
						}] : []
					};

					if(posterImage)
					{
						data.push({
							id     : posterImage.attachmentId,
							status : 1
						})
					}

					if(attachments)
					{
						for(const attachmentDetails of attachments)
						{
							formattedAttachmentObjArr.push({
								name          : attachmentDetails.originalName,
								attachmentId  : attachmentDetails.id,
								attachmentUrl : attachmentDetails.path,
								userId        : userDetails.User.id,
								mimeType      : attachmentDetails.mimeType,
								isThumbnail   : 0,
								isPoster      : 0
							})

							data.push({
								id    : attachmentDetails.id,
								status: 1
							})
						}

						videoObj.attachments = [...videoObj.attachments, ...formattedAttachmentObjArr];
					}

					const parentVideo = await VideoService.createVideo([videoObj], transaction);

					if(subVideos)
					{
						let subVideoArr = [];

						for(const subVidDetails of subVideos)
						{
							const parentVideoId = parentVideo[0].id;
							let formattedAttachmentObjArrSubVid = [];

							let videoObj = {
								topicId        : topicId,
								parentId       : parentVideoId,
								isTopic        : 0,
								status         : 1,
								order          : null,
								isFree         : 0,
								accessibleDate : subVidDetails.accessibleDate,
								noOfDays       : subVidDetails.noOfDays,
								price          : null,
								VideoContents  : [{
									name         : subVidDetails.name, 
									userId       : userDetails.User.id,
									audioLink    : subVidDetails.audioLink ? subVidDetails.audioLink : null,
									videoLink    : subVidDetails.videoLink,
									languageCode : languageCode,
									shortText    : subVidDetails.shortText,
									description  : subVidDetails.description,
									subTitle     : subVidDetails.subTitle ? subVidDetails.subTitle : null
								}],
								attachments    : subVidDetails.posterImage ? [{
									...subVidDetails.posterImage,
									isThumbnail : 0,
									isPoster    : 1,
									userId      : userDetails.User.id
								}] : []
							}

							if(subVidDetails.posterImage)
							{
								data.push({
									id     : posterImage.attachmentId,
									status : 1
								})
							}

							if(subVidDetails.attachments)
							{
								for(const attachmentDetails of subVidDetails.attachments)
								{
									formattedAttachmentObjArrSubVid.push({
										name          : attachmentDetails.originalName,
										attachmentId  : attachmentDetails.id,
										attachmentUrl : attachmentDetails.path,
										userId        : userDetails.User.id,
										mimeType      : attachmentDetails.mimeType,
										isThumbnail   : 0,
										isPoster      : 0
									})

									data.push({
										id    : attachmentDetails.id,
										status: 1
									})
								}

								videoObj.attachments = [...videoObj.attachments, ...formattedAttachmentObjArrSubVid];
							}

							subVideoArr.push(videoObj);
						}

						await VideoService.createVideo(subVideoArr, transaction);
					}
				}
				else
				{
					throw "INVALID_TOPIC_OR_PARENT_ID";
				}
			}

			if(data.length)
			{
				let reqPayload = {data};
				await Webhook.updateAttachmentService(reqPayload, transaction);
			}

			await transaction.commit();
			return h.response({success:false,message:req.i18n.__("VIDEO_CREATED_SUCCESSFULLY"),responseData:{}}).code(201);
		}
		catch (error) {
			console.error(error);

			await transaction.rollback();

			if(error == "INVALID_TOPIC_OR_PARENT_ID")
			{
				return h.response({success:false,message:req.i18n.__(error),responseData:{}}).code(401);
			}
			else
			{
				return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
			}
		}
	},

	/**
	 * @desc Retrives a new Container, Topic and level 0 Video
	 * @param {*} req 
	 * @param {*} h 
	 * @returns 
	 */
	readTopic: async(req, h) => {
		try {

			const userDetails = req.auth.credentials ? req.auth.credentials.userData : null;

			const { limit, pageNumber, orderByParameter, orderByValue, type } = req.query;
			const languageCode   = req.headers.language;

			const whereClause = {
				isTopic  : 1,
				status   : 1
			}

			switch (type) {
				case "Seminars":
					{
						const responseData = await VideoService.getMultiLingual({...whereClause, topicType : "Seminars"}, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails);
			
						return h.response({success:true,message:req.i18n.__("TOPICS_FETCHED_SUCCESSFULLY"),responseData}).code(200);
						break;
					}
				case "KUBYstudy":
					{
						const responseData = await VideoService.getMultiLingual({...whereClause, topicType : "KUBYstudy"}, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails);
			
						return h.response({success:true,message:req.i18n.__("TOPICS_FETCHED_SUCCESSFULLY"),responseData}).code(200);
						break;
					}
				default :
				{
					const responseDataSeminar   = await VideoService.getMultiLingual({...whereClause, topicType : "Seminars"}, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails, true);
					const responseDataKubyStudy = await VideoService.getMultiLingual({...whereClause, topicType : "KUBYstudy"}, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails, true);
					let responseDataTraining = null;
					let shopProduct = null;

					if(userDetails?.User?.id) {
						const purchasedSeminar = await Models.Video.findOne({ 
							where: { topicType: {[Op.or]: ["Seminars", "Trainings"]} }, 
							// where: { topicType: "Seminars" }, 
							include: [
								{model: Models.PurchasedTopic, required: true, where: { userId: userDetails?.User?.id }},
								{
									required   : true,
									model      : Models.VideoLanguage,
									where: { languageCode:languageCode  },
									// attributes: ["id"]
								},
							] 
						});



						console.log(" ================ purchasedSeminar =============", purchasedSeminar)



						if(purchasedSeminar) {
							responseDataTraining = await VideoService.getMultiLingual({...whereClause, topicType : "Trainings"}, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails, true);
						}
					}
					// for product
					if(userDetails?.User?.id) {
						let purchasedSeminar = await Models.Video.findAll({ 
							where: { topicType: "ShopProduct" }, 
							include: [
								{model: Models.PurchasedTopic, required: true, where: { userId: userDetails?.User?.id }}
							] 
						});

						purchasedSeminar = JSON.parse(JSON.stringify(purchasedSeminar))
						const videos = [];
						for(let item of purchasedSeminar) {
							videos.push(item.id);
						}



						if(videos.length > 0) {
							whereClause["id"] = videos;
							shopProduct = await VideoService.getMultiLingual({...whereClause, topicType : "ShopProduct"}, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails, false);
						}
					}
					const responseData = {
						"Seminars"  : responseDataSeminar,
						"KUBYstudy" : responseDataKubyStudy,
						"Trainings": responseDataTraining !== null ? responseDataTraining : null,
						"shopProduct": shopProduct !== null ? shopProduct : null,
					}
		
					return h.response({success:true,message:req.i18n.__("TOPICS_FETCHED_SUCCESSFULLY"),responseData}).code(200);
					break;
				}
			}
		}
		catch (error) {
			console.error(error);
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	readPurchasedTopic: async(req, h) => {
		try {

			const userDetails = req.auth.credentials ? req.auth.credentials.userData : null;

			const { limit, pageNumber, orderByParameter, orderByValue } = req.query;
			const languageCode   = req.headers.language;

			const whereClause = { isTopic  : 1, status   : 1 }
			let responseDataTraining = null;
			let responseDataSeminar = null;
			let responseDataKubyStudy = null;
			let shopProduct = null;

			if(userDetails?.User?.id) {
				let purchasedSeminar = await Models.Video.findAll({
					where: { topicType: "Seminars" }, 
					include: [
						{model: Models.PurchasedTopic, required: true, where: { userId: userDetails?.User?.id }}
					] 
				});

				purchasedSeminar = JSON.parse(JSON.stringify(purchasedSeminar))
				const videos = [];
				for(let item of purchasedSeminar) {
					videos.push(item.id);
				}


				if(videos.length > 0) {
					let seminarWhere = {...whereClause, id: videos, topicType: "Seminars"}
					// whereClause["id"] = videos;

					responseDataSeminar = await VideoService.getMultiLingual(seminarWhere, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails, false);
				}
			}

			if(userDetails?.User?.id) {
				let purchasedSeminar = await Models.Video.findAll({
					where: { topicType: "KUBYstudy" }, 
					include: [
						{model: Models.PurchasedTopic, required: true, where: { userId: userDetails?.User?.id }}
					] 
				});

				purchasedSeminar = JSON.parse(JSON.stringify(purchasedSeminar))
				const videos = [];
				for(let item of purchasedSeminar) {
					videos.push(item.id);
				}


				if(videos.length > 0) {
					let seminarWhere = {...whereClause, id: videos, topicType: "KUBYstudy"}
					// whereClause["id"] = videos;

					responseDataKubyStudy = await VideoService.getMultiLingual(seminarWhere, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails, false);
				}
			}

			

					if(userDetails?.User?.id) {
						const purchasedSeminar = await Models.Video.findOne({ 
							where: { topicType: "Seminars" }, 
							include: [
								{model: Models.PurchasedTopic, required: true, where: { userId: userDetails?.User?.id }},
								{
									required   : true,
									model      : Models.VideoLanguage,
									where: { languageCode:languageCode  },
									// attributes: ["id"]
								},
							] 
						});


						if(purchasedSeminar) {
							responseDataTraining = await VideoService.getMultiLingual({...whereClause, topicType : "Trainings"}, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails, true);
						}
					}
					// for product
					if(userDetails?.User?.id) {
						let purchasedSeminar = await Models.Video.findAll({
							where: { topicType: "ShopProduct" }, 
							include: [
								{model: Models.PurchasedTopic, required: true, where: { userId: userDetails?.User?.id }}
							] 
						});

						purchasedSeminar = JSON.parse(JSON.stringify(purchasedSeminar))
						const videos = [];
						for(let item of purchasedSeminar) {
							videos.push(item.id);
						}


						if(videos.length > 0) {
							whereClause["id"] = videos;
							shopProduct = await VideoService.getMultiLingual({...whereClause, topicType : "ShopProduct"}, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails, false);
						}
					}
					const responseData = {
						"Seminars"  : responseDataSeminar,
						"KUBYstudy" : responseDataKubyStudy,
						"Trainings": responseDataTraining,
						"shopProduct": shopProduct,
					}
		
					return h.response({success:true,message:req.i18n.__("TOPICS_FETCHED_SUCCESSFULLY"),responseData}).code(200);
				
			
		}
		catch (error) {
			console.error(error);
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	/**
	 * @desc Retrives a new Chapter, level 1 Video
	 * @param {*} req 
	 * @param {*} h 
	 * @returns 
	 */
	readChapters: async(req, h) => {
		try {
			const userDetails   = req.auth.credentials.userData;
			const languageCode  = req.headers.language;
			const { limit, pageNumber, topicId, orderByParameter, orderByValue } = req.query;

			const whereClause = {
				isTopic  : 0,
				status   : 1,
				topicId  : topicId,
				parentId : null
			}


			if(!userDetails.Role.includes("admin")) {
				const videoInfo = await Models.Video.findOne({ where: { id: topicId } });
				if(!videoInfo) {
					return h.response({success:false,message:req.i18n.__("INVALID_VIDEO_ID_PROVIDED"),responseData:{}}).code(400);
				}

				const checkAccess = await Models.PurchasedTopic.findOne({ where: {userId: userDetails.User.id, videoId: topicId}, order: [["createdAt", "DESC"]] });

				if(!checkAccess) {
					return h.response({success:false,message:req.i18n.__("ACCESS_DENIED"),responseData:{}}).code(400);
				}

				if(checkAccess.status === 0) {
					return h.response({success:false,message:req.i18n.__("SUBSCRIPTION_REVOKED_BY_ADMIN"),responseData:{}}).code(400);
				}

				if(videoInfo.isSubscriptionBased === 1) {
					if(Moment(checkAccess.endDate).add(2,"days") <  Moment()) {
						return h.response({success:false,message:req.i18n.__("SUBSCRIPTION_ENDED"),responseData:{}}).code(400);
					}
				}
			}

			const topicDetails = await VideoService.getTopicDetails(topicId, languageCode, userDetails);

			const responseData = await VideoService.getMultiLingual(whereClause, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails);
			responseData["topicDetails"] = topicDetails;

			for(const vidDetails of responseData.topicList)
			{
				const parentId = vidDetails.id;

				const whereClause = {
					isTopic  : 0,
					status   : 1,
					topicId  : topicId,
					parentId : parentId
				}

				let percentageWatched = 0;

				if(userDetails.Role != "admin")
				{
					percentageWatched = await VideoService.percentageVideoWatched(topicId, parentId, userDetails.User.id);
				}

				const responseDataSubVid = await VideoService.getMultiLingual(whereClause, 99999999, pageNumber, languageCode, orderByParameter, orderByValue, userDetails);

				vidDetails.subVideos         = responseDataSubVid.topicList;
				vidDetails.percentageWatched = +percentageWatched;
			}

			return h.response({success:true,message:req.i18n.__("CHAPTERS_FETCHED_SUCCESSFULLY"),responseData}).code(200);
		}
		catch (error) {
			console.error(error);
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	readChaptersAdmin: async(req, h) => {
		try {
			const userDetails   = req.auth.credentials.userData;
			const languageCode  = req.headers.language;
			const { limit, pageNumber, topicId, parentId, orderByParameter, orderByValue } = req.query;
			const responseData = {
				videoList: [],
				meta: { topicDetails: null, parentDetails: null }
			};

			const videoInfo = await Models.Video.findOne({ 
				where: { id: topicId },
				include: [
					{ required: true, model: Models.VideoContent }, 
					{ model: Models.VideoLanguage }
				]
			});
			if(!videoInfo) {
				return h.response({success:false,message:req.i18n.__("INVALID_VIDEO_ID_PROVIDED"),responseData:{}}).code(400);
			}

			const whereClause = {
				isTopic: 0, status: 1, topicId: topicId, parentId: null
			}


			if(parentId) {
				const parentInfo = await Models.Video.findOne({
					where: { id: parentId },
					include: [
						{ required: true, model: Models.VideoContent }, 
						{ model: Models.VideoLanguage }
					]
				});
				if(!parentInfo) {
					return h.response({success:false,message:req.i18n.__("INVALID_PARENT_ID_PROVIDED"),responseData:{}}).code(400);
				}
				responseData["meta"]["parentDetails"] = parentInfo;
				whereClause["parentId"] = parentId;
			}

			

			const topic = await Models.Video.findAll({
				where: whereClause,
				include: [
					{ required: true, model: Models.VideoContent }, 
					{ model: Models.VideoLanguage },
					{ required: false, model: Models.Attachment, attributes: attachmentAttributes,  as: "posterImage", where: { isPoster: 1} }, 
					{ required: false, model: Models.Attachment, attributes: attachmentAttributes, as: "attachments", where: { isPoster : 0 } }
				],
				order: [[orderByParameter, orderByValue]]
			});

			responseData["videoList"] = topic;
			responseData["meta"]["topicDetails"] = videoInfo;
			return h.response({success:true,message:req.i18n.__("CHAPTERS_FETCHED_SUCCESSFULLY"),responseData}).code(200);
		}
		catch (error) {
			console.error(error);
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	readChapterById: async(req, h) => {
		try {
			const userDetails   = req.auth.credentials.userData;
			const languageCode  = req.headers.language;
			const { id } = req.query;
			

			const topic = await Models.Video.findOne({
				where: {id: id},
				include: [
					{ required: true, model: Models.VideoContent }, 
					{ model: Models.VideoLanguage },
					{ required: false, model: Models.Attachment, attributes: attachmentAttributes, as: "posterImage", where: { isPoster: 1} }, 
					{ required: false, model: Models.Attachment, attributes: attachmentAttributes, as: "attachments", where: { isPoster : 0 } }
				]
			});

			return h.response({success:true,message:req.i18n.__("CHAPTERS_FETCHED_SUCCESSFULLY"),responseData: topic}).code(200);
		}
		catch (error) {
			console.error(error);
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	createSingleVideo: async(req, h) => {
		const transaction = await Models.sequelize.transaction();

		try {
			const userDetails  = req.auth.credentials.userData;
			const languageCode = "en";
			let data = [];

			const {name, audioLink, videoLink, shortText, description, accessibleDate, topicId, parentId, subTitle, attachments, posterImage, noOfDays} = req.payload;

			const validTopic = await Models.Video.findOne({where: { id: topicId, isTopic: 1 }, transaction});
			if(!validTopic) {
				await transaction.rollback();
				return h.response({success:false,message:req.i18n.__("INVALID_TOPIC_ID"),responseData:{}}).code(400);
			}

			if(parentId) {
				const validVideo = await Models.Video.findOne({where : { topicId: topicId, id: parentId }, transaction});
				if(!validVideo) {
					await transaction.rollback();
					return h.response({success:false,message:req.i18n.__("INVALID_PARENT_ID"),responseData:{}}).code(400);
				}
			}

			let attachmentArray = [];
			for(const attachmentDetails of attachments) {
				attachmentArray.push({
					name          : attachmentDetails.originalName,
					attachmentId  : attachmentDetails.id,
					attachmentUrl : attachmentDetails.path,
					userId        : userDetails.User.id,
					mimeType      : attachmentDetails.mimeType,
					isThumbnail   : 0,
					isPoster      : 0
				})
			}

			let videoObj = {
				topicId: topicId,
				parentId: parentId,
				isTopic: 0,
				status: 1,
				order: null,
				isFree: 0,
				accessibleDate: accessibleDate,
				noOfDays: noOfDays,
				price: null,
				VideoContents  : [{
					name: name, 
					userId: userDetails.User.id,
					audioLink: audioLink,
					videoLink: videoLink,
					languageCode: languageCode,
					shortText: shortText,
					description: description,
					subTitle: subTitle
				}],
				attachments: attachmentArray
				// attachments: posterImage ? [{
				// 	...posterImage,
				// 	isThumbnail: 0,
				// 	isPoster: 1,
				// 	userId: userDetails.User.id
				// }] : []
			}

			const createdVideo = await Models.Video.create(videoObj, {
				include : [{
					model : Models.VideoContent
				},
				{
					model : Models.Attachment,
					as : "attachments",
				}
			],
				transaction
			});

			await Models.Video.update({ sortOrder: createdVideo.id }, { where: { id: createdVideo.id }, transaction });

			// if(data.length)
			// {
			// 	let reqPayload = {data};
			// 	await Webhook.updateAttachmentService(reqPayload, transaction);
			// }

			await transaction.commit();
			return h.response({success:false,message:req.i18n.__("VIDEO_CREATED_SUCCESSFULLY"),responseData:createdVideo}).code(201);
		}
		catch (error) {
			console.error(error);
			await transaction.rollback();
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	updateSingleVideo: async(req, h) => {
		const transaction = await Models.sequelize.transaction();

		try {
			const userDetails  = req.auth.credentials.userData;
			const languageCode = "en";
			let data = [];

			const {id, name, audioLink, videoLink, shortText, description, accessibleDate, topicId, parentId, subTitle, attachments, posterImage, noOfDays, sortOrder} = req.payload;

			const validTopic = await Models.Video.findOne({where: { id: id }, transaction});
			if(!validTopic) {
				await transaction.rollback();
				return h.response({success:false,message:req.i18n.__("INVALID_VIDEO_ID"),responseData:{}}).code(400);
			}

			if(parentId) {
				const validVideo = await Models.Video.findOne({where : { topicId: topicId, id: parentId }, transaction});
				if(!validVideo) {
					await transaction.rollback();
					return h.response({success:false,message:req.i18n.__("INVALID_PARENT_ID"),responseData:{}}).code(400);
				}
			}

			let attachmentArray = [];
			for(const attachmentDetails of attachments) {
				attachmentArray.push({
					name          : attachmentDetails.originalName,
					attachmentId  : attachmentDetails.id,
					attachmentUrl : attachmentDetails.path,
					userId        : userDetails.User.id,
					mimeType      : attachmentDetails.mimeType,
					isThumbnail   : 0,
					isPoster      : 0,
					videoId: id
				})
			}

			await Models.Attachment.destroy({ where: { videoId: id }, transaction });

			await Models.Video.update({
				topicId: topicId,
				parentId: parentId,
				isTopic: 0,
				status: 1,
				order: null,
				isFree: 0,
				accessibleDate: accessibleDate,
				noOfDays: noOfDays,
				price: null,
				sortOrder
			}, { where: {id: id}, transaction });

			await Models.VideoContent.update({
				name: name, 
				userId: userDetails.User.id,
				audioLink: audioLink,
				videoLink: videoLink,
				languageCode: languageCode,
				shortText: shortText,
				description: description,
				subTitle: subTitle
			}, { where: { videoId: id }, transaction });

			await Models.Attachment.bulkCreate(attachmentArray, { transaction });
			// await Models.Attachment.

			await transaction.commit();
			return h.response({success:false,message:req.i18n.__("VIDEO_CREATED_SUCCESSFULLY"),responseData:{}}).code(200);
		}
		catch (error) {
			console.error(error);
			await transaction.rollback();
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	deletingSingleVideo: async(req,h) => {
		const transaction = await Models.sequelize.transaction();
		try {
			const {id} = req.query;

			const validTopic = await Models.Video.findOne({where: { id: id }, transaction});
			if(!validTopic) {
				await transaction.rollback();
				return h.response({success:false,message:req.i18n.__("INVALID_VIDEO_ID"),responseData:{}}).code(400);
			}

			if(validTopic.parentId === null) {
				const childTopics = await Models.Video.findOne({ where: { parentId: validTopic.id }, transaction });
				if(childTopics) {
					await transaction.rollback();
					return h.response({success:false,message:req.i18n.__("DELETE_SUB_LESSONS_FIRST"),responseData:{}}).code(400);
				}
			}

			await Models.Video.destroy({ where: { id: id }, transaction });

			await transaction.commit();
			return h.response({success:false,message:req.i18n.__("VIDEO_DELETED_SUCCESSFULLY"),responseData:{}}).code(200);
		} catch (error) {
			console.error(error);
			await transaction.rollback();
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},
	/**
	 * @desc Retrives a new lesson, level 2 Video and sub videos
	 * @param {*} req 
	 * @param {*} h 
	 * @returns 
	 */
	readVideos: async(req, h) => {
		try {
			const userDetails  = req.auth.credentials.userData;
			const languageCode = req.headers.language;

			const { limit, topicId, parentId, pageNumber, orderByParameter, orderByValue } = req.query;

			const whereClause = {
				isTopic  : 0,
				status   : 1,
				topicId  : topicId,
				parentId : parentId
			}

			const responseData = await VideoService.getMultiLingual(whereClause, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails);

			return h.response({success:true,message:req.i18n.__("TOPICS_FETCHED_SUCCESSFULLY"),responseData}).code(200);
		}
		catch (error) {
			console.error(error);
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	/**
	 * @desc Retrives a new Container, Topic and level 0 Video
	 * @param {*} req 
	 * @param {*} h 
	 * @returns 
	 */
	readTopicAdmin: async(req, h) => {
		try {


			// if(req.auth.credentials.userData.Role.includes('admin')) {
			// 	const data = await addTopicFromExcelSubTopics();
			// 	console.log(data)
			// 	return 
			// }

			const userDetails = req.auth.credentials.userData;
			const { limit, pageNumber, orderByParameter, orderByValue } = req.query;
			const languageCode   = req.headers.language;

			const whereClause = {
				isTopic  : 1,
				status   : 1
			}

			const responseData = await VideoService.getMultiLingual(whereClause, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails);

			return h.response({success:true,message:req.i18n.__("TOPICS_FETCHED_SUCCESSFULLY"),responseData}).code(200);
		}
		catch (error) {
			console.error(error);
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	/**
	 * 
	 * @param {*} req 
	 * @param {*} h 
	 * @returns 
	 */
	deleteCourse: async(req, h) => {
		const transaction = await Models.sequelize.transaction();

		try {
			const userDetails  = req.auth.credentials.userData;
			const videoId      = req.query.id;

			const isVideoExists = await Models.Video.findOne({
				where : {
					id : videoId
				}
			});

			if(isVideoExists)
			{
				const isTopic = isVideoExists.isTopic;
				let isSubVideoExists = null;

				if(isTopic)
				{
					isSubVideoExists = await Models.Video.findOne({
						where : {
							topicId : videoId
						}
					});
				}
				else
				{
					isSubVideoExists = await Models.Video.findOne({
						where : {
							parentId : videoId
						}
					});
				}

				if(isSubVideoExists)
				{
					await transaction.rollback();
					return h.response({success:false,message:req.i18n.__("PLEASE_DELETE_ALL_SUB_COURSES"),responseData:{}}).code(400);
				}
				else
				{
					await isVideoExists.destroy({transaction});

					await transaction.commit();
					return h.response({success:true,message:req.i18n.__("COURSE_DELETED_SUCCESSFULLY"),responseData:{}}).code(204);
				}
			}
			else
			{
				await transaction.rollback();
				return h.response({success:false,message:req.i18n.__("COURSE_NOT_FOUND"),responseData:{}}).code(400);
			}
		}
		catch (error) {
			await transaction.rollback();

			console.error(error);
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	/**
	 * 
	 * @param {*} req 
	 * @param {*} h 
	 * @returns 
	 */
	readCourseById: async(req, h) => {
		try {
			const userDetails  = req.auth.credentials.userData;
			const languageCode = req.headers.language;
			const { id }       = req.query;

			const limit            = 1
			const pageNumber       = 1;
			const orderByParameter = "id";
			const orderByValue     = "DESC";

			const whereClause = {
				id : id
			}

			const responseData = await VideoService.getMultiLingual(whereClause, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails);

			return h.response({success:true,message:req.i18n.__("COURSE_FETCHED_SUCCESSFULLY"),responseData : responseData.topicList[0]}).code(200);
		}
		catch (error) {
			console.error(error);
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	/**
	 * 
	 * @param {*} req 
	 * @param {*} h 
	 * @returns 
	 */
	editTitle: async(req, h) => {
		const transaction = await Models.sequelize.transaction();

		try {
			const {name, audioLink, videoLink, shortText, description, isFree, accessibleDate, subTitle, topicType, semester, attachments, price, id, posterImage, noOfDays, salesPageLink, isSubscriptionBased} = req.payload;
			let videoLanguages = req.payload.videoLanguages;
			if(videoLanguages === null) videoLanguages = [];

			let digistoreId = req.payload.digistoreId;
			if(digistoreId) {
				digistoreId = digistoreId.replace(/[^0-9,]+/g, '')
				.replace(/,{2,}/g, ',')
				.replace(/(^,|,$)/g, '');
			}

			const languageCode = req.headers.language;
			const userDetails  = req.auth.credentials.userData;

			let data                      = [];
			let removalData               = [];
			let formattedAttachmentObjArr = [];

			const isTopicExists = await Models.Video.findOne({
				where : {
					id : id
				},
				include : [{
					model : Models.VideoContent
				}]
			});

			if(!isTopicExists)
			{
				await transaction.rollback();
				return h.response({success:false,message:req.i18n.__("COURSE_NOT_FOUND"),responseData:{}}).code(400);
			}

			const isOldPoster = await Models.Attachment.findOne({
				where : {
					videoId      : id,
					isPoster     : 1,
					attachmentId : posterImage.attachmentId
				}
			});

			if(!isOldPoster)
			{
				const oldPosterDetails = await Models.Attachment.findOne({
					where :{
						videoId      : id,
						isPoster     : 1,
					}
				});

				if(oldPosterDetails)
				{
					await oldPosterDetails.destroy({transaction});

					removalData.push({
						id     : oldPosterDetails.attachmentId,
						status : 0
					})
				}

				data.push({
					id     : posterImage.attachmentId,
					status : 1
				});
			}

			let titleObj = {
				topicId        : null,
				parentId       : null,
				isTopic        : 1,
				status         : 1,
				order          : null,
				isFree         : isFree,
				accessibleDate : accessibleDate,
				noOfDays       : noOfDays,
				topicType      : topicType,
				semester       : topicType == "KUBYstudy" ? semester : null,
				price          : price || null,
				digistoreIdString    : digistoreId || null,
				salesPageLink  : salesPageLink || null,
				isSubscriptionBased: isSubscriptionBased || 0,
				VideoContents  : [{
					name         : name, 
					userId       : userDetails.User.id,
					audioLink    : audioLink,
					videoLink    : videoLink,
					languageCode : languageCode,
					shortText    : shortText,
					description  : description,
					subTitle     : subTitle ? subTitle : null
				}],
				Attachments    : !isOldPoster ? [{
					...posterImage,
					isThumbnail : 0,
					isPoster    : 1,
					userId      : userDetails.User.id
				}] : []
			};

			if(attachments)
			{
				let retainAttachment = [];

				for(const attachmentDetails of attachments)
				{
					formattedAttachmentObjArr.push({
						name          : attachmentDetails.originalName,
						attachmentId  : attachmentDetails.id,
						attachmentUrl : attachmentDetails.path,
						userId        : userDetails.User.id,
						mimeType      : attachmentDetails.mimeType,
						isThumbnail   : 0,
						isPoster      : 0
					})

					data.push({
						id    : attachmentDetails.id,
						status: 1
					})

					const isAttachmentExists = await Models.Attachment.findOne({
						where : {
							videoId      : id,
							isPoster     : 0,
							attachmentId : attachmentDetails.id
						}
					});
	
					if(!isAttachmentExists)
					{
						(titleObj.Attachments).push(attachmentDetails);
					}
					else
					{
						retainAttachment.push(isAttachmentExists.attachmentId)
					}
				}

				const attachmentIdToDestroy = await Models.Attachment.findAll({
					where : {
						videoId      : id,
						isPoster     : 0,
						attachmentId : {
							[Op.notIn]  : retainAttachment
						}
					},
					attributes : ["id", "attachmentId"]
				});

				for(const attachmentDet of attachmentIdToDestroy)
				{
					removalData.push({
						id     : attachmentDet.attachmentId,
						status : 0
					})

					await Models.Attachment.destroy({
						where : {
							id : attachmentDet.id
						}, transaction
					});
				}
			}
			else
			{
				const attachmentIdToDestroy = await Models.Attachment.findAll({
					where : {
						videoId      : id,
						isPoster     : 0
					},
					attributes : ["id", "attachmentId"]
				});

				if(attachmentIdToDestroy.length)
				{
					await Models.Attachment.destroy({
						where : {
							videoId      : id,
							isPoster     : 0
						}, transaction
					});

					for(const attachmentDet of attachmentIdToDestroy)
					{
						removalData.push({
							id     : attachmentDet.attachmentId,
							status : 0
						})
					}
				}
			}

			if(data.length)
			{
				let reqPayload = {data};
				await Webhook.updateAttachmentService(reqPayload, transaction);
			}

			if(removalData.length)
			{
				let reqPayload = {data : removalData};
				await Webhook.updateAttachmentService(reqPayload, transaction);
			}

			await Models.Video.update(titleObj, {
				include : [{
					model : Models.VideoContent
				}, {
					model : Models.Attachment
				}],
				where : {
					id : id
				},
				transaction
			});

			await isTopicExists.update({
				...titleObj
			}, {transaction});

			await Models.VideoContent.update({
				...titleObj.VideoContents[0]
			}, {
				where : {
					videoId : id
				},
				transaction
			});

			if(titleObj.Attachments)
			{
				let videoAttachment = titleObj.Attachments.map((obj) => {
					obj.videoId = id
					return obj;
				});
	
				await Models.Attachment.bulkCreate(
					videoAttachment
				, {transaction});
			}

			await Models.VideoProduct.destroy({ where: { videoId: id }, paranoid: false, transaction });
			if(digistoreId && digistoreId !== "") {
				let ids = digistoreId.split(",");
				for(let item of ids) {
					await Models.VideoProduct.create({ videoId: id, digistoreId: item }, { transaction });
				}
			}

			await Models.VideoLanguage.destroy({ where: { videoId: id }, paranoid: false, transaction });
			for(let language of videoLanguages) {
				await Models.VideoLanguage.create( {videoId: id, languageCode: language}, { transaction } )
			}

			await transaction.commit();
			return h.response({success:true,message:req.i18n.__("TOPIC_UPDATED_SUCCESSFULLY"),responseData:{}}).code(204);
		}
		catch (error) {
			console.error(error);

			await transaction.rollback();
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	/**
	 * 
	 * @param {*} req 
	 * @param {*} h 
	 * @returns 
	 */
	editLessons: async(req, h) => {
		const transaction = await Models.sequelize.transaction();

		try {
			const userDetails      = req.auth.credentials.userData;
			const videoArray       = req.payload.lessons;
			const deleteVideoArray = req.payload.deleteVideos;
			// const languageCode     = req.headers.language;
			const languageCode     = "en";

			let topicIdForDelete;

			for(const videoDetails of videoArray)
			{
				const {name, audioLink, videoLink, shortText, description, accessibleDate, subVideos, topicId, parentId, subTitle, attachments, posterImage, id} = videoDetails;
				let noOfDays = videoDetails.noOfDays;
				if(noOfDays == "") noOfDays = 0;

				const isValidTopic = await VideoService.validateTopic(topicId, transaction);
				let isValidVideo = false;

				topicIdForDelete = topicId;

				if(parentId)
				{
					isValidVideo = await VideoService.validateVideo(topicId, parentId, transaction);
				}

				if(isValidTopic && ((parentId && isValidVideo) || (!parentId && !isValidVideo)))
				{
					const videoObj = {
						topicId        : topicId,
						parentId       : parentId ? parentId : null,
						isTopic        : 0,
						status         : 1,
						order          : null,
						isFree         : 0,
						accessibleDate : accessibleDate,
						noOfDays       : noOfDays,
						price          : null
					};

					const VideoContentObj = {
						name         : name, 
						userId       : userDetails.User.id,
						audioLink    : audioLink ? audioLink : null,
						videoLink    : videoLink,
						languageCode : languageCode,
						shortText    : shortText,
						description  : description,
						subTitle     : subTitle ? subTitle : null
					};

					const createdVideoId = await VideoService.updateVideo(id, videoObj, transaction);

					if(posterImage)
					{
						const isPosterCreated = await VideoService.posterAttachment(userDetails, posterImage, createdVideoId, transaction);
						if(!isPosterCreated) throw new Error("ERROR_WHILE_CREATING_POSTER");
					}

					if(attachments)
					{
						const isAttachmentCreated = await VideoService.updateAttachments(userDetails, attachments, createdVideoId, transaction);
						if(!isAttachmentCreated) throw new Error("ERROR_WHILE_CREATING_ATTACHMENT");
					}

					const createdVidContent = await VideoService.updateVideoContent(languageCode, createdVideoId, VideoContentObj, transaction);

					if(!createdVidContent) throw new Error("ERROR_WHILE_CREATING_VIDEO_CONTENT");

					if(subVideos)
					{
						for(const subVidDetails of subVideos)
						{
							const parentVideoId = createdVideoId;

							const subVideoObj = {
								topicId        : topicId,
								parentId       : parentVideoId,
								isTopic        : 0,
								status         : 1,
								order          : null,
								isFree         : 0,
								accessibleDate : subVidDetails.accessibleDate,
								noOfDays       : subVidDetails.noOfDays,
								price          : null
							}

							const cratedSubVideoId = await VideoService.updateVideo(subVidDetails.id, subVideoObj, transaction);
							if(!cratedSubVideoId) throw new Error("ERROR_WHILE_CREATING_VIDEO");

							const subVideoContent = {
								name         : subVidDetails.name, 
								userId       : userDetails.User.id,
								audioLink    : subVidDetails.audioLink ? subVidDetails.audioLink : null,
								videoLink    : subVidDetails.videoLink,
								languageCode : languageCode,
								shortText    : subVidDetails.shortText,
								description  : subVidDetails.description,
								subTitle     : subVidDetails.subTitle ? subVidDetails.subTitle : null
							}

							const createdSubVidContent = await VideoService.updateVideoContent(languageCode, cratedSubVideoId, subVideoContent, transaction);
							if(!createdSubVidContent) throw new Error("ERROR_WHILE_CREATING_VIDEO_CONTENT");

							if(subVidDetails.posterImage)
							{
								const isPosterCreated = await VideoService.posterAttachment(userDetails, subVidDetails.posterImage, cratedSubVideoId, transaction);
								if(!isPosterCreated) throw new Error("ERROR_WHILE_CREATING_POSTER");
							}

							if(subVidDetails.attachments)
							{
								const isAttachmentCreated = await VideoService.updateAttachments(userDetails, subVidDetails.attachments, cratedSubVideoId, transaction);
								if(!isAttachmentCreated) throw new Error("ERROR_WHILE_CREATING_ATTACHMENT");
							}
						}
					}
				}
				else
				{
					throw new Error("INVALID_TOPIC_OR_PARENT_ID");
				}
			}

			if(deleteVideoArray.length)
			{
				await VideoService.deleteLessonChapters(topicIdForDelete, deleteVideoArray, transaction);
			}

			await transaction.commit();
			return h.response({success:true,message:req.i18n.__("VIDEO_UPDATED_SUCCESSFULLY"),responseData:{}}).code(201);
		}
		catch (error) {
			console.error(error);

			await transaction.rollback();
			return h.response({success:false,message:req.i18n.__(error.message),responseData:{}}).code(500);
		}
	},

	/**
	 * 
	 * @param {*} req 
	 * @param {*} h 
	 * @returns 
	 */
	deleteAllChapters: async(req, h) => {
		const transaction = await Models.sequelize.transaction();

		try {
			const {id} = req.query;
			const topicIdForDelete = id;
			const deleteVideoArray = null;

			await VideoService.deleteLessonChapters(topicIdForDelete, deleteVideoArray, transaction);

			await transaction.commit();
			return h.response({success:true,message:req.i18n.__("VIDEOS_FETCHED_SUCCESSFULLY"),responseData:{}}).code(200);
		}
		catch (error) {
			console.error(error);

			await transaction.rollback();
			return h.response({success:false,message:req.i18n.__(error.message),responseData:{}}).code(500);
		}
	},

	/**
	 * 
	 * @param {*} req 
	 * @param {*} h 
	 * @returns 
	 */
	updateTimeStamp: async(req, h) => {
		try {
			const userDetails = req.auth.credentials.userData;
			const {videoId, amountWatched, totalLength} = req.payload;

			let response = await VideoService.updateTimeStamp(videoId, amountWatched, totalLength, userDetails);

			if(response !== true) {
				return h.response({success:false,message:req.i18n.__("INVALID_VIDEO"),responseData:{}}).code(400);
			}

			return h.response({success:true,message:req.i18n.__("TIME_STAMP_UPDATED_SUCCESSFULLY"),responseData:{}}).code(204);
		}
		catch (error) {
			console.error(error);
			return h.response({success:false,message:req.i18n.__(error.message),responseData:{}}).code(500);
		}
	},

	/**
	 * 
	 * @param {*} req 
	 * @param {*} h 
	 * @returns 
	 */
	giveReview: async(req, h) => {
		const transaction = await Models.sequelize.transaction();

		try {
			const {videoId, comment, rating} = req.payload;
			const userDetails = req.auth.credentials ? req.auth.credentials.userData : null;

			const isVideoPurchased = await Models.PurchasedTopic.findOne({
				where: {
					videoId : videoId,
					userId  : userDetails.User.id
				}
            });
    
            // const isVideoPurchased = 1;

			const isVideoExists = await Models.Video.findOne({
				where : {
					id : videoId
				}
			});

			if(isVideoExists && isVideoPurchased)
			{
				const updateReview = await Models.VideoUser.findOne({
					where : {
						videoId       : videoId,
						userId        : userDetails.User.id
					}
				}, {transaction});

				if(updateReview)
				{
					await updateReview.update({
						rating        : rating,
						comment       : comment
					}, {transaction});
				}
				else
				{
					const createdReview = await Models.VideoUser.create({
						videoId       : videoId,
						userId        : userDetails.User.id,
						rating        : rating,
						comment       : comment,
						amountWatched : 0,
						totalLength   : 0
					}, {transaction});
				}

				const reviews = await Models.VideoUser.findOne({
					where      : {
						videoId : videoId,
						userId  : userDetails.User.id
					},
					include    : {
						model      : Models.User,
						attributes : [["user_id", "id"], "firstName", "lastName", "title", "profilePhotoUrl", "profilePhotoId"]
					},
					attributes : ["id", "rating", "comment", "createdAt", "updatedAt"],
					transaction
				});

				await transaction.commit()
				return h.response({success:true,message:req.i18n.__("REVIEW_POSTED_SUCCESSFULLY"),responseData:reviews}).code(201);
			}
			else
			{
				await transaction.rollback()
				return h.response({success:false,message:req.i18n.__("INVALID_VIDEO_ID"),responseData:{}}).code(400);
			}
		}
		catch (error) {
			console.error(error);

			await transaction.rollback();
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	/**
	 * 
	 * @param {*} req 
	 * @param {*} h 
	 * @returns 
	 */
	getReviews: async(req, h) => {
		try
		{

			// 			const data = await addTopicFromExcelSubTopics();
			// console.log(data)
			// return 
			const {videoId, pageNumber, limit, orderByValue, orderByParameter} = req.query;
			const userDetails = req.auth.credentials ? req.auth.credentials.userData : null;

			const videoIds = await Models.Video.findAll({
				where : {
					[Op.or]: [
						{ id       : videoId },
						{ topicId  : videoId },
						{ parentId : videoId }
					]
				},
				attributes : [[Models.sequelize.fn("GROUP_CONCAT", Models.sequelize.col("id")), "videoIds"]]
			});

			const videoIdArr = videoIds[0].dataValues.videoIds.split(",");

			if(!videoIds[0].dataValues.videoIds)
			{
				return h.response({success:true,message:req.i18n.__("REVIEW_FETCHED_SUCCESSFULLY"),responseData:{}}).code(200);
			}

			const reviews = await Models.VideoUser.findAndCountAll({
				where      : {
					videoId : videoIdArr,
					comment : { [Op.not] : null },
					rating  : { [Op.not] : null },
					// userId  : { [Op.not] : userDetails.User.id},
				},
				include    : {
					model      : Models.User,
					attributes : [["user_id", "id"], "firstName", "lastName", "title", "profilePhotoUrl", "profilePhotoId"]
				},
				attributes : ["id", "rating", "comment", "createdAt", "updatedAt"],
				limit      : limit,
            	offset     : (pageNumber - 1) * limit,
				order      : [[orderByParameter, orderByValue]]
			});

			const totalPages = await Common.getTotalPages(reviews.count, limit);

			const responseData = {
				totalPages,
				perPage      : limit,
				reviewList    : reviews.rows,
				totalRecords : reviews.count,
				baseUrl      : process.env.IMAGE_BASE_URL
			}

			return h.response({success:true,message:req.i18n.__("REVIEW_FETCHED_SUCCESSFULLY"),responseData:responseData}).code(200);
		}
		catch (error)
		{
			console.error(error);
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	/**
	 * 
	 * @param {*} req 
	 * @param {*} h 
	 */
	getReviewForUser: async(req, h) => {
		try
		{
			const {videoId} = req.query;
			const userDetails = req.auth.credentials ? req.auth.credentials.userData : null;

			const review = await Models.VideoUser.findOne({
				where : {
					videoId : videoId,
					userId  : userDetails.User.id
				},
				attributes : ["id", "rating", "comment", "createdAt", "updatedAt"],
			});

			if(review)
			{
				return h.response({success:true,message:req.i18n.__("REVIEW_FETCHED_SUCCESSFULLY"),responseData:review}).code(200);
			}
			else
			{
				return h.response({success:true,message:req.i18n.__("REVIEW_FETCHED_SUCCESSFULLY"),responseData:{}}).code(200);
			}
		}
		catch (error)
		{
			console.error(error);
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	/**
	 * 
	 * @param {*} req 
	 * @param {*} h 
	 */
	getDashboardData: async(req, h) => {
		try
		{
			const userDetails  = req.auth.credentials ? req.auth.credentials.userData : null;
			const languageCode = req.headers.language;

			const orderByParameter = "id";
			const orderByValue     = "DESC";
			const pageNumber       = 1;
			const limit            = 5;

			const whereClause = {
				isTopic  : 1,
				status   : 1
			}

			const responseDataSeminar   = await VideoService.getMultiLingual({...whereClause, topicType : "Seminars"}, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails);
			const responseDataKubyStudy = await VideoService.getMultiLingual({...whereClause, topicType : "KUBYstudy"}, limit, pageNumber, languageCode, orderByParameter, orderByValue, userDetails);

			const responseData = {
				"Seminars"  : responseDataSeminar.topicList,
				"KUBYstudy" : responseDataKubyStudy.topicList
			}

			return h.response({success:true,message:req.i18n.__("TOPICS_FETCHED_SUCCESSFULLY"),responseData}).code(200);
		}
		catch (error)
		{
			console.error(error);
			return h.response({success:false,message:req.i18n.__("SOMETHING_WENT_WRONG"),responseData:{}}).code(500);
		}
	},

	/**
	 * 
	 * @param {*} req 
	 * @param {*} h 
	 */
	// confirmPaymentHelper: async(payload, transaction) => {
	// 	try{

	// 		const dgStoreOrderId = payload.order_id;
	// 		const checkExistingOrder = await Models.Order.findOne({ where: { 'data.order_id': dgStoreOrderId } });
	// 		if(checkExistingOrder) return { success: true, message: "ORDER_ALREADY_EXISTS", data: checkExistingOrder };

	// 		const dgStoreProductId = payload.product_id;
	// 		if(!dgStoreProductId) return { success: false, message: "INVALID_PRODUCT_ID_PROVIDED_BY_GATEWAY", data: {} };
			

	// 		const topicInfo = await Models.Video.findOne({ where: { digistoreId: dgStoreProductId } });
	// 		const videoId = topicInfo.id;
	// 		const isSubscriptionBased = topicInfo.isSubscriptionBased;

	// 		let userId = null;
	// 		if(payload?.userId) userId = payload?.userId;
			

	// 		if(userId === null) {
	// 			const data = {
	// 				email: payload.buyer_email, firstName: payload.buyer_first_name,
	// 				lastName: payload.buyer_last_name, existingUserEmailSend: 1
	// 			}
	// 			let responseData = await Axios({
	// 				method: "post", headers: {language: req.headers.language}, data: data,
	// 				url: `${process.env.USER_ONBOARDING_DOMAIN}/create-dg-user`
	// 			});
	// 			responseData = responseData.data.userId;
	// 			if(responseData) userId = responseData;
	// 		}
			
	// 		let order = await Models.Order.create({ videoId: videoId, userId : userId, isPaid: 1, data: payload }, {transaction});
	// 		if(!order) return { success: false, message: "PAYMENT_NOT_CONFIRMED", data: {} }
			
	// 		let date= new Date();
	// 		await Models.PurchasedTopic.create({
	// 			userId: userId,
	// 			videoId: videoId,
	// 			hasPassed: 0,
	// 			purchase_date: date,
	// 			starting_date: date,
	// 			activeSubscription: isSubscriptionBased == 1 ? 1 : null
	// 		},{transaction});

	// 		const vidInfo = await Models.Video.findOne({
	// 			where : { id : videoId },
	// 			include : {
	// 				model : Models.VideoContent,
	// 				attributes : ["name", "subTitle", "videoLink"]
	// 			},
	// 			attributes : ["topicType"]
	// 		})

	// 		const userObj = await Models.User.findOne({
	// 			where : { userId : userId }
	// 		});

	// 		let earningHistoryObj = {
	// 			topicId: videoId, data: payload, amount: payload?.amount,
	// 			amountReceived: payload?.earned_amount, userId: userId,
	// 			topicObject: vidInfo, userObject: userObj
	// 		}

	// 		await Axios({
	// 			method: 'post',
	// 			url: `${process.env.USER_ONBOARDING_DOMAIN}/earning-history`,
	// 			headers: {},
	// 			data: earningHistoryObj
	// 		});

	// 		return { success: true, message: "REQUEST_SUCCESSFULL", data: order }
	// 	}
	// 	catch(error) {
	// 		console.error('Error in Add orders',error)

	// 		await transaction.rollback();
	// 		return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
	// 	}
	// },
	// confirmPayment: async(req, h) => {
	// 	const transaction = await Models.sequelize.transaction();

	// 	try{
	// 		let {data} = req.payload;

	// 		const userDetails = req?.auth?.credentials?.userData;

	// 		let decryprData = await decodeData(data);
	// 		if(!decryprData) {
	// 			await transaction.rollback();
	// 			return h.response({success: false,message: req.i18n.__("INVALID_DATA_PROVIDED_BY_GATEWAY"),responseData: {}}).code(400)
	// 		}

	// 		const payload = {
	// 			order_id: decryprData?.order_id,
	// 			product_id: decryprData?.product_id,
	// 			buyer_email: decryprData?.buyer_email,
	// 			buyer_first_name: decryprData.buyer_first_name,
	// 			buyer_last_name: decryprData.buyer_last_name,
	// 			amount: decryprData.amount,
	// 			earned_amount: decryprData.earned_amount,
	// 			userId: userDetails?.User.id ? userDetails?.User.id : null
	// 		}

	// 		const controller = await confirmPaymentHelper(payload, req.headers.language, "thankyou-page", transaction);	
	// 		if(!controller?.success) {
	// 			await transaction.rollback();
	// 			return h.response({success: false,message: req.i18n.__(controller.message),responseData: {}}).code(400)
	// 		}

	// 		await transaction.commit();
	// 		// let emailPayload = { 
	// 		// 	orderId: decryprData.order_id, productId: decryprData.product_id, amount: decryprData.amount,
	// 		// 	paymentMethod: "Online", date: new Date(), email: decryprData.buyer_email,  
	// 		// 	firstName: decryprData.buyer_first_name, lastName: decryprData.buyer_last_name
	// 		// }
	// 		// sendEmail(emailPayload, "PRODUCT_PURCHASE")
	// 		return h.response({success: true,message: req.i18n.__("ATTRIBUTE_SUCCESSFULLY_CREATED"),responseData:controller.data}).code(200)
	// 	}
	// 	catch(error) {
	// 		console.error('Error in Add orders',error)

	// 		await transaction.rollback();
	// 		return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
	// 	}
	// },

	confirmPayment: async(req, h) => {
		const transaction = await Models.sequelize.transaction();

		try{
			let {data} = req.payload;

			const userDetails = req?.auth?.credentials?.userData;

			let decryprData = await decodeData(data);
			if(!decryprData) {
				await transaction.rollback();
				return h.response({success: false,message: req.i18n.__("INVALID_DATA_PROVIDED_BY_GATEWAY"),responseData: {}}).code(400)
			}

			const newObject = {};
			for (const key in decryprData) {
				if (key.startsWith('product_id') && key !== "product_id") {
					newObject[key] = decryprData[key];
				}
			}

			console.log(newObject);

			// return h.response({success: true,message: req.i18n.__("ATTRIBUTE_SUCCESSFULLY_CREATED"),responseData:decryprData}).code(200)

			const payload = {
				order_id: decryprData?.order_id,
				product_id: decryprData?.product_id,
				buyer_email: decryprData?.buyer_email,
				buyer_first_name: decryprData.buyer_first_name,
				buyer_last_name: decryprData.buyer_last_name,
				amount: decryprData.amount,
				earned_amount: decryprData.earned_amount,
				userId: userDetails?.User.id ? userDetails?.User.id : null
			}

			const controller = await confirmPaymentHelper(payload, req.headers.language, "thankyou-page", transaction);	
			if(!controller?.success) {
				await transaction.rollback();
				return h.response({success: false,message: req.i18n.__(controller.message),responseData: {}}).code(400)
			}


			for(let item in newObject) {
				const newPayload = {
					userId: controller?.meta?.userId ? controller?.meta?.userId : userDetails?.User.id ? userDetails?.User.id : null, 
					parentId: controller?.meta?.parentId ? controller?.meta?.parentId : null, 
					product_id: newObject[item]
				}	

				if(newPayload.userId) {
					let helperController = await addonsHelper(newPayload, transaction);
					if(!helperController?.success) {
						// await transaction.rollback();
						// return h.response({success: false,message: req.i18n.__(helperController.message),responseData: {}}).code(400)
					}
				}
				console.log(newPayload, " ========================= newPayload")
				

			}


			await transaction.commit();
			// let emailPayload = { 
			// 	orderId: decryprData.order_id, productId: decryprData.product_id, amount: decryprData.amount,
			// 	paymentMethod: "Online", date: new Date(), email: decryprData.buyer_email,  
			// 	firstName: decryprData.buyer_first_name, lastName: decryprData.buyer_last_name
			// }
			// sendEmail(emailPayload, "PRODUCT_PURCHASE")
			return h.response({success: true,message: req.i18n.__("ATTRIBUTE_SUCCESSFULLY_CREATED"),responseData:controller.data}).code(200)
		}
		catch(error) {
			console.error('Error in Add orders',error)

			await transaction.rollback();
			return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
		}
	},

	userTopicList: async(req, h) => {
		try {
			const userId = req.query.userId;
	
			const topicInfo = await Models.Video.findAll({
				include: [
					{
						model: Models.VideoContent
					},
					// {
					// 	model: Models.VideoLanguage
					// },
					{
						model: Models.PurchasedTopic,
						where: { userId: userId },
						required: true
					}
				],
				order: [[{model: Models.PurchasedTopic}, 'createdAt', 'DESC']]
			});
	
			return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULL"),responseData:{records: topicInfo}}).code(200);
		} catch (error) {
			console.log(error);
			return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
		}
	},

	changeUserTopicStatus: async(req, h) => {
		const transaction = await Models.sequelize.transaction();
		try {
			const userId = req.query.userId;
			const topicId = req.query.topicId;
			const status = 0;
			// const status = req.payload.status;

			const topicInfo = await Models.Video.findOne({ where: { id: topicId } });
			if(!topicInfo) {
				await transaction.rollback();
				return h.response({success: false,message: req.i18n.__("INVALID_TOPIC_ID_PROVIDED"),responseData: {}}).code(400)
			}

			const purchasedTopic = await Models.PurchasedTopic.findOne({ where: { videoId: topicId, userId: userId }, order: [["createdAt", "DESC"]] });
			if(!purchasedTopic) {
				await transaction.rollback();
				return h.response({success: false,message: req.i18n.__("USER_DOESNOT_HAVE_ACCESS_TO_TOPIC"),responseData: {}}).code(400)
			}

			const updatedPurchasedTopic = await purchasedTopic.update({ status: status }, { transaction });

			await transaction.commit();
			return h.response({success: true,message: req.i18n.__("REQUEST_SUCCESSFULL"),responseData:updatedPurchasedTopic}).code(200);
		} catch (error) {
			console.log(error)
			await transaction.rollback();
			return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
		}
	},
	// confirmPayment: async(req, h) => {
	// 	const transaction = await Models.sequelize.transaction();

	// 	try{
	// 		let {data} = req.payload;

	// 		const userDetails = req?.auth?.credentials?.userData;

	// 		let decryprData = await decodeData(data);
	// 		if(!decryprData) {
	// 			await transaction.rollback();
	// 			return h.response({success: false,message: req.i18n.__("INVALID_DATA_PROVIDED_BY_GATEWAY"),responseData: {}}).code(400)
	// 		}

	// 		const dgStoreOrderId = decryprData.order_id;
	// 		const checkExistingOrder = await Models.Order.findOne({ where: { 'data.order_id': dgStoreOrderId } });
	// 		console.log("checkExistingOrder ========== ", checkExistingOrder, " =========== ",dgStoreOrderId )
	// 		if(checkExistingOrder) {
	// 			await transaction.commit()
	// 			return h.response({success: true,message: req.i18n.__("ORDER_ALREADY_EXISTS"),responseData:checkExistingOrder}).code(200)
	// 		}

	// 		const dgStoreProductId = decryprData.product_id;
	// 		if(!dgStoreProductId) {
	// 			await transaction.rollback();
	// 			return h.response({success: false,message: req.i18n.__("INVALID_PRODUCT_ID_PROVIDED_BY_GATEWAY"),responseData: {}}).code(400)
	// 		}

	// 		const topicInfo = await Models.Video.findOne({ where: { digistoreId: dgStoreProductId } });
	// 		console.log(topicInfo)
	// 		const videoId = topicInfo.id;
	// 		const isSubscriptionBased = topicInfo.isSubscriptionBased;
	// 		console.log(isSubscriptionBased, " =================== isSubscriptionBased")
	// 		let userId = null;
	// 		if(userDetails?.User?.id) {
	// 			userId = userDetails?.User?.id;
	// 		}

	// 		if(userId === null) {
	// 			console.log(decryprData, " ============= decryprData decryprData")
	// 			const data = {
	// 				email: decryprData.buyer_email,
	// 				firstName: decryprData.buyer_first_name,
	// 				lastName: decryprData.buyer_last_name,
	// 				existingUserEmailSend: 1
	// 			}
	// 			let responseData = await Axios({
	// 				method: "post", headers: {language: req.headers.language}, data: data,
	// 				url: `${process.env.USER_ONBOARDING_DOMAIN}/create-dg-user`
	// 			});
	// 			responseData = responseData.data.userId;
	// 			if(responseData) userId = responseData;
	// 		}
			
	// 		//console.log(decryprData.custom);

			
	// 		let order = await Models.Order.create({ videoId: videoId, userId : userId, isPaid: 1, data: decryprData }, {transaction});
	
	// 		if(!order) {
	// 			await transaction.rollback();
	// 			return h.response({success: false,message: req.i18n.__("PAYMENT_NOT_CONFIRMED"),responseData: {}}).code(400)   
	// 		}

	// 		console.log({
	// 			userId       : userId,
	// 			videoId      : videoId,
	// 			purchaseDate : new Date(),
	// 			startingDate : new Date(),
	// 			hasPassed    : 0,
	// 			activeSubscription: isSubscriptionBased == 1 ? 1 : null
	// 		}, " ============ before insert")
	// 		let date= new Date();
	// 		const purchasedTopicModel = await Models.PurchasedTopic.create({
	// 			userId       : userId,
	// 			videoId      : videoId,
	// 			hasPassed    : 0,
	// 			purchase_date : date,
	// 			starting_date : date,
	// 			activeSubscription: isSubscriptionBased == 1 ? 1 : null
	// 		},{transaction});

	// 		console.log(purchasedTopicModel, " ============== purchasedTopicModel")

	// 		const vidInfo = await Models.Video.findOne({
	// 			where : {
	// 				id : videoId
	// 			},
	// 			include : {
	// 				model : Models.VideoContent,
	// 				attributes : ["name", "subTitle", "videoLink"]
	// 			},
	// 			attributes : ["topicType"]
	// 		})

	// 		const userObj = await Models.User.findOne({
	// 			where : {
	// 				userId : userId
	// 			}
	// 		});

	// 		let earningHistoryObj = {
	// 			topicId         : videoId,
	// 			data            : decryprData,
	// 			amount          : decryprData?.amount,
	// 			amountReceived  : decryprData?.earned_amount,
	// 			userId          : userId,
	// 			topicObject   	: vidInfo,
	// 			userObject      : userObj
	// 		}

	// 		let responseData = await Axios({
	// 			method  : 'post',
	// 			url     : `${process.env.USER_ONBOARDING_DOMAIN}/earning-history`,
	// 			headers : {},
	// 			data    : earningHistoryObj
	// 		});


	// 		await transaction.commit();
	// 		// let emailPayload = { 
	// 		// 	orderId: decryprData.order_id, productId: decryprData.product_id, amount: decryprData.amount,
	// 		// 	paymentMethod: "Online", date: new Date(), email: decryprData.buyer_email,  
	// 		// 	firstName: decryprData.buyer_first_name, lastName: decryprData.buyer_last_name
	// 		// }
	// 		// sendEmail(emailPayload, "PRODUCT_PURCHASE")
	// 		return h.response({success: true,message: req.i18n.__("ATTRIBUTE_SUCCESSFULLY_CREATED"),responseData:order}).code(200)
	// 	}
	// 	catch(error) {
	// 		console.error('Error in Add orders',error)

	// 		await transaction.rollback();
	// 		return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WRONG"), responseData: {}}).code(500);
	// 	}
	// }

	studyProductWebhook: async(payload, language, mode, transaction) => {
		try{
	
			//const dgStoreOrderId = payload.order_id;
			//const checkExistingOrder = await Models.Order.findOne({ where: { 'data.order_id': dgStoreOrderId } });
			//if(checkExistingOrder) return { success: true, message: "ORDER_ALREADY_EXISTS", data: checkExistingOrder };
	
			//const dgStoreProductId = payload.product_id;
			//if(!dgStoreProductId) return { success: false, message: "INVALID_PRODUCT_ID_PROVIDED_BY_GATEWAY", data: {} };
			
			const courseId = req.payload.courseId;
			const userId = req.payload.userId;
			const topicInfo = await Models.Video.findOne({ where: { id: courseId } });
			const videoId = topicInfo.id;
	
			//let userId = null;
			//if(payload?.userId) userId = payload?.userId;
			
	
			if(userId === null) {
				let language = "en";
				const videoLanguageInfo = await Models.VideoLanguage.findOne({ where: { videoId: videoId } });
				if(videoLanguageInfo) language = videoLanguageInfo.languageCode;
				const data = {
					email: payload.buyer_email, firstName: payload.buyer_first_name,
					lastName: payload.buyer_last_name, existingUserEmailSend: 1
				}
				let responseData = await Axios({
					method: "post", headers: {language: language}, data: data,
					url: `${process.env.USER_ONBOARDING_DOMAIN}/create-dg-user`
				});
				responseData = responseData.data.userId;
				if(responseData) userId = responseData;
			}
			
			//let order = await Models.Order.create({ mode: mode, videoId: videoId, userId : userId, isPaid: 1, data: payload }, {transaction});
			//if(!order) return { success: false, message: "PAYMENT_NOT_CONFIRMED", data: {} }
			
			let date= Moment(new Date());
			let endDate= Moment(new Date()).add(1, "month");
			await Models.PurchasedTopic.create({
				userId: userId,
				videoId: videoId,
				hasPassed: 0,
				purchaseDate: date,
				startingDate: date,
				endDate: null,
				activeSubscription: null,
				status: 1
			},{transaction});
	
	
	
	

			return { success: true, message: "REQUEST_SUCCESSFULL", data: order }
		}
		catch(error) {
			console.error('Error in Add orders',error)
	
			return { success: false, message: "ERROR", data: {} }
		}
	}
}



const confirmPaymentHelper = async(payload, language, mode, transaction) => {
	try{

		const dgStoreOrderId = payload.order_id;
		const checkExistingOrder = await Models.Order.findOne({ where: { 'data.order_id': dgStoreOrderId } });
		if(checkExistingOrder) return { success: true, message: "ORDER_ALREADY_EXISTS", data: checkExistingOrder };

		const dgStoreProductId = payload.product_id;
		if(!dgStoreProductId) return { success: false, message: "INVALID_PRODUCT_ID_PROVIDED_BY_GATEWAY", data: {} };
		

		const videoIdExists = await Models.VideoProduct.findOne({ where: { digistoreId: dgStoreProductId } });
        if(!videoIdExists) {
            await transaction.commit();
            return h.response({success:true,message:req.i18n.__("NO_VIDEO"),responseData:{}}).code(200);
        }


		const topicInfo = await Models.Video.findOne({ where: { id: videoIdExists.videoId } });
		if(!topicInfo) {
			return { success: true, message: "VIDEO_NOT_AVAILABLE", data: checkExistingOrder };
		}
		const videoId = topicInfo.id;
		const isSubscriptionBased = topicInfo.isSubscriptionBased;

		let userId = null;
		if(payload?.userId) userId = payload?.userId;
		

		if(userId === null) {
			//let language = language;
			const videoLanguageInfo = await Models.VideoLanguage.findOne({ where: { videoId: videoId } });
			if(videoLanguageInfo) language = videoLanguageInfo.languageCode;
			const data = {
				email: payload.buyer_email, firstName: payload.buyer_first_name,
				lastName: payload.buyer_last_name, existingUserEmailSend: 1
			}
			let responseData = await Axios({
				method: "post", headers: {language: language}, data: data,
				url: `${process.env.USER_ONBOARDING_DOMAIN}/create-dg-user`
			});
			responseData = responseData.data.userId;
			if(responseData) userId = responseData;
		}
		
		let order = await Models.Order.create({ mode: mode, videoId: videoId, userId : userId, isPaid: 1, data: payload }, {transaction});
		if(!order) return { success: false, message: "PAYMENT_NOT_CONFIRMED", data: {} }
		
		let date= Moment(new Date());
		let endDate= Moment(new Date()).add(1, "month");
		const createdEntry = await Models.PurchasedTopic.create({
			userId: userId,
			videoId: videoId,
			hasPassed: 0,
			purchaseDate: date,
			startingDate: date,
			endDate: isSubscriptionBased == 1 ? endDate: null,
			activeSubscription: isSubscriptionBased == 1 ? 1 : null,
			status: 1
		},{transaction});

		const parentId = createdEntry.id;

		const vidInfo = await Models.Video.findOne({
			where : { id : videoId },
			include : {
				model : Models.VideoContent,
				attributes : ["name", "subTitle", "videoLink"]
			},
			attributes : ["topicType"]
		})

		const userObj = await Models.User.findOne({
			where : { userId : userId }
		});

		let earningHistoryObj = {
			topicId: videoId, data: payload, amount: payload?.amount,
			amountReceived: payload?.earned_amount, userId: userId,
			topicObject: vidInfo, userObject: userObj
		}

		await Axios({
			method: 'post',
			url: `${process.env.USER_ONBOARDING_DOMAIN}/earning-history`,
			headers: {},
			data: earningHistoryObj
		});

		return { success: true, message: "REQUEST_SUCCESSFULL", data: order, meta: { parentId, userId } }
	}
	catch(error) {
		console.error('Error in Add orders',error)

		return { success: false, message: "ERROR", data: {} }
	}
}

const addonsHelper = async(payload, transaction) => {
	try{

		const videoIdExists = await Models.VideoProduct.findOne({ where: { digistoreId: payload.product_id } });
        if(!videoIdExists) {
            await transaction.commit();
            return h.response({success:true,message:req.i18n.__("NO_VIDEO"),responseData:{}}).code(200);
        }

		
		const topicInfo = await Models.Video.findOne({ where: { id: videoIdExists.videoId } });
		if(!topicInfo) return { success: false, message: "INVALID_PRODUCT_ID_PROVIDED_BY_GATEWAY", data: {} };
		const videoId = topicInfo.id;
		
		const alreadyExists = await Models.PurchasedTopic.findOne({ where: { userId: payload.userId, videoId: videoId } });
		if(alreadyExists) {
			return { success: true, message: "ALREADY_EXISTS", data: {} };
		}

		let date= Moment(new Date());
		let endDate= Moment(new Date()).add(1, "month");
		await Models.PurchasedTopic.create({
			userId: payload?.userId,
			videoId: videoId,
			hasPassed: 0,
			purchaseDate: date,
			startingDate: date,
			endDate: null,
			activeSubscription: null,
			status: 1,
			parentId: payload.parentId
		},{transaction});

		return { success: true, message: "REQUEST_SUCCESSFULL", data: {} }
	}
	catch(error) {
		console.error('Error in Add orders',error)
		return { success: false, message: "ERROR", data: {} }
	}
}