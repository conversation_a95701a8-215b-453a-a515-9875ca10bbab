require('dotenv').config();

module.exports = 
{
    SALT_ROUNDS          : 10,
    IV_LENGTH            : 16,
    CIPHER_ALGORITHM     : "aes-256-ctr",
    MAX_PAGINATION_LIMIT : 50,
    PLAN_TYPES : {
        FREE : 1,
        PAID : 2
    },
    ROLES : {
        SUPER_ADMIN    : 1,
        ACCOUNT_HOLDER : 2,
        END_USER       : 3
    },
    WEBHOOK: {
        USER_ONBOARDING : [
            process.env.VIDEO_DOMAIN + "/webhook/user",
            process.env.EVENT_DOMAIN + "/webhook/user"
        ]
    },
    URL:{
        ATTACHMENT_UPDATE       :   `${process.env.ATTACHMENT_DOMAIN}/attachment/update`,
        SEND_EMAIL              :   `${process.env.EMAIL_SERVICE}/email/send`
    },
    VIEWS: {
        LISTING          : 1,
        DETAILED         : 2,
        SUBVIDEODETAILED : 3
    },
    WEBHOOK_STATUS: {
        INACTIVE: 0,
        ON_PAYMENT: 1,
        ON_REFUND: 2,
        ON_CHARGEBACK: 3,
        ON_PAYMENT_MISSED: 4,
        PAYMENT_DENIAL: 5,
        ON_REBILL_CANCELLED: 6,
        ON_REBILL_RESUMED: 7
      }
}