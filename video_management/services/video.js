const Models  = require("../models/index.js");
const Common  = require("../common.js");
const Webhook = require("./webhook.js");
const Vimeo   = require('vimeo').Vimeo;
const Axios   = require("axios");
const excelToJson = require('convert-excel-to-json');
//Added by sachin khanna
const { Op, QueryTypes } = require("sequelize");

// const data =  [
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 1: Wellcome and Introduction",
//      "H": "https:\/\/vimeo.com\/398884133\/4c319f660c",
//      "L": "Important insights from the video:\n\n1\nC<PERSON><PERSON> is not a healer. Instead, he introduces you to the KUBYmethod, which helps you to learn how to solve your own problem. \n2\nLearning that you can solve your own problems means incredible freedom for you. \n3\nThe best solution for your situation comes from within yourself. You will learn exactly how to access this knowledge in this online seminar.\nYou will find your bonus material for the video seminar at the end of part 3! "
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 2: What do you want to improve in your life",
//      "H": "https:\/\/vimeo.com\/398884169\/08746db17e",
//      "L": "Important insights from the video:\n\n1\nIf we describe our illness as a project, we can more easily see the path to healing as a challenge. \n2\nThe Online Basic Seminar is divided into three parts. The first part is about finding out what you want to heal in the first place.  \n3\nYou don't have to work through the seminar in one weekend.\n\nIf you have technical problems with your computer, Johannes Transchel will help you (0.42€\/minute. He will send the invoice by email, payment by bank transfer or PayPal) You can book Johannes here."
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 3: Make a list of your projects",
//      "H": "https:\/\/vimeo.com\/*********\/eae11db0fb",
//      "L": "In this video, Clemens helps you to find your project and to find your pain picture in this context. \n\nImportant insights from part 1:\n\n1\nTake a close look at what you really want to heal \n2\nThe KUBY method is not about knowing, but about looking. Our feelings are the guideposts\n3\nWrite down a situation in the present tense that has caused you suffering"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 4: Project",
//      "H": "https:\/\/vimeo.com\/*********\/6acd190e9a"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 5:Choice of project",
//      "H": "https:\/\/vimeo.com\/398884172\/7967414bd4"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 6: Questionnaire",
//      "H": "https:\/\/vimeo.com\/398884153\/468e2a0020"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 7: What does illness mean for you",
//      "H": "https:\/\/vimeo.com\/398884163\/4e75971c42",
//      "L": "The questionnaire can be found below this video, please fill it out right now."
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 8: The five human images: 1 A mechanical being",
//      "H": "https:\/\/vimeo.com\/398884171\/83e7680682"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 9: The five human images: 2 A biochemical being",
//      "H": "https:\/\/vimeo.com\/398884170\/46259a2e04"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 10: The five human images: 3 An energetic being",
//      "H": "https:\/\/vimeo.com\/398903650\/7251e98928"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 11: The five human images: 4 An information being",
//      "H": "https:\/\/vimeo.com\/398903637\/76982bbd9c"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 12: The five human images: 5 Emotional-spiritual being",
//      "H": "https:\/\/vimeo.com\/398903614\/121d33a187"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 13: Since when",
//      "H": "https:\/\/vimeo.com\/398903616\/7fd6e36b1f"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 14: Interim State",
//      "H": "https:\/\/vimeo.com\/398903593\/4db53efbde"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 15: What has happened before?",
//      "H": "https:\/\/vimeo.com\/398903615\/86ae1b00db"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 16: The project you took over from someone",
//      "H": "https:\/\/vimeo.com\/398903591\/83976c92eb"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 17: Project - Diabetes",
//      "H": "https:\/\/vimeo.com\/398903648\/99e3f1b67f"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 18: Brain frequencies and intuition",
//      "H": "https:\/\/vimeo.com\/398903582\/2db91322bf"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 19:Task breathing exercise",
//      "H": "https:\/\/vimeo.com\/398903606\/42bf1b7428"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 20: Intuition is soul",
//      "H": "https:\/\/vimeo.com\/398903613\/2c8253bcdc"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 21: Example cancer",
//      "H": "https:\/\/vimeo.com\/398923801\/1b48c17dbc"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 22: Soul talk",
//      "H": "https:\/\/vimeo.com\/398923859\/0b32cfcf29"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 23:Bring back the repressed stuff to your consciousness",
//      "H": "https:\/\/vimeo.com\/398923845\/8af41e9514"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 24: Your resistance is already helpful",
//      "H": "https:\/\/vimeo.com\/398923849\/08ab05f6cb"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 25: Becoming concrete with your intuition",
//      "H": "https:\/\/vimeo.com\/398923852\/016e56b95f"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 26: Finding the root causes",
//      "H": "https:\/\/vimeo.com\/398923843\/3bf8d6a3eb"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 27: How to create a pain image with the model",
//      "H": "https:\/\/vimeo.com\/398923837\/3e38859448"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 28: It is never too late for a new past",
//      "H": "https:\/\/vimeo.com\/398923868\/9efbecf234"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 29: The pain image as a scene",
//      "H": "https:\/\/vimeo.com\/398923815\/b577d86bcf"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 30: The link between the project and the pain image",
//      "H": "https:\/\/vimeo.com\/398956546\/a0633c2a40"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 31: Project - sleeping problems",
//      "H": "https:\/\/vimeo.com\/398923860\/1cc4cba297"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 32: Project - unfulfilled wish to have a child",
//      "H": "https:\/\/vimeo.com\/398923865\/d5e5ff2830"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Video 33: Final word",
//      "H": "https:\/\/vimeo.com\/398923799\/a9e91e4d05"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 2",
//      "D": "Working on the pain image",
//      "G": "Video 1: Introduction Part 2 Working on the pain image",
//      "H": "https:\/\/vimeo.com\/399103508\/db09d92bbb",
//      "L": "Welcome to the second part of the Healthy without Medicine seminar."
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 2",
//      "D": "Working on the pain image",
//      "G": "Video 2: Project  - Tinnitus",
//      "H": "https:\/\/vimeo.com\/399103584\/91c21ccdc7"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 2",
//      "D": "Working on the pain image",
//      "G": "Video 3: How do I write down a pain image correctly",
//      "H": "https:\/\/vimeo.com\/399103534\/9a2b929265"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 2",
//      "D": "Working on the pain image",
//      "G": "Video 4: The project as a measure for success",
//      "H": "https:\/\/vimeo.com\/399103548\/c7db4f98f6"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 2",
//      "D": "Working on the pain image",
//      "G": "Video 5: Project - Paralysis of the legs",
//      "H": "https:\/\/vimeo.com\/399103603\/a163e245d7"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 2",
//      "D": "Working on the pain image",
//      "G": "Video 6: Project- relation with your mother",
//      "H": "https:\/\/vimeo.com\/399103593\/9e4845cb2f"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 2",
//      "D": "Working on the pain image",
//      "G": "Video 7: Project - fear of flying",
//      "H": "https:\/\/vimeo.com\/399103555\/8597c6acc6"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 2",
//      "D": "Working on the pain image",
//      "G": "Video 8: From the pain image to the re-writing",
//      "H": "https:\/\/vimeo.com\/399103564\/412f289c7e"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 2",
//      "D": "Working on the pain image",
//      "G": "Video 9:  Project - inguinal hernia",
//      "H": "https:\/\/vimeo.com\/399103580\/1288c72348"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 2",
//      "D": "Working on the pain image",
//      "G": "Video 10: Project - Spiritual helper with sleeping problems",
//      "H": "https:\/\/vimeo.com\/399103591\/5bfdb38278"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 2",
//      "D": "Working on the pain image",
//      "G": "Video 11: End of part 2 summary and preview on part 3",
//      "H": "https:\/\/vimeo.com\/399103536\/**********"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 1: Introduction Part 3",
//      "H": "https:\/\/vimeo.com\/399933955\/26a3cf58f5",
//      "L": "Welcome to the third part of the Healthy without Medicine seminar."
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 2: From the pain image to the re-writing",
//      "H": "https:\/\/vimeo.com\/399933947\/c03f540909"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 3: Re-writing of the project- tinnitus",
//      "H": "https:\/\/vimeo.com\/399933977\/c97ba53253"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 4: Check your re-writing",
//      "H": "https:\/\/vimeo.com\/399933897\/c0ad737ddf"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 5: Spiritual helpers in the re-writing",
//      "H": "https:\/\/vimeo.com\/399933925\/2d4f0d3303"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 6: Re-writing with a spiritual helper- project back ache",
//      "H": "https:\/\/vimeo.com\/399933972\/04f97a8f39"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 7: Rewriting requires creativity and imagination",
//      "H": "https:\/\/vimeo.com\/399933937\/d782e17788"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 8: Questions about the pain image",
//      "H": "https:\/\/vimeo.com\/399933913\/3ea4435134"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 9: Questions about diagnosis and their true content",
//      "H": "https:\/\/vimeo.com\/399933929\/6cfcbe19a3"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 10: Question- does the soul know the re-writing in advance-",
//      "H": "https:\/\/vimeo.com\/399933904\/ed793ff12e"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 11: Frage: Question- is re-writing always done in the alpha state-",
//      "H": "https:\/\/vimeo.com\/399933873\/d9691ea8cd"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 12: Frage: Question- what role does faith have in healing-",
//      "H": "https:\/\/vimeo.com\/399933841\/1112ab4f07"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 13: Re-writing- project hernia",
//      "H": "https:\/\/vimeo.com\/399933960\/fde6adcee5"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 14: Frage:  Can I also write a pain image for someone else-",
//      "H": "https:\/\/vimeo.com\/399933862\/7ce3f81e99"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 15: Can I also write  a pain image and a healing image -for things where I was the wrongdoer-",
//      "H": "https:\/\/vimeo.com\/399933893\/126325e6a6"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 16: Frage: What do I need the re-writing for-",
//      "H": "https:\/\/vimeo.com\/399933859\/f51d56549f"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 17: Frage: How do I manifest a re-writing-",
//      "H": "https:\/\/vimeo.com\/399933869\/74fb7d085f"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 18: The healing drama-",
//      "H": "https:\/\/vimeo.com\/399933855\/80767f7a2a"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 19:  Mantra",
//      "H": "https:\/\/vimeo.com\/399933921\/78d5389a5c"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 20: Thought control",
//      "H": "https:\/\/vimeo.com\/399933928\/d84a5f9a73"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 21: Mantras in Tibet",
//      "H": "https:\/\/vimeo.com\/399933892\/505874aeda"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 22: Affirmation",
//      "H": "https:\/\/vimeo.com\/399933940\/1e5a88e56b"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 23: Questionnaire Part 2",
//      "H": "https:\/\/vimeo.com\/399933801\/2a00a97695",
//      "L": "The questionnaire can be found below this video, please fill it out right now."
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Creating your own reality",
//      "G": "Video 24: Final words",
//      "H": "https:\/\/vimeo.com\/399933761\/2112b23534"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Manifestiere dein Heilbild",
//      "G": "Bonus 1: Gesund ohne Medizin Manuskript (PDF)",
//      "H": "https:\/\/vimeo.com\/864358708\/d5583ca12b?share=copy",
//      "I": "https:\/\/www.kubymethode.com\/wp-content\/uploads\/2020\/11\/Gesund-ohne-Medizin-Manuskript.pdf",
//      "J": "You can download the manuscript for the GoM video seminar here"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Manifestiere dein Heilbild",
//      "G": "Bonus 2: Strategien für erfolgreiches Seelenschreiben (PDF)",
//      "H": "https:\/\/vimeo.com\/864358758?share=copy",
//      "I": "https:\/\/www.kubymethode.com\/wp-content\/uploads\/2020\/11\/Strategien-fuer-erfolgreiches-Seelenschreiben.pdf",
//      "J": "Here you can download the document"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Manifestiere dein Heilbild",
//      "G": "Bonus 3: So kommst du in deine Intuition mit Clemens Kuby (Audio)",
//      "H": "https:\/\/vimeo.com\/483073912\/fa464874e2"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Manifestiere dein Heilbild",
//      "G": "Bonus 4: Die Bedeutung von Seelenschreiben",
//      "H": "https:\/\/vimeo.com\/493387300\/b22a7d9819"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Manifestiere dein Heilbild",
//      "G": "Bonus 5: Die Wahrheit ist nur eine Interpretation (unveröffentlichtes Video von Clemens Kuby)",
//      "H": "https:\/\/vimeo.com\/255797275\/a1bbf7a65e",
//      "L": "Dieses Video ist während der Dreharbeiten zum 1. Semester des KUBYstudiums entstanden."
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 3",
//      "D": "Manifestiere dein Heilbild",
//      "G": "Bonus 6: Die 12 Stationen beim Seelenschreiben®",
//      "H": "https:\/\/vimeo.com\/864358671\/dba700dd6f?share=copy",
//      "L": "https:\/\/www.kubymethode.com\/wp-content\/uploads\/2023\/04\/Seelenschreiben%C2%AE-12-Stationen-1.pdf"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Teil 4",
//      "D": "KUBYstarthilfe",
//      "G": "Aufzeichnung KUBYstarthilfe Meeting vom 23. November 2022",
//      "H": "https:\/\/vimeo.com\/775263159\/d25f215f58",
//      "L": "KUBYbegleiterin Annette Metzner beantwortet die Fragen der Teilnehmer.\n\n\nKUBYbegleiterin Elisabeth Weyand übernahm die Moderation. Ein persönliches Begleitungsgespräch kannst du unter folgendem Link buchen: www.clemenskuby.com\/begleiter"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Teil 4",
//      "D": "KUBYstarthilfe",
//      "G": "Aufzeichnung KUBYstarthilfe Meeting vom 07. Dezember 2022",
//      "H": "https:\/\/vimeo.com\/779188312\/331e854b86",
//      "L": "KUBYbegleiterin Annette Metzner beantwortet die Fragen der Teilnehmer.\n\n\nKUBYbegleiter Björn Thomsen übernahm die Moderation. Ein persönliches Begleitungsgespräch kannst du unter folgendem Link buchen: www.clemenskuby.com\/begleiter"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Teil 4",
//      "D": "KUBYstarthilfe",
//      "G": "Aufzeichnung KUBYstarthilfe Meeting vom 18. Januar 2023",
//      "H": "https:\/\/vimeo.com\/809035073\/e6b57cec01",
//      "L": "KUBYbegleiterin Annette Metzner beantwortet die Fragen der Teilnehmer.\n\n\nKUBYbegleiterin Elisabeth Weyand übernahm die Moderation. Ein persönliches Begleitungsgespräch kannst du unter folgendem Link buchen: www.clemenskuby.com\/begleiter"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Teil 4",
//      "D": "KUBYstarthilfe",
//      "G": "Hier anmelden für KUBYstarthilfe Meeting am 29. März 2023",
//      "H": "https:\/\/vimeo.com\/813152753\/9332af0ce4",
//      "L": "Unter folgendem Link kannst du dich für das kommende Live-Meeting anmelden: https:\/\/us06web.zoom.us\/meeting\/register\/tZMsf-Gorj0iH9xk1JITJAceaLuP3Co66y3w"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images",
//      "G": "Erklärvideo: So nutzt du die Vorlage fürs Seelenschreiben richtig",
//      "H": "https:\/\/vimeo.com\/259600950\/b2e39f1a0c",
//      "L": "In diesem Video wird dir nochmals ausführlich erklärt, was es mit den einzelnen Elementen bei der Vorlage für die Seelentexte auf sich hat.\n\nHier kannst du folgende Vorlagen herunterladen:\n\n1\nKlicken: Seelentext-Vorlage für Microsoft Word\n2\nKlicken: Seelentextvorlage für das Schreibprogramm Pages auf dem Mac-Computer\n3\nKlicken: rtf-Vorlage für alternative Schreibprogramme für Open Office"
//     },
//     {
//      "A": 314748,
//      "B": "Healthy without medicine",
//      "C": "Part 1",
//      "D": "Finding your project and the different types of human images\n",
//      "G": "Bonus: So geht Seelenschreiben in Tabellenform",
//      "H": "https:\/\/vimeo.com\/494059840\/252a7f19cd",
//      "L": "Hier kannst du folgende Vorlagen herunterladen:\n\n1\nKlicken: Seelentext-Vorlage für Microsoft Word\n2\nKlicken: Seelentextvorlage für das Schreibprogramm Pages auf dem Mac-Computer\n3\nKlicken: rtf-Vorlage für alternative Schreibprogramme für Open Office"
//     }
// ]

// const addTopicFromExcelSubTopics = async () => {
//     const transaction = await Models.sequelize.transaction();
//     const excelToJson = require('convert-excel-to-json');
//     try {
        
//         // const result = excelToJson({
//         //     sourceFile:"C:/Users/<USER>/Downloads/Illuminz • KUBYtraining(Online Training).xlsx"
//         // });

//         // console.log(JSON.stringify(result["Tabellenblatt1"]));
       
//         // return
//         let insertObj = {};
//         let topicId = 0;

//         const getAttachmentDetails = async (url) => {
//             // return null
//             try {
//                 let responseData = await Axios({
//                     method: 'post', data: { url },
//                     url: `${process.env.ATTACHMENT_DOMAIN}/uploadFromUrl`,
//                     headers: {  'Content-Type': 'application/json' }
//                 });
    
//                 return responseData.data.responseData;
//             } catch (error) {
//                 return null
//             }
//         }

//         let attachmentObj = {};
//         // console.log(" ======================  data", JSON.stringify(data))
//         console.log(" ====================== ")
//         console.log(" ====================== ")
//         // console.log(data, " == ")

//         // return 
//         for(const objIdx of data) {
            
//             const obj = objIdx;
//             // console.log(objIdx.D, "=======", objIdx.G)
//             // continue;
//             // return
//             attachmentObj = {};

//             if(objIdx < 1) { continue }

//             else if(false && objIdx == 1) {
//                 if(obj.E) {
//                     attachmentObj = await getAttachmentDetails(obj.E);
//                 }

//                 let titleObj = {
//                   topicId: null,
//                   parentId: null,
//                   isTopic: 1,
//                   status: 1,
//                   order: null,
//                   isFree: 1,
//                   accessibleDate: null,
//                   noOfDays: null,
//                   topicType: "Seminars",
//                   semester: 1,
//                   price: null,
//                   digistoreId: obj.A,
//                   VideoContents: [
//                     {
//                       name: obj.D || obj.G,
//                       userId: 1,
//                       audioLink:
//                         obj.I && obj.I.split(".").at(-1) == "mp3"
//                           ? await getAttachmentDetails(obj.I).then((res) => {
//                               return res?.path || null;
//                             })
//                           : null,
//                       videoLink: obj.H,
//                       languageCode: "en",
//                       shortText: null,
//                       description: obj.L,
//                       subTitle: obj.G
//                     }
//                   ],
//                   attachments:
//                     attachmentObj?.id || null
//                       ? [
//                           {
//                             name: attachmentObj.originalName,
//                             attachmentId: attachmentObj.id,
//                             attachmentUrl: attachmentObj.path,
//                             userId: 1,
//                             mimeType: attachmentObj.mimeType,
//                             isThumbnail: 0,
//                             isPoster: 0
//                           }
//                         ]
//                       : []
//                 };

//                 console.log(titleObj);

//                 const createdVideo = await Models.Video.create(titleObj, {
//                     include : [{
//                         model : Models.VideoContent
//                     }, {
//                         model : Models.Attachment,
//                         as : "attachments",
//                     }],
//                     transaction
//                 });

//                 topicId = createdVideo.id;
//                 continue;
//             }

//             topicId = 8164;

//             if(!insertObj[obj.C])
//             {
//                 if(obj.E)
//                 {
//                     attachmentObj = await getAttachmentDetails(obj.E)
//                 }

//                 console.log(obj.D, obj.G, " =============== D G")
//                 console.log(obj, " ===================== >>> obj")

//                 let videoObj = {
//                     topicId        : topicId,
//                     parentId       : null,
//                     isTopic        : 0,
//                     status         : 1,
//                     order          : null,
//                     isFree         : 1,
//                     accessibleDate : null,
//                     noOfDays       : null,
//                     price          : null,
//                     VideoContents  : [{
//                         name         : obj.D || obj.G, 
//                         userId       : 1,
//                         audioLink    : obj.I && obj.I.split(".").at(-1) == "mp3" ? await getAttachmentDetails(obj.I).then((res) => {
//                             console.log(" ======================== ")
//                             console.log(res, " ============== res of attachment")
//                             console.log(" ======================== ")
//                             return res?.path || null
//                         }) : null,
//                         videoLink    : obj.H,
//                         languageCode : "en",
//                         shortText    : null,
//                         description  : obj.L,
//                         subTitle     : obj.G
//                     }],
//                     attachments    : attachmentObj?.id || null ? [{
//                         name          : attachmentObj.originalName,
//                         attachmentId  : attachmentObj.id,
//                         attachmentUrl : attachmentObj.path,
//                         userId        : 1,
//                         mimeType      : attachmentObj.mimeType,
//                         isThumbnail   : 0,
//                         isPoster      : 0
//                     }] : []
//                 };

//                 console.log(videoObj, " ======= video obj 1");
//                 const createdVideo = await Models.Video.create(videoObj, {
//                     include : [{
//                         model : Models.VideoContent
//                     }, {
//                         model : Models.Attachment,
//                         as : "attachments",
//                     }],
//                     transaction
//                 });

//                 insertObj[obj.C] = createdVideo.id;



//                 let subVideoObj = {
//                     topicId        : topicId,
//                     parentId       : createdVideo.id,
//                     isTopic        : 0,
//                     status         : 1,
//                     order          : null,
//                     isFree         : 1,
//                     accessibleDate : null,
//                     noOfDays       : null,
//                     price          : null,
//                     VideoContents  : [{
//                         name         : obj.G || obj.D, 
//                         userId       : 1,
//                         audioLink    : obj.I && obj.I.split(".").at(-1) == "mp3" ? await getAttachmentDetails(obj.I).then((res) => {return res?.path || null}) : null,
//                         videoLink    : obj.H,
//                         languageCode : "en",
//                         shortText    : null,
//                         description  : obj.L,
//                         subTitle     : obj.G
//                     }],
//                     attachments    : attachmentObj?.id || null ? [{
//                         name          : attachmentObj.originalName,
//                         attachmentId  : attachmentObj.id,
//                         attachmentUrl : attachmentObj.path,
//                         userId        : 1,
//                         mimeType      : attachmentObj.mimeType,
//                         isThumbnail   : 0,
//                         isPoster      : 0
//                     }] : []
//                 };

//                 await Models.Video.create(subVideoObj, {
//                     include : [{
//                         model : Models.VideoContent
//                     }, {
//                         model : Models.Attachment,
//                         as : "attachments",
//                     }],
//                     transaction
//                 });


//             }
//             else
//             {
//                 if(obj.E)
//                 {
//                     attachmentObj = await getAttachmentDetails(obj.E)
//                 }

//                 let videoObj = {
//                     topicId        : topicId,
//                     parentId       : insertObj[obj.C],
//                     isTopic        : 0,
//                     status         : 1,
//                     order          : null,
//                     isFree         : 1,
//                     accessibleDate : null,
//                     noOfDays       : null,
//                     price          : null,
//                     VideoContents  : [{
//                         name         : obj.G || obj.D, 
//                         userId       : 1,
//                         audioLink    : obj.I && obj.I.split(".").at(-1) == "mp3" ? await getAttachmentDetails(obj.I).then((res) => {return res?.path || null}) : null,
//                         videoLink    : obj.H,
//                         languageCode : "en",
//                         shortText    : null,
//                         description  : obj.L,
//                         subTitle     : obj.G
//                     }],
//                     attachments    : attachmentObj?.id || null ? [{
//                         name          : attachmentObj.originalName,
//                         attachmentId  : attachmentObj.id,
//                         attachmentUrl : attachmentObj.path,
//                         userId        : 1,
//                         mimeType      : attachmentObj.mimeType,
//                         isThumbnail   : 0,
//                         isPoster      : 0
//                     }] : []
//                 };

//                 console.log(videoObj);

//                 const createdVideo = await Models.Video.create(videoObj, {
//                     include : [{
//                         model : Models.VideoContent
//                     }, {
//                         model : Models.Attachment,
//                         as : "attachments",
//                     }],
//                     transaction
//                 });
//             }
//         }

//         await transaction.commit();
//     } catch (error) {
//         console.log(error);
//         await transaction.rollback();
//     }
// }

// setTimeout(async() => {
//     console.log(" executed ")
//     await addTopicFromExcelSubTopics();
// }, 50000);


const addTopicFromExcel = async () => {
    const transaction = await Models.sequelize.transaction();

    try {
        const result = excelToJson({
            sourceFile: '/Users/<USER>/Documents/Work/kuby_backend/video_management/Illuminz • Reinkarnations-Seminar_.xlsx'
        });

        console.log(result["Tabellenblatt1"]);
        // console.log(result[0]);

        let insertObj = {};
        let topicId = 0;

        const getAttachmentDetails = async (url) => {
            try {
                let responseData = await Axios({
                    method  : 'post',
                    url     : `${process.env.ATTACHMENT_DOMAIN}/uploadFromUrl`,
                    headers: { 
                        'Content-Type': 'application/json'
                    },
                    data    : { url }
                });
    
                return responseData.data.responseData;
            } catch (error) {
                return null
            }
        }

        let attachmentObj = {};

        for(const objIdx in result["Tabellenblatt1"])
        {
            attachmentObj = {};
            const obj = result["Tabellenblatt1"][objIdx];

            if(objIdx < 1)
            {
                continue;
            }
            else if(objIdx == 1)
            {
                if(obj.E)
                {
                    attachmentObj = await getAttachmentDetails(obj.E);
                }

                let titleObj = {
                    topicId        : null,
                    parentId       : null,
                    isTopic        : 1,
                    status         : 1,
                    order          : null,
                    isFree         : 1,
                    accessibleDate : null,
                    noOfDays       : null,
                    topicType      : "Seminars",
                    semester       : 1,
                    price          : null,
                    digistoreId    : obj.A,
                    VideoContents  : [{
                        name         : obj.D || obj.G, 
                        userId       : 1,
                        audioLink    : obj.I && obj.I.split(".").at(-1) == "mp3" ? await getAttachmentDetails(obj.I).then((res) => {return res?.path || null}) : null,
                        videoLink    : obj.H,
                        languageCode : "en",
                        shortText    : null,
                        description  : obj.L,
                        subTitle     : obj.G
                    }],
                    attachments    : attachmentObj?.id || null ? [{
                        name          : attachmentObj.originalName,
                        attachmentId  : attachmentObj.id,
                        attachmentUrl : attachmentObj.path,
                        userId        : 1,
                        mimeType      : attachmentObj.mimeType,
                        isThumbnail   : 0,
                        isPoster      : 0
                    }] : []
                };

                console.log(titleObj);

                const createdVideo = await Models.Video.create(titleObj, {
                    include : [{
                        model : Models.VideoContent
                    }, {
                        model : Models.Attachment,
                        as : "attachments",
                    }],
                    transaction
                });

                topicId = createdVideo.id;
                continue;
            }

            if(!insertObj[obj.C])
            {
                if(obj.E)
                {
                    attachmentObj = await getAttachmentDetails(obj.E)
                }

                let videoObj = {
                    topicId        : topicId,
                    parentId       : null,
                    isTopic        : 0,
                    status         : 1,
                    order          : null,
                    isFree         : 1,
                    accessibleDate : null,
                    noOfDays       : null,
                    price          : null,
                    VideoContents  : [{
                        name         : obj.D || obj.G, 
                        userId       : 1,
                        audioLink    : obj.I && obj.I.split(".").at(-1) == "mp3" ? await getAttachmentDetails(obj.I).then((res) => {return res?.path || null}) : null,
                        videoLink    : obj.H,
                        languageCode : "en",
                        shortText    : null,
                        description  : obj.L,
                        subTitle     : obj.G
                    }],
                    attachments    : attachmentObj?.id || null ? [{
                        name          : attachmentObj.originalName,
                        attachmentId  : attachmentObj.id,
                        attachmentUrl : attachmentObj.path,
                        userId        : 1,
                        mimeType      : attachmentObj.mimeType,
                        isThumbnail   : 0,
                        isPoster      : 0
                    }] : []
                };

                console.log(videoObj);
                const createdVideo = await Models.Video.create(videoObj, {
                    include : [{
                        model : Models.VideoContent
                    }, {
                        model : Models.Attachment,
                        as : "attachments",
                    }],
                    transaction
                });

                insertObj[obj.C] = createdVideo.id;
            }
            else
            {
                if(obj.E)
                {
                    attachmentObj = await getAttachmentDetails(obj.E)
                }

                let videoObj = {
                    topicId        : topicId,
                    parentId       : insertObj[obj.C],
                    isTopic        : 0,
                    status         : 1,
                    order          : null,
                    isFree         : 1,
                    accessibleDate : null,
                    noOfDays       : null,
                    price          : null,
                    VideoContents  : [{
                        name         : obj.D || obj.G, 
                        userId       : 1,
                        audioLink    : obj.I && obj.I.split(".").at(-1) == "mp3" ? await getAttachmentDetails(obj.I).then((res) => {return res?.path || null}) : null,
                        videoLink    : obj.H,
                        languageCode : "en",
                        shortText    : null,
                        description  : obj.L,
                        subTitle     : obj.G
                    }],
                    attachments    : attachmentObj?.id || null ? [{
                        name          : attachmentObj.originalName,
                        attachmentId  : attachmentObj.id,
                        attachmentUrl : attachmentObj.path,
                        userId        : 1,
                        mimeType      : attachmentObj.mimeType,
                        isThumbnail   : 0,
                        isPoster      : 0
                    }] : []
                };

                console.log(videoObj);

                const createdVideo = await Models.Video.create(videoObj, {
                    include : [{
                        model : Models.VideoContent
                    }, {
                        model : Models.Attachment,
                        as : "attachments",
                    }],
                    transaction
                });
            }
        }

        await transaction.commit();
    } catch (error) {
        console.log(error);
        await transaction.rollback();
    }
}

const getAuthorizedVideo = async() => {
    const transaction = await Models.sequelize.transaction();

    try {
        const videoUrls = await Models.VideoContent.findAll({
            where : {
                vimeoStatus : '0'
            },
            limit : 15,
            order : [["id", "DESC"]]
        });
    
        for(const video of videoUrls)
        {
            let videoUrl = video.dataValues.videoLink
    
            const clientId     = process.env.CLIENT_IDENTIFIER;
            const clientSecret = process.env.CLIENT_SECRET;
            const accessToken  = process.env.TOKEN;
    
            let client = new Vimeo(clientId, clientSecret, accessToken);

            try {
                const url = await new Promise((resolve, reject) => {
                    client.request({
                        method : 'GET', path   : '/videos',
                        query: { links: videoUrl },
                    }, async function (error, body, status_code, headers) {
                        if (error) {
                            console.log("IN ERROR BLOCK", error);
        
                            await Models.VideoContent.update({
                                vimeoStatus : '2'
                            }, {
                                where : { id : video.dataValues.id },
                                transaction
                            });

                            reject(null)
                        }
                        else {
                            await Models.VideoContent.update({
                                vimeoStatus : '1',
                                videoLink   : body.data[0].player_embed_url
                            }, {
                                where : {
                                    id : video.dataValues.id
                                },
                                transaction
                            });

                            const vimeoUrl = (body.data[0].player_embed_url).split("?")[0];
                            const vimeoId  = vimeoUrl.split("/").at(-1);

                            client.request({
                                method : 'put',
                                path   : `/videos/${vimeoId}/privacy/domains/${process.env.VIMEO_WHITELIST_DOMAIN}`,
                            }, async function (error, body, status_code, headers) {
                                if (error) {
                                    console.log("White List domain Error", error);
                                }
                                else {
                                    console.log("White listed successfully");
                                }
                            });

                            resolve(body.data[0].player_embed_url)
                        }
                    })
                })
            } catch (error) {
                continue;
            }
        }

        await transaction.commit();
        return true;
    } catch (error) {
        await transaction.rollback();
        return false;
    }
}

const getLinkArray = async() => {
    const transaction = await Models.sequelize.transaction();

    try {
        const videoUrls = await Models.VideoContent.findAll({
            where : {
                linkArray : null,
                videoLink : {
                    [Op.not] : null
                }
            },
            limit : 30,
            order : [["id", "DESC"]]
        });
    
        for(const video of videoUrls)
        {
            let videoUrl = video.dataValues.videoLink
            const vimeoUrl = videoUrl.split("?")[0];
            const vimeoId  = vimeoUrl.split("/").at(-1);
    
            const clientId     = process.env.CLIENT_IDENTIFIER;
            const clientSecret = process.env.CLIENT_SECRET;
            const accessToken  = process.env.TOKEN;
    
            let client = new Vimeo(clientId, clientSecret, accessToken);

            try {
                const url = await new Promise((resolve, reject) => {
                    client.request({
                        method : 'GET',
                        path   : `/videos/${vimeoId}`
                    }, async function (error, body, status_code, headers) {
                        if (error) {
                            reject(null)
                        }
                        else {
                            let linkArray = [];

                            if(!body?.play?.progressive)
                            {
                                reject(null)
                            }
                            else
                            {
                                for(const linkObj of body.play.progressive)
                                {
                                    linkArray.push({
                                        link      : linkObj.link,
                                        rendition : linkObj.rendition
                                    })
                                }

                                await Models.VideoContent.update({
                                    linkArray : linkArray
                                }, {
                                    where : {
                                        id : video.dataValues.id
                                    },
                                    transaction
                                });

                                console.log("Link Array Updated");

                                resolve(null)
                            }
                        }
                    })
                })
            } catch (error) {
                continue;
            }
        }

        await transaction.commit();
        return true;
    } catch (error) {
        console.error(error);

        await transaction.rollback();
        return false;
    }
}

const whiteListDomain = async() => {
    const transaction = await Models.sequelize.transaction();

    try {
        const videoUrls = await Models.VideoContent.findAll({
            where : {
                vimeoStatus : '2'
            },
            limit : 45,
            order : [["updated_at", "DESC"]]
        });
    
        for(const video of videoUrls)
        {
            let videoUrl = video.dataValues.videoLink
    
            const clientId     = process.env.CLIENT_IDENTIFIER;
            const clientSecret = process.env.CLIENT_SECRET;
            const accessToken  = process.env.TOKEN;
    
            let client = new Vimeo(clientId, clientSecret, accessToken);

            try {
                const url = await new Promise((resolve, reject) => {
                    const vimeoUrl = videoUrl.split("?")[0];
                    const vimeoId  = vimeoUrl.split("/").at(-1);

                    client.request({
                        method : 'put',
                        path   : `/videos/${vimeoId}/privacy/domains/${process.env.VIMEO_WHITELIST_DOMAIN}`,
                    }, async function (error, body, status_code, headers) {
                        if (error) {
                            console.log("White List domain Error", error);
                            reject(null);
                        }
                        else {
                            await Models.VideoContent.update({
                                vimeoStatus : '1'
                            }, {
                                where : {
                                    id : video.dataValues.id
                                },
                                transaction
                            });

                            console.log("White listed successfully");
                            resolve(null);
                        }
                    });
                })
            } catch (error) {
                continue;
            }
        }

        await transaction.commit();
        return true;
    } catch (error) {
        await transaction.rollback();
        return false;
    }
}

module.exports = 
{
    validateTopic: async(topicId, transaction) => {
        const validTopic = await Models.Video.findOne({
            where : {
                id      : topicId,
                isTopic : 1
            }
        }, {transaction});

        if(validTopic)
        {
            return true;
        }
        else
        {
            return false;
        }
    },
    validateVideo: async(topicId, videoId, transaction) => {
        const validVideo = await Models.Video.findOne({
            where : {
                topicId : topicId,
                id      : videoId
            }
        }, {transaction});

        if(validVideo)
        {
            return true;
        }
        else
        {
            return false;
        }
    },
    createVideo: async(videos, transaction) => {
        const createdVideo = await Models.Video.bulkCreate(videos, {
            include : [{
                model : Models.VideoContent
            }, {
                model : Models.Attachment,
                as : "attachments",
            }],
            transaction
        });

        return createdVideo;
    },
    getMultiLingual: async(whereClause, limit, pageNumber, languageCode, by, val, userDetails, flag = false) => {


        let languageWhere = {}
        let languageRequired = false;
        if(userDetails?.Role != "admin") {
            if(flag === true) {
                languageRequired = true;
                languageWhere = { languageCode }
            }
        }


        const count = await Models.Video.count({
            where : whereClause,
            include : [{
                required   : true,
                model      : Models.VideoContent,
                attributes : ["id"],
                // where: languageWhere
            },
            {
                required   : languageRequired,
                model      : Models.VideoLanguage,
                where: languageWhere,
                // attributes: ["id"]
            },
        ] 
        })

        const topic = await Models.Video.findAll({
            attributes : [[Models.sequelize.literal(`
                JSON_OBJECTAGG(VideoContents.language_code,
                    JSON_OBJECT('id', Video.id, 'isSubscriptionBased', Video.is_subscription_based, 'salesPageLink', Video.sales_page_link, 'digistoreId', Video.digistore_id, 'digistoreIdString', Video.digistore_id_string,
                            'isFree', Video.is_free, 'price', Video.price, 'accessibleDate', date(accessible_date),
                            'noOfDays', Video.no_of_days, 'name', VideoContents.name, 'subTitle',
                            sub_title, 'audioLink', audio_link, 'videoLink', video_link, 'linkArray',
                            link_array, 'shortText', short_text, 'description', description, 'topicType',
                            Video.topic_type, 'semester', Video.semester, 'canShowVideo',
                            if(date(NOW()) >= if(accessible_date is NUll, date(NOW()), date(accessible_date)), 1, 0),
                            'amountWatched',
                            COALESCE(VideoUsers.amount_watched, 0),
                            'userReview',
                            JSON_OBJECT("rating", rating, "comment", comment, "createdAt", VideoUsers.created_at, "updatedAt", VideoUsers.updated_at),
                            'posterImage',
                            IF(posterImage.attachment_id IS NULL, CAST( 'null' AS JSON ), JSON_OBJECT('attachmentId',
                                posterImage.attachment_id, 'attachmentUrl', posterImage.attachment_url, 'mimeType',
                                posterImage.mime_type, 'name', posterImage.name))
                                ))
            `), "content"],
            [Models.sequelize.literal(`
                IF(COUNT(attachments.attachment_id) = 0, JSON_ARRAY(), JSON_ARRAYAGG(JSON_OBJECT('id',
                    attachments.attachment_id,
                    'path',
                    attachments.attachment_url,
                    'mimeType',
                    attachments.mime_type,
                    'originalName',
                    attachments.name)))
            `), "attachmentsArr"],


             "id", "topicType", "semester", "isTopic", "digistoreId","digistoreIdString", "noOfDays", "price"],
            where : whereClause,
            include : [{
                required   : true,
                model      : Models.VideoContent,
                attributes : ["id"],
                // where: languageWhere
            }, 
            {
                required   : languageRequired,
                model      : Models.VideoLanguage,
                where: languageWhere,
                // attributes: ["id"]
            },
            {
                required   : false,
                model      : Models.Attachment,
                as         : "posterImage",
                where      : {
                    isPoster : 1
                },
                attributes : ["id"]
            }, {
                required   : false,
                model      : Models.Attachment,
                as         : "attachments",
                where      : {
                    isPoster : 0
                },
                attributes : ["id"]
            }, {
                required   : false,
                model      : Models.VideoUser,
                attributes : ["id", "totalLength", "amountWatched", "percentageWatched", "rating", "comment"],
                where      : userDetails ? {
                    userId : userDetails.User.id
                } : {}
            }],
            limit    : limit,
            offset   : (pageNumber - 1) * limit,
            group    : Models.sequelize.col("VideoContents.video_id"),
            subQuery : false,
            order    : [[by, val]]
        });

        const responseArray = [];

        for(const topicRow of topic) {


            console.log(topicRow, " ================== topic row")


            const rating = await avgRating(topicRow.dataValues.id);
            let videoLanguagesList = await Models.VideoLanguage.findAll({ where: { videoId: topicRow.dataValues.id } })
            if(topicRow.dataValues.content[languageCode])
            {
                topicRow.dataValues.content[languageCode].posters = topicRow.dataValues.posterImage;
                topicRow.dataValues.content[languageCode].attachments = topicRow.dataValues.attachmentsArr;
                topicRow.dataValues.content[languageCode].rating = rating;
                topicRow.dataValues.content[languageCode].videoLanguages = videoLanguagesList;

                if(topicRow.dataValues.isTopic == 1 && userDetails?.Role != "admin" && userDetails)
                {
                    const userVideoDetails = await Models.PurchasedTopic.findOne({
                        where : {
                            userId  : userDetails.User.id,
                            videoId : topicRow.dataValues.id,
                            status: {[Op.or]: [1,7]}
                        }
                    });

                    let isPurchased = 0

                    if(userVideoDetails)
                    {
                        isPurchased = 1
                    }

                    topicRow.dataValues.content[languageCode].percentageWatched = +(await getPercentage(topicRow.dataValues.id, null, userDetails.User.id))
                    topicRow.dataValues.content[languageCode].isPurchased  = isPurchased;
                    topicRow.dataValues.content[languageCode].startingDate = isPurchased ? userVideoDetails.startingDate : "";
                    topicRow.dataValues.content[languageCode].hasPassed    = isPurchased ? userVideoDetails.hasPassed : "";

                    let customField = {
                        videoId : topicRow.dataValues.id, 
                        userId : userDetails.User.id
                    };

                    customField = JSON.stringify(customField);

                    const dgStoreUrlObj = await Common.DgStoreRequest(`https://www.digistore24.com/api/call/createBuyUrl/?product_id=${topicRow.dataValues.digistoreId}&payment_plan[first_amount]=${topicRow.dataValues.price}&tracking[custom]=${customField}`)

                    isPurchased == 0 ? topicRow.dataValues.content[languageCode].purchaseLink = dgStoreUrlObj?.data?.data?.url : topicRow.dataValues.content[languageCode].purchaseLink = ""
                }
                else
                {
                    topicRow.dataValues.content[languageCode].percentageWatched = topicRow.dataValues.VideoUsers[0]?.percentageWatched ? +(+topicRow.dataValues.VideoUsers[0]?.percentageWatched).toFixed(2) : 0;
                }

                if(userDetails?.Role != "admin" && userDetails)
                {
                    if(!topicRow.dataValues.content[languageCode].canShowVideo)
                    {
                        topicRow.dataValues.content[languageCode].videoLink = null;
                    }
                }

                delete topicRow.dataValues.content[languageCode].canShowVideo;

                responseArray.push(topicRow.dataValues.content[languageCode])
            }
            else
            {
                console.log(topicRow.dataValues.content)

                let availableLanguage = "en"
                for(let item in topicRow.dataValues.content) {
                    availableLanguage = item;
                }


                topicRow.dataValues.content[availableLanguage] = topicRow.dataValues.content[availableLanguage] ? topicRow.dataValues.content[availableLanguage] : {};

                topicRow.dataValues.content[availableLanguage]["posters"] = topicRow.dataValues.posterImage;
                topicRow.dataValues.content[availableLanguage]["attachments"] = topicRow.dataValues.attachmentsArr;
                topicRow.dataValues.content[availableLanguage]["rating"] = rating;

                if(topicRow.dataValues.isTopic == 1 && userDetails?.Role != "admin" && userDetails)
                {
                    const userVideoDetails = await Models.PurchasedTopic.findOne({
                        where : {
                            userId  : userDetails.User.id,
                            videoId : topicRow.dataValues.id,
                            status: {[Op.or]: [1,7]}
                        }
                    });

                    let isPurchased = 0

                    if(userVideoDetails)
                    {
                        isPurchased = 1
                    }

                    topicRow.dataValues.content[availableLanguage]["percentageWatched"] = +(await getPercentage(topicRow.dataValues.id, null, userDetails.User.id))
                    topicRow.dataValues.content[availableLanguage]["isPurchased"]  = isPurchased;
                    topicRow.dataValues.content[availableLanguage]["startingDate"] = isPurchased ? userVideoDetails.startingDate : "";
                    topicRow.dataValues.content[availableLanguage]["hasPassed"]    = isPurchased ? userVideoDetails.hasPassed : "";

                    let customField = {
                        videoId : topicRow.dataValues.id, 
                        userId : userDetails.User.id
                    };

                    customField = JSON.stringify(customField);

                    const dgStoreUrlObj = await Common.DgStoreRequest(`https://www.digistore24.com/api/call/createBuyUrl/?product_id=${topicRow.dataValues.digistoreId}&payment_plan[first_amount]=${topicRow.dataValues.price}&tracking[custom]=${customField}`)

                    console.log("dgStoreUrlObj", dgStoreUrlObj);

                    isPurchased == 0 ? topicRow.dataValues.content[availableLanguage]["purchaseLink"] = dgStoreUrlObj?.data?.data?.url : topicRow.dataValues.content[availableLanguage].purchaseLink = ""
                }
                else
                {
                    topicRow.dataValues.content[availableLanguage]["percentageWatched"] = topicRow.dataValues.VideoUsers[0]?.percentageWatched ? +(+topicRow.dataValues.VideoUsers[0]?.percentageWatched).toFixed(2) : 0;
                }

                if(userDetails?.Role != "admin" && userDetails)
                {
                    if(!topicRow.dataValues.content[availableLanguage]["canShowVideo"])
                    {
                        topicRow.dataValues.content[availableLanguage]["videoLink"] = null;
                    }
                }

                topicRow.dataValues.content[availableLanguage].videoLanguages = videoLanguagesList;
                delete topicRow.dataValues.content[availableLanguage]["canShowVideo"];

                responseArray.push(topicRow.dataValues.content[availableLanguage])
            }
        }

        const totalPages = await Common.getTotalPages(count, limit);

        const responseData = {
            totalPages,
            perPage      : limit,
            topicList    : responseArray,
            totalRecords : count,
            baseUrl      : process.env.IMAGE_BASE_URL,
        }

        return responseData;
    },
    // getMultiLingual: async(whereClause, limit, pageNumber, languageCode, by, val, userDetails) => {
    //     const count = await Models.Video.count({
    //         where : whereClause
    //     })

    //     const topic = await Models.Video.findAll({
    //         attributes : [[Models.sequelize.literal(`
    //             JSON_OBJECTAGG(VideoContents.language_code,
    //                 JSON_OBJECT('id',
    //                         Video.id,
    //                         'salesPageLink',
    //                         Video.sales_page_link,
    //                         'digistoreId',
    //                         Video.digistore_id,
    //                         'isFree',
    //                         Video.is_free,
    //                         'price',
    //                         Video.price,
    //                         'accessibleDate',
    //                         date(accessible_date),
    //                         'noOfDays',
    //                         Video.no_of_days,
    //                         'name',
    //                         VideoContents.name,
    //                         'subTitle',
    //                         sub_title,
    //                         'audioLink',
    //                         audio_link,
    //                         'videoLink',
    //                         video_link,
    //                         'linkArray',
    //                         link_array,
    //                         'shortText',
    //                         short_text,
    //                         'description',
    //                         description,
    //                         'topicType',
    //                         Video.topic_type,
    //                         'semester',
    //                         Video.semester,
    //                         'canShowVideo',
    //                         if(date(NOW()) >= if(accessible_date is NUll, date(NOW()), date(accessible_date)), 1, 0),
    //                         'amountWatched',
    //                         COALESCE(VideoUsers.amount_watched, 0),
    //                         'userReview',
    //                         JSON_OBJECT("rating", rating, "comment", comment, "createdAt", VideoUsers.created_at, "updatedAt", VideoUsers.updated_at),
    //                         'posterImage',
    //                         IF(posterImage.attachment_id IS NULL, CAST( 'null' AS JSON ), JSON_OBJECT('attachmentId',
    //                             posterImage.attachment_id,
    //                             'attachmentUrl',
    //                             posterImage.attachment_url,
    //                             'mimeType',
    //                             posterImage.mime_type,
    //                             'name',
    //                             posterImage.name))))
    //         `), "content"],
    //         [Models.sequelize.literal(`
    //             IF(COUNT(attachments.attachment_id) = 0, JSON_ARRAY(), JSON_ARRAYAGG(JSON_OBJECT('id',
    //                 attachments.attachment_id,
    //                 'path',
    //                 attachments.attachment_url,
    //                 'mimeType',
    //                 attachments.mime_type,
    //                 'originalName',
    //                 attachments.name)))
    //         `), "attachmentsArr"], "id", "topicType", "semester", "isTopic", "digistoreId", "noOfDays", "price"],
    //         where : whereClause,
    //         include : [{
    //             required   : true,
    //             model      : Models.VideoContent,
    //             attributes : ["id"]
    //         }, {
    //             required   : false,
    //             model      : Models.Attachment,
    //             as         : "posterImage",
    //             where      : {
    //                 isPoster : 1
    //             },
    //             attributes : ["id"]
    //         }, {
    //             required   : false,
    //             model      : Models.Attachment,
    //             as         : "attachments",
    //             where      : {
    //                 isPoster : 0
    //             },
    //             attributes : ["id"]
    //         }, {
    //             required   : false,
    //             model      : Models.VideoUser,
    //             attributes : ["id", "totalLength", "amountWatched", "percentageWatched", "rating", "comment"],
    //             where      : userDetails ? {
    //                 userId : userDetails.User.id
    //             } : {}
    //         }],
    //         limit    : limit,
    //         offset   : (pageNumber - 1) * limit,
    //         group    : Models.sequelize.col("VideoContents.video_id"),
    //         subQuery : false,
    //         order    : [[by, val]]
    //     });

    //     const responseArray = [];

    //     for(const topicRow of topic)
    //     {
    //         const rating = await avgRating(topicRow.dataValues.id);

    //         if(topicRow.dataValues.content[languageCode])
    //         {
    //             topicRow.dataValues.content[languageCode].posters = topicRow.dataValues.posterImage;
    //             topicRow.dataValues.content[languageCode].attachments = topicRow.dataValues.attachmentsArr;
    //             topicRow.dataValues.content[languageCode].rating = rating;

    //             if(topicRow.dataValues.isTopic == 1 && userDetails?.Role != "admin" && userDetails)
    //             {
    //                 const userVideoDetails = await Models.PurchasedTopic.findOne({
    //                     where : {
    //                         userId  : userDetails.User.id,
    //                         videoId : topicRow.dataValues.id
    //                     }
    //                 });

    //                 let isPurchased = 0

    //                 if(userVideoDetails)
    //                 {
    //                     isPurchased = 1
    //                 }

    //                 topicRow.dataValues.content[languageCode].percentageWatched = +(await getPercentage(topicRow.dataValues.id, null, userDetails.User.id))
    //                 topicRow.dataValues.content[languageCode].isPurchased  = isPurchased;
    //                 topicRow.dataValues.content[languageCode].startingDate = isPurchased ? userVideoDetails.startingDate : "";
    //                 topicRow.dataValues.content[languageCode].hasPassed    = isPurchased ? userVideoDetails.hasPassed : "";

    //                 let customField = {
    //                     videoId : topicRow.dataValues.id, 
    //                     userId : userDetails.User.id
    //                 };

    //                 customField = JSON.stringify(customField);

    //                 const dgStoreUrlObj = await Common.DgStoreRequest(`https://www.digistore24.com/api/call/createBuyUrl/?product_id=${topicRow.dataValues.digistoreId}&payment_plan[first_amount]=${topicRow.dataValues.price}&tracking[custom]=${customField}`)

    //                 isPurchased == 0 ? topicRow.dataValues.content[languageCode].purchaseLink = dgStoreUrlObj?.data?.data?.url : topicRow.dataValues.content[languageCode].purchaseLink = ""
    //             }
    //             else
    //             {
    //                 topicRow.dataValues.content[languageCode].percentageWatched = topicRow.dataValues.VideoUsers[0]?.percentageWatched ? +(+topicRow.dataValues.VideoUsers[0]?.percentageWatched).toFixed(2) : 0;
    //             }

    //             if(userDetails?.Role != "admin" && userDetails)
    //             {
    //                 if(!topicRow.dataValues.content[languageCode].canShowVideo)
    //                 {
    //                     topicRow.dataValues.content[languageCode].videoLink = null;
    //                 }
    //             }

    //             delete topicRow.dataValues.content[languageCode].canShowVideo;

    //             responseArray.push(topicRow.dataValues.content[languageCode])
    //         }
    //         else
    //         {
    //             topicRow.dataValues.content["en"] = topicRow.dataValues.content["en"] ? topicRow.dataValues.content["en"] : {};

    //             topicRow.dataValues.content["en"]["posters"] = topicRow.dataValues.posterImage;
    //             topicRow.dataValues.content["en"]["attachments"] = topicRow.dataValues.attachmentsArr;
    //             topicRow.dataValues.content["en"]["rating"] = rating;

    //             if(topicRow.dataValues.isTopic == 1 && userDetails?.Role != "admin" && userDetails)
    //             {
    //                 const userVideoDetails = await Models.PurchasedTopic.findOne({
    //                     where : {
    //                         userId  : userDetails.User.id,
    //                         videoId : topicRow.dataValues.id
    //                     }
    //                 });

    //                 let isPurchased = 0

    //                 if(userVideoDetails)
    //                 {
    //                     isPurchased = 1
    //                 }

    //                 topicRow.dataValues.content["en"]["percentageWatched"] = +(await getPercentage(topicRow.dataValues.id, null, userDetails.User.id))
    //                 topicRow.dataValues.content["en"]["isPurchased"]  = isPurchased;
    //                 topicRow.dataValues.content["en"]["startingDate"] = isPurchased ? userVideoDetails.startingDate : "";
    //                 topicRow.dataValues.content["en"]["hasPassed"]    = isPurchased ? userVideoDetails.hasPassed : "";

    //                 let customField = {
    //                     videoId : topicRow.dataValues.id, 
    //                     userId : userDetails.User.id
    //                 };

    //                 customField = JSON.stringify(customField);

    //                 const dgStoreUrlObj = await Common.DgStoreRequest(`https://www.digistore24.com/api/call/createBuyUrl/?product_id=${topicRow.dataValues.digistoreId}&payment_plan[first_amount]=${topicRow.dataValues.price}&tracking[custom]=${customField}`)

    //                 console.log("dgStoreUrlObj", dgStoreUrlObj);

    //                 isPurchased == 0 ? topicRow.dataValues.content["en"]["purchaseLink"] = dgStoreUrlObj?.data?.data?.url : topicRow.dataValues.content["en"].purchaseLink = ""
    //             }
    //             else
    //             {
    //                 topicRow.dataValues.content["en"]["percentageWatched"] = topicRow.dataValues.VideoUsers[0]?.percentageWatched ? +(+topicRow.dataValues.VideoUsers[0]?.percentageWatched).toFixed(2) : 0;
    //             }

    //             if(userDetails?.Role != "admin" && userDetails)
    //             {
    //                 if(!topicRow.dataValues.content["en"]["canShowVideo"])
    //                 {
    //                     topicRow.dataValues.content["en"]["videoLink"] = null;
    //                 }
    //             }

    //             delete topicRow.dataValues.content["en"]["canShowVideo"];

    //             console.log(topicRow.dataValues.content["en"]);

    //             responseArray.push(topicRow.dataValues.content["en"])
    //         }
    //     }

    //     const totalPages = await Common.getTotalPages(count, limit);

    //     const responseData = {
    //         totalPages,
    //         perPage      : limit,
    //         topicList    : responseArray,
    //         totalRecords : count,
    //         baseUrl      : process.env.IMAGE_BASE_URL,
    //     }

    //     return responseData;
    // },
    updateAttachments: async(userDetails, attachmentObjArr, videoId, transaction) => {
        try {
            let data                      = [];
            let removalData               = [];
            let newAttachmentIds          = [];
            let formattedAttachmentObjArr = [];

            const prevAttachmentIds = await Models.Attachment.findAll({
                where : {
                    videoId  : videoId,
                    isPoster : 0
                },
                attributes : ["attachmentId"]
            });

            await Models.Attachment.destroy({
                where : {
                    videoId  : videoId,
                    isPoster : 0
                },
                force: true, transaction
            });

            for(const attachmentObj of attachmentObjArr)
            {
                formattedAttachmentObjArr.push({
                    name          : attachmentObj.originalName,
                    attachmentId  : attachmentObj.id,
                    attachmentUrl : attachmentObj.path,
                    userId        : userDetails.User.id,
                    mimeType      : attachmentObj.mimeType,
                    isThumbnail   : 0,
                    isPoster      : 0,
                    videoId       : videoId
                })

                newAttachmentIds.push(attachmentObj.id);

                data.push({
                    id    : attachmentObj.id,
                    status: 1
                })
            }

            for(const attachmentObj of prevAttachmentIds)
            {
                const id = attachmentObj.attachmentId;

                if(!newAttachmentIds.includes(id))
                {
                    removalData.push({
                        id    : id,
                        status: 0
                    })
                }
            }

            removalData.length ? 
                await Webhook.updateAttachmentService({data : removalData}, transaction) : 
                "";

            await Webhook.updateAttachmentService({data}, transaction);
            await Models.Attachment.bulkCreate(formattedAttachmentObjArr, {transaction})

            return true
        }
        catch (error) {
            console.error(error);
            return false;
        }
    },
    posterAttachment: async(userDetails, posterImageObj, videoId, transaction) => {
        try {
            let data                      = [];
            let removalData               = [];
            let formattedAttachmentObjArr = [];

            const oldPosterDetails = await Models.Attachment.findOne({
                where :{
                    videoId  : videoId,
                    isPoster : 1
                }
            });

            if(oldPosterDetails)
            {
                await oldPosterDetails.destroy({force: true, transaction});

                removalData.push({
                    id     : oldPosterDetails.attachmentId,
                    status : 0
                })
            }

            formattedAttachmentObjArr.push({
                ...posterImageObj,
                isThumbnail : 0,
                isPoster    : 1,
                userId      : userDetails.User.id,
                videoId     : videoId
            });

            data.push({
                id     : posterImageObj.attachmentId,
                status : 1
            })

            removalData.length ? 
                await Webhook.updateAttachmentService({data : removalData}, transaction) : 
                "";

            await Webhook.updateAttachmentService({data}, transaction);
            await Models.Attachment.bulkCreate(formattedAttachmentObjArr, {transaction})

            return true
        }
        catch (error)
        {
            return false
        }
    },
    updateVideoContent: async(languageCode, videoId, VideoContentObj, transaction) => {
        try {
            const isContentExists = await Models.VideoContent.findOne({
                where : {
                    videoId      : videoId,
                    languageCode : languageCode
                }
            })

            if(isContentExists)
            {
                await isContentExists.update({
                    ...VideoContentObj
                }, {transaction});
            }
            else
            {
                await Models.VideoContent.create({
                    ...VideoContentObj,
                    videoId : videoId
                }, {transaction});
            }

            return true;
        } catch (error) {
            console.error(error)
            return false;
        }
    },
    updateVideo: async(videoId, videoObj, transaction) => {
        try {
            if(videoId)
            {
                const isVideoExists = await Models.Video.findOne({
                    where : {
                        id: videoId
                    }
                });

                if(isVideoExists && videoId)
                {
                    await isVideoExists.update({
                        ...videoObj
                    }, {transaction});

                    return videoId;
                }
                else
                {
                    const videoId = await Models.Video.create({
                        ...videoObj
                    }, {transaction});

                    return videoId.id
                }
            }
            else
            {
                const newObj = await Models.Video.create({
                    ...videoObj
                }, {transaction});

                return newObj.id;
            }
        } catch (error) {
            console.error(error)
            return false
        }
    },
    deleteLessonChapters: async(topicId, idArray, transaction) => {
        const orClause = idArray ? {
            [Op.or] : [
                {topicId  : idArray},
                {id       : idArray},
                {parentId : idArray}
            ]} : {};

        await Models.Video.destroy({
            where : {
                topicId : topicId,
                ...orClause
            },
            transaction
        });
    },
    percentageVideoWatched: async(topicId, parentId, userId) => {
        return await getPercentage(topicId, parentId, userId);
    },
    getTopicDetails: async(topicId, languageCode, userDetails) => {
        const topicDetails = await Models.Video.findOne({
            where : {
                id         : topicId,
                isTopic    : 1
            },
            attributes : ["id"],
            include : {
                model      : Models.VideoContent,
                attributes : [[Models.sequelize.literal("JSON_OBJECTAGG(language_code, name)"), "content"]]
            }
        });

        const nameObj = topicDetails.VideoContents[0].dataValues.content;
        let availableLanguage = "en"
        for(let item in nameObj) {
            availableLanguage = item;
        }
        console.log(nameObj, " ================================ nameObj")
        const percentageWatched = await getPercentage(topicId, null, userDetails.User.id);

        let lastWatchedVideo = await Models.Video.findOne({
            where: { topicId: topicId },
            distinct: true,
            col: "id",
            subQuery: false,
            include: [
                {
                    required: true,
                    model: Models.VideoUser,
                    where: { userId: userDetails.User.id }
                }
            ],
            order: [[{model: Models.VideoUser}, "updatedAt", "DESC"]]
        });
        let lastWatchedId = null;
        if(lastWatchedVideo) lastWatchedId = lastWatchedVideo.id;

        return {
            name              : nameObj[availableLanguage],
            percentageWatched : percentageWatched,
            lastWatchedId
        };
    },
    updateTimeStamp: async(videoId, amountWatched, totalLength, userDetails) => {
        // const transaction = await Models.sequelize.transaction();

        try
        {
            await Models.VideoUser.upsert({
                userId: userDetails.User.id,
                videoId: videoId,
                amountWatched: amountWatched,
                totalLength: totalLength
            });

            return true;
            // await transaction.commit();
            // const isVideoExists = await Models.Video.findOne({
            //     where : {
            //         id : videoId
            //     }
            // });

            // if(!isVideoExists) {
            //     // await transaction.rollback();
            //     return false;
            // }

            // const isVideoPurchased = await Models.PurchasedTopic.findOne({
            //     where: {
            //         videoId : isVideoExists.topicId,
            //         userId  : userDetails.User.id
            //     }
            // });

            // console.log(" ========================================== topic ====================================")
            // console.log(" ========================================== topic ====================================")
            // console.log(" ========================================== topic ====================================")
            // console.log(" ========================================== topic ====================================")
            // console.log(isVideoPurchased)
            // console.log(" ========================================== topic ====================================")
            // console.log(" ========================================== topic ====================================")
            // console.log(" ========================================== topic ====================================")
            // console.log(" ========================================== topic ====================================")

            // const isVideoPurchased = 1;

            // if(isVideoExists && isVideoPurchased)
            // {
                // await Models.VideoUser.upsert({
                //     userId        : userDetails.User.id,
                //     videoId       : videoId,
                //     amountWatched : amountWatched,
                //     totalLength   : totalLength
                // });

                // // await transaction.commit();
                // return true;
            // }
            // else
            // {
            //     // await transaction.rollback();
            //     return false;
            //     throw new Error("CustmError", "INVALID_VIDEO_ID");
            // }
        } catch (error) {
            return false
            // await transaction.rollback();

            // if(error.name = "CustmError")
            // {
            //     throw new Error(error.messasge);
            // }
            // else
            // {
            //     throw new Error("SOMETHING_WENT_WRONG");
            // }
        }
    },
    // addTopicFromExcel,
    // addTopicFromExcelSubTopics,
    getAuthorizedVideo,
    whiteListDomain,
    getLinkArray
}

const getPercentage = async(topicId, parentId, userId) => {
    const subVideoCount = await Models.Video.count({
        where : {
            topicId  : topicId,
            parentId : parentId ? parentId : { [Op.not] : null }
        }
    });

    const totalAmountWatched = await Models.VideoUser.findAll({
        attributes : [[Models.sequelize.fn("sum", Models.sequelize.literal(`amount_watched / total_length`)), "totalAmount"]],
        where      : {
            userId : userId
        },
        include    : {
            required   : true,
            model      : Models.Video,
            attributes : [],
            where    : {
                topicId  : topicId,
                parentId : parentId ? parentId : { [Op.not] : null }
            }
        }
    });

    const amoountWatched = totalAmountWatched[0].dataValues.totalAmount;

    return (amoountWatched / subVideoCount) * 100;
}

const avgRating = async(id) => {
    const ids = await Models.Video.findOne({
        where : {
            [Op.or] : [
                {id       : id},
                {parentId : id},
                {topicId  : id}
            ]
        },
        attributes : [[Models.sequelize.fn("GROUP_CONCAT", Models.sequelize.col("id")), "videoIds"]]
    })

    const videoIdArr =  ids.dataValues.videoIds.split(",");

    const avgRating = await Models.VideoUser.findOne({
        where : {
            videoId : videoIdArr,
            rating  : {
                [Op.not] : null
            }
        },
        attributes : [[Models.sequelize.literal("sum(rating) / count(*)"), "avgRating"], [Models.sequelize.literal("count(*)"), "totalRating"]]
    });


    const ratingObj = await Models.sequelize.query(`
        with 
            standardRating as (select rating from (Values ROW(5), ROW(4), Row(3), Row(2), Row(1)) rt (rating)),
            ratingCount as (select coalesce(rating, 0) rating, coalesce(count(*), 0) as counts from vidMgt_video_users
        WHERE
            video_id in (?)
        GROUP BY coalesce(rating, 0))
        SELECT 
            standardRating.rating rating, coalesce(counts, 0) count, ROUND(coalesce((coalesce(counts, 0) / ?) * 100, 0), 2) percentage
        FROM
            standardRating left outer join ratingCount on ratingCount.rating = standardRating.rating;
    `, {type: QueryTypes.SELECT, replacements: [ids.dataValues.videoIds, +avgRating.dataValues.totalRating || 0]});

    return {
        average    : +avgRating.dataValues.avgRating || 0,
        totalCount : +avgRating.dataValues.totalRating || 0,
        details    : ratingObj
    };
}

const digiStorePayment = async(userId, price, transaction) => {
    


    return { success: true, data: createOrder }
}