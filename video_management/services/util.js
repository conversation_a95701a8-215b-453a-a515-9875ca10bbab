const Crypto    = require("crypto");
const Constants = require("../constants");

require('dotenv').config();

module.exports =
{
	getEncrytedToken: async function(payload)
	{
		let iv = Crypto.randomBytes(Constants.IV_LENGTH);
		let cipher = Crypto.createDecipheriv(Constants.CIPHER_ALGORITHM, Buffer.from(process.env.ENCRYPTION_KEY), iv);

		let token = cipher.update(JSON.stringify(payload));
		token = Buffer.concat([token, cipher.final()]);
		token = iv.toString('hex') + ':' + token.toString('hex');
		return token;
	},
	getDecryptedToken: async function(payload)
	{
		let payloadParts = payload.split(':');
		let iv = Buffer.from(payloadParts.shift(), 'hex');
		let encrypted = Buffer.from(payloadParts.join(':'), 'hex');

		let decipher = Crypto.createDecipheriv(Constants.CIPHER_ALGORITHM, Buffer.from(process.env.ENCRYPTION_KEY), iv);
		let decrypted = decipher.update(encrypted);
		decrypted = Buffer.concat([decrypted, decipher.final()]);
		decrypted = decrypted.toString();
		decrypted = JSON.parse(decrypted);
		return decrypted;
	},
	getHash: async function(payload = null)
	{
		let randomBytes = payload ? payload : Crypto.randomBytes(64).toString("hex");
		let hash = Crypto.createHmac("sha512", process.env.ENCRYPTION_KEY);
		hash.update(randomBytes);
		const value = hash.digest("hex");

		return {
			hash: value,
			payload: randomBytes
		};
	}
};