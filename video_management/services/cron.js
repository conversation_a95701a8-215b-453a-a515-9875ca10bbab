module.exports = {
    CRON:[
        {
            name: "fiveMinuteCron",
            time: "*/5 * * * *",
            timezone: "Asia/Calcutta",
            request: {
            method: "GET",
                url: "/cron/fiveMinuteCron"
            },
            onComplete: async (res) => {
                console.log("------------ <PERSON><PERSON> Job Executed ( Every Five Minutes ) --------------");
            }
        },
        {
            name: "oneMinuteCron",
            time: "* * * * *",
            timezone: "Asia/Calcutta",
            request: {
            method: "GET",
                url: "/cron/oneMinuteCron"
            },
            onComplete: async (res) => {
                console.log("------------ <PERSON><PERSON> Job Executed ( Every One Minutes ) --------------");
            }
        },
    ]
}