const Models    = require("../models/index.js");
const Constants = require("../constants.js");
const Axios     = require("axios");

module.exports = {
    updateUserInServices : async(data, transaction) => {
        const urls = Constants.WEBHOOK.USER_ONBOARDING
        
        for(const url of urls)
        {
            const axiosObj = {
                url    : url,
                method : "POST",
                data   : data
            };
console.log('axiosObj',axiosObj)
            await 
                Axios(axiosObj)
                .then()
                .catch(async () => {
                    await Models.Request.create({
                        axiosObj : axiosObj,
                        sucess   : 0
                    }, {transaction});
                });
        }

        return true;
    },
    updateAttachmentService : async(reqPayload, transaction) => {
        console.log(reqPayload);
        const url = Constants.URL.ATTACHMENT_UPDATE;

        const axiosObj = {
            url    : url,
            method : "PATCH",
            data   : reqPayload
        };

        await 
            Axios(axiosObj)
            .then()
            .catch(async () => {
                await Models.Request.create({
                    axiosObj : axiosObj,
                    sucess   : 0
                }, {transaction});
            });
    }
}