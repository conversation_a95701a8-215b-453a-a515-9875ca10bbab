let validationObj = 
{
    containerName     : /^[a-zA-Z0-9 ]*$/,
    containerId       : /^[0-9]*$/,
    videoName         : /^[a-zA-Z0-9 ]*$/,
    videoId           : /^[0-9]*$/,
    videoLink         : /^<iframe[^>]*>\s*<\/iframe>/,
    videoAttachmentId : /^[0-9]*$/,
    audioLink         : /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/,
    shortText         : /^(.|\s)*[a-zA-Z]+(.|\s)*$/,
    description       : /^(.|\s)*[a-zA-Z]+(.|\s)*$/,
    shopProductId     : /^[0-9]*$/,
    elemetId          : /^[0-9]*$/,
    beforeElementId   : /^[0-9]*$/,
    type              : /^VIDEO$|^CONTAINER$|^SUBVIDEO$/
}

module.exports = [
    {
        isValidInput : async(inputObj) => {
            let isValid = true;

            for(const itemKey in inputObj)
            {
                const item    = inputObj[itemKey];
                const pattern = validationObj[itemKey];

                if(!pattern.test(item))
                {
                    isValid = false;
                    break;
                }
            }

            return isValid
        }
    }
]