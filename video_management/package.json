{"name": "kuby_backend", "version": "1.0.0", "description": "Microservice For Courses/Videos Management", "main": "server.js", "scripts": {"start": "ENVIRONMENT=DEV node startServer", "test": "start-server-and-test start http://localhost:3001/ cypress:open", "addRoutes": "addRoutes", "cypress:open": "cypress open", "make-migration": "./node_modules/sequelize-auto-migrations/bin/makemigration.js", "migrate": "npx sequelize db:migrate", "make-seeder": "npx sequelize-cli seed:generate --name", "seed-data": "npx sequelize-cli db:seed:all"}, "repository": {"type": "git", "url": "git+https://github.com/illuminzcode/kuby_backend"}, "keywords": ["<PERSON><PERSON>"], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/illuminzcode/kuby_backend/issues"}, "homepage": "https://github.com/illuminzcode/kuby_backend#readme", "dependencies": {"@hapi/hapi": "^20.2.2", "@hapi/inert": "^6.0.5", "@hapi/vision": "^6.1.0", "@joi/date": "^2.1.0", "axios": "^1.2.2", "convert-excel-to-json": "^1.7.0", "crypto": "^1.0.1", "crypto-js": "^4.1.1", "cypress-router": "^1.2.6", "dotenv": "^16.0.1", "hapi-auth-jwt2": "^10.2.0", "hapi-cron": "^1.1.0", "hapi-i18n": "^3.0.1", "hapi-swagger": "^14.5.2", "install": "^0.13.0", "joi": "^17.6.0", "jsonwebtoken": "^8.5.1", "moment": "^2.29.4", "mysql2": "^2.3.3", "npm": "^8.12.1", "path": "^0.12.7", "sequelize": "^6.20.1", "sequelize-auto-migrations": "github:scimonster/sequelize-auto-migrations#a063aa6535a3f580623581bf866cef2d609531ba", "socket.io": "^4.5.4", "vimeo": "^2.3.1"}, "devDependencies": {"cypress": "^10.7.0", "jest": "^28.1.1", "sequelize-cli": "^6.4.1", "start-server-and-test": "^1.14.0"}}