const Hapi         = require("@hapi/hapi");
const Common       = require("./common.js");
const Jwt          = require("jsonwebtoken");
const VideoService = require("./services/video.js");

const tls =
{
    key: Fs.readFileSync("/mnt/monoCompany/ssl/certificates/api.monospace.au.key"),
    cert: Fs.readFileSync("/mnt/monoCompany/ssl/certificates/api.monospace.au.crt")
};

const socketserver = new Hapi.server({
	host: process.env.NODE_HOST,
	port: 3011,
	// tls: tls,
	routes: 
	{
		cors:
		{
			origin: ["*"],
			headers:
			[
				"accept",
				"authorization",
				"Content-Type",
				"If-None-Match",
				"language",
				"utcoffset",
			],
			additionalHeaders:
			[
				"Access-Control-Allow-Origin",
				"Access-Control-Allow-Headers",
				"Origin, X-Requested-With, Content-Type",
			],
		},
	},
});

module.exports.connectSocket = async() => {
    socketserver.start(() => {
        console.log("Socket Started")
    });

    const io = require("socket.io")(socketserver.listener);

    io.use(async(socket, next) => {
        await authSocketMiddleware(socket, next);
    })

    io.on("connection", async(socket) => {
        console.log("Client connected from server at socket - " + socket.id);

        socket.on("updateTimeStamp", async(data, temp, callback) => {
            try
            {
                const userDetails = socket.user;
                const {videoId, amountWatched, totalLength} = data;

                await VideoService.updateTimeStamp(videoId, amountWatched, totalLength, userDetails);

                socket.emit("updatedTimeStamp", {
                    success : true,
                    message : "TIME_STAMP_UPDATED_SUCCESSFULLY"
                });
            }
            catch (error)
            {
                socket.emit("updatedTimeStamp", {
                    success : false,
                    message : error.message
                });
            }
        })
    })
}

const authSocketMiddleware = async(socket, next) => {
    const token = socket.handshake.headers.authorisation;

    try
    {
        const decoded = await Common.validateToken(Jwt.decode(token));

        if(!decoded.isValid)
        {
            return next(new Error("NOT AUTHORIZED"));
        }
        else
        {
            socket.user = decoded.credentials;
        }
    }
    catch (err)
    {
        console.log(err);
        return next(new Error("NOT AUTHORIZED"));
    }

    next();
};