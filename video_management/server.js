const Hapi          = require("@hapi/hapi");
const HapiI18n      = require("hapi-i18n");
const Inert         = require("@hapi/inert");
const Vision        = require("@hapi/vision");
const HapiSwagger   = require("hapi-swagger");
const HapiJwt       = require("hapi-auth-jwt2");
const { sequelize } = require("./models");
const RoutesArr     = require("./routes");
const Common        = require("./common.js");
const HapiCron      = require('hapi-cron');
const CronJobs      = require("./services/cron.js")
// const SocketServer  = require("./socket");

require('dotenv').config()


console.log(" ==================== ")

module.exports.init = async() =>
{
    const server = Hapi.server({
        port: process.env.NODE_PORT,
        host: process.env.NODE_ENV == "production" ? process.env.NODE_HOST_LIVE : process.env.NODE_HOST,
		routes:
		{
			cors:
			{
				origin: ['*'],
				headers: ['Accept', 'Authorization', 'Content-Type', 'If-None-Match',"language","timezone"],
				additionalHeaders: ["Access-Control-Allow-Origin","Access-Control-Allow-Headers","Origin, X-Requested-With, Content-Type"]
			}
		}
    });

    const swaggerOptions = 
	{
		info:
		{
			title: "Course Management Api Documentation",
			version: "1.0.0"
		},
		securityDefinitions: {
			Bearer: {
				type: "apiKey",
				name: "Authorization",
				in: "header"
			}
		},
		grouping: "tags"
	}

    const i18nOptions = 
	{
		locales: ["en", "de"], // default locale is first locale found in the list in this case "en"
		directory: __dirname + "/locales",
		languageHeaderField: "language", // reading the language in language header
	}

    try
    {
        await sequelize.authenticate().then(async() => 
        {
            console.info("DATABASE CONNECTED");

            if(process.env.ENVIRONMENT == "TEST")
			{
				await sequelize.query("SET FOREIGN_KEY_CHECKS = 0", { raw: true ,logging: false}).then(async () =>
				{
					await sequelize.sync({force: true, logging: false}).then(async () =>
					{
						await sequelize.query("SET FOREIGN_KEY_CHECKS = 1", { raw: true , logging: false})
						console.info("MODELS LOADED");
					});
				})
			}
			else
			{
				await sequelize.sync({force: false, logging: true }).then(() =>
				{
					console.info("MODELS LOADED");
				});
			}
        })
    }
    catch(error)
    {
        console.error("DATABASE CONNECTION FAILED");
		console.error(error);
    }

	await server.register([
        Inert,
		Vision,
		{
			plugin: HapiI18n,
			options: i18nOptions
		},
        {
            plugin: HapiSwagger,
            options: swaggerOptions
        },
		{
			plugin: HapiCron,
			options: {
				jobs : CronJobs.CRON
			}
		},
		HapiJwt
	]);

	await server.auth.strategy("jwt", "jwt", 
	{
		complete: true,
		key: Common.privateKey, 
		validate : Common.validateToken,
		verifyOptions: 
		{
			algorithms: [ "HS256" ]
		}
	});

	server.auth.default("jwt");

    server.route({
		method: "GET",
		path: "/",
		options:
		{
			auth: false,
			handler: (request, h) => 
			{
				return h.response({success:true,message:request.i18n.__("API ENDPOINT FOR ONLINE SCHULE"),responseData:{}}).code(200);
			}
		}
	});

	server.route(await RoutesArr.init());
	console.info("ROUTES LOADED");

    await server.start();

    console.info("SERVER IS RUNNING ON", server.info.uri);

	// await SocketServer.connectSocket();

	return server;
};

process.on("unhandledRejection", (err) => 
{
    console.error(err);
    process.exit(1);
})