'use strict';

module.exports = {
    async up (queryInterface, Sequelize) {
      return queryInterface.bulkInsert('notification_types',[
        {type:'soulwriting-customer-submit',status:1,created_at: new Date(),updated_at: new Date()}, //1
        {type:'soulwriting-companion-submit',status:1,created_at: new Date(),updated_at: new Date()}, //2
        {type:'meeting-scheduled',status:1,created_at: new Date(),updated_at: new Date()}, //3
        {type:'meeting-rescheduled',status:1,created_at: new Date(),updated_at: new Date()}, //4
        {type:'meeting-cancelled',status:1,created_at: new Date(),updated_at: new Date()}, //5
      ])
    },
  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */

    await queryInterface.bulkDelete('notification_types', null, {});
  }
};
