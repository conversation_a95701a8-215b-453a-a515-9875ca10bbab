'use strict';

module.exports = {
  async up (queryInterface, Sequelize) {
      await queryInterface.bulkInsert('notification_contents',[
        {type_id:1,title:'Soulwriting Submitted',content:'You have received a new soulwriting - {{title}} from {{customer}}.',replacements:"title,customer",status:1,created_at: new Date(),updated_at: new Date()},
        {type_id:2,title:'Soulwriting Feedback',content:'{{companion}} has shared feedback for {{title}} soulwriting.',replacements:"title,companion",status:1,created_at: new Date(),updated_at: new Date()},
        {type_id:3,title:'Meeting scheduled',content:'Your one-to-one meeting has been scheduled with {{user_name}} at {{start_date}}.',replacements:"user_name,start_date",status:1,created_at: new Date(),updated_at: new Date()},
        {type_id:4,title:'Meeting rescheduled',content:'Your one-to-one meeting has been rescheduled with {{user_name}} from {{start_date}} to {{new_start_date}}.',replacements:"user_name,start_date,new_start_date",status:1,created_at: new Date(),updated_at: new Date()},
        {type_id:5,title:'Meeting cancelled',content:'Your one-to-one meeting has been cancelled with {{user_name}} at {{start_date}}.',replacements:"user_name,start_date",status:1,created_at: new Date(),updated_at: new Date()},
      ])
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */

      await queryInterface.bulkDelete('notification_contents', null, {});
  }
};
