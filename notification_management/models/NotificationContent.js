module.exports = (sequelize, DataTypes) => {
  let NotificationContent = sequelize.define(
    "NotificationContent",
    {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false },
      typeId: { type: DataTypes.INTEGER, allowNull: false },
      status: { type: DataTypes.INTEGER, defaultValue: 1 },
      title: { type: DataTypes.STRING, allowNull: false },
      content: { type: DataTypes.TEXT, allowNull: false },
      language: { type: DataTypes.STRING, defaultValue: null },
      replacements: { type: DataTypes.TEXT, allowNull: true }
    },
    {
      paranoid: true,
      underscored: true,
      tableName: "notification_contents"
    }
  );

  NotificationContent.associate = (models) => {
    NotificationContent.belongsTo(models.NotificationType, { foreignKey: "typeId" });
  };

  return NotificationContent;
};
