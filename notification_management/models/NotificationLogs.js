module.exports = (sequelize, DataTypes) => {
  let NotificationLog = sequelize.define(
    "NotificationLog",
    {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false },
      receiverId: { type: DataTypes.INTEGER, allowNull: false },
      sessionId: { type: DataTypes.JSON, allowNull: true },
      title: { type: DataTypes.STRING, allowNull: false },
      content: { type: DataTypes.TEXT, allowNull: false },
      language: { type: DataTypes.STRING, defaultValue: null },
      replacements: { type: DataTypes.JSON, allowNull: true },
      data: { type: DataTypes.JSON, allowNull: true },
      isRead: { type: DataTypes.INTEGER, defaultValue: 0 },
      status: { type: DataTypes.INTEGER, defaultValue: 1 }
    },
    {
      paranoid: true,
      underscored: true,
      tableName: "notification_logs"
    }
  );

  NotificationLog.associate = (models) => {};

  return NotificationLog;
};
