module.exports = (sequelize, DataTypes) => {
  let NotificationType = sequelize.define(
    "NotificationType",
    {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false },
      status: { type: DataTypes.INTEGER, defaultValue: 1 },
      type: { type: DataTypes.STRING, allowNull: false }
    },
    {
      paranoid: true,
      underscored: true,
      tableName: "notification_types"
    }
  );

  NotificationType.associate = (models) => {
    NotificationType.hasMany(models.NotificationContent, { foreignKey: "typeId" });
  };

  return NotificationType;
};
