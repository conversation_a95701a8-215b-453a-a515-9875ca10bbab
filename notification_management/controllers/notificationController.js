const {sendNotification} = require("../services/notificationSender");
const { addMessageToQueue } = require("../kafkaProducer");

const getTokens = async (userId) => {
    return
    try {
      let query = {userId}
      let userInfo = await Common.axiosRequest(`${process.env.USER_ONBOARDING_DOMAIN}/user/session`,'get',{},query);
      deviceToken = userInfo?.data?.sessionInfo?.deviceToken;
      return deviceToken;
    } catch (error) {
      console.error(error);
      return [];
    }
};

exports.sendNotification = async(req, h) => {
    const transaction = await Models.sequelize.transaction();
    try {
        let {type, replacements, data} = req.payload;

        const userId = req.payload.userId;
        // const userInfo = await Models.User.findOne({where: {userId: userId}});
        // if(!userInfo) {
        //     await transaction.rollback();
        //     return h.response(Common.generateError(req,400,'USER_DOES_NOT_EXIST',null)).code(400);
        // }
        
        const notificationContent = await Models.NotificationContent.findOne({include : {model : Models.NotificationType,where : {type : type}}});
        if(!notificationContent) {
            await transaction.rollback()
            return h.response({success:false}).code(200);
        }
        let typeId = notificationContent.dataValues.typeId;
        let descriptionContent = notificationContent.dataValues.content;
        let titleContent = notificationContent.dataValues.title;
        for (let r in replacements) {
            descriptionContent= descriptionContent.replace(`{{${r}}}`,replacements[r]);
            titleContent= titleContent.replace(`{{${r}}}`,replacements[r]);
        }
        let notificationData = {title: titleContent,body: descriptionContent,readStatus: "0",type: typeId.toString(),isRead: "0" };
        let res = await Models.NotificationLog.create(
            {
                status: 1,receiverId: userId,type: typeId,sessionId: null,
                content: notificationContent.dataValues.content,replacements: replacements,
                data: data,title: notificationContent.dataValues.title
            },{ 
                transaction: transaction 
            }
        );

        notificationData.id=res.dataValues.id.toString();

        let QueuePayload = {
            userId: userId,
            notificationData: notificationData
        }

        addMessageToQueue(QueuePayload, "notification", {});

        const sessionId=await getTokens(userId);
        if(sessionId) {
            await sendNotification(sessionId, notificationData);
            // await transaction.rollback();
            // return h.response(Common.generateError(req,400,'DEVICE_TOKEN_NOT_FOUND',null)).code(400);
        }
        await transaction.commit();
        return h.response({success:true}).code(200);
    } catch (error) {
        console.error(error);
        await transaction.rollback()
        return h.response({success:false}).code(500);
    }
}