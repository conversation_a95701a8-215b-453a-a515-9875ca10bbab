
const Models             = require("../models/index.js");
const  NotificationSender  =  require("../services/notificationSender.js");
const {getTokens}= require('../services/getTokens')
const Moment=require('moment')
require('dotenv').config();

module.exports = {
    // Find Notification Count 
    notificationcount:async(req,h)=>{
        try{
            const userId=req.auth.credentials.userData.User.id;
            const unreadCount=await Models.NotificationLog.count({where:{receiverId:userId,isRead:0}});
            const count=await Models.NotificationLog.count({where:{receiverId:userId}});
            return h.response({success:true,responseData:{unreadCount,count}}).code(200)
        }
        catch(error){
            console.error(error);
            return h.response({success:false}).code(500);
        }
    },
    // Send Notification    
    sendNotification : async(req, h) => {
        const transaction = await Models.sequelize.transaction();
        try {
            const {typeId, replacements, data, userId} = req.payload;
            const sessionIds=await getTokens(userId);
            const notificationContent = await Models.NotificationContent.findOne({include : {model : Models.NotificationType,where : {status : 1,id : typeId}}});
            if(!notificationContent) { 
                await transaction.rollback()
                return h.response({success:false}).code(200);
            }
            
            let notificationData = {
              title: notificationContent.dataValues.title, description: notificationContent.dataValues.content,
              readStatus: "0", isRead: "0", data: null, type: typeId.toString(), eventId: data?.eventId.toString() || null
            };
            
            let replacedContent=notificationData['description'];
            for (const r in replacements) {
                if(r=='start_date' || r=='new_start_date') { 
                    replacedContent=replacedContent.slice(0,-1);
                    let utcOffset=req.headers.utcoffset
                    console.log('r',replacements[r])
                    console.log('utcOffset',utcOffset)

                    let date=new Date(replacements[r])
                    let time=new Date(replacements[r])

                    date= new Date( date.setMinutes(date.getMinutes()-utcOffset))
                    time= new Date(time.setMinutes(time.getMinutes()-utcOffset))
                    
                    time=time.getTime()
                    replacedContent= replacedContent.replace(`{{${r}}}`,date.toString());
                    replacedContent= replacedContent+ ' at '+time.toString()+' .'
                } else {
                    replacedContent= replacedContent.replace(`{{${r}}}`,replacements[r]);
                }
            }
            notificationData['description']=replacedContent
            let res = await Models.NotificationLog.create(
                {
                    status: 1,   receiverId: userId, type: typeId, sessionId: sessionIds,
                    content: notificationContent.dataValues.content, replacements: replacements,
                    data: data, title: notificationContent.dataValues.title
                },
                { transaction: transaction }
            );
            notificationData.id=res.dataValues.id.toString();
            await NotificationSender.sendNotification(sessionIds, notificationData);
            addMessageToQueue(notificationData, "create-notification", null)
            // send to the kafka producer
            await transaction.commit();
            return h.response({success:true}).code(200);
        }
        catch (error) {
            console.error(error);
            await transaction.rollback()
            return h.response({success:false}).code(500);
        }
    },

    getNotifications:async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try{
            const limit = req.query.limit !== null ? req.query.limit > Constants.MAX_PAGINATION_LIMIT ? Constants.MAX_PAGINATION_LIMIT : req.query.limit : Constants.PAGINATION_LIMIT;
            const offset = (req.query.pageNumber - 1) * limit;
            const orderByValue = req.query.orderByValue;
            const orderByParameter = req.query.orderByParameter;
            const userId=req.auth.credentials.userData.User.id;
            let where = {receiverId:userId}
            if(req.query.status!==null)
                where={...where,status:req.query.status};
                let options={
                    where,
                    order: [
                            [orderByParameter, orderByValue]
                            ],
                    subQuery: false,
                }
                if (req.query.pageNumber !== null) 
                options = {
                    ... options,
                    limit,
                    offset
                }
                const unReadNotifications=await Models.NotificationLog.count({where:{receiverId:userId,isRead:0}})
                const notifications = await Models.NotificationLog.findAndCountAll(options);
                const totalPages = await Common.getTotalPages(notifications.count, limit);
                const responseData = {
                    totalPages,
                    perPage: limit,
                    notifications: notifications.rows,
                    totalRecords: notifications.count,
                    unReadNotifications:unReadNotifications
                };
                for (const i of notifications.rows) {
                        await i.update({isRead:1},{transaction})
                }
                await transaction.commit()
                return h.response({success: true, message: req.i18n.__("REQUEST_SUCCESSFUL"), responseData: responseData}).code(200);
        }
        catch (error) {
            console.error(error);
            await transaction.rollback()
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WORNG"), responseData: responseData}).code(500);
        }
    },

    updateNotification:async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try {
            const {typeId,id,replacements,data}=req.payload;
            const notificationContent = await Models.NotificationContent.findOne({
                include : {model : Models.NotificationType,where : {status : 1,id : typeId}}});
            if(!notificationContent)
            {
                await transaction.rollback()
                return h.response({success:false}).code(200);
            }

            let record= await Models.NotificationLog.findOne({where:{id:id}});
            if(!record)
            {
                await transaction.rollback();
                return h.response({success:false}).code(400);
            }
          
            await record.update({
                status       : 1,
                type         : typeId.toString(),
                content      : notificationContent.dataValues.content,
                data         : data,
                title        : notificationContent.dataValues.title
            }, {transaction  : transaction});
            await transaction.commit();
            let res=await Models.NotificationLog.findOne({where:{id:id}})
            return h.response({success:true,responseData:res}).code(200);
        }
        catch (error) {
            console.error(error);
            await transaction.rollback()
            return h.response({success:false}).code(500);
        }
    },

    sendSocketNotification:async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try {
            const {typeId, replacements, data, userId} = req.payload;
           const notificationContent = await Models.NotificationContent.findOne({
               include : {model : Models.NotificationType,where : {status : 1,id : typeId}}});
           if(!notificationContent)
           {
               await transaction.rollback()
               return h.response({success:false}).code(200);
           }
         
           await Models.NotificationLog.create({
               status       : 1,
               receiverId   : userId,
               type         : typeId,
               sessionId    : [],
               content      : notificationContent.dataValues.content,
               replacements : replacements,
               data         : data,
               title        : notificationContent.dataValues.title
           }, {transaction  : transaction});
           await transaction.commit();
            // send notificationbysocket-----
            let count=await Models.NotificationLog.count({where:{isRead:0}});
            await sendNotificationCount(userId,count)
           return h.response({success:true}).code(200);
        }
        catch (error) {
            console.error(error);
            await transaction.rollback()
            return h.response({success:false}).code(500);
        }
    },

    updateEntityNotification:async(req,h)=>{
        const transaction = await Models.sequelize.transaction();
        try {
            console.log('req.payload',req.payload)

            const {typeId,id,replacements,entityId,receiverId}=req.payload;
            if(entityId===2)
            {
                const notificationContent = await Models.NotificationContent.findOne({
                    include : {model : Models.NotificationType,where : {status : 1,id : typeId}}});
                if(!notificationContent)
                {
                    await transaction.rollback()
                    return h.response({success:false}).code(200);
                }
    
                let record= await Models.NotificationLog.findOne({where:{
                    'data.inviteId':id.toString(),receiverId}});
                console.log('record',record)
                if(!record)
                {
                    await transaction.rollback();
                    return h.response({success:true}).code(200);
                }
                await record.update({
                    status       : 1,
                    type         : typeId,
                    content      : notificationContent.dataValues.content,
                    data         : record.dataValues.data,
                    title        : notificationContent.dataValues.title,
                    replacements : replacements
                }, {transaction  : transaction});
                console.log('record After update',record.dataValues)
                await transaction.commit();
                return h.response({success:true,responseData:record}).code(200);
            }
            else if(entityId===1)
            {
                const notificationContent = await Models.NotificationContent.findOne({
                    include : {model : Models.NotificationType,where : {status : 1,id : typeId}}});
                if(!notificationContent)
                {
                    await transaction.rollback()
                    return h.response({success:false}).code(200);
                }
    
                let record= await Models.NotificationLog.findOne({where:{
                    'data.eventId':id.toString(),receiverId
                }});

                if(!record)
                {
                    await transaction.rollback();
                    return h.response({success:false}).code(400);
                }
                await record.update({
                    status       : 1,
                    type         : typeId,
                    content      : notificationContent.dataValues.content,
                    data         : record.dataValues.data,
                    title        : notificationContent.dataValues.title,
                    replacements : replacements
                }, {transaction  : transaction});
                await transaction.commit();
                return h.response({success:true,responseData:record}).code(200);
            }
            
        }
        catch (error) {
            console.error(error);
            await transaction.rollback()
            return h.response({success:false}).code(500);
        }
    },

    readNotification:async(req,h)=>{
        const transaction=await Models.sequelize.transaction()
        try{
            const {status,id}=req.payload;
            let record=await Models.NotificationLog.findOne({where:{id:id}});
            if(!record)
            {
                await transaction.rollback()
                return h.response({success: false, message: req.i18n.__("RECORD_NOT_FOUND"), responseData: {}}).code(400);
            }
            await record.update({isRead:status},{transaction})
            await transaction.commit()
            return h.response({success: true, message: req.i18n.__("SUCCESSFULLY_UPDATED"), responseData: record}).code(200);
        }
        catch(error)
        {
            await transaction.rollback()
            console.error(error);
            return h.response({success: false, message: req.i18n.__("SOMETHING_WENT_WORNG"), responseData: responseData}).code(500);
        }
    }
}

const sendNotificationCount=async(userId,count)=>{
    try{
        let response=await Axios({
            method: 'POST',
            url:Constants.URL.SEND_NOTIFICATION_COUNT,
            data: {userId:userId,count}
          })
    }
    catch(error){
        console.log('Error in Send socket Data:',error);
    }

}