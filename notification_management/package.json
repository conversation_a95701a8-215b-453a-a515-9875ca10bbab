{"name": "notification_service", "version": "1.0.0", "description": "Notification with FCM", "main": "server.js", "scripts": {"make-migration": "./node_modules/sequelize-auto-migrations/bin/makemigration.js", "migrate": "NODE_ENV=development npx sequelize db:migrate", "make-seeder": "npx sequelize-cli seed:generate --name", "seeder": "NODE_ENV=development npx sequelize-cli db:seed:all", "test": "start-server-and-test start http://localhost:3000/ cypress:open", "start": "nodemon server.js"}, "repository": {"type": "git", "url": ""}, "keywords": ["FCM"], "author": "Il<PERSON>inz", "license": "ISC", "dependencies": {"@hapi/hapi": "^21.1.0", "@hapi/inert": "^7.0.0", "@hapi/vision": "^7.0.0", "axios": "^1.2.1", "bcrypt": "^5.1.0", "cypress-router": "^1.2.6", "dotenv": "^16.0.3", "firebase-admin": "^11.4.1", "hapi-auth-jwt2": "^10.2.0", "hapi-auto-route": "^3.0.4", "hapi-i18n": "^3.0.1", "hapi-swagger": "^15.0.0", "install": "^0.13.0", "joi": "^17.7.0", "jsonwebtoken": "^9.0.0", "moment": "^2.29.4", "mysql2": "^2.3.3", "nodemon": "^2.0.20", "npm": "^9.2.0", "path": "^0.12.7", "sequelize": "^6.28.0", "sequelize-auto-migrations": "github:scimonster/sequelize-auto-migrations#a063aa6535a3f580623581bf866cef2d609531ba"}, "devDependencies": {"cypress": "^12.2.0", "jest": "^29.3.1", "sequelize-cli": "^6.5.2", "start-server-and-test": "^1.15.2"}}