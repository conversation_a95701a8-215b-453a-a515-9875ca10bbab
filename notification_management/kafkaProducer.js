const Kafka = require('node-rdkafka');
const topic = "KUBY"
const stream = Kafka.Producer.createWriteStream({
  'metadata.broker.list': '18.194.39.64:9092'
}, {}, {
  topic: topic
});

stream.on('error', (err) => {
  console.error('Error in our kafka stream');
  console.error(err);
});

exports.addMessageToQueue = (payload, type, token = null) => {
  let data = { type: type, payload: payload, token: token };
  console.log(data)
  const success = stream.write(Buffer.from(JSON.stringify(data), "utf8"));
  if (success) {
    console.log(`message queued -- sent score data`);
  } else {
    console.log('Too many messages in the queue already..');
  }
}