'use strict';

var Sequelize = require('sequelize');

/**
 * Actions summary:
 *
 * createTable "notification_logs", deps: []
 * createTable "notification_types", deps: []
 * createTable "notification_contents", deps: [notification_types]
 *
 **/

var info = {
    "revision": 1,
    "name": "noname",
    "created": "2022-09-23T10:25:50.047Z",
    "comment": ""
};

var migrationCommands = function(transaction) {
    return [{
            fn: "createTable",
            params: [
                "notification_logs",
                {
                    "id": {
                        "type": Sequelize.INTEGER,
                        "field": "id",
                        "autoIncrement": true,
                        "primaryKey": true,
                        "allowNull": false
                    },
                    "status": {
                        "type": Sequelize.INTEGER,
                        "field": "status",
                        "allowNull": false
                    },
                    "receiverId": {
                        "type": Sequelize.INTEGER,
                        "field": "receiver_id",
                        "allowNull": false
                    },
                    "type": {
                        "type": Sequelize.STRING,
                        "field": "type",
                        "allowNull": false
                    },
                    "sessionId": {
                        "type": Sequelize.STRING,
                        "field": "session_id",
                        "allowNull": false
                    },
                    "content": {
                        "type": Sequelize.STRING,
                        "field": "content",
                        "allowNull": false
                    },
                    "replacements": {
                        "type": Sequelize.JSON,
                        "field": "replacements",
                        "allowNull": false
                    },
                    "title": {
                        "type": Sequelize.STRING,
                        "field": "title",
                        "allowNull": false
                    },
                    "createdAt": {
                        "type": Sequelize.DATE,
                        "field": "created_at",
                        "allowNull": false
                    },
                    "updatedAt": {
                        "type": Sequelize.DATE,
                        "field": "updated_at",
                        "allowNull": false
                    },
                    "deletedAt": {
                        "type": Sequelize.DATE,
                        "field": "deleted_at"
                    }
                },
                {
                    "charset": "utf8",
                    "transaction": transaction
                }
            ]
        },
        {
            fn: "createTable",
            params: [
                "notification_types",
                {
                    "id": {
                        "type": Sequelize.INTEGER,
                        "field": "id",
                        "autoIncrement": true,
                        "primaryKey": true,
                        "allowNull": false
                    },
                    "status": {
                        "type": Sequelize.INTEGER,
                        "field": "status",
                        "allowNull": false
                    },
                    "type": {
                        "type": Sequelize.STRING,
                        "field": "type",
                        "allowNull": false
                    },
                    "createdAt": {
                        "type": Sequelize.DATE,
                        "field": "created_at",
                        "allowNull": false
                    },
                    "updatedAt": {
                        "type": Sequelize.DATE,
                        "field": "updated_at",
                        "allowNull": false
                    },
                    "deletedAt": {
                        "type": Sequelize.DATE,
                        "field": "deleted_at"
                    }
                },
                {
                    "charset": "utf8",
                    "transaction": transaction
                }
            ]
        },
        {
            fn: "createTable",
            params: [
                "notification_contents",
                {
                    "id": {
                        "type": Sequelize.INTEGER,
                        "field": "id",
                        "autoIncrement": true,
                        "primaryKey": true,
                        "allowNull": false
                    },
                    "typeId": {
                        "type": Sequelize.INTEGER,
                        "field": "type_id",
                        "onDelete": "CASCADE",
                        "onUpdate": "CASCADE",
                        "references": {
                            "model": "notification_types",
                            "key": "id"
                        },
                        "allowNull": false
                    },
                    "status": {
                        "type": Sequelize.INTEGER,
                        "field": "status",
                        "allowNull": false
                    },
                    "title": {
                        "type": Sequelize.STRING,
                        "field": "title",
                        "allowNull": false
                    },
                    "content": {
                        "type": Sequelize.TEXT,
                        "field": "content",
                        "allowNull": false
                    },
                    "language": {
                        "type": Sequelize.ENUM('de', 'en'),
                        "field": "language",
                        "default": "en",
                        "allowNull": false
                    },
                    "createdAt": {
                        "type": Sequelize.DATE,
                        "field": "created_at",
                        "allowNull": false
                    },
                    "updatedAt": {
                        "type": Sequelize.DATE,
                        "field": "updated_at",
                        "allowNull": false
                    },
                    "deletedAt": {
                        "type": Sequelize.DATE,
                        "field": "deleted_at"
                    }
                },
                {
                    "charset": "utf8",
                    "transaction": transaction
                }
            ]
        }
    ];
};
var rollbackCommands = function(transaction) {
    return [{
            fn: "dropTable",
            params: ["notification_contents", {
                transaction: transaction
            }]
        },
        {
            fn: "dropTable",
            params: ["notification_logs", {
                transaction: transaction
            }]
        },
        {
            fn: "dropTable",
            params: ["notification_types", {
                transaction: transaction
            }]
        }
    ];
};

module.exports = {
    pos: 0,
    useTransaction: true,
    execute: function(queryInterface, Sequelize, _commands)
    {
        var index = this.pos;
        function run(transaction) {
            const commands = _commands(transaction);
            return new Promise(function(resolve, reject) {
                function next() {
                    if (index < commands.length)
                    {
                        let command = commands[index];
                        console.log("[#"+index+"] execute: " + command.fn);
                        index++;
                        queryInterface[command.fn].apply(queryInterface, command.params).then(next, reject);
                    }
                    else
                        resolve();
                }
                next();
            });
        }
        if (this.useTransaction) {
            return queryInterface.sequelize.transaction(run);
        } else {
            return run(null);
        }
    },
    up: function(queryInterface, Sequelize)
    {
        return this.execute(queryInterface, Sequelize, migrationCommands);
    },
    down: function(queryInterface, Sequelize)
    {
        return this.execute(queryInterface, Sequelize, rollbackCommands);
    },
    info: info
};
