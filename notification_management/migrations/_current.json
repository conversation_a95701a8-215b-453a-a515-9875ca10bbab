{"tables": {"notification_contents": {"tableName": "notification_contents", "schema": {"id": {"allowNull": false, "primaryKey": true, "autoIncrement": true, "field": "id", "seqType": "Sequelize.INTEGER"}, "typeId": {"allowNull": false, "references": {"model": "notification_types", "key": "id"}, "onUpdate": "CASCADE", "onDelete": "CASCADE", "field": "type_id", "seqType": "Sequelize.INTEGER"}, "status": {"allowNull": false, "field": "status", "seqType": "Sequelize.INTEGER"}, "title": {"allowNull": false, "field": "title", "seqType": "Sequelize.STRING"}, "content": {"allowNull": false, "field": "content", "seqType": "Sequelize.TEXT"}, "language": {"allowNull": false, "default": "en", "field": "language", "seqType": "Sequelize.ENUM('de', 'en')"}, "createdAt": {"allowNull": false, "field": "created_at", "seqType": "Sequelize.DATE"}, "updatedAt": {"allowNull": false, "field": "updated_at", "seqType": "Sequelize.DATE"}, "deletedAt": {"field": "deleted_at", "seqType": "Sequelize.DATE"}}, "charset": "utf8", "indexes": []}, "notification_logs": {"tableName": "notification_logs", "schema": {"id": {"allowNull": false, "primaryKey": true, "autoIncrement": true, "field": "id", "seqType": "Sequelize.INTEGER"}, "status": {"allowNull": false, "field": "status", "seqType": "Sequelize.INTEGER"}, "receiverId": {"allowNull": false, "field": "receiver_id", "seqType": "Sequelize.INTEGER"}, "type": {"allowNull": false, "field": "type", "seqType": "Sequelize.STRING"}, "sessionId": {"allowNull": false, "field": "session_id", "seqType": "Sequelize.STRING"}, "content": {"allowNull": false, "field": "content", "seqType": "Sequelize.STRING"}, "replacements": {"allowNull": false, "field": "replacements", "seqType": "Sequelize.JSON"}, "title": {"allowNull": false, "field": "title", "seqType": "Sequelize.STRING"}, "createdAt": {"allowNull": false, "field": "created_at", "seqType": "Sequelize.DATE"}, "updatedAt": {"allowNull": false, "field": "updated_at", "seqType": "Sequelize.DATE"}, "deletedAt": {"field": "deleted_at", "seqType": "Sequelize.DATE"}}, "charset": "utf8", "indexes": []}, "notification_types": {"tableName": "notification_types", "schema": {"id": {"allowNull": false, "primaryKey": true, "autoIncrement": true, "field": "id", "seqType": "Sequelize.INTEGER"}, "status": {"allowNull": false, "field": "status", "seqType": "Sequelize.INTEGER"}, "type": {"allowNull": false, "field": "type", "seqType": "Sequelize.STRING"}, "createdAt": {"allowNull": false, "field": "created_at", "seqType": "Sequelize.DATE"}, "updatedAt": {"allowNull": false, "field": "updated_at", "seqType": "Sequelize.DATE"}, "deletedAt": {"field": "deleted_at", "seqType": "Sequelize.DATE"}}, "charset": "utf8", "indexes": []}}, "revision": 1}