const controller = require("../controllers/notificationController");

module.exports = [
  {
    method: "POST",
    path: "/notification",
    handler: controller.sendNotification,
    options: {
      tags: ["api", "Notification"],
      notes: "Endpoint to send Notifications",
      description: "Send Notification",
      auth: false,
      validate: {
        headers: Joi.object(Common.headers()).options({ allowUnknown: true }),
        options: { abortEarly: false },
        payload: {
          replacements: Joi.object().required().example({ o: "p" }).default({}),
          type: Joi.string().example("soulwriting-customer-submit || soulwriting-companion-submit || meeting-scheduled || meeting-rescheduled || meeting-cancelled").required().error((errors) => {return Common.routeError(errors, "USER_ID_IS_REQUIRED")}),
          data: Joi.object().optional().example({ demo: 1 }).default({}),
          userId: Joi.number().integer().example(1).required().error((errors) => { return Common.routeError(errors, "USER_ID_IS_REQUIRED")}) 
        },
        validator: <PERSON><PERSON>
      }
    }
  }
];  