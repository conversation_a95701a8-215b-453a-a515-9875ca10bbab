const controller = require("../controllers/notificationController");
const Joi             = require("joi");
const Common          = require("../common.js");

module.exports = [
    // {
	// 	method  : "POST",
	// 	path    : "/notification",
	// 	handler : controller.sendNotification,
	// 	options : {
	// 		tags        : ["api", "Notification"],
	// 		notes       : "Endpoint to send Notifications",
	// 		description : "Send Notification",
	// 		auth        : false,
	// 		validate    : {
	// 			headers: Joi.object(Common.headers()).options({allowUnknown: true}),
	// 			  options: {
	// 				abortEarly: false
	// 			  },
	// 			payload : {
	// 				replacements : Joi.object().required().example({'o':'p'}).default({}),
	// 				typeId       : Joi.number().integer().example(1).required().default(1),
	// 				data         : Joi.object().optional().example({'demo':1}).default({}),
	// 				userId		 : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'USER_ID_IS_REQUIRED')})
	// 			},
	// 			validator: Joi
	// 		}
	// 	}
	// },
	// {
	// 	method  : "PATCH",
	// 	path    : "/notification",
	// 	handler : controller.updateNotification,
	// 	options : {
	// 		tags        : ["api", "Notification"],
	// 		notes       : "Endpoint to Update Notifications",
	// 		description : "Update Notification",
	// 		auth        : false,
	// 		validate    : {
	// 			headers: Joi.object(Common.headers()).options({allowUnknown: true}),
	// 			  options: {
	// 				abortEarly: false
	// 			  },
	// 			payload : {
	// 				typeId       : Joi.number().integer().example(1).required().default(1),
	// 				id		     : Joi.number().integer().example(1).optional(),
	// 				replacements :Joi.object().optional().example({'o':'p'}).default({}),
	// 				data         : Joi.object().optional().example({'demo':1}).default({}),
	// 			},
	// 			validator: Joi
	// 		}
	// 	}
	// },
	// {
	// 	method  : "PATCH",
	// 	path    : "/notification-entity",
	// 	handler : controller.updateEntityNotification,
	// 	options : {
	// 		tags        : ["api", "Notification"],
	// 		notes       : "Endpoint to Update Notifications",
	// 		description : "Update Notification",
	// 		auth        : false,
	// 		validate    : {
	// 			headers: Joi.object(Common.headers()).options({allowUnknown: true}),
	// 			  options: {
	// 				abortEarly: false
	// 			  },
	// 			payload : {
	// 				typeId       : Joi.number().integer().example(1).required().default(1),
	// 				id		     : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
	// 				replacements :Joi.object().required().example({'o':'p'}).default({}),
	// 				data         : Joi.object().optional().example({'demo':1}).default({}),
	// 				receiverId	 : Joi.number().integer().optional().example(1).default(null),
	// 				entityId	 :	Joi.number().integer().example(2)
	// 			},
	// 			validator: Joi
	// 		}
	// 	}
	// },
	// {
	// 	method  : "GET",
	// 	path    : "/notification",
	// 	handler : controller.getNotifications,
	// 	options : {
	// 		tags        : ["api", "Notification"],
	// 		notes       : "Endpoint to get Notifications List",
	// 		description : "Get Notification",
	// 		auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
	// 		validate    : {
	// 			headers: Joi.object(Common.headers(true)).options({allowUnknown: true}),
	// 			  options: {
	// 				abortEarly: false
	// 			  },
	// 			query : {
	// 				limit: Joi.number().integer().optional().default(null),
    //                 status: Joi.number().valid(0,1).optional().default(null),
    //                 searchText: Joi.string().max(250).optional().default(null),
    //                 pageNumber: Joi.number().integer().min(1).optional().default(null),
    //                 orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
    //                 orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
	// 			},
	// 			validator: Joi
	// 		}
	// 	}
	// },
	// {
	// 	method  : "POST",
	// 	path    : "/socket-notification",
	// 	handler : controller.sendSocketNotification,
	// 	options : {
	// 		tags        : ["api", "Notification"],
	// 		notes       : "Endpoint to send Notifications",
	// 		description : "Send Notification",
	// 		auth        : false,
	// 		validate    : {
	// 			headers: Joi.object(Common.headers()).options({allowUnknown: true}),
	// 			  options: {
	// 				abortEarly: false
	// 			  },
	// 			payload : {
	// 				replacements : Joi.object().required().example({'o':'p'}).default({}),
	// 				typeId       : Joi.number().integer().example(1).required().default(1),
	// 				data         : Joi.object().optional().example({'demo':1}).default({}),
	// 				userId		 : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'USER_ID_IS_REQUIRED')}),
	// 			},
	// 			validator: Joi
	// 		}
	// 	}
	// },
	// {
	// 	method  : "PATCH",
	// 	path    : "/status",
	// 	handler : controller.readNotification,
	// 	options : {
	// 		tags        : ["api", "Notification"],
	// 		notes       : "Endpoint to update status Notifications",
	// 		description : "update status Notification",
	// 		auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
	// 		validate    : {
	// 			headers: Joi.object(Common.headers(true)).options({allowUnknown: true}),
	// 			  options: {
	// 				abortEarly: false
	// 			  },
	// 			payload : {
	// 				status   : Joi.number().integer().valid(0,1).example(1).required().error(errors=>{return Common.routeError(errors,'STATUS_IS_REQUIRED')}),
	// 				id		 : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'NOTIFICATION_ID_IS_REQUIRED')}),
	// 			},
	// 			validator: Joi
	// 		}
	// 	}
	// },
	
	// {
	// 	method  : "GET",
	// 	path    : "/notification-count",
	// 	handler : controller.notificationcount,
	// 	options : {
	// 		tags        : ["api", "Notification"],
	// 		notes       : "Endpoint to get Notifications Count",
	// 		description : "Get Notification",
	// 		auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
	// 		validate    : {
	// 			headers: Joi.object(Common.headers(true)).options({allowUnknown: true}),
	// 			  options: {
	// 				abortEarly: false
	// 			  },
	// 			query : {
					
	// 			},
	// 			validator: Joi
	// 		}
	// 	}
	// },
]  