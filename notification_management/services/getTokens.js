let Axios=require('axios');
let Constants=require('../constants')
module.exports = {
	getTokens : async(userId) => {
		try  {
			let response=await Axios({
                method: 'get',
                url:Constants.URL.NOTIFICATION_SESSION_IDS,
                params: {
                  userId:userId
                }
              })
			 
              let data=response.data.sessionIds;
			  console.log('data',data)
              return data;
		}
		catch (error) {
			console.error(error);
			return []
		}
	}
}