
const {app} = require("./firebaseAuth.js");
require('dotenv').config();

const notification_options = {
    priority: "high",
    timeToLive: 60 * 60 * 24
};
module.exports = {
	sendNotification : async(sessionIds, notificationData) => {
		try  {
			if(sessionIds.length==0)
				return true
			
			let payload={}
			payload={
				notification:{
					title:notificationData.title,
					body:notificationData.description,
					data:JSON.stringify(notificationData) 
				},
				data:{
					title:notificationData.title,
					body:notificationData.description,
					data:JSON.stringify(notificationData) 
				},
				
			}
			await app.messaging().sendToDevice(sessionIds,payload,notification_options);
			return true
		}
		catch (error) {
			console.error(error);
			return 0;
		}
	}
}
