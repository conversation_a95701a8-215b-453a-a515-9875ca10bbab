{"name": "kuby", "version": "1.0.0", "description": "Gateway", "scripts": {"test": "lab --leaks --timeout 30000", "start": "node server"}, "repository": {}, "keywords": ["kuby"], "author": "illuminz", "license": "ISC", "dependencies": {"@hapi/boom": "^10.0.0", "@hapi/hapi": "^21.0.0", "@hapi/inert": "^7.0.0", "@hapi/vision": "^7.0.0", "@joi/date": "^2.1.0", "axios": "^1.1.3", "bcrypt": "^5.1.0", "dotenv": "^16.0.3", "form-data": "^4.0.0", "formdata-node": "^5.0.0", "fs": "^0.0.1-security", "hapi-auth-jwt2": "^10.2.0", "hapi-auto-route": "^3.0.4", "hapi-cron": "^1.1.0", "hapi-i18n": "^3.0.1", "hapi-swagger": "^15.0.0", "http": "^0.0.1-security", "joi": "^17.7.0", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.29.4", "mysql2": "^2.3.3", "ngrok": "^4.3.3", "path": "^0.12.7", "socket.io": "^4.5.3", "socket.io-redis": "^6.1.1", "stream": "^0.0.2"}, "devDependencies": {"@hapi/code": "^9.0.1", "@hapi/lab": "^25.0.1", "chai": "^4.3.7"}}