exports.initializeSocket = async () => {
    global.io = require("socket.io")(server.listener, { allowEIO3: true });

    io.on("connection", async (socket) => {
        let token = socket?.handshake?.headers?.authorization;
        if(!token || token === undefined) {
            socket.emit("custom_error", {  status: 401, message: "Please provide a valid token" })
            socket.disconnect(true)
        }

        let tokenData = await Common.validateToken(Jwt.decode(token));
        if(!tokenData || tokenData?.credentials === undefined) {
            socket.emit("custom_error", {  status: 401, message: "Please provide a valid token" })
            socket.disconnect(true)
        }

        let userId = tokenData?.credentials?.userData?.User?.id;

        socket.join("kuby");
        socket.join("user_" + userId);

        socket.on('test', async (payload, temp, callback) => {
            socket.emit("test", "socket working!")
        })
        
    });
};