exports.privateKey='ABCDEFGHIJKLMNOPQRSTUVWXYZ123456';
exports.algorithm="aes-256-cbc";
exports.iv='QWERTY1234567890';

decrypt = (text) => {
  let decipher = crypto.createDecipheriv(this.algorithm, this.privateKey, this.iv);
  let decrypted = decipher.update(text, "hex", "utf8");
  decrypted = decrypted + decipher.final("utf8");
  return decrypted;
}

encrypt = (text) => {
  let cipher = crypto.createCipheriv(this.algorithm,this.privateKey, this.iv);
  let encrypted =cipher.update(text, "utf8", "hex");
  encrypted = encrypted + cipher.final("hex");
  return encrypted;
}

readHTMLFile = (path,callback) => {
  Fs.readFile(path, { encoding: "utf-8" }, function(err, html) {
    if (err) {
      throw err;
      callback(err);
    } else {
      callback(null, html);
    }
  });
};


exports.prefunction = (req,h) => {
  global.LanguageCodes = process.env.ALL_LANGUAGE_CODE.split(',');
  global.LanguageIds = process.env.ALL_LANGUAGE_ID.split(',').map(function(item) {
    return parseInt(item, 10);
  });
  global.utcOffset = req.headers.utcoffset;
  return true;
}

exports.routeError = (errors,message) => {
  errors.forEach(err=>{ 
    switch(err.code) {
      case "any.required":
        err.message=message;
        break
      case "string.guid":
        err.message='PLEASE_ENTER_A_VALID_UUID'
        break
      case "string.max":
        err.message=`LENGTH_CANNOT_BE_GREATER_THAN_${err.local.limit}`
        break
      case "string.min":
        err.message=`MINIMUM_LENGTH_MUST_BE_${err.local.limit}`
        break
    }
  });
  return errors
}

exports.customValidator = (element,helper) => {
  let schemaType = helper.schema.type;
  switch(schemaType) {
    case "string":
      if(element.replace(/\s+/g, " ").trim() === '') return helper.message('ENETERED_STRING_SHOULD_NOT_BE_EMPTY')
      if(!isNaN(element)) return helper.message('ENTERED_NAME_MUST_CONTAIN_ATLEAT_ONE_CHARACTER')
      return element.replace(/\s+/g, " ").trim()
  }
}

exports.validateToken = async (token) => {
  fetchtoken = JSON.parse(decrypt(token.data));
  var diff = Moment().diff(Moment(token.iat * 1000));
  if (diff > 0) {
    return {
      isValid: true,
      credentials: { userData: fetchtoken, scope: [...fetchtoken.Role,...fetchtoken.Permissions] }
    }; 
  }
  return {
    isValid: false
  };
};

exports.signToken = (tokenData) => {
    return Jwt.sign(
      { data: encrypt(JSON.stringify(tokenData))},
      this.privateKey
    );
};

exports.headers = (authorized) => {
	let Globalheaders = {
        language    :  Joi.string().optional().default(process.env.DEFAULT_LANGUANGE_CODE),
        utcoffset   :  Joi.string().optional().default(0),
        timezone    :  Joi.string().optional().default(null),
        
    };
	if (authorized){
        _.assign(Globalheaders, {authorization: Joi.string().required().description("Token to identify user who is performing the action")});
	}
	return Globalheaders;
};


exports.generateCode = (requestedlength) => {
  const char = '1234567890'; 
  const length = typeof requestedlength !='undefined' ? requestedlength : 4;
  let randomvalue = '';
  for ( let i = 0; i < length; i++) {
    const value = Math.floor(Math.random() * char.length);
    randomvalue += char.substring(value, value + 1).toUpperCase();
  }
  return randomvalue;
}

exports.FailureError = (err,req) => {
  const updatedError = err;
	updatedError.output.payload.message = [];
	let customMessages = {};
	if (err.isJoi && Array.isArray(err.details) && err.details.length > 0){
		err.details.forEach((error) => {
			customMessages[error.context.label] = req.i18n.__(error.message);
		});
	}
	delete updatedError.output.payload.validation;
	updatedError.output.payload.error =  req.i18n.__('BAD_REQUEST');
  if(err.details[0].type === 'string.email') {
    updatedError.output.payload.message = req.i18n.__(
      "PLEASE_ENTER_A_VALID_EMAIL"
    );
  } else if(err.details[0].type === 'any.required') {
    updatedError.output.payload.message = req.i18n.__(
      err.details[0].message
    );
  } else {
    updatedError.output.payload.message = req.i18n.__(
      "ERROR_WHILE_VALIDATING_REQUEST"
    );
  }
	updatedError.output.payload.errors = customMessages;
	return updatedError;
}

exports.generateError = (req,statusCode,message,error)=>{
  let customError = {status:false,responseData:{}};
  switch(statusCode) {
    case 400:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__(message);
      break;
    case 401:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__('UNAUTHORIZED_REQUEST');
      break;
    case 403:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__('PERMISSION_DENIED');
      break;
    case 422:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__(message);
      break;
    case 500:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__('INTERNAL_SERVER_ERROR');
      break;
    default:
      customError['error'] = error?.message;
      customError['message'] = req.i18n.__('UNKNOWN_ERROR_OCCURED');
      break;
  }
  return customError;
}

exports.getTotalPages = async (records, perpage) => {
  let totalPages = Math.ceil(records / perpage);
  return totalPages;
};