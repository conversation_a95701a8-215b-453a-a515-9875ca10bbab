const Kafka = require('node-rdkafka');

const topic = "KUBY"
var consumer = new Kafka.KafkaConsumer({
  'group.id': 'apigateway',
  'metadata.broker.list': '18.194.39.64:9092',
}, {});

consumer.connect();

consumer.on('error', (err) => {
  console.error('Error in our kafka consumer');
  console.error(err);
});

const dataParser = (data) => {
  let responseType = JSON.parse(data).type;
  let payload =  typeof JSON.parse(data)?.payload === "string" ? JSON.parse(JSON.parse(data)?.payload) : JSON.parse(data)?.payload;
  let tokenData = JSON.parse(data).token;

  return {responseType, payload, tokenData}
}

consumer.on('ready', () => {
  console.log('consumer ready..')
  consumer.subscribe([topic]);
  consumer.consume();
}).on('data', async function(data) {
  let response = data.value.toString();
  let responseType = await dataParser(response).responseType;
  let payload =  await dataParser(response).payload;
  let tokenData = await dataParser(response).token;

  switch (responseType) {
    case "notification":
      io.to(`user_${payload.userId}`).emit("notification", payload.notificationData);
      break;

    default:
        break;
  }
  console.log(`received message: ${response}`);
});