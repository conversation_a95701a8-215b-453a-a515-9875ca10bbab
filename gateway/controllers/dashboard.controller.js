const FormData = require('form-data');
const setRequestObject=(req,reqdomain,auth)=>{
    let headers = {}
    if(req.headers?.language !== undefined) headers['language']=req.headers.language;
    if(req.headers?.utcoffset !== undefined) headers['utcoffset']=req.headers.utcoffset;
    if(auth==1 && req.headers?.authorization !== undefined) headers['authorization']=req.headers.authorization;
    return requestObject = {
        url: req.path,
        headers: headers,
        method: req.method,
        baseURL: reqdomain,
    }
}

const uniqueArray = (array) => {
    const hashTable = {};
    const uniqueArray = [];
  
    for (let i = 0; i < array.length; i++) {
      const element = array[i];
      if (!hashTable[element]) {
        hashTable[element] = true;
        uniqueArray.push(element);
      }
    }
    return uniqueArray;
}

module.exports={
    mobileDashboard:async(req,h)=>{
        try{
            // fatching Companion----
            let params = {
                random : 1,
                limit  : 5
            }
            req.path = '/companion'
            let requestObject = setRequestObject(req,process.env.USER_ONBOARDING_DOMAIN,1)
            requestObject     = (req.method.toLowerCase() === 'get' ||req.method.toLowerCase() === 'delete') ? {...requestObject,params:params} : {...requestObject,data:req.payload}

            let companionResponse = Axios.request(requestObject);

            // fatching notification Count ---
            req.path = '/notification-count'
            requestObject = setRequestObject(req,process.env.NOTIFICATION_DOMAIN,1)
            requestObject = {...requestObject,params:{}};

            let notificationResponse = Axios.request(requestObject);

            // fetching Kuby courses
            req.path = '/course/getDashboardData'
            requestObject = setRequestObject(req,process.env.VIDEO_DOMAIN,1)
            requestObject = {...requestObject,params:{}};

            let courseResponse = Axios.request(requestObject);

            req.path = '/product-list'
            requestObject = setRequestObject(req,process.env.PRODUCT_MANAGEMENT_GATEWAY,0)
            requestObject = {...requestObject,params:{limit:5}};

            let productResponse = Axios.request(requestObject);

            req.path = '/cart-value'
            requestObject = setRequestObject(req,process.env.PRODUCT_MANAGEMENT_GATEWAY,1)
            // requestObject = {...requestObject,params:{limit:5}};

            let cartResponse = Axios.request(requestObject);

            // Setting Response --
            let responseData={}
            // /cart-value
            await Promise.all([companionResponse, notificationResponse, courseResponse,productResponse,cartResponse]).then((res) => {
                responseData.companion      = res[0]?.data?.responseData?.users;
                responseData.notification   = res[1]?.data?.responseData;
                responseData                = {...responseData, ...res[2]?.data?.responseData}
                responseData.product        = res[3]?.data?.responseData?.products
                responseData.productsInCart =   res[4]?.data?.responseData?.productInCart
            }).catch((err) => {
                throw new Error(err);
            })

            return h.response({success:true,message: req.i18n.__("SUCCESSFULLY_FATCHED"), responseData}).code(200)
            // return h.response(response.data).code(response.status);
        }
        catch(error)
        {
            console.error('Error',error)
            return h.response(error?.response?.data ).code(error.response.status);
        }
    },
    companionDashboardCount: async(req, h) => {
        try {
            console.log("1234567890 -> inside you");
            console.log("req.headers", req.headers);
            let headers = {}
            if(req.headers?.language !== undefined) headers['language']=req.headers.language;
            if(req.headers?.utcoffset !== undefined) headers['utcoffset']=req.headers.utcoffset;
            if(req.headers?.authorization !== undefined) headers['authorization']=req.headers.authorization;
            if(req.headers?.timezone!==undefined && req.headers?.timezone !== null)headers['timezone']=req.headers.timezone;

            let eventRequestObject = setRequestObject(req,process.env.EVENT_MANAGEMENT_GATEWAY, 1);
            eventRequestObject = (req.method.toLowerCase() === 'get' ||req.method.toLowerCase() === 'delete') ? {...eventRequestObject,params:req.query} : {...eventRequestObject,data:req.payload}
            let eventResponse = await Axios.request(eventRequestObject);
            eventResponse = eventResponse?.data;

            const eventUsers = eventResponse?.responseData?.users ? eventResponse?.responseData?.users : [];
            const meetingCount = eventResponse?.responseData?.meetingCount ? eventResponse?.responseData?.meetingCount : 0;
            //const eventEarning = eventResponse?.responseData?.earning ? eventResponse?.responseData?.earning : 0;


            let ProjectRequestObject = setRequestObject(req,process.env.SOUL_WRITING_DOMAIN, 1);
            ProjectRequestObject = (req.method.toLowerCase() === 'get' ||req.method.toLowerCase() === 'delete') ? {...ProjectRequestObject,params:req.query} : {...ProjectRequestObject,data:req.payload}
            let projectResponse = await Axios.request(ProjectRequestObject);
            projectResponse = projectResponse?.data

            const projectUsers = projectResponse?.responseData?.users ? projectResponse?.responseData?.users : [];
            const projectCount = projectResponse?.responseData?.soulWritingCount ? projectResponse?.responseData?.soulWritingCount : 0;

            
            let PaymentRequestObject = setRequestObject(req,process.env.USER_ONBOARDING_DOMAIN, 1);
            PaymentRequestObject = (req.method.toLowerCase() === 'get' ||req.method.toLowerCase() === 'delete') ? {...PaymentRequestObject,params:req.query} : {...PaymentRequestObject,data:req.payload}
            let paymentResponse = await Axios.request(PaymentRequestObject);
            paymentResponse = paymentResponse?.data

            const paymentAmount = paymentResponse?.responseData ? paymentResponse?.responseData : 0;

            

            //const projectEarning = projectResponse?.responseData?.earning ? projectResponse?.responseData?.earning : 0;

            let usersArray = [...eventUsers, ...projectUsers];
            let finalUserArray = uniqueArray(usersArray);

            let responseData = { 
                userCount: finalUserArray.length,
                soulWritingCount: projectCount,
                meetingCount: meetingCount,
                earning: paymentAmount
            } 

            return h.response({success:true,message: req.i18n.__("SUCCESSFULLY_FATCHED"), responseData}).code(200)
        } catch (error) {
            console.error('Error',error)
            return h.response(error?.response?.data ).code(error.response.status);
        }
    }
}

