const FormData = require('form-data');
/**     Function for seeting request object of axois    **/ 
const setRequestObject=(req,reqdomain)=>{
    let headers = {}
    if(req.headers?.language !== undefined) headers['language']=req.headers.language;
    if(req.headers?.utcoffset !== undefined) headers['utcoffset']=req.headers.utcoffset;
    if(req.headers?.authorization !== undefined) headers['authorization']=req.headers.authorization;
    if(req.headers?.timezone!==undefined && req.headers?.timezone !== null)headers['timezone']=req.headers.timezone;
    return requestObject = {
        url: req.path,
        headers: headers,
        method: req.method,
        baseURL: reqdomain,
    }
}

module.exports={

/** Handling **/    
   emailGateway:async(req,h) => {
        try {
             let requestObject = setRequestObject(req,process.env.EMAIL_GATEWAY_DOMAIN)
            requestObject = (req.method.toLowerCase() === 'get') ? {...requestObject,params:req.query} : {...requestObject,data:req.payload}
            const response = await Axios.request(requestObject);
            return h.response(response.data).code(response.status);
        } catch(error) {
            console.error('Error',error.response)
            return h.response(error.response.data).code(error.response.status);
        }
    },
    
    fileGateway:async(req,h) => {
        try {
            let requestObject = setRequestObject(req,process.env.ATTACHMENT_DOMAIN)
            let data;
            if(req.method.toLowerCase()==='post' && req.path === '/attachment/upload')
            {
                const form = new FormData();
                form.append('files',await Fs.createReadStream(req.payload.files.path), {
                    contentType:req.payload.files.headers['content-type'] ,
                    name:'files',
                    filename:req.payload.files.filename,
                  });
                if(req.payload.user_id!==null)
                {form.append('user_id',req.payload.user_id)}  
                data=form
            }
            else{data=req.payload}
            if(req.method.toLowerCase() ==='get')
            {
                req.query.returnData=1
            }
            requestObject = (req.method.toLowerCase() === 'get' ||req.method.toLowerCase()==='delete') ? {...requestObject,params:req.query} : {...requestObject,data}
            const response = await Axios.request(requestObject);
            if(/^\/resources\/attachments\/\d{1,4}\/\d{1,2}\/\d{1,2}\/\S*$/gm.test(req.path))
            {
                const bitmap = new Buffer(response.data, 'base64');
                return h.response(bitmap).type(response.headers['content-type']).code(200)
            }
            else if(/^\/resources\/soulwriting\/\S+$/gm.test(req.path))
            {
                const bitmap = new Buffer(response.data, 'base64');
                return h.response(bitmap).type(response.headers['content-type']).code(200)
            }
            else if(/^\/resources\/attachments\/thumbnail\/\d{1,4}\/\d{1,2}\/\d{1,2}\/\S*$/gm.test(req.path))
            {
                const bitmap = new Buffer(response.data, 'base64');
                return h.response(bitmap).type(response.headers['content-type']).code(200)
            }
            // else if(/^\/resources\/meetings\/\d{1,4}\/\d{1,2}\/\d{1,2}\/\S*$/gm.test(req.path))
            else if(req.path.includes("meetings"))
            {
                const bitmap = new Buffer(response.data, 'base64');
                return h.response(bitmap).type(response.headers['content-type']).code(200)
            }
            else if(req.path === '/attachment/view'){
                const bitmap = new Buffer(response.data, 'base64');
                return h.response(bitmap).type(response.headers['content-type']).code(response.status)
            }
            else if(req.path === '/attachment/meeting/download'){
                const bitmap = new Buffer(response.data, 'base64');
                
                return h.response(bitmap).type(response.headers['content-type']).header('content-disposition',response.headers['content-disposition']).code(response.status)
            }
            return h.response(response.data).code(response.status);
        } catch(error) {
            console.log(error, " ==================== ")
            console.error('Error',error.response.data)
            return h.response(error.response.data).code(error.response.status);
        }
    },
    useronboardingGateway:async(req,h) => {
        try {
            let requestObject = setRequestObject(req,process.env.USER_ONBOARDING_DOMAIN)
            requestObject = (req.method.toLowerCase() === 'get' ||req.method.toLowerCase() === 'delete') ? {...requestObject,params:req.query} : {...requestObject,data:req.payload}
            const response = await Axios.request(requestObject);
            return h.response(response.data).code(response.status);
        } catch(error) {
            console.error('Error',error)
            return h.response(error.response.data).code(error.response.status);
        }
    },
    
    videoManagementGateway:async(req, h) => {
        try {
            let requestObject = setRequestObject(req,process.env.VIDEO_DOMAIN);
            requestObject     = (req.method.toLowerCase() === 'get' || req.method.toLowerCase() === 'delete') ? 
                                {...requestObject,params:req.query} : 
                                {...requestObject,data:req.payload};
    
            const response = await Axios.request(requestObject);
            return h.response(response.data).code(response.status);
        }
        catch (error) {
            console.error('Error',error)
            return h.response(error.response.data).code(error.response.status);
        }
    },
    
    evantManagementgateway:async(req,h) => {
        try {
            let requestObject = setRequestObject(req,process.env.EVENT_MANAGEMENT_GATEWAY)
            requestObject = (req.method.toLowerCase() === 'get' ||req.method.toLowerCase() === 'delete') ? {...requestObject,params:req.query} : {...requestObject,data:req.payload}
            const response = await Axios.request(requestObject);
            return h.response(response.data).code(response.status);
        } catch(error) {
            console.error('Error',error)
            return h.response(error.response.data).code(error.response.status);
        }
    },
    
    notificationManagementGateway:async(req,h) => {
        try {
            let requestObject = setRequestObject(req,process.env.NOTIFICATION_DOMAIN)
            requestObject = (req.method.toLowerCase() === 'get' ||req.method.toLowerCase() === 'delete') ? {...requestObject,params:req.query} : {...requestObject,data:req.payload}
            const response = await Axios.request(requestObject);
            return h.response(response.data).code(response.status);
        } catch(error) {
            console.error('Error',error)
            return h.response(error.response.data).code(error.response.status);
        }
    },
    
    productManagementgateway:async(req,h)=>{
        try {
            let requestObject = setRequestObject(req,process.env.PRODUCT_MANAGEMENT_GATEWAY)
            requestObject = (req.method.toLowerCase() === 'get' ||req.method.toLowerCase() === 'delete') ? {...requestObject,params:req.query} : {...requestObject,data:req.payload}
            const response = await Axios.request(requestObject);
            return h.response(response.data).code(response.status);
        } catch(error) {
            console.error('Error',error)
            return h.response(error.response.data).code(error.response.status);
        }
    },
    
    zoomMeetingGateway:async(req,h)=>{
        try {
            let requestObject = setRequestObject(req,process.env.ZOOM_DOMAIN)
            requestObject = (req.method.toLowerCase() === 'get' ||req.method.toLowerCase() === 'delete') ? {...requestObject,params:req.query} : {...requestObject,data:req.payload}
            const response = await Axios.request(requestObject);
            return h.response(response.data).code(response.status);
        } catch(error) {
            console.error('Error',error)
            return h.response(error.response.data).code(error.response.status);
        }
    },

    soulWritingGateway:async(req,h)=>{
        try {
            let requestObject = setRequestObject(req,process.env.SOUL_WRITING_DOMAIN)
            requestObject = (req.method.toLowerCase() === 'get' ||req.method.toLowerCase() === 'delete') ? {...requestObject,params:req.query} : {...requestObject,data:req.payload}
            console.log('requestObject',requestObject)
            const response = await Axios.request(requestObject);
            return h.response(response.data).code(response.status);
        } catch(error) {
            console.error('Error',error)
            return h.response(error.response.data).code(error.response.status);
        }
    }
    
}





