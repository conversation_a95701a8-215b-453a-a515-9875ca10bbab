const {fileGateway} = require('../controllers/gatewayController');

module.exports = [
	// ***********************************  ATTACHMENT ENDPOINTS  ***********************************
    {
		method: "POST",
        path: "/attachment/upload",
        handler: fileGateway,
		options: {
			tags: ["api", "Attachment"],
			notes: "Endpoint to upload single/multiple attachments",
			description:"Upload attachment",
			auth: false,
			validate: {
				options: {
					abortEarly: false
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
                payload: Joi.object({
                    files: Joi.any()
                        .meta({ swaggerType: 'file'}).required()
                        .description('Array of files or object'),
                    user_id: Joi.number().optional().default(null)
                }),
				validator: Jo<PERSON>
			},
            payload: {
                maxBytes: 10000000,
                output: "file",
                parse: true,
                multipart: true,
                timeout: 60000,
            },
            plugins: {
                'hapi-swagger': {
                    payloadType: 'form'
                }
            },
			pre : [{method: Common.prefunction}]
		}
    },
    {
		method: "DELETE",
		path: "/attachment/delete",
		handler:fileGateway,
		options: {
			tags: ["api", "Attachment"],
			notes: "Endpoint to delete single/multiple attachments by id",
			description:"Delete attachment",
			auth: false,
			validate: {
				options: {
					abortEarly: false
				},
				query: {
                    ids : Joi.string().required().description('Comma seperated ids to delete multiple attachments')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/attachment/download",
		handler :fileGateway,
		options: {
			tags: ["api", "Attachment"],
			notes: "Endpoint to download attachment",
			description: "Download Attachment",
			auth: false,
			validate: {
				options: {
					abortEarly: false
				},
				query: {
                    id : Joi.number().required()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/attachment/meeting/download",
		handler :fileGateway,
		options: {
			tags: ["api", "Attachment"],
			notes: "Endpoint to download attachment",
			description: "Download Attachment",
			// auth: false,
			auth: {strategy: "jwt"},
			validate: {
				headers: Joi.object({
                    authorization: Joi.string().required().description("Token to identify user who is performing the action")
                }).unknown(true),
				options: {
					abortEarly: false
				},
				query: {
                    id : Joi.string().required()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/attachment/pdf/download",
		handler :fileGateway,
		options: {
			tags: ["api", "Attachment"],
			notes: "Endpoint to download attachment",
			description: "Download Attachment",
			auth: false,
			// auth: {strategy: "jwt"},
			validate: {
				// headers: Joi.object({
                //     authorization: Joi.string().required().description("Token to identify user who is performing the action")
                // }).unknown(true),
				options: {
					abortEarly: false
				},
				query: {
                    path : Joi.string().required()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/attachment/view",
		handler :fileGateway,
		options: {
			tags: ["api", "Attachment"],
			notes: "Endpoint to view attachment",
			description: "View Attachment",
			auth: false,
			validate: {
				options: {
					abortEarly: false
				},
				query: {
                    id : Joi.number().required()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},

	{
		method : "PATCH",
		path : "/attachment/update",
		handler : fileGateway,
		options: {
			tags: ["api", "Attachment"],
			notes: "Endpoint to update attachment status",
			description: "Update Attachment",
			auth: false,
			validate: {
				options: {
					abortEarly: false
				},
				payload: {
                    data : Joi.array()
					.items({
					  id: Joi.number()
						.required(),
					  status: Joi.number()
						.required()
					}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/attachment/valid",
		handler : fileGateway,
		options: {
			tags: ["api", "Attachment"],
			notes: "Endpoint to check Valid attachment",
			description: "Valid Attachment",
			auth: false,
			validate: {
				options: {
					abortEarly: false
				},
				query: {
                    id : Joi.number().required()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
        method: "GET",
        path: "/resources/attachments/{file*}",
        options: {
            tags: [
				"api", "Attachment"
            ],
            plugins: {
                "hapi-swagger": {}

            },
            notes: "Access static content",
            description: "Access Static content",
            auth: false,
             validate: {
                options: {
                    abortEarly: false
                },
                // query: {
                //     data: Joi.number().default(0)
                // },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
        },
        handler: fileGateway
    },
	{
        method: "GET",
        path: "/resources/soulwriting/{file*}",
        options: {
            tags: [
				"api", "Attachment"
            ],
            plugins: {
                "hapi-swagger": {}

            },
            notes: "Access static content",
            description: "Access Static content",
            auth: false,
             validate: {
                options: {
                    abortEarly: false
                },
                // query: {
                //     data: Joi.number().default(0)
                // },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
        },
        handler: fileGateway
    },
	{
		method  : "POST",
		path    : "/uploadFromUrl",
		options : {
			handler     : fileGateway,
			description : "Download from URL",
			notes       : "Upload from URL",
			tags        : ["api", "Attachment"],
			auth        : false,
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query :
				{
					url : Joi.string().trim().required(),
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "POST",
		path    : "/excel-to-json",
		options : {
			handler     : fileGateway,
			description : "Download from URL",
			notes       : "Upload from URL",
			tags        : ["api", "Attachment"],
			auth        : false,
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
				}).options
				({
					"allowUnknown"  : true
				}),
				payload :
				{
					url : Joi.string().trim().required(),
					deleteFile: Joi.number().integer().valid(0,1).optional().default(0)
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "POST",
		path    : "/generate-pdf",
		options : {
			handler     : fileGateway,
			description : "Download from URL",
			notes       : "Upload from URL",
			tags        : ["api", "Attachment"],
            auth: {strategy: "jwt"},
            validate: {
                headers: Joi.object({
                    authorization: Joi.string().required().description("Token to identify user who is performing the action")
                }).unknown(true),
                options: {
                    abortEarly: false
                },
				payload :
				{
					projectId : Joi.number().required(),
                    version: Joi.number().required(),
                    language: Joi.string().required(),
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	}
]