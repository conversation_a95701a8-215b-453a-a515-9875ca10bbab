const {soulWritingGateway} = require('../controllers/gatewayController');

module.exports = [
    // ################################################################################################
    // #                                        Category Routes                                       #
    // ################################################################################################
    {
        method : "POST",
        path : "/soul-writing/category",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Category"],
            notes: "Endpoint to post new category",
            description: "Add Category",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    name: Joi.string().example('pane name').required().error(errors=>{return Common.routeError(errors,'NAME_IS_REQUIRED')}),   
                    description: Joi.string().example('pane name').optional().allow(null).default(null),                   
                    type: Joi.number().integer().example('1 (optional)').optional().default(null),  
                    videoLink: Joi.string().example("video link (optional)").optional().default(null),
                    languageCode: Joi.string().example("en").required().error(errors=>{return Common.routeError(errors,'LANGUAGE_CODE_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/category",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Category"],
            notes: "Endpoint to get all category",
            description: "Get all Category",
            auth:false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                query: {   

                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "PATCH",
        path : "/soul-writing/category",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Category"],
            notes: "Endpoint to update existing category",
            description: "Update Category",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    name: Joi.string().example('pane name').allow(null).optional(), 
                    description: Joi.string().example('pane name').optional().allow(null).default(null),                    
                    id: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),  
                    videoLink: Joi.string().example("video link (optional)").allow(null).optional(),
                    // languageCode: Joi.string().example("en").required().error(errors=>{return Common.routeError(errors,'LANGUAGE_CODE_IS_REQUIRED')}) 
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/category/{id}",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Category"],
            notes: "Endpoint to get category by id",
            description: "Get Category By Id",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {                        
                    id : Joi.string().example(1).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "DELETE",
        path : "/soul-writing/category",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Category"],
            notes: "Endpoint to delete category",
            description: "Delete Category",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {                        
                    id : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')})
                },
                query: {
                    email : Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },

    // ################################################################################################
    // #                                       Character Routes                                       #
    // ################################################################################################


    {
        method : "POST",
        path : "/soul-writing/character",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Character"],
            notes: "Endpoint to post new Character",
            description: "Add Character",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: { 
                    projectId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}), 
                    age: Joi.string().example("age").required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}), 
                    lifeStatus: Joi.number().integer().example(19).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                    passAwayDate: Joi.string().optional().allow(null), 
                    title: Joi.string().example('pane name').required().error(errors=>{return Common.routeError(errors,'TITLE_IS_REQUIRED')}),                      
                    name: Joi.string().example('pane name').required().error(errors=>{return Common.routeError(errors,'NAME_IS_REQUIRED')}),                      
                    degreeOfKinship: Joi.string().example('pane name (optional)').optional().default(null),
                    Acronym: Joi.string().example('pane name (optional)').optional().default(null),
                    contact: Joi.string().example('pane name (optional)').optional(),
                    noOfMeetings: Joi.string().example('pane name (optional)').optional(),
                    sympethetic: Joi.string().example('pane name (optional)').optional(),
                    distance: Joi.string().example('pane name (optional)').optional(),
                    color: Joi.object().keys({
                        backgroundColor: Joi.string().example('pane name (optional)').optional().default(null),
                        fontColor: Joi.string().example('pane name (optional)').optional().default(null)
                    }).optional(),
                    protogonistObject: Joi.object().optional(),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/character",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Character"],
            notes: "Endpoint to get all Character",
            description: "Get all Character",
            auth: {strategy: 'jwt', mode: "optional"},
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                query: {   
                    projectId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}), 
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/character/kinship",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Character"],
            notes: "Endpoint to get all Character",
            description: "Get all Character",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                query: {
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "PATCH",
        path : "/soul-writing/character",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Character"],
            notes: "Endpoint to update existing Character",
            description: "Update Character",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {       
                    id: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),  
                    projectId: Joi.number().integer().example(1).optional(), 
                    age: Joi.string().example("age").optional(), 
                    lifeStatus: Joi.number().integer().example(19).optional(),
                    passAwayDate: Joi.string().optional().allow(null), 
                    title: Joi.string().example('pane name').optional(),                      
                    name: Joi.string().example('pane name').optional(),                      
                    degreeOfKinship: Joi.string().example('pane name (optional)').optional(),    
                    Acronym: Joi.string().example('pane name (optional)').optional() ,
                    contact: Joi.string().example('pane name (optional)').optional(),
                    noOfMeetings: Joi.string().example('pane name (optional)').optional(),
                    sympethetic: Joi.string().example('pane name (optional)').optional(),
                    distance: Joi.string().example('pane name (optional)').optional(),
                    color: Joi.object().keys({
                        backgroundColor: Joi.string().example('pane name (optional)').optional().default(null),
                        fontColor: Joi.string().example('pane name (optional)').optional().default(null)
                    }).optional(),
                    protogonistObject: Joi.object().optional(),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/character/{id}",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Character"],
            notes: "Endpoint to get Character by id",
            description: "Get Character By Id",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {                        
                    id : Joi.string().example(1).required().error(errors=>{return Common.routeError(errors,'Character_ID_IS_REQUIRED')})
                },
                query: {
                    email : Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/character/questions",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Character"],
            notes: "Endpoint to get all questions for Character",
            description: "Get all questions for Character",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "DELETE",
        path : "/soul-writing/character",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Character"],
            notes: "Endpoint to delete Character",
            description: "Delete Character",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                query: {                        
                    id : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'CHARACTER_ID_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },

    // ################################################################################################
    // #                                       Project Routes                                         #
    // ################################################################################################

    {
        method : "POST",
        path : "/soul-writing/project",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to post new projects",
            description: "Add Project",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    title: Joi.string().example('pane name').required().error(errors=>{return Common.routeError(errors,'NAME_IS_REQUIRED')}),                      
                    description: Joi.string().example('pane name').optional().default("Dummy Description"),                      
                    reason: Joi.string().example('pane name').required().error(errors=>{return Common.routeError(errors,'REASON_IS_REQUIRED')}),                     
                    companionId: Joi.number().integer().example('1 (optional)').optional(),  
                    // projectListId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PROJECT_LIST_ID_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/project",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to get all Project",
            description: "Get all Project",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                query: {   
                    companionStatus: Joi.number().integer().example(1).optional(),
                    limit: Joi.number().integer().optional().default(null),
					pageNumber : Joi.number().integer().min(1).optional().default(null),
                    historyDays : Joi.number().integer().min(1).optional(),
					orderByValue: Joi.string().allow('ASC', 'DESC').optional().default('DESC'),
					orderByParameter: Joi.string().allow('createdAt','id','updatedAt').optional().default('updatedAt'),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "PATCH",
        path : "/soul-writing/project",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to update existing project",
            description: "Update Project",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    title: Joi.string().example('pane name').optional(),                     
                    description: Joi.string().example('pane name').optional(),                     
                    reason: Joi.string().example('pane name').optional(),                     
                    id: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),  
                    companionId: Joi.number().integer().example('1 (optional)').optional(),  
                    projectMeta: Joi.object().keys().optional(),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/project/{id}",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to get project by id",
            description: "Get project By Id",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {                        
                    id : Joi.string().example(1).required().error(errors=>{return Common.routeError(errors,'project_ID_IS_REQUIRED')})
                },
                query: {                        
                    version : Joi.number().example(1).optional()
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/project-pdf/{id}",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to get project by id",
            description: "Get project By Id",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {                        
                    id : Joi.number().example(1).required().error(errors=>{return Common.routeError(errors,'project_ID_IS_REQUIRED')})
                },
                query: {                        
                    version : Joi.number().example(1).optional()
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "DELETE",
        path : "/soul-writing/project",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to delete project",
            description: "Delete project",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {                        
                    id : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'project_ID_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/content/{projectId}",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Content"],
            notes: "Endpoint to get all lines",
            description: "Get all lines",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {   
                    projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')})
                },
                query: {   
                    version: Joi.number().integer().example(10).optional()
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/content-pdf/{projectId}",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Content"],
            notes: "Endpoint to get all lines",
            description: "Get all lines",
            auth:false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {   
                    projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')})
                },
                query: {   
                    version: Joi.number().integer().example(10).optional()
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/content-word/{projectId}",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Content"],
            notes: "Endpoint to get all lines",
            description: "Get all lines",
            auth:false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                params: {   
                    projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')})
                },
                query: {   
                    version: Joi.number().integer().example(10).optional()
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/soul-writing/content",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Content"],
            notes: "Endpoint to create new lines",
            description: "Add line",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                    projectMeta: Joi.object().keys().optional().default({}),
                    isSaveAsDraft: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'SAVE_AS_DRAFT_IS_REQUIRED')}),
                    companionId:  Joi.number().integer().example('1 (optional)').optional(),
                    activeStage: Joi.number().integer().example("3").optional(),
                    contents: Joi.array().items({
                        categoryId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')}),
                        characterId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CHARACTER_ID_IS_REQUIRED')}),
                        lineNumber: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'LINE_NUMBER_IS_REQUIRED')}),
                        content: Joi.string().example("line content").required().error(errors=>{return Common.routeError(errors,'LINE_CONTENT_IS_REQUIRED')}),
                        painpictureCollapse: Joi.number().integer().valid(0,1).example(1).optional().default(0),
                        isRedLine: Joi.number().integer().valid(0, 1).optional().default(0),
                        highlightedText: Joi.array().items().allow(null).optional(),
                        uuid: Joi.string().uuid().optional().allow(null)
                    }).min(1).required().error(errors=>{return Common.routeError(errors,'CONTENTS_REQUIRED')}),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/soul-writing/word-estimation",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Content"],
            notes: "Endpoint to generete estimation for new content",
            description: "Add line",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                    projectMeta: Joi.object().keys().optional().default({}),
                    isSaveAsDraft: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'SAVE_AS_DRAFT_IS_REQUIRED')}),
                    companionId:  Joi.number().integer().example('1 (optional)').optional(),
                    activeStage: Joi.number().integer().example("3").optional(),
                    contents: Joi.array().items({
                        categoryId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')}),
                        characterId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CHARACTER_ID_IS_REQUIRED')}),
                        lineNumber: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'LINE_NUMBER_IS_REQUIRED')}),
                        content: Joi.string().example("line content").allow("").optional().default(""),
                        painpictureCollapse: Joi.number().integer().valid(0,1).example(1).optional().default(0),
                        isRedLine: Joi.number().integer().valid(0, 1).optional().default(0),
                        highlightedText: Joi.array().items().allow(null).optional(),
                        uuid: Joi.string().uuid().optional().allow(null)
                    }).optional().default([])
                    // .min(1).required().error(errors=>{return Common.routeError(errors,'CONTENTS_REQUIRED')}),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/soul-writing/word-estimation-test",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Content"],
            notes: "Endpoint to generete estimation for new content",
            description: "Add line",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                    projectMeta: Joi.object().keys().optional().default({}),
                    isSaveAsDraft: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'SAVE_AS_DRAFT_IS_REQUIRED')}),
                    companionId:  Joi.number().integer().example('1 (optional)').optional(),
                    activeStage: Joi.number().integer().example("3").optional(),
                    contents: Joi.array().items({
                        categoryId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')}),
                        characterId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CHARACTER_ID_IS_REQUIRED')}),
                        lineNumber: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'LINE_NUMBER_IS_REQUIRED')}),
                        content: Joi.string().example("line content").allow("").optional().default(""),
                        painpictureCollapse: Joi.number().integer().valid(0,1).example(1).optional().default(0),
                        isRedLine: Joi.number().integer().valid(0, 1).optional().default(0),
                        highlightedText: Joi.array().items().allow(null).optional(),
                        uuid: Joi.string().uuid().optional().allow(null)
                    }).optional().default([])
                    // .min(1).required().error(errors=>{return Common.routeError(errors,'CONTENTS_REQUIRED')}),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/soul-writing/companion-submit",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Content"],
            notes: "Endpoint to submit comments",
            description: "Add Comments",
            auth:{strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                    isSubmitToUser: Joi.number().integer().example(0).optional().default(0),
                    contents: Joi.array().items(Joi.object().keys({
                        id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'LINE_ID_IS_REQUIRED')}),
                        content: Joi.string().example("line content").allow("").optional().default(""),
                        // content:Joi.string().required().error(errors=>{return Common.routeError(errors,'CONTENT_IS_REQUIRED')})
                    })).min(1).required().error(errors=>{return Common.routeError(errors,'CONTENTS_ARE_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
        {
            method : "POST",
            path : "/soul-writing/companion-submit-content",
            handler : soulWritingGateway,
            options: {
                tags: ["api", "Soul Writing Content"],
                notes: "Endpoint to submit comments",
                description: "Add Comments",
                auth:{strategy: 'jwt'},
                validate: {
                    headers: Joi.object(Common.headers(true)).options({
                        allowUnknown: true
                    }),
                    options: {
                        abortEarly: false
                    },
                    payload: {   
                        projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                        isSubmitToUser: Joi.number().integer().example(0).optional().default(0),
                        contents: Joi.array().items({
                            categoryId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')}),
                            characterId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CHARACTER_ID_IS_REQUIRED')}),
                            lineNumber: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'LINE_NUMBER_IS_REQUIRED')}),
                            painpictureCollapse: Joi.number().integer().valid(0,1).example(1).optional().default(0),
                            content: Joi.string().example("line content").allow("").optional().default(""),
                            isRedLine: Joi.number().integer().valid(0, 1).optional().default(0),
                            highlightedText: Joi.array().items().allow(null).optional().default([]),
                            uuid: Joi.string().uuid().optional().allow(null).default(null),
                            id: Joi.number().optional().allow(null).default(null),
                            comment: Joi.string().optional().allow(null, "").default(null)
                        }).optional().default([])
                    },
                    failAction: async (req, h, err) => {
                        return Common.FailureError(err, req);
                    },
                    validator: Joi
                },
                pre : [{method: Common.prefunction}]
            }
        },
        {
            method : "POST",
            path : "/soul-writing/companion-submit-content-with-comments",
            handler : soulWritingGateway,
            options: {
                tags: ["api", "Soul Writing Content"],
                notes: "Endpoint to submit comments",
                description: "Add Comments",
                auth:{strategy: 'jwt'},
                validate: {
                    headers: Joi.object(Common.headers(true)).options({
                        allowUnknown: true
                    }),
                    options: {
                        abortEarly: false
                    },
                    payload: {   
                        projectId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                        isSubmitToUser: Joi.number().integer().example(0).optional().default(0),
                        contents: Joi.array().items({
                            categoryId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')}),
                            characterId: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'CHARACTER_ID_IS_REQUIRED')}),
                            lineNumber: Joi.number().integer().example(10).required().error(errors=>{return Common.routeError(errors,'LINE_NUMBER_IS_REQUIRED')}),
                            painpictureCollapse: Joi.number().integer().valid(0,1).example(1).optional().default(0),
                            content: Joi.string().example("line content").allow("").optional().default(""),
                            isRedLine: Joi.number().integer().valid(0, 1).optional().default(0),
                            highlightedText: Joi.array().items().allow(null).optional().default([]),
                            uuid: Joi.string().uuid().optional().allow(null).default(null),
                            id: Joi.number().optional().allow(null).default(null),
                            comment: Joi.string().optional().allow(null, "").default(null)
                        }).optional().default([]),
                        comments: Joi.object().optional().default(null)
                    },
                    failAction: async (req, h, err) => {
                        return Common.FailureError(err, req);
                    },
                    validator: Joi
                },
                pre : [{method: Common.prefunction}]
            }
        },
    {
		method : "POST",
		path : "/order-soul-writing",
		handler : soulWritingGateway,
		options : {
			tags: ["api", "Cart-Order"],
			notes: "Add Cart-Order",
			description: "Add Cart-Order",
			auth : false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options : {
					abortEarly: false
				},
				payload : {
                  data:Joi.string().required().error(errors=>{return Common.routeError(errors,'DATA_IS_REQUIRED')})
				},
				failAction: async(req,h, err) => {
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "GET",
		path : "/order-soul-writing",
		handler : soulWritingGateway,
		options : {
			tags: ["api", "Order List"],
			notes: "GET Cart-Order",
			description: "GET Cart-Order",
			auth : {strategy: "jwt", scope : ["admin","costumer",'companion','student','order_management']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly: false
				},
				query : {
                    limit: Joi.number().integer().optional(),
					userId:Joi.number().integer().optional(),
                    pageNumber: Joi.number().integer().min(1).optional(),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id'),
                    // categoryId: Joi.string().allow(null).optional().default(null),
					startDate:Joi.date().example('2022-07-05T04:00:00Z').optional(),
					endDate:Joi.date().example('2022-07-05T04:00:00Z').optional()
				},
				failAction: async(req,h, err) => {
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
        method : "GET",
        path : "/order-soul-writing-byid",
        handler : soulWritingGateway,
        options : {
          tags: ["api", "Order List"],
          notes: "GET Cart-Order",
          description: "GET Cart-Order",
          auth : {strategy: "jwt", scope : ["admin","costumer",'companion','student','order_management']},
          validate: {
            headers: Joi.object(Common.headers(true)).options({
              allowUnknown: true
            }),
            options : {
              abortEarly: false
            },
            query : {
              orderId:Joi.number().integer().required(),
            },
            failAction: async(req,h, err) => {
              return  Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      },
    {
        method : "POST",
        path : "/soul-writing/comment",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Comment"],
            notes: "Endpoint to post new Comment",
            description: "Add Comment",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {
                    lineUuid: Joi.string().uuid().example("8c314e52-e602-483d-9a8f-4d4b3c8d8422").required().error(errors=>{return Common.routeError(errors,'LINE_UUID_IS_REQUIRED')}), 
                    comment: Joi.string().example('comment text').required().error(errors=>{return Common.routeError(errors,'COMMENT_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/comment",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Comment"],
            notes: "Endpoint to get all Comment",
            description: "Get all Comment",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                query: {   
                    projectId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}), 
                    limit: Joi.number().integer().optional().default(null),
                    pageNumber: Joi.number().integer().min(1).optional().default(null),
                    orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt','id').optional().default('id')
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "PATCH",
        path : "/soul-writing/comment",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Comment"],
            notes: "Endpoint to update existing Comment",
            description: "Update Comment",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {       
                    id: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
                    comment: Joi.string().example('comment text').required().error(errors=>{return Common.routeError(errors,'COMMENT_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "DELETE",
        path : "/soul-writing/comment",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Comment"],
            notes: "Endpoint to delete Comment",
            description: "Delete Comment",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {                        
                    id : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'COMMENT_ID_IS_REQUIRED')})
                },
                query: {
                    email : Joi.string().required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },

    {
            method : "PATCH",
            path : "/soul-writing/meta-comments",
            handler : soulWritingGateway,
            options: {
                tags: ["api", "Soul Writing Comment"],
                notes: "Endpoint to delete Comment",
                description: "Delete Comment",
                auth: {strategy: 'jwt'},
                validate: {
                    headers: Joi.object(Common.headers(true)).options({
                        allowUnknown: true
                    }),
                    options: {
                        abortEarly: false
                    },
                    payload: {                        
                        projectId : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                        comments : Joi.any()
                    },
                    failAction: async (req, h, err) => {
                        return Common.FailureError(err, req);
                    },
                    validator: Joi
                },
                pre : [{method: Common.prefunction}]
            }
        },

    {
        method : "POST",
        path : "/soul-writing/project-list",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to post new project list",
            description: "Add Project List",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    projectList: Joi.array().items(Joi.string()).min(1).required().error(errors=>{return Common.routeError(errors,'PROJECT_LIST_ARRAY_REQUIRED')}), 
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/soul-writing/project-list",
        handler : soulWritingGateway,
        options: {
            tags: ["api", "Soul Writing Project"],
            notes: "Endpoint to post new project list",
            description: "Add Project List",
            auth: {strategy: 'jwt'},
            validate: {
                headers: Joi.object(Common.headers(true)).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                query: {
                  
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/project/offline-check",
        handler :soulWritingGateway,
        options: {
          tags: ["api", "Soul Writing Project"],
          notes: "To check for offline version",
          description: "Offline Check",
          auth: {strategy: 'jwt'},
          validate: {
            headers: Joi.object(Common.headers(true)).options({
              allowUnknown: true
            }),
            payload:{
                version: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'VERSION_IS_REQUIRED')}),
                projectId: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'PROJECT_ID_IS_REQUIRED')}),
                lastUpdatedAt: Joi.date().required().error(errors=>{return Common.routeError(errors,'DATE_IS_REQUIRED')})
            },
            options: {
              abortEarly: false
            },
            failAction: async (req, h, err) => {
              return Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      },
      {
        method : "GET",
        path : "/project/user-summary",
        handler : soulWritingGateway,
        options : {
          tags: ["api", "Summary"],
          notes: "GET Soulwriting summary",
          description: "GET Soulwriting summary",
          auth : {strategy: "jwt", scope : ["admin",'companion','student']},
          validate: {
            headers: Joi.object(Common.headers(true)).options({
              allowUnknown: true
            }),
            options : {
              abortEarly: false
            },
            query : {
              userId:Joi.number().integer().required(),
            },
            failAction: async(req,h, err) => {
              return  Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      },
      {
        method : "GET",
        path : "/project/legacy",
        handler : soulWritingGateway,
        options : {
          tags: ["api", "Soul Writing Project"],
          notes: "GET Event summary from legacy list",
          description: "GET Event summary from legacy list",
          auth : {strategy: "jwt"},
          validate: {
            headers: Joi.object(Common.headers(true)).options({
              allowUnknown: true
            }),
            options : {
              abortEarly: false
            },
            query : {
              limit: Joi.number().integer().optional().default(20),
              pageNumber: Joi.number().integer().min(1).optional().default(1),
              userId:Joi.number().integer().optional().default(null),
              id:Joi.number().integer().optional().default(null),
            },
            failAction: async(req,h, err) => {
              return  Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      },
      {
        method : "POST",
        path : "/soul-writing/test",
        handler :soulWritingGateway,
        options: {
          tags: ["api", "Event"],
          notes: "Completed Meetings",
          description: "Completed Meetings",
          auth: false,
          validate: {
            headers: Joi.object(Common.headers()).options({
              allowUnknown: true
            }),
            payload:{
              companions: Joi.array().required()     
            },
            options: {
              abortEarly: false
            },
            failAction: async (req, h, err) => {
              return Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      }
]
