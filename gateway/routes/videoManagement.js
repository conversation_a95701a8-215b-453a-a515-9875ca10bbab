const controller = require('../controllers/gatewayController');

const videoModel = Joi.object({
    id             : Joi.number().integer(),
	name           : Joi.string().trim(),
    topicId        : Joi.number().min(0).integer().example("5"),
	parentId       : Joi.number().min(0).integer().example("5").optional().default(null),
	audioLink      : Joi.string().trim().optional().allow("", null),
	videoLink      : Joi.string().trim().uri(),
	shortText      : Joi.string().trim().optional().allow(null).default(null),
	description    : Joi.string().trim().optional().allow(null, "").default(null),
	isFree         : Joi.number().integer().valid(0,1),
	accessibleDate : Joi.date().utc().optional().allow(null).default(null),
	noOfDays       : Joi.any().optional().allow(null, "").default(null),
	subTitle       : Joi.string().trim().example("Sub Title").optional(),
	topicType      : Joi.string().trim().valid("Seminars", "KUBYstudy").example("Seminars"),
	price          : Joi.number().when("isFree", {
		is        : 1,
		then      : Joi.number().precision(2).min(0).default(0).optional().allow(null),
		otherwise : Joi.number().precision(2).min(1).required()
	}),
	attachments    : Joi.array().items(Joi.object().keys({
		originalName : Joi.string().trim().required(),
		id           : Joi.number().integer().required(),
		path         : Joi.string().trim().required(),
		mimeType     : Joi.string().trim().required()
	})).optional().options({
		allowUnknown : true
	}),
	posterImage    : Joi.object().keys({
		name          : Joi.string().trim().required(),
		attachmentId  : Joi.number().integer().required(),
		attachmentUrl : Joi.string().trim().required(),
		mimeType      : Joi.string().trim().required()
	}),
	semester       : Joi.number().integer().when("topicType", {
		is        : "KUBYstudy",
		then      : Joi.number().integer().min(1).required(),
		otherwise : Joi.optional().valid("", null)
	}),
	digistoreId    : Joi.string().trim().optional().allow(null, ""),
	salesPageLink  : Joi.string().trim().uri().optional().default(null).allow("", null)
}).label("Course");

const commentModel = Joi.object({
	id             : Joi.number().integer().min(0).integer(),
	videoId        : Joi.number().integer().min(0).integer().required(),
	comment        : Joi.string().trim().required(),
	commentId      : Joi.number().integer().min(0).integer().default(null).optional().allow(null),
}).label("Comment");

const salesPageModel = Joi.object({
    id            : Joi.number().integer().required(),
    videoId       : Joi.number().min(0).integer().example("5").required(),
	videoLink     : Joi.string().trim().example("asdf.com").required(),
	title         : Joi.string().trim().example("Title").required(),
	subTitle      : Joi.string().trim().example("Sub Title").required(),
    description   : Joi.string().trim().example("Description").required(),
    staticContent : Joi.string().trim().example("<html>").required()
}).label("SalesPage");

module.exports = [
	{
		method  : "POST",
		path    : "/test/video-controller",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Make Payment Confirm",
			notes       : "Confirm the Payment from Digistore",
			tags        : ["api", "Course"],
			auth        : false,
			validate: {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en")
				}).options
				({
					"allowUnknown"  : true
				}),
				payload : {
					data:Joi.string().required().error(errors=>{return Common.routeError(errors,'DATA_IS_REQUIRED')})
				},
				failAction: async(req,h, err) => {
					return (err);
				},
				validator: Joi
			},
		}
	},
    {
		method  : "POST",
		path    : "/course/topic",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Add new Video",
			notes       : "Create Courses, Lessons and interview",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope : ["admin", "self_practice", "video-management"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				payload   : videoModel.keys({
					id          : Joi.number().integer().min(1).example(1).forbidden(),
					topicId     : Joi.number().integer().min(1).example(1).forbidden(),
					parentId    : Joi.number().integer().min(1).example(1).forbidden(),
					videoLink   : Joi.string().trim().uri().optional().default(null).allow(null),
					topicType   : Joi.string().trim().valid("Seminars", "KUBYstudy", "Trainings","ShopProduct").example("Seminars").required(),
					semester    : Joi.number().integer().when("topicType", {
									is        : "KUBYstudy",
									then      : Joi.number().integer().min(1).required(),
									otherwise : Joi.optional().valid("", null)
								}),
					posterImage : Joi.object().keys({
									name          : Joi.string().trim().required(),
									attachmentId  : Joi.number().integer().required(),
									attachmentUrl : Joi.string().trim().required(),
									mimeType      : Joi.string().trim().required()
								}).required(),
					videoLanguages: Joi.array().items(Joi.string()).optional(),
					isSubscriptionBased: Joi.number().optional().allow(null).default(0)
				}).options({ presence: 'required' }),
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
		{
			method  : "POST",
			path    : "/admin/course",
			options : {
				handler     : controller.videoManagementGateway,
				description : "Add new Video",
				notes       : "Create Courses, Lessons and interview",
				tags        : ["api", "Course"],
				auth        : {strategy:"jwt", scope : ["admin", "video-management", "self_practice"]},
				validate    : {
					headers : Joi.object
					({
						"language"      : Joi.string().default("en"),
						"Authorization" : Joi.string()
					}).options
					({
						"allowUnknown"  : true
					}),
					payload : {
						id: Joi.number().optional().allow(null).default(null),
						name: Joi.string().trim().required(), 
						audioLink: Joi.string().trim().optional().allow(null).default(null), 
						videoLink: Joi.string().trim().optional().allow(null).default(null), 
						shortText: Joi.string().trim().optional().allow(null).default(null), 
						description: Joi.string().trim().optional().allow(null).default(null), 
						accessibleDate: Joi.date().utc().optional().allow(null).default(null),
						topicId: Joi.number().integer().required().example("5"), 
						parentId: Joi.number().integer().optional().example("5"),
						subTitle: Joi.string().trim().optional().allow(null).default(null), 
						attachments: Joi.array().items(Joi.object().keys({
							originalName: Joi.string().trim().required(),
							id: Joi.number().integer().required(),
							path: Joi.string().trim().required(),
							mimeType: Joi.string().trim().required()
						})).optional().options({
							allowUnknown: true
						}),
						posterImage: Joi.object().keys({
							name: Joi.string().trim().required(),
							attachmentId: Joi.number().integer().required(),
							attachmentUrl: Joi.string().trim().required(),
							mimeType: Joi.string().trim().required()
						}),
						noOfDays: Joi.any().optional().allow(null).default(null)
					},
					failAction: async (req, h, err) => {
						return (err);
					},
					validator : Joi
				}
			}
		},
		{
			method  : "PATCH",
			path    : "/admin/course",
			options : {
				handler     : controller.videoManagementGateway,
				description : "Add new Video",
				notes       : "Create Courses, Lessons and interview",
				tags        : ["api", "Course"],
				auth        : {strategy:"jwt", scope : ["admin", "video-management", "self_practice"]},
				validate    : {
					headers : Joi.object
					({
						"language"      : Joi.string().default("en"),
						"Authorization" : Joi.string()
					}).options
					({
						"allowUnknown"  : true
					}),
					payload : {
						id: Joi.number().required(),
						name: Joi.string().trim().required(), 
						audioLink: Joi.string().trim().optional().allow(null).default(null), 
						videoLink: Joi.string().trim().optional().allow(null).default(null), 
						shortText: Joi.string().trim().optional().allow(null).default(null), 
						description: Joi.string().trim().optional().allow(null,"").default(null), 
						accessibleDate: Joi.date().utc().optional().allow(null).default(null),
						topicId: Joi.number().integer().required().example("5"), 
						parentId: Joi.number().integer().optional().example("5"),
						subTitle: Joi.string().trim().optional().allow(null).default(null), 
						attachments: Joi.array().items(Joi.object().keys({
							originalName: Joi.string().trim().required(),
							id: Joi.number().integer().required(),
							path: Joi.string().trim().required(),
							mimeType: Joi.string().trim().required()
						})).optional().options({
							allowUnknown: true
						}),
						posterImage: Joi.object().keys({
							name: Joi.string().trim().required(),
							attachmentId: Joi.number().integer().required(),
							attachmentUrl: Joi.string().trim().required(),
							mimeType: Joi.string().trim().required()
						}),
						noOfDays: Joi.any().optional().allow(null).default(null),
						sortOrder: Joi.number().optional().allow(null).default(null)
					},
					failAction: async (req, h, err) => {
						return (err);
					},
					validator : Joi
				}
			}
		},
			{
				method  : "DELETE",
				path    : "/admin/course",
				options : {
					handler     : controller.videoManagementGateway,
					description : "Add new Video",
					notes       : "Create Courses, Lessons and interview",
					tags        : ["api", "Course"],
					auth        : {strategy:"jwt", scope : ["admin", "video-management", "self_practice"]},
					validate    : {
						headers : Joi.object
						({
							"language"      : Joi.string().default("en"),
							"Authorization" : Joi.string()
						}).options
						({
							"allowUnknown"  : true
						}),
						query : {
							id: Joi.number().required()
						},
						failAction: async (req, h, err) => {
							return (err);
						},
						validator : Joi
					}
				}
			},
	{
		method  : "POST",
		path    : "/course",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Add new Video",
			notes       : "Create Courses, Lessons and interview",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope : ["admin", "self_practice", "video-management"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				payload : Joi.object().keys({
					lessons : Joi.array().items(
						videoModel.keys({
							id        : Joi.number().integer().min(1).example(1).forbidden(),
							topicType : Joi.string().trim().valid("Seminars", "KUBYstudy").example("Seminars").forbidden(),
							semester  : Joi.forbidden(),
							isFree    : Joi.forbidden(),
							price     : Joi.forbidden(),
							videoLink 	: Joi.string().when("parentId", {
								is        : null,
								then      : Joi.optional().allow(null)
							}),
							posterImage : Joi.object().keys({
								name          : Joi.string().trim().required(),
								attachmentId  : Joi.number().integer().required(),
								attachmentUrl : Joi.string().trim().required(),
								mimeType      : Joi.string().trim().required()
							}).optional(),
							subVideos : Joi.array().items(
								videoModel.keys({
									id        : Joi.number().integer().min(1).example(1).forbidden(),
									topicType : Joi.string().trim().valid("Seminars", "KUBYstudy").example("Seminars").forbidden(),
									parentId  : Joi.number().integer().min(1).example(1).forbidden(),
									topicId   : Joi.number().integer().min(1).example(1).forbidden(),
									semester  : Joi.forbidden(),
									isFree    : Joi.forbidden(),
									price     : Joi.forbidden(),
									posterImage : Joi.object().keys({
										name          : Joi.string().trim().required(),
										attachmentId  : Joi.number().integer().required(),
										attachmentUrl : Joi.string().trim().required(),
										mimeType      : Joi.string().trim().required()
									}).optional()
								}).options({ presence: 'required' })
							).optional()
						}).options({ presence: 'required' }),
					)
				}),
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "PATCH",
		path    : "/course",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Add new Video",
			notes       : "Create Courses, Lessons and interview",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope : ["admin", "self_practice", "video-management"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				payload : Joi.object().keys({
					lessons : Joi.array().items(
						videoModel.keys({
							id        : Joi.number().integer().min(1).example(1).optional(),
							topicType : Joi.string().trim().valid("Seminars", "KUBYstudy").example("Seminars").forbidden(),
							semester  : Joi.forbidden(),
							isFree    : Joi.forbidden(),
							price     : Joi.forbidden(),
							videoLink 	: Joi.string().when("parentId", {
								is        : null,
								then      : Joi.optional().allow(null)
							}),
							posterImage : Joi.object().keys({
								name          : Joi.string().trim().required(),
								attachmentId  : Joi.number().integer().required(),
								attachmentUrl : Joi.string().trim().required(),
								mimeType      : Joi.string().trim().required()
							}).optional(),
							subVideos : Joi.array().items(
								videoModel.keys({
									id        : Joi.number().integer().min(1).example(1).optional(),
									topicType : Joi.string().trim().valid("Seminars", "KUBYstudy").example("Seminars").forbidden(),
									parentId  : Joi.number().integer().min(1).example(1).forbidden(),
									topicId   : Joi.number().integer().min(1).example(1).forbidden(),
									semester  : Joi.forbidden(),
									isFree    : Joi.forbidden(),
									price     : Joi.forbidden(),
									posterImage : Joi.object().keys({
										name          : Joi.string().trim().required(),
										attachmentId  : Joi.number().integer().required(),
										attachmentUrl : Joi.string().trim().required(),
										mimeType      : Joi.string().trim().required()
									}).optional()
								}).options({ presence: 'required' })
							).optional()
						}).options({ presence: 'required' }),
					),
					deleteVideos : Joi.array().items(Joi.number().integer()).min(0).optional()
				}),
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "GET",
		path    : "/course/getLessons",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Get Video",
			notes       : "Get Courses, Lessons and Interviews",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope: ["admin", "self_practice", "video-management", "costumer", "student", "companion"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query :
				{
					topicId          : Joi.number().integer().example(1).required().default(1),
					parentId         : Joi.number().integer().example(1).required().default(1),
					pageNumber       : Joi.number().integer().example(1).optional().default(1),
					limit            : Joi.number().integer().example("13 (optional)").default(10).optional(),
					orderByValue     : Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter : Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "GET",
		path    : "/course/getChapters",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Get Video",
			notes       : "Get Courses, Lessons and Interviews",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope: ["admin", "self_practice", "video-management", "costumer", "student", "companion"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query :
				{
					topicId          : Joi.number().integer().example(1).required().default(1),
					pageNumber       : Joi.number().integer().example(1).optional().default(1),
					limit            : Joi.number().integer().example("13 (optional)").default(999).optional(),
					orderByValue     : Joi.string().valid('ASC', 'DESC').optional().default('ASC'),
                    orderByParameter : Joi.string().valid('createdAt', 'id', 'sortOrder').optional().default('sortOrder')
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
		{
			method  : "GET",
			path    : "/admin/course/getChapters",
			options : {
				handler     : controller.videoManagementGateway,
				description : "Get Video",
				notes       : "Get Courses, Lessons and Interviews",
				tags        : ["api", "Course"],
				auth        : {strategy:"jwt", scope: ["admin", "video-management", "costumer", "student", "companion", "self_practice"]},
				validate    : {
					headers : Joi.object
					({
						"language"      : Joi.string().default("en"),
						"Authorization" : Joi.string()
					}).options
					({
						"allowUnknown"  : true
					}),
					query :
					{
						topicId          : Joi.number().integer().example(1).required().default(1),
						parentId          : Joi.number().integer().example(1).optional().allow(null).default(null),
						pageNumber       : Joi.number().integer().example(1).optional().default(1),
					limit            : Joi.number().integer().example("13 (optional)").default(999).optional(),
						orderByValue     : Joi.string().valid('ASC', 'DESC').optional().default('ASC'),
						orderByParameter : Joi.string().valid('createdAt', 'id', 'sortOrder').optional().default('sortOrder')
					},
					failAction: async (req, h, err) => {
						return (err);
					},
					validator : Joi
				}
			}
		},
			{
				method  : "GET",
				path    : "/course/getChapterById",
				options : {
					handler     : controller.videoManagementGateway,
					description : "Get Video",
					notes       : "Get Courses, Lessons and Interviews",
					tags        : ["api", "Course"],
					auth        : {strategy:"jwt", scope: ["admin", "video-management", "costumer", "student", "companion", "self_practice"]},
					validate    : {
						headers : Joi.object
						({
							"language"      : Joi.string().default("en"),
							"Authorization" : Joi.string()
						}).options
						({
							"allowUnknown"  : true
						}),
						query :
						{
							id: Joi.number().integer().example(1).required().default(1)
						},
						failAction: async (req, h, err) => {
							return (err);
						},
						validator : Joi
					}
				}
			},
	{
		method  : "GET",
		path    : "/course/user",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Get Video",
			notes       : "Get Courses, Lessons and Interviews",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope: ["admin"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query :
				{
					userId: Joi.number().integer().example(1).required(),
					pageNumber       : Joi.number().integer().example(1).optional().default(1),
					limit            : Joi.number().integer().example("13 (optional)").default(9999999).optional(),
					orderByValue     : Joi.string().valid('ASC', 'DESC').optional().default('ASC'),
                    orderByParameter : Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "DELETE",
		path    : "/course/revoke",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Get Video",
			notes       : "Get Courses, Lessons and Interviews",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope: ["admin"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query :
				{
					userId: Joi.number().integer().example(1).required(),
					topicId: Joi.number().integer().example(1).required(),
					// pageNumber       : Joi.number().integer().example(1).optional().default(1),
					// limit            : Joi.number().integer().example("13 (optional)").default(9999999).optional(),
					// orderByValue     : Joi.string().valid('ASC', 'DESC').optional().default('ASC'),
                    // orderByParameter : Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "GET",
		path    : "/course/getTopic",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Get Video",
			notes       : "Get Courses, Lessons and Interviews",
			tags        : ["api", "Course"],
			auth        : { strategy: "jwt", mode: "optional" },
			response: {
				status: {
					200: Joi.any(),
					400: Joi.any(),
					500: Joi.any()
				},
			},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query :
				{
					pageNumber       : Joi.number().integer().example(1).optional().default(1),
					limit            : Joi.number().integer().example("13 (optional)").default(9999999).optional(),
					orderByValue     : Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter : Joi.string().valid('createdAt', 'id').optional().default('id'),
					type             : Joi.string().trim().valid("Seminars", "KUBYstudy").optional().allow(null)
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "GET",
		path    : "/course/getPurchasedTopic",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Get Video",
			notes       : "Get Courses, Lessons and Interviews",
			tags        : ["api", "Course"],
			auth        : { strategy: "jwt" },
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query :
				{
					pageNumber       : Joi.number().integer().example(1).optional().default(1),
					limit            : Joi.number().integer().example("13 (optional)").default(999999).optional(),
					orderByValue     : Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter : Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "POST",
		path    : "/course/comment",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Comment on Video",
			notes       : "Comment on Courses, Lessons and Interviews",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope: ["admin", "self_practice", "video-management", "costumer", "student", "companion"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				payload : commentModel.keys({
					id  : Joi.number().integer().min(1).example(1).forbidden(),
				}),
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "GET",
		path    : "/course/admin/getTopic",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Get Video",
			notes       : "Get Courses, Lessons and Interviews",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope: ["admin", "self_practice", "video-management"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query :
				{
					pageNumber       : Joi.number().integer().example(1).optional().default(1),
					limit            : Joi.number().integer().example("13 (optional)").default(10).optional(),
					orderByValue     : Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter : Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "DELETE",
		path    : "/course",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Delete Video",
			notes       : "Delete Courses, Lessons and Interviews",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope: ["admin", "self_practice", "video-management"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query :
				{
                    id : Joi.number().integer().required()
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "GET",
		path    : "/course",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Get Video",
			notes       : "Get Courses, Lessons and Interviews",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope: ["admin", "self_practice", "video-management"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query :
				{
					id : Joi.number().integer().example(1).min(1).required(),
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "PATCH",
		path    : "/course/topic",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Edit the topic",
			notes       : "Edit Chapter, Lessons and Topic",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope : ["admin", "self_practice", "video-management"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				payload   : videoModel.keys({
					id        : Joi.number().integer().min(1).example(1).required(),
					topicId   : Joi.number().integer().min(1).example(1).forbidden(),
					parentId  : Joi.number().integer().min(1).example(1).forbidden(),
					videoLink   : Joi.string().trim().uri().optional().default(null).allow(null),
					topicType : Joi.string().trim().valid("Seminars", "KUBYstudy","Trainings","ShopProduct").example("Seminars").required(),
					semester  : Joi.number().integer().when("topicType", {
									is        : "KUBYstudy",
									then      : Joi.number().integer().min(1).required(),
									otherwise : Joi.optional().valid("", null)
								}),
					videoLanguages: Joi.array().items(Joi.string()).optional(),
					isSubscriptionBased: Joi.number().optional().allow(null).default(0)
				}).options({ presence: 'required' }),
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "DELETE",
		path    : "/course/deleteAllVideos",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Delete Video",
			notes       : "Delete Courses, Lessons and Interviews",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope: ["admin", "self_practice", "video-management"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query :
				{
                    id : Joi.number().integer().required()
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "PATCH",
		path    : "/course/timestamp",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Update timestamp of video",
			notes       : "Update timestamp",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope: ["admin", "self_practice", "video-management", "costumer", "student", "companion"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				payload : {
					videoId       : Joi.number().min(1).integer().required(),
					amountWatched : Joi.number().min(1).required(),
					totalLength   : Joi.number().min(1).required()
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "POST",
		path    : "/course/giveReview",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Give Video Review",
			notes       : "Give Course's, Lesson's and Interview's Review",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope: ["admin", "self_practice", "video-management", "costumer", "student", "companion"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				payload :
				{
					videoId : Joi.number().integer().example(1).required(),
					rating  : Joi.number().integer().valid(1, 2, 3, 4, 5).required(),
					comment : Joi.string().trim().required(),
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "GET",
		path    : "/course/getReviews",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Get Video Reviews",
			notes       : "Get Courses, Lessons and Interviews Reviews",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope: ["admin", "self_practice", "video-management", "costumer", "student", "companion"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query :
				{
					videoId          : Joi.number().integer().min(1).example(1).required().default(1),
					pageNumber       : Joi.number().integer().example(1).optional().default(1),
					limit            : Joi.number().integer().example("13 (optional)").default(10).optional(),
					orderByValue     : Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter : Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
		method  : "GET",
		path    : "/course/getReviewForUser",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Get Video Reviews",
			notes       : "Get Courses, Lessons and Interviews Reviews",
			tags        : ["api", "Course"],
			auth        : {strategy:"jwt", scope: ["admin", "self_practice", "video-management", "costumer", "student", "companion"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query :
				{
					videoId : Joi.number().integer().min(1).example(1).required().default(1)
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
	},
	{
        method  : "POST",
		path    : "/salesPage",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Add new Sales Page",
			notes       : "Create sales page for particular topic",
			tags        : ["api", "Sales"],
			auth        : {strategy:"jwt", scope : ["admin"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				payload : salesPageModel.keys({
					id  : Joi.number().integer().min(1).example(1).forbidden()
				}).options({ presence: 'required' }),
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
    },
    {
        method  : "GET",
		path    : "/salesPage",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Gets the Sales Page Listing",
			notes       : "Get sales page listing",
			tags        : ["api", "Sales"],
			auth        : false,
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query : {
					pageNumber       : Joi.number().integer().example(1).optional().default(1),
					limit            : Joi.number().integer().example("13 (optional)").default(10).optional(),
					orderByValue     : Joi.string().valid('ASC', 'DESC').optional().default('ASC'),
                    orderByParameter : Joi.string().valid('createdAt', 'videoId').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
    },
	{
        method  : "GET",
		path    : "/salesPage/getById",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Gets the Sales Page Details",
			notes       : "Get sales page Details",
			tags        : ["api", "Sales"],
			auth        : false,
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query : {
					videoId : Joi.number().min(0).integer().example("5").required(),
				},
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
    },
	{
        method  : "DELETE",
		path    : "/salesPage",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Delete Sales Page",
			notes       : "Delete sales page for particular topic",
			tags        : ["api", "Sales"],
			auth        : {strategy:"jwt", scope : ["admin", "self_practice", "self_practice"]},
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				query : Joi.object().keys({
					id      : Joi.number().min(1).integer(),
					videoId : Joi.number().min(1).integer()
				}).xor("id", "videoId"),
				failAction: async (req, h, err) => {
					return (err);
				},
				validator : Joi
			}
		}
    },
	{
		method  : "POST",
		path    : "/confirmPayment",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Make Payment Confirm",
			notes       : "Confirm the Payment from Digistore",
			tags        : ["api", "Course"],
			auth        : { strategy: "jwt", mode: "optional" },
			validate: {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options({
					allowUnknown: true
				}),
				options : {
					abortEarly: false
				},
				payload : {
					data:Joi.string().required().error(errors=>{return Common.routeError(errors,'DATA_IS_REQUIRED')})
				},
				failAction: async(req,h, err) => {
					return (err);
				},
				validator: Joi
			},
		}
	},
	// {
	// 	method  : "POST",
	// 	path    : "/confirmPayment/test",
	// 	options : {
	// 		handler     : controller.videoManagementGateway,
	// 		description : "Make Payment Confirm",
	// 		notes       : "Confirm the Payment from Digistore",
	// 		tags        : ["api", "Course"],
	// 		auth        : { strategy: "jwt", mode: "optional" },
	// 		validate: {
	// 			headers : Joi.object
	// 			({
	// 				"language"      : Joi.string().default("en"),
	// 				"Authorization" : Joi.string()
	// 			}).options({
	// 				allowUnknown: true
	// 			}),
	// 			options : {
	// 				abortEarly: false
	// 			},
	// 			payload : {
	// 				data:Joi.string().required().error(errors=>{return Common.routeError(errors,'DATA_IS_REQUIRED')})
	// 			},
	// 			failAction: async(req,h, err) => {
	// 				return (err);
	// 			},
	// 			validator: Joi
	// 		},
	// 	}
	// },
	{
		method  : "POST",
		path    : "/webhook/digistore",
		options : {
			handler     : controller.videoManagementGateway,
			description : "Add and update User",
			notes       : "Add and update User",
			tags        : ["api", "Webhook"],
			auth        : false,
			validate    : {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options
				({
					"allowUnknown"  : true
				}),
				//payload   : webhookUserModel.keys().options({ presence: 'required' }),
				validator : Joi
			}
		}
	},
]