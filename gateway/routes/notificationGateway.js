const {notificationManagementGateway} = require('../controllers/gatewayController');

module.exports = [
	// ***********************************  NOTIFICATIONS ENDPOINTS  ***********************************
    {
		method  : "GET",
		path    : "/notification",
		handler : notificationManagementGateway,
		options : {
			tags        : ["api", "Notification"],
			notes       : "Endpoint to get Notifications List",
			description : "Get Notification",
            auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
			validate    : {
				headers: Joi.object(Common.headers(true)).options({allowUnknown: true}),
				  options: {
					abortEarly: false
				  },
				query : {
					limit: Joi.number().integer().optional(),
                    status: Joi.number().valid(0,1).optional(),
                    searchText: Joi.string().max(250).optional(),
                    pageNumber: Joi.number().integer().min(1).optional(),
                    orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
				},
				validator: Joi
			}
		}
	},
    {
		method  : "PATCH",
		path    : "/status",
		handler : notificationManagementGateway,
		options : {
			tags        : ["api", "Notification"],
			notes       : "Endpoint to update status Notifications",
			description : "update status Notification",
			auth        : {strategy: 'jwt',scope:["owner"]},
			validate    : {
				headers: Joi.object(Common.headers(true)).options({allowUnknown: true}),
				  options: {
					abortEarly: false
				  },
				payload : {
					status   : Joi.number().integer().valid(0,1).example(1).required().error(errors=>{return Common.routeError(errors,'STATUS_IS_REQUIRED')}),
					id		 : Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'NOTIFICATION_ID_IS_REQUIRED')}),
				},
				validator: Joi
			}
		}
	},
]