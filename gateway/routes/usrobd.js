const {useronboardingGateway} = require('../controllers/gatewayController');
// useronboardingGateway

module.exports = [
    {
		method : "GET",
		path : "/language",
		handler :useronboardingGateway,
		options: {
			tags: ["api", "Language"],
			notes: "Endpoint to get Languages",
			description: "Get Languages",
			auth:false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
					limit: Joi.number().integer().optional(),
                    status: Joi.number().valid(0,1).optional(),
                    searchText: Joi.string().max(250).optional(),
                    pageNumber: Joi.number().integer().min(1).optional(),
                    orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt','id','name').optional().default('name')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/user/resendCode",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to resend verification code",
			description: "Resend Verification Code",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					email: Joi.string().email().required().error(errors=>{return Common.routeError(errors,'TOKEN_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/user/verify-token",
		handler :useronboardingGateway,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to Verify Token ",
			description: "User can verify token with this url ",
            auth :false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
                query: {
                    token:Joi.string().required().example("token {required}").error(errors=>{return Common.routeError(errors,'TOKEN_IS_REQUIRED')})
                    },
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	
    {
		method : "POST",
		path : "/user/login",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to allow user to login to portal with Singpass and Phone",
			description: "User login",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					role			:	Joi.number().integer().example(1).description('1 for admin 2 for member 4 for companion').required().error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
					email			: 	Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
					password		: 	Joi.string().example('abcd12345').required().error(errors=>{return Common.routeError(errors,'PASSWORD_IS_REQUIRED')}),
					deviceType		: 	Joi.number().integer().example(1).allow(1,2,3).description("1-android,2-ios,3-web").optional(),
					deviceToken 	:	Joi.string().example('device-token').optional()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "POST",
		path : "/user/login-companion",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to allow user to login to portal with Singpass and Phone",
			description: "User login",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					role			:	Joi.number().integer().example(1).description('1 for admin 2 for member 4 for companion').required().error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
					email			: 	Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
					password		: 	Joi.string().example('abcd12345').required().error(errors=>{return Common.routeError(errors,'PASSWORD_IS_REQUIRED')}),
					deviceType		: 	Joi.number().integer().example(1).allow(1,2,3).description("1-android,2-ios,3-web").optional(),
					deviceToken 	:	Joi.string().example('device-token').optional()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
		{
			method : "POST",
			path : "/user/resend-verify-email",
			handler : useronboardingGateway,
			options: {
				tags: ["api", "User"],
				notes: "Endpoint to allow user to login to portal with Singpass and Phone",
				description: "User login",
				auth: false,
				validate: {
					headers: Joi.object(Common.headers()).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload: {
						email			: 	Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
    {
		method : "POST",
		path : "/user/login-customer",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to allow user to login to portal with Singpass and Phone",
			description: "User login",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					role			:	Joi.number().integer().example(1).description('1 for admin 2 for member 4 for companion').required().error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
					email			: 	Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
					password		: 	Joi.string().example('abcd12345').required().error(errors=>{return Common.routeError(errors,'PASSWORD_IS_REQUIRED')}),
					deviceType		: 	Joi.number().integer().example(1).allow(1,2,3).description("1-android,2-ios,3-web").optional(),
					deviceToken 	:	Joi.string().example('device-token').optional()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/user/forget-password",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to allow user to login to portal with Singpass and Phone",
			description: "User Forget Password",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					email		: Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/user/reset-password",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to allow user to login to portal with Singpass and Phone",
			description: "User login",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					token		: Joi.string().example('eredds').required().error(errors=>{return Common.routeError(errors,'TOKEN_IS_REQUIRED')}),
					password	: Joi.string().example('abcd12345').required().error(errors=>{return Common.routeError(errors,'PASSWORD_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/user/signup",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to allow user to signup",
			description: "User Signup",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					title			: Joi.string().example('Mr').required().error(errors=>{return Common.routeError(errors,'TITLE_IS_REQUIRED')}),
					firstName		: Joi.string().example('User').required().error(errors=>{return Common.routeError(errors,'FIRST_NAME_IS_REQUIRED')}),
					lastName		: Joi.string().example('Singh').required().error(errors=>{return Common.routeError(errors,'LAST_NAME_IS_REQUIRED')}),
					email			: Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
					password		: Joi.string().example('abcd12345').required().error(errors=>{return Common.routeError(errors,'PASSWORD_IS_REQUIRED')}),
					deviceType		: Joi.number().integer().example(1).allow(1,2,3).description("1-android,2-ios,3-web").optional(),
					deviceToken 	: Joi.string().example('device-token').optional(),
					optionalData	: Joi.object().example({}).optional(),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},


	{
			method : "PATCH",
			path : "/user/profile",
			handler : useronboardingGateway,
			options: {
				tags: ["api", "User"],
				notes: "Endpoint to allow Alter Profile use gender constatnts 0 for mail 1 for female 2 for other",
				description: "Alter Profile",
				auth: {strategy: 'jwt'},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload: {
						email		: Joi.string().example("<EMAIL>").trim().optional(),
						firstName	: Joi.string().example("John {optional}").max(250).optional(),
						lastName	: Joi.string().example("John {optional}").max(250).optional(),
						title		: Joi.string().example("John {optional}").max(250).optional(),
						gender		: Joi.number().integer().example(1).allow(0,1,2).description('use 0 for mail 1 for female 2 for other ').optional(),
						reason		: Joi.string().example("reason {optional}").optional(),
						about		: Joi.string().example("about {optional}").optional(),
						vita		: Joi.string().example("vita {optional}").optional(),
						video		: Joi.string().example("video url {optional}").optional(),
						meetingPrice: Joi.string().example("14.4 {optional}").optional(),
						attachment	: Joi.object().optional(),
						languages	:Joi.array().example([1,2,3]).items(
							Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'LANGUAGE_ID_IS_REQUIRED')})
						).min(1).optional(),
						scheduleTime	:Joi.number().integer().example(24).optional(),
						reScheduleTime	:Joi.number().integer().example(24).optional(),
						cancelTime		:Joi.number().integer().example(24).optional(),
						countryCode: Joi.string().example("+91").optional(),
						phoneNumber: Joi.string().example("1234567890").optional()
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "POST",
			path : "/user/change-password",
			handler : useronboardingGateway,
			options: {
				tags: ["api", "User"],
				notes: "Endpoint to allow user to change Password",
				description: "Change Password",
				auth: {strategy: 'jwt'},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload: {
						oldPassword: Joi.string().example("password").trim().max(250).required().error(errors=>{return Common.routeError(errors,'OLD_PASSWORD_IS_REQUIRED')}),
						newPassword: Joi.string().example("newPassword").trim().max(250).required().error(errors=>{return Common.routeError(errors,'NEW_PASSWORD_IS_REQUIRED')}),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/users",
			handler : useronboardingGateway,
			options: {
				tags: ["api", "Admin"],
				notes: "Endpoint to Get All Users",
				description: "Get Users By Admin ",
				auth: {strategy: 'jwt',scope:["admin","user_listing","companion_management"]},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query: Joi.object({
						limit: Joi.number().integer().optional(),
						status: Joi.number().valid(0,1).optional(),
						searchText: Joi.string().max(250).optional(),
						name: Joi.string().max(250).optional(),
						email: Joi.string().max(250).optional(),
						phone: Joi.string().max(250).optional(),
						pageNumber: Joi.number().integer().min(1).optional(),
						orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
						orderByParameter: Joi.string().optional().default('id'),
						// orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
						// role:Joi.number().integer().optional()
						role: Joi.array().single().items(Joi.number().integer().optional()).optional().allow(''),
					}).rename('role[]', 'role', { ignoreUndefined: true }),
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "GET",
			path : "/user-profile",
			handler : useronboardingGateway,
			options: {
				tags: ["api", "User"],
				notes: "Endpoint to Get User Profile",
				description: "Get User Profile",
				auth: {strategy: 'jwt'},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query: {
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
		{
			method : "PATCH",
			path : "/user/status",
			handler : useronboardingGateway,
			options: {
				tags: ["api", "Admin"],
				notes: "Endpoint to allow update user profile status",
				description: "Update Profile",
				auth: {strategy: 'jwt',scope:['admin']},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload: {
						userId		: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'USER_ID_IS_REQUIRED')}),
						status		: Joi.number().integer().required().example(1).valid(1,0).error(errors=>{return Common.routeError(errors,'STATUS_IS_REQUIRED')})
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
// ******************************************** ACL ROUTES *************************************************

		{
			method : "Get",
			path : "/role-permissions",
			handler : useronboardingGateway,
			options: {
				tags: ["api", "ACL"],
				notes: "Endpoint to get Role with permissions",
				description: "Get Role",
				auth: {strategy: 'jwt', scope:['admin','manage_roles','admin_user_management']},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query:{
						roleId:Joi.number().required().error(errors=>{return Common.routeError(errors,'ROLE_ID_IS_REQUIRED')}),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
	
	
	// Get Roles
	  {
			method : "Get",
			path : "/role",
			handler : useronboardingGateway,
			options: {
				tags: ["api", "ACL"],
				notes: "Endpoint to get roles",
				description: "Get Roles",
				auth: {strategy: 'jwt', scope:['admin','manage-roles','admin_user_management']},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query:{
						limit: Joi.number().integer().optional(),
						allRole:Joi.number().integer().allow(1).optional(),
						name:Joi.string().max(250).optional(),
						pageNumber: Joi.number().integer().min(1).optional(),
						orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
						orderByParameter: Joi.string().valid('createdAt','id').optional().default('id')
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
	// Delete a Role	
	  {
			method : "DELETE",
			path : "/role",
			handler : useronboardingGateway,
			options: {
				tags: ["api", "ACL"],
				notes: "Endpoint to delete Roles",
				description: "Delete Role",
				auth: {strategy: 'jwt', scope:['admin','manage-roles','admin_user_management']},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query:{
						roleId:Joi.number().required().error(errors=>{return Common.routeError(errors,'ROLE_ID_IS_REQUIRED')}),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
	// Update Roles	
	  {
			method : "PATCH",
			path : "/role",
			handler : useronboardingGateway,
			options: {
				tags: ["api", "ACL"],
				notes: "Endpoint to update Role with Permissions",
				description: "Update Role",
				auth: {strategy: 'jwt', scope:['admin','manage-roles','admin_user_management']},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload:{
						roleId:Joi.number().required().error(errors=>{return Common.routeError(errors,'ROLE_ID_IS_REQUIRED')}),
						name:Joi.string().required().error(errors=>{return Common.routeError(errors,'ROLE_NAME_IS_REQUIRED')}),
						permissions:Joi.array().items(Joi.number()).required().min(1).error(errors=>{return Common.routeError(errors,'PERMISSIONS_IS_REQUIRED')}),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
	// Create Role-----
	  {
			method : "POST",
			path : "/role",
			handler : useronboardingGateway,
			options: {
				tags: ["api", "ACL"],
				notes: "Endpoint to create a new Role with Permissions",
				description: "Create Roles",
				auth: {strategy: 'jwt', scope:['admin','manage-roles','admin_user_management']},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload:{
						name:Joi.string().required().error(errors=>{return Common.routeError(errors,'ROLE_NAME_IS_REQUIRED')}),
						permissions:Joi.array().items(Joi.number()).required().min(1).error(errors=>{return Common.routeError(errors,'PERMISSIONS_IS_REQUIRED')}),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
	// Get Permissions ------
	  {
			method : "GET",
			path : "/permissions",
			handler : useronboardingGateway,
			options: {
				tags: ["api", "ACL"],
				notes: "Endpoint to get all permissions",
				description: "Get permissions",
				auth: {strategy: 'jwt', scope:['admin','manage-roles','manage-permissions']},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					query: {
						limit: Joi.number().integer().optional(),
						searchText:Joi.string().max(250).optional(),
						pageNumber: Joi.number().integer().min(1).optional(),
						orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
						orderByParameter: Joi.string().valid('createdAt','id').optional().default('id')
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
		},
	// Users Crud For ACL
	{
		method : "POST",
		path : "/acl-users",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "ACL"],
			notes: "Endpoint to create a new User",
			description: "Create Users",
			auth: {strategy: 'jwt', scope:['admin','admin_user_management']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					name:Joi.string().required().example('name').error(errors=>{return Common.routeError(errors,'NAME_IS_REQUIRED')}),
					role:Joi.array().items(Joi.number()).example([1,2,3,4]).required().min(1).error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
					email:Joi.string().email().required().example('<EMAIL>').error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		
			method : "PATCH",
			path : "/acl-users",
			handler : useronboardingGateway,
			options: {
				tags: ["api", "ACL"],
				notes: "Endpoint to update a new User",
				description: "Update Users",
				auth: {strategy: 'jwt', scope:['admin','admin_user_management']},
				validate: {
					headers: Joi.object(Common.headers(true)).options({
						allowUnknown: true
					}),
					options: {
						abortEarly: false
					},
					payload:{
						id:Joi.number().integer().required().example(1).error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
						name:Joi.string().required().example('name').error(errors=>{return Common.routeError(errors,'NAME_IS_REQUIRED')}),
						role:Joi.array().items(Joi.number()).example([1,2,3,4]).required().min(1).error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
						email:Joi.string().email().required().example('<EMAIL>').error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
					},
					failAction: async (req, h, err) => {
						return Common.FailureError(err, req);
					},
					validator: Joi
				},
				pre : [{method: Common.prefunction}]
			}
	},
	{
		method : "GET",
		path : "/acl-users",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "ACL"],
			notes: "Endpoint to get ACL users",
			description: "Get ACL users by admin",
			auth: {strategy: 'jwt',scope:["admin","admin_user_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					limit: Joi.number().integer().optional(),
					status: Joi.number().valid(0,1).optional(),
					searchText: Joi.string().max(250).optional(),
					name: Joi.string().max(250).optional(),
					email: Joi.string().max(250).optional(),
					pageNumber: Joi.number().integer().min(1).optional(),
					orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
					orderByParameter: Joi.string().optional().default('id')
					// orderByParameter: Joi.string().valid('createdAt','id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "DELETE",
		path : "/acl-users",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "ACL"],
			notes: "Endpoint to delete Roles",
			description: "Delete Role",
			auth: {strategy: 'jwt', scope:['admin','manage-roles',"admin_user_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
					id:Joi.number().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/acl-users-id",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "ACL"],
			notes: "Endpoint to get ACL users",
			description: "Get ACL users by admin",
			auth: {strategy: 'jwt',scope:["admin","admin_user_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/users",
		handler :useronboardingGateway,
		options: {
			tags: ["api", "Admin"],
			notes: "Endpoint to add User By Admin",
			description: "Add User By Admin",
			auth:{strategy:'jwt',scope:["admin","manage-users","user_listing","companion_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					    email		: Joi.string().example("<EMAIL>").trim().required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
						firstName	: Joi.string().example("John {optional}").max(250).optional(),
                        role        : Joi.array().items(Joi.number().integer()).min(1).example([4]).description('2 for member 1 for companion').required().error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
						lastName	: Joi.string().example("John {optional}").max(250).optional(),
						title		: Joi.string().example("John {optional}").max(250).optional(),
						gender		: Joi.number().integer().example(1).allow(0,1,2).description('use 0 for mail 1 for female 2 for other ').optional(),
						reason		: Joi.string().example("reason {optional}").optional(),
						about		: Joi.string().example("about {optional}").optional(),
						vita		: Joi.string().example("vita {optional}").optional(),
                        experience  : Joi.string().example("2015-03-25 {optional}").optional(),
						video		: Joi.string().optional(),
						attachment	: Joi.object().optional(),
						meetingPrice:Joi.string().optional(),
						meetingProductId: Joi.string().optional().allow(null, ""),
						soulwritingProductId: Joi.string().optional().allow(null, ""),
						languages	:Joi.array().example([1,2,3]).items(
							Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'LANGUAGE_ID_IS_REQUIRED')})
						).min(1).optional(),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	// Endpoint to update code
	{
		method : "PATCH",
		path : "/users",
		handler :useronboardingGateway,
		options: {
			tags: ["api", "Admin"],
			notes: "Endpoint to add User By Admin",
			description: "Add User By Admin",
			auth:{strategy:'jwt',scope:["admin","manage-users","user_listing","companion_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
						id			: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),	
					    email		: Joi.string().example("<EMAIL>").trim().optional(),
						firstName	: Joi.string().example("John {optional}").max(250).optional(),
                        role        : Joi.array().items(Joi.number().integer()).min(1).example([4]).description('2 for member 1 for companion').required().error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
						lastName	: Joi.string().example("John {optional}").max(250).optional(),
						title		: Joi.string().example("John {optional}").max(250).optional(),
						gender		: Joi.number().integer().example(1).allow(0,1,2).description('use 0 for mail 1 for female 2 for other ').optional(),
						reason		: Joi.string().example("reason {optional}").optional(),
						about		: Joi.string().example("about {optional}").optional(),
						vita		: Joi.string().example("vita {optional}").optional(),
                        experience  : Joi.string().example("2015-03-25 {optional}").optional(),
						video		: Joi.string().optional(),
						attachment	: Joi.object().optional(),
						meetingPrice:Joi.string().optional(),
						meetingProductId: Joi.string().optional().allow(null, ""),
						soulwritingProductId: Joi.string().optional().allow(null, ""),
						languages	:Joi.array().example([1,2,3]).items(
							Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'LANGUAGE_ID_IS_REQUIRED')})
						).min(1).optional()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
// Delete a Companion
	{
		method : "DELETE",
		path : "/users",
		handler :useronboardingGateway,
		options: {
			tags: ["api", "Admin"],
			notes: "Endpoint to Delete User By Admin",
			description: "Delete User By Admin",
			auth:{strategy:'jwt',scope:["admin","manage-users","user_listing","companion_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
						id	: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/users-id",
		handler :useronboardingGateway,
		options: {
			tags: ["api", "Admin"],
			notes: "Endpoint to Get User By Admin",
			description: "Get User By Admin",
			auth:{strategy:'jwt',scope:["admin","manage-users","user_listing","companion_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
						id	: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/companion",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Companion"],
			notes: "Endpoint to Get Companion",
			description: "Get Companion",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					limit: Joi.number().integer().optional(),
					searchText: Joi.string().max(250).optional(),
					status: Joi.number().optional(),
					name: Joi.string().max(250).optional(),
					email: Joi.string().max(250).optional(),
					phone: Joi.string().max(250).optional(),
					pageNumber: Joi.number().integer().min(1).optional(),
					orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
					orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
					random:Joi.number().integer().valid(1,0).optional().default(0),
					role:Joi.number().integer().example(3).valid(3,4).description('3 for student 4 for professional').optional(),
					gender:Joi.number().integer().example(1).valid(1,2).description('Accepted 1 ,2').optional(),
					language:Joi.number().integer().example(1).optional(),
					lowerExperience:Joi.number().integer().example(1).description("lower in years").optional(),
					hignerExperience:Joi.number().integer().example(1).description("Higher in years").optional(),
					rating:Joi.number().integer().example(1).allow(1,2,3,4,5),
					price:Joi.number().integer().example(1)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/my-companion",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Companion"],
			notes: "Endpoint to Get Companion",
			description: "Get Companion",
			auth: {strategy: "jwt"},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					limit: Joi.number().integer().optional(),
					searchText: Joi.string().max(250).optional(),
					status: Joi.number().optional(),
					name: Joi.string().max(250).optional(),
					email: Joi.string().max(250).optional(),
					phone: Joi.string().max(250).optional(),
					pageNumber: Joi.number().integer().min(1).optional(),
					orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
					orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
					random:Joi.number().integer().valid(1,0).optional().default(0),
					role:Joi.number().integer().example(3).valid(3,4).description('3 for student 4 for professional').optional(),
					gender:Joi.number().integer().example(1).valid(1,2).description('Accepted 1 ,2').optional(),
					language:Joi.string().example("en").optional(),
					lowerExperience:Joi.number().integer().example(1).description("lower in years").optional(),
					hignerExperience:Joi.number().integer().example(1).description("Higher in years").optional(),
					rating:Joi.number().integer().example(1).allow(1,2,3,4,5),
					price:Joi.number().integer().example(1),
					onlyMyCompanions: Joi.number().integer().valid(0,1).optional()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/active-companion",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Companion"],
			notes: "Endpoint to Get Companion",
			description: "Get Companion",
			auth: {strategy: "jwt", mode: "optional"},
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					limit: Joi.number().integer().optional(),
					searchText: Joi.string().max(250).optional(),
					name: Joi.string().max(250).optional(),
					email: Joi.string().max(250).optional(),
					phone: Joi.string().max(250).optional(),
					pageNumber: Joi.number().integer().min(1).optional(),
					orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
					orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
					random:Joi.number().integer().valid(1,0).optional().default(0),
					role:Joi.number().integer().example(3).valid(3,4).description('3 for student 4 for professional').optional(),
					gender:Joi.number().integer().example(1).valid(1,2).description('Accepted 1 ,2').optional(),
					language:Joi.string().example("en").optional(),
					lowerExperience:Joi.number().integer().example(1).description("lower in years").optional(),
					hignerExperience:Joi.number().integer().example(1).description("Higher in years").optional(),
					rating:Joi.number().integer().example(1).allow(1,2,3,4,5),
					price:Joi.number().integer().example(1),
					soulwritingProductId: Joi.number().integer().optional().example(1).default(0),
					meetingProductId: Joi.number().integer().optional().example(1).default(0),
					showData: Joi.string().valid("companion", "student", "student-meeting-count", null).allow(null).optional()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/companion-id",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Companion"],
			notes: "Endpoint to Get Companion by Id",
			description: "Get Companion by Id",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
				id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}).min(1).optional(),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	//******** Routes for update Profile *****************
	{
		method : "GET",
		path : "/update-user-profiles",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Settings"],
			notes: "Endpoint to sync UserProfiles",
			description: "Enpoint to sync Userprofile",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
				code:Joi.string().example("1").required().error(errors=>{return Common.routeError(errors,'CODE_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/user/logout",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to allow user to Logout From a Device",
			description: "Logout From a Device",
			auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					//sessionId: Joi.string().example("sessioId").trim().required().error(errors=>{return Common.routeError(errors,'FCM_TOKEN_IS_REQUIRED')}),
					deviceType:Joi.number().integer().example(1).allow(1,2,3).description("1-android,2-ios,3-web").required().error(errors=>{return Common.routeError(errors,'DEVICE_TYPE_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "PATCH",
		path : "/user/device-token",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to allow user to update device token",
			description: "Endpoint to update Device Token",
			auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					deviceType:Joi.number().integer().example(1).allow(1,2,3).description("1-android,2-ios,3-web").required().error(errors=>{return Common.routeError(errors,'DEVICE_TYPE_IS_REQUIRED')}),
					deviceToken 	:	Joi.string().example('device-token').required().error(errors=>{return Common.routeError(errors,'DEVICE_TOKEN_IS_REQUIRED')})

				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/create-user-zoom",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Settings"],
			notes: "Endpoint to sync Zoom User Accounts",
			description: "Enpoint to sync Zoom User Accounts",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
				code:Joi.string().example("1").required().error(errors=>{return Common.routeError(errors,'CODE_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/address",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Address"],
			notes: "Endpoint to add address",
			description:"Create address",
			auth: {strategy: 'jwt'},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
				   title:Joi.string().example('mr').required().error(errors=>{return Common.routeError(errors,'TITLE_IS_REQUIRED')}),
				   fullName:Joi.string().example('hero').required().error(errors=>{return Common.routeError(errors,'fullName_IS_REQUIRED')}),
				   line1:Joi.string().example('mr').optional(),
				   line2:Joi.string().example('mr').optional(),
				   city:Joi.string().example('njb').required().error(errors=>{return Common.routeError(errors,'CITY_IS_REQUIRED')}),
				   zipCode:Joi.number().integer().example(123456).required().error(errors=>{return Common.routeError(errors,'ZIP_CODE_IS_REQUIRED')}),
				   country:Joi.string().example('mr').required().error(errors=>{return Common.routeError(errors,'COUNTERY_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "PATCH",
		path : "/address",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Address"],
			notes: "Endpoint to update address",
			description:"Update address",
			auth: {strategy: 'jwt'},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					id:Joi.number().integer().example(2).required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
				    title:Joi.string().example('mr').optional(),
				    fullName:Joi.string().example('hero').optional(),
				    line1:Joi.string().example('mr').optional(),
				    line2:Joi.string().example('mr').optional(),
				    city:Joi.string().example('njb').optional(),
				    zipCode:Joi.number().integer().example(2211122).optional(),
				    country:Joi.string().example('mr').optional()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	
	{
		method : "GET",
		path : "/address",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Address"],
			notes: "Endpoint to get address",
			description:"Fetch group",
			auth: {strategy: 'jwt', scope: ["admin","manage_address","costumer"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					//fullName:Joi.string().max(250).optional(),
					limit: Joi.number().integer().optional(),
                    pageNumber: Joi.number().integer().min(1).optional(),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	
	{
		method : "DELETE",
		path : "/address",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Address"],
			notes: "Endpoint to Delete address",
			description:"Delete Ctegory",
			auth: {strategy: 'jwt', scope: ["admin","manage_address","costumer"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/zoom-status",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Zoom Profile Status"],
			notes: "Endpoint to Update Zoom Profile Status",
			description: "Endpoint to Update Zoom Profile Status",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					email		: Joi.string().example("<EMAIL>").trim().required(),
					status		: Joi.number().integer().valid(1,0).example(1).required()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/settings",
		handler :useronboardingGateway,
		options: {
			tags: ["api", "Admin"],
			notes: "Endpoint to add User By Admin",
			description: "Add User By Admin",
			auth:{strategy:'jwt',scope:["admin","manage-settings","settings"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
						data:Joi.array().example([{'key':'key','value':'value'}]).items(
							Joi.object().example({'key':'key','value':'value'})
						).min(1).required(),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	
	
	{
		method : "GET",
		path : "/settings",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Admin"],
			notes: "Endpoint to Get All Users",
			description: "Get Users By Admin ",
			auth: {strategy: 'jwt', mode: "optional"},
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					// limit: Joi.number().integer().optional(),
					// pageNumber: Joi.number().integer().min(1).optional().default(1),
					orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
					orderByParameter: Joi.string().valid('createdAt','id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},

	{
		method : "Get",
		path : "/page",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Pages"],
			notes: "Endpoint to get all pages",
			description: "Get Pages",
			auth: {strategy: 'jwt', scope: ['admin','static_pages']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
					
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "Get",
		path : "/page/{reference}",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Pages"],
			notes: "Endpoint to get page by id",
			description: "Get Page by id",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				params:{
					reference: Joi.alternatives(Joi.number(), Joi.string()).required().error(errors=>{return Common.routeError(errors,'REFERENCE_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "DELETE",
		path : "/page",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Pages"],
			notes: "Endpoint to delete page",
			description: "Delete Page",
			auth: {strategy: 'jwt',scope: ['admin','static_pages']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					id: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'PAGE_ID_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "PATCH",
		path : "/page",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Pages"],
			notes: "Endpoint to update page",
			description: "Update Page",
			auth: {strategy: 'jwt',scope: ['admin','static_pages']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					id: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
					body: Joi.string().example('page html (optional)').optional(),
					title: Joi.string().example('page title (optional)').optional(),
					slug: Joi.string().example('page url (optional)').optional(),
					status: Joi.number().integer().example('0 | 1 (optional)').optional()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
    {
		method : "POST",
		path : "/page",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Pages"],
			notes: "Endpoint to create a new page",
			description: "Create Pages",
			auth: {strategy: 'jwt',scope: ['admin','static_pages']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					body: Joi.string().required().error(errors=>{return Common.routeError(errors,'BODY_IS_REQUIRED')}),
					title: Joi.string().required().error(errors=>{return Common.routeError(errors,'TITLE_IS_REQUIRED')}),
					slug: Joi.string().required().error(errors=>{return Common.routeError(errors,'SLUG_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/earning-history",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Webhook Earning History"],
			notes: "Webhook Endpoint to add earning history",
			description: "Webhook Endpoint to add earning history",
			auth: {strategy: "jwt"},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
					limit: Joi.number().integer().optional().default(null),
					pageNumber : Joi.number().integer().min(1).optional().default(null),
					historyDays : Joi.number().integer().min(1).optional().default(null),
					orderByValue: Joi.string().allow('ASC', 'DESC').optional().default('DESC'),
					orderByParameter: Joi.string().allow('createdAt','id').optional().default('id'),
					type: Joi.string().valid("soul-writiing","meeting","topic","shop").optional()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/user/history",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint for companion to check user history",
			description: "Endpoint for companion to check user history",
			auth: { strategy: "jwt" },
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
					userId: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'USER_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]	
		}
	},
	{
		method : "GET",
		path : "/admin/request-access",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Admin"],
			notes: "Login to user account",
			description: "Login to user account",
			auth: {strategy: "jwt", scope: ["admin"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
					userId: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'USER_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/admin/grant-access",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Admin"],
			notes: "Login to user account",
			description: "Login to user account",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers(false)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
					key: Joi.string().required().error(errors=>{return Common.routeError(errors,'KEY_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/admin/forget-password",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Admin"],
			notes: "allow admin to generate reset password link for users",
			description: "User login",
			auth: {strategy: "jwt", scope: ["admin"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					email: Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/admin/create-user",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Admin"],
			notes: "Endpoint to add new user",
			description: "Enpoint to add new user",
			auth: {strategy: "jwt", scope: ["admin"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					title: Joi.string().example("<EMAIL>").trim().required().error(errors=>{return Common.routeError(errors,'TITLE_IS_REQUIRED')}),
					email: Joi.string().example("<EMAIL>").trim().required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
					firstName: Joi.string().example("John {optional}").max(250).required().error(errors=>{return Common.routeError(errors,'FIRST_NAME_IS_REQUIRED')}),
					lastName: Joi.string().example("John {optional}").max(250).required().error(errors=>{return Common.routeError(errors,'LAST_NAME_IS_REQUIRED')}),
					language : Joi.string().optional().default("en")
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "PATCH",
		path : "/admin/update-user",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "Admin"],
			notes: "Endpoint to add new user",
			description: "Enpoint to add new user",
			auth: {strategy: "jwt", scope: ["admin"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload:{
					id: Joi.number().example(1).required().error(errors=>{return Common.routeError(errors,'USER_ID_IS_REQUIRED')}),
					title: Joi.string().example("<EMAIL>").trim().required().error(errors=>{return Common.routeError(errors,'TITLE_IS_REQUIRED')}),
					email: Joi.string().example("<EMAIL>").trim().required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
					firstName: Joi.string().example("John {optional}").max(250).required().error(errors=>{return Common.routeError(errors,'FIRST_NAME_IS_REQUIRED')}),
					lastName: Joi.string().example("John {optional}").max(250).required().error(errors=>{return Common.routeError(errors,'LAST_NAME_IS_REQUIRED')}),
					language : Joi.string().optional().default("en")
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/user/enable-soul-writing",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to enable soul writing",
			description: "Endpoint to enable soul writing",
			auth: {strategy: "jwt",mode:"optional"},
			validate: {
				headers : Joi.object
				({
					"language"      : Joi.string().default("en"),
					"Authorization" : Joi.string()
				}).options({
					allowUnknown: true
				}),
				payload:{
					data:Joi.string().example('data').error(err=>{return Common.routeError(err,'DATA_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/user/soul-writing-status",
		handler : useronboardingGateway,
		options: {
			tags: ["api", "User"],
			notes: "Endpoint to get soul writing status",
			description: "Endpoint to get soul writing status",
			auth: {strategy: "jwt"},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query:{
					userId:Joi.number().example(1).optional().allow(null).default(null),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	}
]
