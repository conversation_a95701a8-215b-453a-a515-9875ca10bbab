const {zoomMeetingGateway} = require('../controllers/gatewayController');

module.exports = [
    {
        method : "POST",
        path : "/zoom/webhook",
        handler : zoomMeetingGateway,
        options: {
            tags: ["api", "Zoom"],
            notes: "Webhook to get recording",
            description: "Recording Webhook",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/zoom/authorize",
        handler : zoomMeetingGateway,
        options: {
            tags: ["api", "Zoom"],
            notes: "Endpoint to authorize zoom account",
            description: "Authorize Zoom",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    email: Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),                     
                    code: Joi.string().example('asdfgrtyucvbnsdfg').required().error(errors=>{return Common.routeError(errors,'CODE_IS_REQUIRED')}),  
                    userId: Joi.number().integer().example('1 (optional)').optional(),  
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/zoom/de-authorize",
        handler : zoomMeetingGateway,
        options: {
            tags: ["api", "Zoom"],
            notes: "Endpoint to authorize zoom account",
            description: "Authorize Zoom",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    email: Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')})
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/calendar/authorize",
        handler : zoomMeetingGateway,
        options: {
            tags: ["api", "Google Calendar"],
            notes: "Endpoint to authorize google account",
            description: "Authorize Google",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {   
                    email: Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),                     
                    code: Joi.string().example('asdfgrtyucvbnsdfg').required().error(errors=>{return Common.routeError(errors,'CODE_IS_REQUIRED')}),  
                    userId: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'CODE_IS_REQUIRED')}),  
                    role: Joi.string().example('companion | customer').required().error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),   
                    //clientId: Joi.string().required().error(errors=>{return Common.routeError(errors,'CLIENT_ID_IS_REQUIRED')}) 
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "POST",
        path : "/calendar/de-authorize",
        handler : zoomMeetingGateway,
        options: {
            tags: ["api", "Google Calendar"],
            notes: "Endpoint to de-authorize Google account",
            description: "De-Authorize Google",
            auth: false,
            validate: {
                headers: Joi.object(Common.headers()).options({
                    allowUnknown: true
                }),
                options: {
                    abortEarly: false
                },
                payload: {
                    email: Joi.string().email().example('<EMAIL>').required().error(errors=>{return Common.routeError(errors,'EMAIL_IS_REQUIRED')}),
                    role: Joi.string().example('companion | customer').required().error(errors=>{return Common.routeError(errors,'ROLE_IS_REQUIRED')}),
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    },
    {
        method : "GET",
        path : "/zoom/replace-refresh-tokens",
        handler : zoomMeetingGateway,
        options: {
            tags: ["api", "Zoom"],
            notes: "Webhook to get recording",
            description: "Recording Webhook",
            auth: false,
            validate: {
                options: {
                    abortEarly: false
                },
                failAction: async (req, h, err) => {
                    return Common.FailureError(err, req);
                },
                validator: Joi
            },
            pre : [{method: Common.prefunction}]
        }
    }
]
