const {evantManagementgateway} = require('../controllers/gatewayController');

module.exports = [
	// ***********************************  Events ENDPOINTS  ***********************************
    
    // Create Event
    {
        method : "POST",
        path : "/events",
        handler : evantManagementgateway,
        options: {
          tags: ["api", "Event"],
          notes: "Endpoint to allow add Event",
          description: "Create Event use 1 for workinghour 2 for meeting 3 for appointment 4 for seminar ",
          auth:{strategy: 'jwt',scope:["admin","costumer","companion","student"]},
          validate: {
            headers: Joi.object(Common.headers(true)).options({
              allowUnknown: true
            }),
            options: {
              abortEarly: false
            },
            payload: {
               title:Joi.string().example('This is my task1').required().error(err=>{return Common.routeError(err,'TITLE_IS_REQUIRED')}),
               groupId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')}),
               startDate:Joi.date().utc().example('2022-07-05T04:00:00Z').required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
               timings:Joi.array().items(Joi.object()).example([{start:"2022-07-05T04:00:00Z",end:"2022-07-05T04:00:00Z",timingType:1}]).optional(),
               participant:Joi.array().items(Joi.object()).example([{userId:1,role:1}]).optional().default([]),
               allDay:Joi.number().example(0).required().error(err=>{return Common.routeError(err,'ALL_DAY_IS_REQUIRED')}),
               description:Joi.string().example('This is my task1 Description').optional(),
               endDate:Joi.string().example('2022-07-05T04:00:00Z Optional').optional(),
               day:Joi.array().example([1,2,3]).items(Joi.number()).optional(),
               week:Joi.array().example([1,2,3]).items(Joi.number()).optional(),
               month:Joi.array().example([1,2,3]).items(Joi.number()).optional(),
               slots:Joi.number().example(10).optional(),
               returnData:Joi.object().example({'id':'1'}).optional(),
               type:Joi.number().example(1).valid(1,2,3,4).optional(),
               duration:Joi.string().example('Duration').optional(),
               remark:Joi.string().example('Remark').optional(),
               link:Joi.string().example('ShareLink').optional(),
               frequency:Joi.array().example([0]).optional(),
               occurance:Joi.number().example(1).optional(),
               owner:Joi.number().example(1).optional()
            },
            failAction: async (req, h, err) => {
              return Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      },
    
    // Update Event  
      {
        method : "PATCH",
        path : "/events",
        handler :evantManagementgateway,
        options: {
          tags: ["api", "Event"],
          
          notes: "Endpoint to allow Update Event",
          description: "Update Event",
          auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
          validate: {
            headers: Joi.object(Common.headers(true)).options({
              allowUnknown: true
            }),
            options: {
              abortEarly: false
            },
            payload: {
                id:Joi.number().example(1).integer().required().error(errors=>{return Common.routeError(errors,'EVENT_ID_REQUIRED')}),
                title:Joi.string().example('This is my task1').required().error(err=>{return Common.routeError(err,'TITLE_IS_REQUIRED')}),
                groupId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')}),
                startDate:Joi.date().utc().example('2022-07-05T04:00:00Z').required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
                endDate:Joi.date().utc().example('2022-07-05T04:00:00Z').optional(),
                timings:Joi.array().items(Joi.object()).example([{start:"2022-07-05T04:00:00Z",end:"2022-07-05T04:00:00Z"}]).optional(),
                allDay:Joi.number().example(0).required().error(err=>{return Common.routeError(err,'ALL_DAY_IS_REQUIRED')}),
                description:Joi.string().example('This is my task1 Description').optional(),
                link:Joi.string().example('ShareLink').optional(),
                occurance:Joi.number().example(1).optional(),
                type:Joi.number().example(1).valid(1,2,3,4).optional(),
                returnData:Joi.object().example({'id':'1'}).optional(),
                frequency:Joi.array().example([0]).optional(),
                day:Joi.array().example([1,2,3]).items(Joi.number()).optional(),
                week:Joi.array().example([1,2,3]).items(Joi.number()).optional(),
                month:Joi.array().example([1,2,3]).items(Joi.number()).optional(),
                participant:Joi.array().items(Joi.object()).example([{userId:1,role:'owner'}]).optional(),
                duration:Joi.string().example('Duration').optional(),
                remark:Joi.string().example('Remark').optional()
            },
            failAction: async (req, h, err) => {
              return Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      },
    
    // Get Events  
      {
        method : "GET",
        path : "/events",
        handler :evantManagementgateway,
        options: {
          tags: ["api", "Event"],
          notes: "Get Event Api",
          description: "Event List",
          auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
          validate: {
            headers: Joi.object(Common.headers(true)).options({
              allowUnknown: true
            }),
            query:{
                        startDate:Joi.date().utc().example('2022-07-11 03:24:30 pm').required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
                        endDate:Joi.date().utc().example('2022-07-11 10:24:30 pm').required().error(err=>{return Common.routeError(err,'END_DATE_IS_REQUIRED')}),
                        groupId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')}),
                        limit: Joi.number().integer().optional(),
                        pageNumber: Joi.number().integer().min(1).optional(),
                        orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
                        orderByParameter: Joi.string().valid('createdAt','id').optional().default('startDate')
            },
            options: {
              abortEarly: false
            },
            failAction: async (req, h, err) => {
              return Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      },
      
    // Get Event by id  
      {
        method : "GET",
        path : "/events/id",
        handler : evantManagementgateway,
        options: {
          tags: ["api", "Event"],
          notes: "Endpoint to allow get event by id",
          description: "Get Event Details",
          auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
          validate: {
            headers: Joi.object(Common.headers(true)).options({
              allowUnknown: true
            }),
            options: {
              abortEarly: false
            },
            query:{
                id:Joi.number().example(" 7 {Required}").integer().required().error(errors=>{return Common.routeError(errors,'SPECIES_ID_REQUIRED')}),
            },
            failAction: async (req, h, err) => {
              return Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      },
    
    //Delete a Event
      {
        method : "DELETE",
        path : "/events",
        handler : evantManagementgateway,
        options: {
          tags: ["api", "Event"],
          notes: "Endpoint to Delete Event",
          description: "Delete Event ",
          auth:false,
          validate: {
            headers: Joi.object(Common.headers()).options({
              allowUnknown: true
            }),
            options: {
              abortEarly: false
            },
            query:{
                id:Joi.number().example(1).integer().required().error(errors=>{return Common.routeError(errors,'EVENT_ID_REQUIRED')})
            },
            failAction: async (req, h, err) => {
              return Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      },
    
    //Complete a Event
    {
      method : "POST",
      path : "/events/complete",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Endpoint to allow add Event",
        description: "Mark as complete",
        auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          payload: {
             eventId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'EVENT_ID_IS_REQUIRED')}),
             timingId:Joi.number().example(1).optional(),
             //userId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'USER_ID_IS_REQUIRED')}),
             attachment:Joi.array().example([{'id':1}]).optional(),
             scheduleDate:Joi.date().example('2022-07-30T04:00:00Z').required().error(err=>{return Common.routeError(err,'EVENT_SCHEDULED_DATE_IS_REQUIRED')})    
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    
    
    
    // Bulk Complete Event
    {
      method : "POST",
      path : "/events/bulk-complete",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Endpoint to allow complete Event",
        description: "Bulk  mark as complete",
        auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          payload: {
            data:Joi.array().items(
              Joi.object().keys(
                {
                eventId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'EVENT_ID_IS_REQUIRED')}),
                timingId:Joi.number().example(1).optional(),
                attachment:Joi.array().example([{'id':1}]).optional(),
                scheduleDate:Joi.date().example('2022-07-30T04:00:00Z').required().error(err=>{return Common.routeError(err,'EVENT_SCHEDULED_DATE_IS_REQUIRED')})
                }
              )
            ).min(1).required().error(err=>{return Common.routeError(err,'EVENT_ID_IS_REQUIRED')})     
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    
    

   
    
    {
      method : "POST",
      path : "/events/action",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Reschedule Event Action",
        description: "Reschedule Events Action",
        auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          payload:{
                  eventId:Joi.number().integer().example(1).error(err=>{return Common.routeError(err,'EVENT_ID_IS_REQUIRED')}),
                  notificationId:Joi.number().integer().example(1).error(err=>{return Common.routeError(err,'NOTIFICATION_ID_IS_REQUIRED')}),
                  option:Joi.number().integer().valid(0,1).description('1 for accept & 0 for rejact').example(1).error(err=>{return Common.routeError(err,'EVENT_ID_IS_REQUIRED')}),
          },
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    // create Events in bulk
    {
      method : "POST",
      path : "/events-bulk",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Endpoint to allow add Events in Bulk",
        description: "Create Event",
        auth:{strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          payload: {
            data:Joi.array().items(
              Joi.object().min(1).keys(
                {
                  title:Joi.string().example('This is my task1').required().error(err=>{return Common.routeError(err,'TITLE_IS_REQUIRED')}),
                  groupId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')}),
                  startDate:Joi.string().example('2022-07-05T04:00:00Z').required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
                  timings:Joi.array().items(Joi.object()).example([{start:"2022-07-05T04:00:00Z",end:"2022-07-05T04:00:00Z",timingType:1}]).optional(),
                  participant:Joi.array().items(Joi.object()).example([{userId:1,role:1}]).optional().default([]),
                  allDay:Joi.number().example(0).required().error(err=>{return Common.routeError(err,'ALL_DAY_IS_REQUIRED')}),
                  description:Joi.string().example('This is my task1 Description ').optional(),
                  endDate:Joi.string().example('2022-07-05T04:00:00Z Optional').optional(),
                  day:Joi.array().example([1,2,3]).items(Joi.number()).optional(),
                  week:Joi.array().example([1,2,3]).items(Joi.number()).optional(),
                  month:Joi.array().example([1,2,3]).items(Joi.number()).optional(),
                  slots:Joi.number().example(10).optional(),
                  returnData:Joi.object().example({'id':'1'}).optional(),
                  type:Joi.number().example(1).valid(1,2,3,4).optional(),
                  duration:Joi.string().example('Duration').optional(),
                  remark:Joi.string().example('Remark').optional(),
                  link:Joi.string().example('ShareLink').optional(),
                  frequency:Joi.array().example([0]).optional(),
                  occurance:Joi.number().example(1).optional(),
                  isCustomSlot: Joi.number().integer().optional().default(0)
                }
              ).optional()
            ).required().error(err=>{return Common.routeError(err,'EVENT_DATA_IS_REQUIRED')}),
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    // patch Events in bulk
    {
      method : "PATCH",
      path : "/events-bulk",
      handler :evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        
        notes: "Endpoint to allow Update Bulk Event",
        description: "Update Event",
        auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          payload: {
            data:Joi.array().items(
              Joi.object().min(1).keys(
                {
                  title:Joi.string().example('This is my task1').required().error(err=>{return Common.routeError(err,'TITLE_IS_REQUIRED')}),
                  groupId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')}),
                  startDate:Joi.string().example('2022-07-05T04:00:00Z').required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
                  timings:Joi.array().items(Joi.object()).example([{start:"2022-07-05T04:00:00Z",end:"2022-07-05T04:00:00Z",timingType:1}]).optional(),
                  participant:Joi.array().items(Joi.object()).example([{userId:1,role:1}]).optional().default([]),
                  allDay:Joi.number().example(0).required().error(err=>{return Common.routeError(err,'ALL_DAY_IS_REQUIRED')}),
                  description:Joi.string().example('This is my task1 Description ').optional(),
                  endDate:Joi.string().example('2022-07-05T04:00:00Z Optional').optional(),
                  day:Joi.array().example([1,2,3]).items(Joi.number()).optional(),
                  week:Joi.array().example([1,2,3]).items(Joi.number()).optional(),
                  month:Joi.array().example([1,2,3]).items(Joi.number()).optional(),
                  slots:Joi.number().example(10).optional(),
                  returnData:Joi.object().example({'id':'1'}).optional(),
                  type:Joi.number().example(1).valid(1,2,3,4).optional(),
                  duration:Joi.string().example('Duration').optional(),
                  remark:Joi.string().example('Remark').optional(),
                  link:Joi.string().example('ShareLink').optional(),
                  frequency:Joi.array().example([0]).optional(),
                  occurance:Joi.number().example(1).optional()
                }
              )
            ).min(1).required().error(err=>{return Common.routeError(err,'EVENT_DATA_IS_REQUIRED')}),
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },

    // get Events in bulk
    {
      method : "GET",
      path : "/events-bulk",
      handler :evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Get Event Api",
        description: "Event List",
        auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          query:{
              groupId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')}),
              isCustomSlot:Joi.number().example(1).valid(0,1).optional().default(0),
              startDate:Joi.date().optional()
          },
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },

    {
      method : "GET",
      path : "/events-slots",
      handler :evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Get Event Api",
        description: "Event List",
        auth:false,
        //auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          query:{
                      companionId:Joi.number().integer().required().error(err=>{return Common.routeError(err,'COMPANION_ID_REQUIRED')}),
                      startDate:Joi.date().utc().example("2022-07-05T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
                      endDate:Joi.date().utc().example("2022-07-06T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'END_DATE_IS_REQUIRED')})
          },
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/events-slots-copy-test",
      handler :evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Get Event Api",
        description: "Event List",
        auth:false,
        //auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          query:{
                      companionId:Joi.number().integer().required().error(err=>{return Common.routeError(err,'COMPANION_ID_REQUIRED')}),
                      startDate:Joi.date().utc().example("2022-07-05T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
                      endDate:Joi.date().utc().example("2022-07-06T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'END_DATE_IS_REQUIRED')})
          },
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
        method : "GET",
        path : "/events-all-companion-slots",
        handler :evantManagementgateway,
        options: {
          tags: ["api", "Event"],
          notes: "Get Event Api",
          description: "Event List",
          auth:false,
          validate: {
            headers: Joi.object(Common.headers()).options({
              allowUnknown: true
            }),
            query:{
              startDate:Joi.date().utc().example("2022-07-05T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
              endDate:Joi.date().utc().example("2022-07-06T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'END_DATE_IS_REQUIRED')})
            },
            options: {
              abortEarly: false
            },
            failAction: async (req, h, err) => {
              return Common.FailureError(err, req);
            },
            validator: Joi
          },
          pre : [{method: Common.prefunction}]
        }
      },
    {
      method : "GET",
      path : "/events-calander",
      handler :evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Get Event Api",
        description: "Event List",
        auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          query:{
                status:Joi.number().integer(),
                startDate:Joi.date().utc().example("2022-07-05T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
                endDate:Joi.date().utc().example("2022-07-06T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'END_DATE_IS_REQUIRED')}),
                flag: Joi.string().valid("companion", "user").allow(null).optional().default("user")
          },
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "POST",
      path : "/events/reschedule",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Reschedule Event",
        description: "Reschedule Events",
        auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          payload:{
                  eventId:Joi.number().integer().example(1).required().error(err=>{return Common.routeError(err,'EVENT_ID_IS_REQUIRED')}),
                  title:Joi.string().example('This is my task1').required().error(err=>{return Common.routeError(err,'TITLE_IS_REQUIRED')}),
                  groupId:Joi.number().example(1).valid(2).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')}),
                  startDate:Joi.date().utc().example('2022-07-05T04:00:00Z').required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
                  timings:Joi.array().items(Joi.object()).example([{start:"2022-07-05T04:00:00Z",end:"2022-07-05T04:00:00Z",timingType:1}]).optional(),
                  participant:Joi.array().items(Joi.object()).example([{userId:1,role:1}]).optional().default([]),
                  allDay:Joi.number().example(0).required().error(err=>{return Common.routeError(err,'ALL_DAY_IS_REQUIRED')}),
                  description:Joi.string().example('This is my task1 Description').optional(),
                  endDate:Joi.string().example('2022-07-05T04:00:00Z Optional').optional(),
                  returnData:Joi.object().example({'id':'1'}).optional(),
                  remark:Joi.string().example('Remark').optional(),
                  link:Joi.string().example('ShareLink').optional(),
          },
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "DELETE",
      path : "/events-cancel",
      handler :evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "CAncel Event Api",
        description: "Used For Cancel a Meeting of a user",
        auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          query:{
          id:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'MEETING_ID_IS_REQUIRED')}),
          reason:Joi.string().example('reason').required().error(err=>{return Common.routeError(err,'CANCEL_REASON_IS_REQUIRED')})
          },
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "DELETE",
      path : "/remove-slots",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Endpoint to Delete Event",
        description: "Delete Event ",
        auth:{strategy: "jwt"},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          query:{
              isCustomSlot:Joi.number().example(1).integer().required().valid(0,1).error(errors=>{return Common.routeError(errors,'EVENT_ID_REQUIRED')})
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/test-digistore",
      handler :evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Upcoming Meetings",
        description: "Upcoming Meetings in next 30 Days",
        auth: false,
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "POST",
      path : "/test",
      handler :evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Completed Meetings",
        description: "Completed Meetings",
        auth: false,
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          payload:{
            companions: Joi.array().required()     
          },
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/upcoming-meetings",
      handler :evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Upcoming Meetings",
        description: "Upcoming Meetings in next 30 Days",
        auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          query:{
                        limit: Joi.number().integer().optional(),
                        pageNumber: Joi.number().integer().min(1).optional(),
                        //userId:Joi.number().integer().min(1).optional().,
                        orderByValue: Joi.string().valid('ASC','DESC').optional().default('ASC'),
                        orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
                        startDate:Joi.string().example('2022-07-05T04:00:00Z').optional(),
                    endDate:Joi.string().example('2022-07-05T04:00:00Z').optional(),
          },
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/completed-meetings",
      handler :evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Completed Meetings",
        description: "Completed Meetings",
        auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          query:{
                        limit: Joi.number().integer().optional(),
                        pageNumber: Joi.number().integer().min(1).optional(),
                        //userId:Joi.number().integer().min(1).optional().,
                        orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
                        orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
                        startDate:Joi.string().example('2022-07-05T04:00:00Z').optional(),
                    endDate:Joi.string().example('2022-07-05T04:00:00Z').optional(),
          },
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/admin/completed-meetings",
      handler :evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Completed Meetings",
        description: "Completed Meetings",
        auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          query:{
                        limit: Joi.number().integer().optional(),
                        pageNumber: Joi.number().integer().min(1).optional(),
                        userId:Joi.number().integer().optional(),
                        orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
                        orderByParameter: Joi.string().valid('createdAt','id').optional().default('id'),
                        startDate:Joi.string().example('2022-07-05T04:00:00Z').optional(),
                    endDate:Joi.string().example('2022-07-05T04:00:00Z').optional(),
                    companion: Joi.string().optional(),
                    customer: Joi.string().optional(),
                    status:Joi.number().integer().optional()
          },
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "POST",
      path : "/meeting-payment",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Meeting Payment"],
        notes: "Meeting Payment Confirmation",
        description: "Meeting payment confirmation",
        auth: false,
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          payload:{
                  data:Joi.string().example('data').error(err=>{return Common.routeError(err,'DATA_IS_REQUIRED')}),
          },
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "POST",
      path : "/zoom-details",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Webhooks"],
        notes: "Endpoint to allow update zoom details",
        description: "Update Zoom Details",
        auth: false,
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          payload: {
              zoomId           :   Joi.number().example(1).required().error(err=>{return Common.routeError(err,'ZOOM_MEETING_ID_IS_REQUIRED')}),
              audioRecording   :   Joi.object().optional(),
              videoRecording   :   Joi.object().optional(),
              audioLink        :   Joi.string().optional(),
              videoLink        :   Joi.string().optional(),
              zoomDuration     :   Joi.string().optional(),
              audioPassword    :   Joi.string().optional(),
              videoPassword    :   Joi.string().optional(),
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "POST",
      path : "/complete-meeting-payment",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Meeting Payment"],
        notes: "Meeting Payment Confirmation",
        description: "Meeting payment confirmation",
        auth:false,
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          payload:{
                  data:Joi.string().example('data').error(err=>{return Common.routeError(err,'DATA_IS_REQUIRED')}),
                
          },
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/invoices",
      handler :evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Completed Meetings",
        description: "Completed Meetings",
        auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          query:{
                        userId:Joi.number().integer().optional(),
                        limit: Joi.number().integer().optional(),
                        pageNumber: Joi.number().integer().min(1).optional().default(1),
                        orderByValue: Joi.string().valid('ASC','DESC').optional().default('DESC'),
                        orderByParameter: Joi.string().valid('createdAt','id').optional().default('id')
          },
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/events-all-slots",
      handler :evantManagementgateway,
      options: {
        tags: ["api", "Event"],
        notes: "Get Event Api",
        description: "Event List",
        auth:false,
        // auth: {strategy: 'jwt',scope:["admin","costumer","companion","student"]},
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          query:{
                      //companionId:Joi.number().integer().required().error(err=>{return Common.routeError(err,'COMPANION_ID_REQUIRED')}),
                      startDate:Joi.date().utc().example("2022-07-05T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'START_DATE_IS_REQUIRED')}),
                      endDate:Joi.date().utc().example("2022-07-06T04:00:00.000Z").required().error(err=>{return Common.routeError(err,'END_DATE_IS_REQUIRED')}),
                      //groupId:Joi.number().example(1).required().error(err=>{return Common.routeError(err,'GROUP_ID_IS_REQUIRED')})
          },
          options: {
            abortEarly: false
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "POST",
      path : "/feedback",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Feedback"],
        notes: "Endpoint to allow user to add feedback",
        description: "Feedback",
        auth:false,
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          payload: {
             token:Joi.string().example('token').required().error(err=>{return Common.routeError(err,'TOKEN_IS_REQUIRED')}),
             feedback:Joi.array().items(Joi.number().integer()).min(1).required().error(err=>{return Common.routeError(err,'FEEDBACK_ARRAY_IS_REQUIRED')}),
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/feedback-questions",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Feedback"],
        notes: "Endpoint to list feedback questions",
        description: "Feedback",
        auth:false,
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          query: {
             token:Joi.string().example('token').required().error(err=>{return Common.routeError(err,'TOKEN_IS_REQUIRED')})
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/feedback/{meetingId}",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Feedback"],
        notes: "Endpoint to get feedback rating",
        description: "Feedback",
        auth:false,
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          params: {
             meetingId:Joi.number().integer().example(1).required().error(err=>{return Common.routeError(err,'MEETING_IS_REQUIRED')})
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/default-feedback-questions",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Feedback"],
        notes: "Endpoint to list feedback questions",
        description: "Feedback",
        auth:false,
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          query: {
             
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "POST",
      path : "/faq-category",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "FAQ Category"],
        notes: "Endpoint to allow admin to create FAQ category",
        description: "FAQs",
        auth:{strategy: "jwt", scope: ["admin","settings"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          payload: {
             name:Joi.string().example('category name').required().error(err=>{return Common.routeError(err,'NAME_IS_REQUIRED')})
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "PATCH",
      path : "/faq-category",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "FAQ Category"],
        notes: "Endpoint to allow admin to update FAQ category",
        description: "FAQs",
        auth:{strategy: "jwt", scope: ["admin","settings"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          payload: {
             id:Joi.number().integer().example(1).required().error(err=>{return Common.routeError(err,'ID_IS_REQUIRED')}),
             name:Joi.string().example('category name').optional()
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/faq-category",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Feedback"],
        notes: "Endpoint to get faq category",
        description: "FAQs",
        auth:false,
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          query: {
            
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "DELETE",
      path : "/faq-category",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "FAQ Category"],
        notes: "Endpoint to allow admin to delete FAQ category",
        description: "FAQs",
        auth:{strategy: "jwt", scope: ["admin","settings"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          payload: {
             id:Joi.number().integer().example(1).required().error(err=>{return Common.routeError(err,'ID_IS_REQUIRED')})
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "POST",
      path : "/faq",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "FAQ Category"],
        notes: "Endpoint to allow admin to create FAQ",
        description: "FAQs",
        auth:{strategy: "jwt", scope: ["admin","settings"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          payload: {
             title:Joi.string().example('title').required().error(err=>{return Common.routeError(err,'TITLE_IS_REQUIRED')}),
             description:Joi.string().example('description').required().error(err=>{return Common.routeError(err,'DESCRIPTION_IS_REQUIRED')}),
             categoryId:Joi.number().integer().example(1).required().error(err=>{return Common.routeError(err,'CATEGORY_ID_IS_REQUIRED')}),
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "PATCH",
      path : "/faq",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "FAQ Category"],
        notes: "Endpoint to allow admin to update FAQ",
        description: "FAQs",
        auth:{strategy: "jwt", scope: ["admin","settings"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          payload: {
             title:Joi.string().example('title'),
             description:Joi.string().example('description'),
             categoryId:Joi.number().integer().example(1),
             id:Joi.number().integer().example(1).required().error(err=>{return Common.routeError(err,'FAQ_ID_IS_REQUIRED')}),
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "DELETE",
      path : "/faq/{id}",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "FAQ Category"],
        notes: "Endpoint to allow admin to delete FAQ",
        description: "FAQs",
        auth:{strategy: "jwt", scope: ["admin","settings"]},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          params: {
            id:Joi.number().integer().example(1).required().error(err=>{return Common.routeError(err,'FAQ_ID_IS_REQUIRED')}),
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/faq",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "FAQ Category"],
        notes: "Endpoint to list FAQ",
        description: "FAQs",
        auth:false,
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          query: {
              limit: Joi.number().integer().optional(),
              pageNumber : Joi.number().integer().min(1).optional(),
              categoryId : Joi.number().integer().optional(),
              orderByValue: Joi.string().allow('ASC', 'DESC').optional().default('DESC'),
              orderByParameter: Joi.string().allow('createdAt','id').optional().default('id'),
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/faq/{id}",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "FAQ Category"],
        notes: "Endpoint to list FAQ",
        description: "FAQs",
        auth:false,
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          params: {
              id:Joi.number().integer().example(1).required().error(err=>{return Common.routeError(err,'FAQ_ID_IS_REQUIRED')}),
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/validate-token/{token}",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Feedback"],
        notes: "Endpoint to valiadte token",
        description: "Feedback",
        auth:false,
        validate: {
          headers: Joi.object(Common.headers()).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          params: {
            token: Joi.string().required().error(err=>{return Common.routeError(err,'TOKEN_IS_REQUIRED')})
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/order-meetings",
      handler : evantManagementgateway,
      options : {
        tags: ["api", "Order List"],
        notes: "GET Cart-Order",
        description: "GET Cart-Order",
        auth : {strategy: "jwt", scope : ["admin","costumer",'companion','student','order_management']},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options : {
            abortEarly: false
          },
          query : {
                      limit: Joi.number().integer().optional(),
            userId:Joi.number().integer().optional(),
                      pageNumber: Joi.number().integer().min(1).optional(),
                      orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                      orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id'),
                      // categoryId: Joi.string().allow(null).optional().default(null),
            startDate:Joi.date().example('2022-07-05T04:00:00Z').optional(),
            endDate:Joi.date().example('2022-07-05T04:00:00Z').optional()
          },
          failAction: async(req,h, err) => {
            return  Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/order-meeting",
      handler : evantManagementgateway,
      options : {
        tags: ["api", "Order List"],
        notes: "GET Cart-Order",
        description: "GET Cart-Order",
        auth : {strategy: "jwt", scope : ["admin","costumer",'companion','student','order_management']},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options : {
            abortEarly: false
          },
          query : {
            orderId:Joi.number().integer().required(),
          },
          failAction: async(req,h, err) => {
            return  Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },

    {
      method : "GET",
      path : "/event/user-summary",
      handler : evantManagementgateway,
      options : {
        tags: ["api", "Summary"],
        notes: "GET Soulwriting summary",
        description: "GET Soulwriting summary",
        auth : {strategy: "jwt", scope : ["admin",'companion','student']},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options : {
            abortEarly: false
          },
          query : {
            userId:Joi.number().integer().required(),
          },
          failAction: async(req,h, err) => {
            return  Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/{companionId}/feedbacks",
      handler : evantManagementgateway,
      options: {
        tags: ["api", "Feedback"],
        notes: "Endpoint to valiadte token",
        description: "Feedback",
        auth:{ strategy: "jwt", scope: ["admin"] },
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options: {
            abortEarly: false
          },
          query: {
            limit: Joi.number().integer().optional().default(20),
            pageNumber: Joi.number().integer().min(1).optional().default(1),
          },
          params: {
            companionId: Joi.number().required().error(err=>{return Common.routeError(err,'COMPANION_ID_IS_REQUIRED')})
          },
          failAction: async (req, h, err) => {
            return Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/event/legacy",
      handler : evantManagementgateway,
      options : {
        tags: ["api", "Event"],
        notes: "GET Event summary from legacy list",
        description: "GET Event summary from legacy list",
        auth : {strategy: "jwt"},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options : {
            abortEarly: false
          },
          query : {
            limit: Joi.number().integer().optional().default(20),
            pageNumber: Joi.number().integer().min(1).optional().default(1),
            userId:Joi.number().integer().optional().default(null),
          },
          failAction: async(req,h, err) => {
            return  Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "POST",
      path : "/event/slot-reminder",
      handler : evantManagementgateway,
      options : {
        tags: ["api", "Event"],
        notes: "GET Event summary from legacy list",
        description: "GET Event summary from legacy list",
        auth : {strategy: "jwt"},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options : {
            abortEarly: false
          },
          payload : {
            companionId:Joi.number().integer().required()
          },
          failAction: async(req,h, err) => {
            return  Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/event/slot-reminder-check",
      handler : evantManagementgateway,
      options : {
        tags: ["api", "Event"],
        notes: "GET Event summary from legacy list",
        description: "GET Event summary from legacy list",
        auth : {strategy: "jwt"},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options : {
            abortEarly: false
          },
          query : {
            companionId:Joi.number().integer().required()
          },
          failAction: async(req,h, err) => {
            return  Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    },
    {
      method : "GET",
      path : "/event/slot-reminder-users",
      handler : evantManagementgateway,
      options : {
        tags: ["api", "Event"],
        notes: "GET Event summary from legacy list",
        description: "GET Event summary from legacy list",
        auth : {strategy: "jwt"},
        validate: {
          headers: Joi.object(Common.headers(true)).options({
            allowUnknown: true
          }),
          options : {
            abortEarly: false
          },
          failAction: async(req,h, err) => {
            return  Common.FailureError(err, req);
          },
          validator: Joi
        },
        pre : [{method: Common.prefunction}]
      }
    }
]
