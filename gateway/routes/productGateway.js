const {productManagementgateway} = require('../controllers/gatewayController');

module.exports = [
	// ***********************************  Products ENDPOINTS  ***********************************
	/* category endpoints */
    {
		method : "POST",
		path : "/category",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product-Category"],
			notes: "Endpoint to define a new group for portal",
			description:"Create group",
			auth: {strategy: 'jwt', scope: ["admin","create_group","manage_group",'owner',"product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
                   // groupTypeId : Joi.number().required().error(errors=>{return Common.routeError(errors,'GROUP_TYPE_IS_REQUIRED')}),
				   	id:Joi.number().optional(),
				   	attachment:Joi.object().optional(),
				   	parentId: Joi.number().optional(),
					   ordering: Joi.number().optional().default(1),
                    name : Joi.string().required().error(errors=>{return Common.routeError(errors,'GROUP_NAME_TYPE_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},

	{
		method : "PATCH",
		path : "/category/status",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product-Category"],
			notes: "Endpoint to update category status",
			description:"Create group",
			auth: {strategy: 'jwt', scope: ["admin","create_group","manage_group",'owner',"product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
				   	id:Joi.number().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
				   	status: Joi.number().required().error(errors=>{return Common.routeError(errors,'STATUS_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	
	{
		method : "GET",
		path : "/category",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product-Category"],
			notes: "Endpoint to get Category",
			description:"Fetch group",
			auth: {strategy: 'jwt', scope: ["admin","manage_category","costumer","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					name:Joi.string().max(250).optional(),
					limit: Joi.number().integer().optional(),
                    pageNumber: Joi.number().integer().min(1).optional(),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/category-id",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product-Category"],
			notes: "Endpoint to get Category",
			description:"Fetch group",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/category-list",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product-Category"],
			notes: "Endpoint to get Category",
			description:"Get all",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					name:Joi.string().optional(),
					orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id', 'ordering').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "DELETE",
		path : "/category",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product-Category"],
			notes: "Endpoint to Delete Category",
			description:"Delete Ctegory",
			auth: {strategy: 'jwt', scope: ["admin","manage_category","costumer","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},

/********************************************************************************************************** */

	 /* attributes endpoints */
	 {
		method : "POST",
		path : "/attribute",
		handler : productManagementgateway,
		options : {
			tags: ["api", "Attributes"],
			notes: "Add Attributes",
			description: "Add Attributes",
			auth : {strategy:'jwt', scope: ["admin", "manage-products", "product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly: false
				},
				payload : {
                    id			: Joi.number().example(1).optional(),
                    categoryId 	: Joi.number().example(1).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')}),
					type 		: Joi.number().example(1).optional(),
					isPaid		: Joi.number().example(1).optional().default(0),
					isVariation : Joi.number().required().error(errors=>{return Common.routeError(errors,'ISVARIENT_IS_REQUIRED')}),
					addOns 		: Joi.number().required().error(errors=>{return Common.routeError(errors,'ADDDONS_IS_REQUIRED')}),
					name 		: Joi.string().trim().required().error(errors=>{return Common.routeError(errors,'NAME_IS_REQUIRED')}),
				    //groupId 	: Joi.number().required().error(errors=>{return Common.routeError(errors,'GROUP_ID_IS_REQUIRED')}),
				},
				failAction: async(req,h, err) => {
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},

	
	{
		method : "GET",
		path : "/attribute",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Attributes"],
			notes : "Get Attributes List",
			description: "Get Attributes List",
			auth : {strategy: 'jwt',scope : ["admin","manage-products", "product_management"]},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					name:Joi.string().max(250).optional(),
					limit: Joi.number().integer().optional(),
					categoryId:Joi.number().integer().optional(),
                    pageNumber: Joi.number().integer().min(1).optional(),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method :Common.prefunction}]
		}
    },
    
    {
		method : "GET",
		path : "/attribute-id",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Attributes"],
			notes : "Get Attributes",
			description: "Get Attributes",
			auth : {strategy: 'jwt',scope : ["admin", "manage-products", "product_management"]},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					id 		: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method : Common.prefunction}]
		}
	},

	{
		method : "DELETE",
		path : "/attribute",
		handler : productManagementgateway,
		options: {
			tags : ["api", "Attributes"],
			notes : "Delete Attribute",
			description : "Delete Attribute",
			auth : {strategy: 'jwt', scope : ["admin","manage-products", "product_management"]},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					id 		: Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre: [{method: Common.prefunction}]
		}
    },
/***********************************************  Products CRUD   *********************************************************** */
	{
		method : "POST",
		path : "/product",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to define a new product",
			description:"Create Product",
			auth: {strategy: 'jwt', scope: ["admin",'manage-product',"product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					name 					: Joi.string().example('name').required().error(errors=>{return Common.routeError(errors,'PRODUCT_NAME_IS_REQUIRED')}),
					descriptionHtml 		: Joi.string().trim().example('descriptionHtml').optional().allow('',null),
                    longDescriptionHtml 	: Joi.string().trim().example('longDescriptionHtml').optional().allow('',null),
                    highlights 	: Joi.string().trim().example('longDescriptionHtml').optional().allow('',null),
					categoryId				: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')}),
					courseId: Joi.number().integer().example(1).allow(null).optional().default(null),
					price					: Joi.array().items(
						{
							currencyId		:	Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PRICE_ID_IS_REQUIRED')}),
							price			:	Joi.number().required().example(1.1).error(errors=>{return Common.routeError(errors,'PRICE_VALUE_IS_REQUIRED')})
						}
					).min(1).required().error(errors=>{return Common.routeError(errors,'PRICE_IS_REQUIRED')}),
			// 		attributeSet		: Joi.array().items(
			// 			Joi.object().keys(
			// 				{
			// 				id		:	Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ATTRIBUTE_ID_IS_REQUIRED')}),
			// 				data	:	Joi.array().items(
			// 					{
			// 						id		:	Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ATTRIBUTE_ID_IS_REQUIRED')}),
			// 						value	:	Joi.string().trim().required().error(errors=>{return Common.routeError(errors,'ATTRIBUTE_VALUE_IS_REQUIRED')}),
			// 					}
			// 				)
			// 				}
			// 			)
			// ).min(1).required().error(errors=>{return Common.routeError(errors,'ATTRIBUTE_SET_IS_REQUIRED')}),
					attachment				:	Joi.object().example({'id':1,'path':'demo'}).optional().allow(null),
					tags					:	Joi.array().items(Joi.number().integer()).example([1,2,3,4]).error(errors=>{return Common.routeError(errors,'TAGS_IS_REQUIRED')}),
					dimensions				:	Joi.object().example({'height':'123',"width":"123","length":'123',"weight":"123"}).error(errors=>{return Common.routeError(errors,'DIMANTIONS_IS_REQUIRED')}),
					additionalInformation	:	Joi.object().example({}).optional().allow({},null),
					productLanguages: Joi.array().items(Joi.string()).optional(),
					ordering: Joi.number().optional().default(1),
					videoLink: Joi.string().trim().optional().allow(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/product",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product",
			description:"Fetch Product List",
			auth: {strategy: 'jwt', scope: ["admin","manage_category","costumer","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					categoryId: Joi.number().integer().optional(),
					isPublished: Joi.number().integer().optional().default(null),
					isFeatured: Joi.number().integer().optional().default(null),
					language: Joi.string().optional().default(null),
					name:Joi.string().max(250).optional(),
					limit: Joi.number().integer().optional(),
                    pageNumber: Joi.number().integer().min(1).optional(),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/product-id",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product by id ",
			description:"Fetch Product By id",
			auth: {strategy: 'jwt', scope: ["admin","manage_category","costumer","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'PRODUCT_ID_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "PATCH",
		path : "/product",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to update an existing Product",
			description:"Update Product",
			auth: {strategy: 'jwt', scope: ["admin",'manage-product',"product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					id					: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
					isPublished			: Joi.number().integer().valid(1,0).required().error(errors=>{return Common.routeError(errors,'IS_PUBLISHED_IS_REQUIRED')}),
					name 				: Joi.string().example('name').required().error(errors=>{return Common.routeError(errors,'PRODUCT_NAME_IS_REQUIRED')}),
					highlights 	: Joi.string().trim().example('longDescriptionHtml').optional().allow('',null),
					descriptionHtml 	: Joi.string().trim().example('descriptionHtml').optional().allow('',null),
                    longDescriptionHtml : Joi.string().trim().example('longDescriptionHtml').optional().allow('',null),
					categoryId			: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'CATEGORY_ID_IS_REQUIRED')}),
					courseId: Joi.number().integer().example(1).optional().allow(null).default(null),
					price				: Joi.array().items(
						{
							currencyId	:	Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PRICE_ID_IS_REQUIRED')}),
							price		:	Joi.number().required().example(1.1).error(errors=>{return Common.routeError(errors,'PRICE_VALUE_IS_REQUIRED')})
						}
					).min(1).required().error(errors=>{return Common.routeError(errors,'PRICE_IS_REQUIRED')}),
					// attributeSet		: Joi.array().items(
					// 	{
					// 				id		:	Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'ATTRIBUTE_ID_IS_REQUIRED')}),
					// 				value	:	Joi.string().trim().example('attribute-value').error(errors=>{return Common.routeError(errors,'VALUE_IS_REQUIRED')})
					// 	}
					// ).min(1).required().error(errors=>{return Common.routeError(errors,'ATTRIBUTE_SET_IS_REQUIRED')}),

					attachment				:	Joi.object().example({'id':1,'path':'demo'}).optional().default({}),
					tags					:	Joi.array().items(Joi.number().integer()).example([1,2,3,4]).error(errors=>{return Common.routeError(errors,'TAGS_IS_REQUIRED')}),
					dimensions				:	Joi.object().keys().example({'height':'123',"width":"123","length":'123',"weight":"123"}).error(errors=>{return Common.routeError(errors,'DIMANTIONS_IS_REQUIRED')}),
					additionalInformation	:	Joi.object().example({}).optional().allow({}).default({}),
					dgStoreId				:	Joi.string().trim().example('longDescriptionHtml').optional().allow(null),
					productLanguages: Joi.array().items(Joi.string()).optional(),
					ordering: Joi.number().optional().default(1),
					videoLink: Joi.string().trim().optional().allow(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "DELETE",
		path : "/product",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product by id",
			description:"Delete Products",
			auth: {strategy: 'jwt', scope: ["admin","manage_products","costumer","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},

	//************************************** Tags************************************************************ */\
	{
		method : "POST",
		path : "/tag",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product-Tag"],
			notes: "Endpoint to define a new tag ",
			description:"Create Tag",
			auth: {strategy: 'jwt', scope: ["admin","manage_tags","manange-products","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
                   // groupTypeId : Joi.number().required().error(errors=>{return Common.routeError(errors,'GROUP_TYPE_IS_REQUIRED')}),
				   	id:Joi.number().optional(),
				   	//parentId: Joi.number().optional(),
					//attachment:Joi.object().optional(),
                    name : Joi.string().required().error(errors=>{return Common.routeError(errors,'GROUP_NAME_TYPE_IS_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	
	{
		method : "GET",
		path : "/tag",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product-Tag"],
			notes: "Endpoint to get Tag",
			description:"Fetch Tag",
			auth: {strategy: 'jwt', scope: ["admin","manage_category","costumer","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					name:Joi.string().max(250).optional(),
					limit: Joi.number().integer().optional(),
                    pageNumber: Joi.number().integer().min(1).optional(),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/tag-id",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product-Tag"],
			notes: "Endpoint to get Tag",
			description:"Fetch Tag",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "DELETE",
		path : "/tag",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product-Tag"],
			notes: "Endpoint to Delete Tag",
			description:"Delete Tag",
			auth: {strategy: 'jwt', scope: ["admin","manage_category","costumer","product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/tag-list",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product-Tag"],
			notes: "Endpoint to get Tag",
			description:"Get all",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					name:Joi.string().optional(),
					orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/product-publish",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to publish a product",
			description:"Publish Product",
			auth: {strategy: 'jwt', scope: ["admin",'manage-product',"product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					id					: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PRODUCT_ID_IS_REQUIRED')}),
					isPublished			: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'IS_PUBLISHED_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/product-featured",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to publish a product",
			description:"Publish Product",
			auth: {strategy: 'jwt', scope: ["admin",'manage-product',"product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					id					: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'PRODUCT_ID_IS_REQUIRED')}),
					isFeatured			: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'IS_PUBLISHED_IS_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/product-details",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product Details",
			description:"Get Product Details",
			auth:false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
					userId:Joi.number().optional()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/product-list",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product",
			description:"Fetch Products",
			auth:false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					name	:	Joi.string().max(250).optional(),
					userId	:	Joi.number().integer().optional(),
					isFeatured: Joi.number().integer().optional(),
					limit: Joi.number().integer().optional(),
					categoryId:Joi.number().integer(),
                    pageNumber: Joi.number().integer().min(1).optional(),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id','price','ordering').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/dg-product-list",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Dg-Store-Product"],
			notes: "Endpoint to Get Dg Store Product List",
			description:"Dg Store Product List",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					//id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/dg-product-id",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Dg-Store-Product"],
			notes: "Endpoint to Get Dg Store Product",
			description:"Dg Store Product ",
			auth: false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
				id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},

	 
    {
		method : "POST",
		path : "/cart-product",
		handler : productManagementgateway,
		options : {
			tags: ["api", "Cart-Product"],
			notes: "Add Cart-Product",
			description: "Add Cart-Product",
			auth : {strategy:'jwt', scope: ["admin","manage_cart","costumer",'companion']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly: false
				},
				payload : {
                    productId 	: Joi.number().example(1).required().error(errors=>{return Common.routeError(errors,'PRODUCT_ID_IS_REQUIRED')}),
                    count 		: Joi.number().example(1).min(1).optional().default(1)
				},
				failAction: async(req,h, err) => {
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},

	
	{
		method : "GET",
		path : "/cart-product",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Cart-Product"],
			notes : "Get Cart-Product List",
			description: "Get Cart-Product List",
			auth : {strategy: 'jwt',scope : ["admin","manage_cart","costumer",'companion']},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					limit: Joi.number().integer().optional(),
                    pageNumber: Joi.number().integer().min(1).optional(),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method :Common.prefunction}]
		}
    },
    
    

	{
		method : "DELETE",
		path : "/cart-product",
		handler : productManagementgateway,
		options: {
			tags : ["api", "Cart-Product"],
			notes : "Delete Attribute",
			description : "Delete Attribute",
			auth : {strategy: 'jwt', scope : ["admin","manage_cart","costumer",'companion']},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					productId 	: Joi.number().example(1).required().error(errors=>{return Common.routeError(errors,'PRODUCT_ID_IS_REQUIRED')}),
                    count 		: Joi.number().example(1).min(1).optional().default(1)
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre: [{method: Common.prefunction}]
		}
    },
	 
    {
		method : "POST",
		path : "/wish-list",
		handler : productManagementgateway,
		options : {
			tags: ["api", "Wish-List"],
			notes: "Add Wish-List",
			description: "Add Wish-List",
			auth : {strategy:'jwt', scope: ["admin","manage_cart","costumer",'companion']},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly: false
				},
				payload : {
                    productId 	: Joi.number().example(1).required().error(errors=>{return Common.routeError(errors,'PRODUCT_ID_IS_REQUIRED')})
				},
				failAction: async(req,h, err) => {
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},

	
	{
		method : "GET",
		path : "/wish-list",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Wish-List"],
			notes : "Get wish-list List",
			description: "Get Wish-List List",
			auth : {strategy: 'jwt',scope : ["admin","manage_cart","costumer",'companion']},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					limit: Joi.number().integer().optional(),
                    pageNumber: Joi.number().integer().min(1).optional(),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method :Common.prefunction}]
		}
    },

	//****************************************** Order */ ******************************************
	{
		method : "POST",
		path : "/order",
		handler : productManagementgateway,
		options : {
			tags: ["api", "Cart-Order"],
			notes: "Add Cart-Order",
			description: "Add Cart-Order",
			auth : false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options : {
					abortEarly: false
				},
				payload : {
                  data:Joi.string().required().error(errors=>{return Common.routeError(errors,'DATA_IS_REQUIRED')}),
				  userId:Joi.number().optional()
				},
				failAction: async(req,h, err) => {
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},

	
	{
		method : "GET",
		path : "/order",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Order List"],
			notes : "Get Cart-Order List",
			description: "Get Cart-Order List",
			auth : {strategy: 'jwt',scope : ["admin","manage_cart","costumer",'companion','order_management','order_management']},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					limit: Joi.number().integer().optional(),
                    pageNumber: Joi.number().integer().min(1).optional(),
					userId:Joi.number().integer().optional(),
                    orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id'),
					categoryId: Joi.string().allow(null).optional(),
					startDate:Joi.date().example('2022-07-05T04:00:00Z').optional(),
					endDate:Joi.date().example('2022-07-05T04:00:00Z').optional()
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method :Common.prefunction}]
		}
    },
	{
		method : "GET",
		path : "/order-id",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Order List"],
			notes : "Get Cart-Order List",
			description: "Get Cart-Order List",
			auth : {strategy: 'jwt',scope : ["admin","manage_cart","costumer",'companion','order_management']},
			validate : {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options : {
					abortEarly : false
				},
				query : {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),	
				},
				failAction : async(req, h, err) =>{
					return  Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method :Common.prefunction}]
		}
    },
	{
		method : "GET",
		path : "/similar-products",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product Details",
			description:"Get Product Details",
			auth:false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
					userId:Joi.number().optional().default(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/admin/similar-products",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product Details",
			description:"Get Product Details",
			auth:false,
			validate: {
				headers: Joi.object(Common.headers()).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					id:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
					userId:Joi.number().optional().default(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/similar-products",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to update an existing Product",
			description:"Update Product",
			auth: {strategy: 'jwt', scope: ["admin",'manage-product',"product_management"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					id: Joi.number().integer().example(1).required().error(errors=>{return Common.routeError(errors,'ID_IS_REQUIRED')}),
					similarProducts: Joi.array().items(Joi.number().optional()).optional().default(null)
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/product-links",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product Details",
			description:"Get Product Details",
			auth: {strategy: 'jwt'},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					productId:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')})
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "POST",
		path : "/product-link",
		handler : productManagementgateway,
		options: {
			tags: ["api", "Product"],
			notes: "Endpoint to get Product Details",
			description:"Get Product Details",
			auth: {strategy: 'jwt'},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				payload: {
					productId:Joi.number().integer().required().error(errors=>{return Common.routeError(errors,'ID_REQUIRED')}),
					productInfo: Joi.array().items({
						type: Joi.string().required().error(errors=>{return Common.routeError(errors,'TYPE_REQUIRED')}),
						url: Joi.string().required().error(errors=>{return Common.routeError(errors,'URL_REQUIRED')})
					}).optional()
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
]