const {mobileDashboard, companionDashboardCount} = require('../controllers/dashboard.controller');

module.exports = [
	// ***********************************  Dashboard Endpoint  ***********************************
 
	
	{
		method : "GET",
		path : "/mobile-dashboard",
		handler : mobileDashboard,
		options: {
			tags: ["api", "Dashboard"],
			notes: "Endpoint to get Mobile Dashboard data",
			description:"Fetch Mobile Data",
			auth:{strategy: 'jwt',scope:["admin","costumer","companion","student"]},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {
					// name:Joi.string().max(250).optional(),
					// limit: Joi.number().integer().optional(),
                    // pageNumber: Joi.number().integer().min(1).optional(),
                    // orderByValue: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
                    // orderByParameter: Joi.string().valid('createdAt', 'id').optional().default('id')
				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	{
		method : "GET",
		path : "/companion-dashboard-count",
		handler : companionDashboardCount,
		options: {
			tags: ["api", "Dashboard"],
			notes: "Endpoint to get Mobile Dashboard data",
			description:"Fetch Mobile Data",
			auth:{strategy: 'jwt'},
			validate: {
				headers: Joi.object(Common.headers(true)).options({
					allowUnknown: true
				}),
				options: {
					abortEarly: false
				},
				query: {

				},
				failAction: async (req, h, err) => {
					return Common.FailureError(err, req);
				},
				validator: Joi
			},
			pre : [{method: Common.prefunction}]
		}
	},
	
]